import os
import json
import pandas as pd
import time

def extract_postprocessing_values(json_directory):
    # List to store the extracted data
    data = []
    
    # Iterate through the JSON files in the directory
    for filename in os.listdir(json_directory):
        if filename.endswith('.json'):
            with open(os.path.join(json_directory, filename), 'r') as f:
                json_data = json.load(f)
            
            # Dictionary to hold the data for the current file
            file_data = {'file_name': filename}
            
            # Extract the postprocessing values from FieldData
            for field in json_data.get('Value', {}).get('FieldData', []):
                field_name = field.get('Name', '')
                postprocessing_value = field.get('PostProcessingValue', '')
                
                # Add the field data to the dictionary
                file_data[field_name] = postprocessing_value
            
            # Append the file data to the list
            data.append(file_data)
    
    # Create a pandas DataFrame from the extracted data
    df = pd.DataFrame(data)
    return df

if __name__ == '__main__':
    # Directory containing the JSON files
    json_directory = "/Users/<USER>/Documents/harsh/Documents/clients/underwritter/9000001 - 1003/de_result"
    save_results_dir = f'/Users/<USER>/Documents/harsh/Documents/clients/underwritter/9000001 - 1003/{int(time.time())}'
    os.makedirs(save_results_dir, exist_ok=True)
    
    # Extract postprocessing values and save to Excel       
    postprocessing_df = extract_postprocessing_values(json_directory)
    postprocessing_df.to_excel(f'{save_results_dir}/1003_table.xlsx', index=False)

    print("Postprocessing results saved at:", save_results_dir)
