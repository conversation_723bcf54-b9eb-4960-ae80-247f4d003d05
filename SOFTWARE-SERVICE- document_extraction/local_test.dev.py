import json
import os
import argparse
import pandas as pd
from docvu_de_core.api import *
import datetime
import yaml

class TestLocal:
    def __init__(self):
        self.api = API(curr_path=os.path.dirname(__file__))
        pass

    def extract_data(self, pdf_data_path=None,
                     ocr_xml_json_path=None,
                     pdf_file_path=None,
                     to_extract_field=None,
                     fetch_prev_to_extract_field=False,
                     classification_required=True,
                     separate_table_data=True):
        print('Running extract_data')
        form_type, search_start_page = (
            self.api._classify(pdf_data_path=pdf_data_path,
                               ocr_xml_json_path=ocr_xml_json_path,
                               pdf_file_path=pdf_file_path,
                               classification_required=classification_required,
                               for_testing=True))
        print("Form Type = {}".format(form_type))

        if form_type == 'misc':
            return None
        de_json = self.api._extract(
            form_type,
            search_start_page,
            pdf_data_path=pdf_data_path,
            ocr_xml_json_path=ocr_xml_json_path,
            pdf_file_path=pdf_file_path,
            to_extract_field=to_extract_field,
            return_indexing_info=False,
            fetch_prev_to_extract_field=fetch_prev_to_extract_field,
            separate_table_data = separate_table_data,
            for_testing = True)
        file_name = ocr_xml_json_path.split('/')[-1]
        if form_type == "long_form":
            file_path = os.path.join('./de_results/long_form_json_results', (file_name + '.json') if not file_name.endswith('json') else file_name)
        elif form_type == "short_form":
            file_path = os.path.join('./de_results/short_form_json_results', (file_name + '.json') if not file_name.endswith('json') else file_name)
        elif form_type == "owner_form":
            file_path = os.path.join('./de_results/owner_form_json_results', (file_name + '.json') if not file_name.endswith('json') else file_name)
        if to_extract_field is None:
            if form_type != 'misc':
                print("de@_json?>?>?>", de_json)
                with open(file_path, 'w', encoding='utf8') as json_file:
                    json.dump(de_json, json_file)
        else:
            if form_type != 'misc':
                print("de_json?>?>?>", de_json)
                with open(file_path, 'w', encoding='utf8') as json_file:
                    json.dump(de_json, json_file)


        return de_json

    def run_all_policies(self, form_csv_path,
                                     ocr_xml_json_path=None,
                                     pdf_file_path=None,
                                     to_extract_field=None,
                                     fetch_prev_to_extract_field=False,
                                     classification_required=False):

        data = {"Form_type": [], "Original_Image_Name": [], "Original_Image_Length": [],
                "Policy_Type_Text": [], "Underwriter": [],
                "Full_Policy_Number": [],
                "File_Number": [], "Order_Number": [], "Reference_Full_Policy_Number": [],
                "Property_Full_Street_Address_line1": [], "Property_City": [], "Property_State_Code": [],
                "Policy_Liability": [], "Effective_Date": [], "Policy_Premium": [],
                "Insured_Full_Name": [], "Interest_Estate": [], "Vesting": [], "Vesting_Deed_Date": [],
                "Vesting_Deed_Document_Number": [], "Vesting_Deed_Recording_Date": [],
                "Legal_Description_Text": [], "Legal_Subdivision": [], "Legal_County_State": [],
                "Loan_Recording_Information_Exhibit": [],
                "Property_Sequence_Number": [], "Legal_Description_Sequence_Number": [], "LoanNumber": [],
                "Property_APN": [], "Property_Full_Address": [], "Property_Full_Street_Address_line2": [],
                "Property_ZIPcode": [], "Title_Company": [], "Vesting_Deed_Book_Number": [],
                "Vesting_Deed_Page_Number": [], "Endorsement_Type": [],
                "Short_Form_Flag": [],
                "Borrower_Full_Name": [], "Loan_Number": [], "Loan_Amount": [], "Loan_Date": [],
                "Loan_Recording_Date": [], "Loan_Document_Number": [], "Loan_Book_Page_Number": [],
                "Loan_Page_Number": [], "Loan_Book_Number": [], "Loan_Recording_Information": [],
                "Exception_Text": [], "Exception_Header": [],
                "Property_County_Address": [], "Addendum_attached": [],
                "Endorsement_Sequence_Number": [],
                "Exception_Sequence_Number": []
                }

        for index, (json_file, pdf_file) in enumerate(zip(ocr_xml_json_path, pdf_file_path)):
            # _, _ = local_test.ocrop.get_ocr_text_and_save_page_xml(pdf_file_path, pre_process=True, flush_dir=False)
            print('Processing {} - {} - {}'.format(index, pdf_file, json_file))
            res = self.extract_data(pdf_data_path=None,
                                         ocr_xml_json_path=json_file,
                                         pdf_file_path=pdf_file,
                                         to_extract_field=to_extract_field,
                                         fetch_prev_to_extract_field=False,
                                         classification_required=classification_required,
                                         separate_table_data = True)

            table_value_mapper = {'Endorsement_Type': ['Endorsement_Type'],
                                  'Exception_Header': ['Exception_Header', 'Exception_Text', 'Exception_Type'],
                                  'Legal_Description_Text_All': ['Legal_Description_Text_All'],
                                  'Legal_Description_Text_Exhibit_All': ['Legal_Description_Exhibit_Text_All']}
            if res is None:
                continue

            if res["Value"]["TableData"] is None:
                res["Value"]["TableData"] = []
            for table_field in res["Value"]["TableData"]:
                table_val_list = []
                row_fields = table_value_mapper[table_field['Name']]
                if not table_field['Rows']:
                    for i in range(len(row_fields)):
                        table_val_list.append({'Name' : row_fields[i], 'Value': []})
                else:
                    for i in range(len(row_fields)):
                        table_val_list.append({'Name': row_fields[i], 'Value': []})
                    for row in table_field['Rows']:
                        for idx, col in enumerate(row['Columns']):
                            if col['Name'] == 'Exception_Header' and col['Value'] in table_val_list[idx]['Value']:
                                continue
                            table_val_list[idx]['Value'].append(col['Value'])

                for kdx, _ in enumerate(table_val_list):
                    table_val_list[kdx]['Value'] = str(table_val_list[kdx]['Value'])
                res["Value"]["FieldData"].extend(table_val_list)

            # print('Processed_ {},- {}'.format(index, pdf_file_path))
            # print("RES::::",res)

            for k, v in data.items():
                print("Finding key element:", k)
                found_k = False

                if k.strip() in res.keys():
                    data[k].append(res[k])
                    found_k = True
                    continue

                for val in res["Value"]['FieldData']:
                    if k == val.get("Name"):
                        data[k].append(val["Value"])
                        found_k = True
                        break

                    if not found_k:
                        print("not found k!!!!", k)
                        data[k].append("Not_Found")

        with open('data.json', 'w') as f:
            f.write(json.dumps(data))
        # Print the length of each array in the data dictionary
        for key, value in data.items():
            print(f"Length of {key}: {len(value)}")

        df = pd.DataFrame(data)
        df.to_csv(form_csv_path)

        return


def replace_space_with_text_in_name(path_dir):
    for dirpath, dirs, files in os.walk(path_dir):
        for filename in files:
            fname = os.path.join(dirpath, filename)
            file_split = filename.split(' ')
            new_fname = "_".join(v for v in file_split)
            new_path = os.path.join(dirpath, new_fname)
            os.rename(fname, new_path)

def list_files_recursively(base_dir, file_extension=None):
    """Helper function to list files recursively based on file extension."""
    all_files = []
    for dirpath, dirs, files in os.walk(base_dir):
        print("Checking directory:", dirpath)  # Print the current directory being checked
        for file in files:
            if file_extension is None or file.endswith(file_extension):
                all_files.append(os.path.join(dirpath, file))
    return all_files

def get_ocr_pdf_files(pdf_dir, ocr_dir):
    # Determine if the inputs are directories or lists of directories
    if isinstance(pdf_dir, list):
        pdf_files = [f for d in pdf_dir for f in list_files_recursively(d, '.pdf')]
    else:
        pdf_files = list_files_recursively(pdf_dir, '.pdf')
        print("PDF files found:", pdf_files)  # Check what files are being read

    if isinstance(ocr_dir, list):
        ocr_files = [f for d in ocr_dir for f in list_files_recursively(d, '.json')]
    else:
        ocr_files = list_files_recursively(ocr_dir, '.json')

    # Extract base names for matching
    ocr_base_names = {os.path.splitext(os.path.basename(f))[0]: f for f in ocr_files}
    matched_pdf_files = []
    matched_ocr_files = []

    # Match PDF files with corresponding OCR JSON files
    for pdf_file in pdf_files:
        pdf_base_name = os.path.splitext(os.path.basename(pdf_file))[0]
        if pdf_base_name in ocr_base_names:
            matched_pdf_files.append(pdf_file)
            matched_ocr_files.append(ocr_base_names[pdf_base_name])
    print("-----------------------------------------------------")
    print("****matched_pdf_files::", matched_pdf_files)
    print("-----------------------------------------------------")
    print("****matched_ocr_files::", matched_ocr_files)
    return matched_pdf_files, matched_ocr_files

def load_config(config_path):
    with open(config_path, 'r') as file:
        return yaml.safe_load(file)

def run():
    print("Running Locally")

    config = load_config('local_test_params.yml')

    settings = config['settings']
    directories = config['directories']

    print(f"Running document extraction with the following settings:")
    for key, value in settings.items():
        print(f"{key}: {value}")

    # Example of using these settings
    lender_policy_ocr_dir = directories['ocr']['lender']
    owner_policy_ocr_dir = directories['ocr']['owner']
    all_policy_ocr_dir = directories['ocr']['all']
    prod_ocr_dir = directories['ocr']['prod']

    lender_pdf_policy_dir = directories['pdf']['lender']
    owner_pdf_policy_dir = directories['pdf']['owner']
    all_policy_pdf_dir = directories['pdf']['all']
    prod_pdf_dir = directories['pdf']['prod']

    mismatch_file = settings['mismatch_file']

    test_one = False
    if settings['mode'] == 'single':
        test_one = True
    elif settings['mode'] == 'prod':
        test_one = True
    else:
        test_one = False

    test_lender = False
    if settings['type'] == 'lender':
        test_lender = True
    else:
        test_lender = False

    test_all = False
    if settings['type'] == 'all':
        test_all = True
    else:
        test_all = False

    test_prod = False
    if settings['type'] == 'prod':
        test_prod = True
    else:
        test_prod = False

    file_name = settings['file_name']
    ocr_xml_json = settings['ocr_xml_json']
    classification_required = settings['classification_required']
    to_extract_field = settings['to_extract_field']
    test_for_mismatch = settings['test_for_mismatch']
    test_all_for_one_key = settings['test_all_for_one_key']

    test_local = TestLocal()
    if test_one:
        if test_lender:
            policy_path = os.path.join(lender_policy_ocr_dir, file_name + ".json")
            # policy_path = os.path.join(lender_policy_ocr_dir, ocr_xml_json + ".json")
            policy_pdf = os.path.join(lender_pdf_policy_dir, file_name + ".pdf")
        elif test_prod:
            policy_path = os.path.join(prod_ocr_dir, ocr_xml_json + ".json")
            policy_pdf = os.path.join(prod_pdf_dir, file_name + ".pdf")
        elif test_all:
            policy_path = os.path.join(all_policy_ocr_dir, ocr_xml_json + ".json")
            policy_pdf = os.path.join(all_policy_pdf_dir, file_name + ".pdf")
        else:
            policy_path = os.path.join(owner_policy_ocr_dir, file_name + ".json")
            policy_pdf = os.path.join(owner_pdf_policy_dir, file_name + ".pdf")
        op = test_local.extract_data(pdf_data_path=None,
                                     ocr_xml_json_path=policy_path,
                                     pdf_file_path=policy_pdf,
                                     to_extract_field=to_extract_field,
                                     fetch_prev_to_extract_field=False,
                                     classification_required=classification_required)
        print(op)
        exit(0)

    if test_for_mismatch:
        print("TEST CASE WORKING")
        # mismatch_file = "C:/Users/<USER>/Documents/SLS_CODE/required_pdf_names_500.xlsx"
        df = pd.read_excel(mismatch_file)
        mismatched_pdf = df["FileName"].tolist()
        classification_required = True
        to_extract_field = None #"Loan_Recording_Information"
        date_id = "01072024" #str(datetime.datetime.now())
        pdf_policy_dir = []
        json_files = []
        for f in mismatched_pdf:
            policy_path = os.path.join(all_policy_ocr_dir, f + ".json")
            policy_pdf = os.path.join(all_policy_pdf_dir, f + ".pdf")
            pdf_policy_dir.append(policy_pdf)
            json_files.append(policy_path)

        # file_path = './de_results/stewart_form_de_500_SLS_old_evaluation_{}_{}.csv'.format(date_id, to_extract_field)
        file_path = './de_results/500_newsls/merged3_pushed_code_stewart_form_de_new_SLS_execution_{}_{}.csv'.format(date_id, to_extract_field)
        test_local.run_all_policies(file_path,
                                    ocr_xml_json_path=json_files,
                                    pdf_file_path=pdf_policy_dir,
                                    to_extract_field=to_extract_field,
                                    fetch_prev_to_extract_field=False,
                                    classification_required=classification_required)
        exit(0)

    if test_all_for_one_key:
        test_lender = False
        test_all = True
        to_extract_field = None #"Loan_Recording_Information"
        date_id = "11062024"
        fetch_prev_to_extract_field = False
        if test_all:
            pdf_policy_dir, json_files = get_ocr_pdf_files(all_policy_pdf_dir, all_policy_ocr_dir)
            if to_extract_field:
                file_path = './de_results/stewart_form_4k_de_{}_{}.csv'.format(date_id, to_extract_field)
            else:
                print("ALL FILE 4K TEST CASE RUNNING")
                file_path = './de_results/stewart_form_4k_de_{}_v1.csv'.format(date_id)
        elif test_lender:
            pdf_policy_dir, json_files = get_ocr_pdf_files(lender_pdf_policy_dir, lender_policy_ocr_dir)
            if to_extract_field:
                file_path = './de_results/stewart_lender_de_{}_{}.csv'.format(date_id, to_extract_field)
            else:
                file_path = './de_results/stewart_lender_de_{}_v1.csv'.format(date_id)
        else:
            pdf_policy_dir, json_files = get_ocr_pdf_files(owner_pdf_policy_dir, owner_policy_ocr_dir)
            if to_extract_field:
                file_path = './de_results/stewart_owner_de_{}_{}.csv'.format(date_id, to_extract_field)
            else:
                file_path = './de_results/stewart_owner_de_{}_v1.csv'.format(date_id)

        test_local.run_all_policies(file_path,
                                     ocr_xml_json_path=json_files,
                                     pdf_file_path=pdf_policy_dir,
                                     to_extract_field=to_extract_field,
                                     fetch_prev_to_extract_field=False,
                                     classification_required=classification_required)
        exit(0)

if __name__ == "__main__":
    run()