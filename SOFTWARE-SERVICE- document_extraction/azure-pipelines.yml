# Trigger the pipeline on PRs and when changes are pushed
trigger:
  branches:
    include:
      - dev/dev         # Trigger on this branch when changes are pushed
      - uat/uat         # Trigger on this branch when changes are pushed
      - prod/prod       # Trigger on this branch when changes are pushed
      - Ibrahim/azure_dev

pr:
  branches:
    include:
      - uat/uat         # Run pipeline on PRs targeting uat/uat
      - prod/prod       # Run pipeline on PRs targeting prod/prod

pool:
  vmImage: ubuntu-latest  # Use the latest Ubuntu image for the build

strategy:
  matrix:
    Python312:
      python.version: '3.12'  # Set Python version to 3.12

variables:
  should_overwrite: true  # Control whether existing blobs should be overwritten
  file_type_filter: '.whl'  # File type filter for the upload

steps:
# Step 1: Set the specified Python version
- task: UsePythonVersion@0
  inputs:
    versionSpec: '$(python.version)'  # Use Python version defined in matrix
  displayName: 'Use Python $(python.version)'

# Step 2: Install dependencies and the Azure Blob Storage SDK
- script: |
    python -m pip install --upgrade pip  # Ensure pip is up-to-date
    pip install -r requirements.txt  # Install project dependencies
    pip install --upgrade packaging  # Ensure packaging is up-to-date
    pip install setuptools==76.1.0 wheel==0.45.1 # upgrade the pip and reinstall packaging package
    pip install azure-storage-blob  # Install Azure Blob Storage SDK
  displayName: 'Install dependencies and Azure SDK'

# Step 3: Verify the packaging module installation
- script: |
    python -c "import packaging; print('Packaging version:', packaging.__version__)"  # Check if packaging is installed and print the version
  displayName: 'Verify packaging installation'

# Step 4: Build the Python package and create a wheel file
- script: |
    pip install setuptools wheel  # Ensure setuptools and wheel are installed
    python setup.py sdist bdist_wheel  # Build source distribution and wheel
  displayName: 'Build package'

# Step 5: Print all environment variables for debugging
- script: |
    echo "Printing all environment variables..."
    printenv  # Print all environment variables for debugging
  displayName: 'Print Environment Variables'

# Step 6: Upload the wheel file to Azure Blob Storage if the branch matches
- script: |
    set -x  # Enable debugging mode to echo each command before execution
    echo "Source branch name: $(Build.SourceBranchName)"  # Print source branch

    # Check if PR target branch exists, fallback to source branch if not
    if [[ -z "$SYSTEM_PULLREQUEST_TARGETBRANCH" ]]; then
        echo "No PR detected, using source branch"
        target_branch=$(echo $(Build.SourceBranchName) | sed 's|refs/heads/||')
    else
        echo "PR detected, target branch: $SYSTEM_PULLREQUEST_TARGETBRANCH"
        target_branch=$(echo "$SYSTEM_PULLREQUEST_TARGETBRANCH" | sed 's|refs/heads/||')  # Use correct variable reference
    fi

    branch_name=$(echo $(Build.SourceBranchName) | sed 's|refs/heads/||')

    # Map the secret variable as an environment variable
    echo "Checking if AZURE_STORAGE_CONNECTION_STRING is available..."

    # Check if the connection string is available
    if [[ -n "$AZURE_STORAGE_CONNECTION_STRING" ]]; then
        echo "AZURE_STORAGE_CONNECTION_STRING is set."
    else
        echo "Error: AZURE_STORAGE_CONNECTION_STRING is empty!"
        exit 1
    fi

    # Convert branch names to lowercase for case-insensitive comparison
    branch_name_lower=$(echo "$branch_name" | tr '[:upper:]' '[:lower:]')
    target_branch_lower=$(echo "$target_branch" | tr '[:upper:]' '[:lower:]')

    # Determine the container name based on branch
    if [[ "$branch_name_lower" =~ ^uat ]] || [[ "$target_branch_lower" =~ ^uat ]]; then
        container_name="docvu-release-uat"
    elif [[ "$branch_name_lower" =~ ^prod ]] || [[ "$target_branch_lower" =~ ^prod ]]; then
        container_name="docvu-release-prod"
    else
        echo "Not on uat or prod branch. Skipping upload."
        exit 0
    fi

    echo "Branch name (original): $(Build.SourceBranchName)"
    echo "Branch name (processed): $branch_name_lower"
    echo "Target branch (processed): $target_branch_lower"
    echo "Using container: $container_name"

    # Run the Python upload script with the necessary parameters
    python upload_script.py \
        --connection_string "$AZURE_STORAGE_CONNECTION_STRING" \
        --container_name "$container_name" \
        --folder_path "dist" \
        --overwrite \
        --file_type_filter ".whl"
  displayName: 'Upload wheel to Azure Blob Storage'
  env:
    AZURE_STORAGE_CONNECTION_STRING: $(AZURE_STORAGE_CONNECTION_STRING)  # Map the secret variable explicitly
