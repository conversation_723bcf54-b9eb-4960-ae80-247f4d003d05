import os
import json
import numpy as np
import pandas as pd
import time

# Function to clean price and numerical fields
def format_df_price(df):
    for column in df.columns:
        col_lower = column.lower()
        if any(keyword in col_lower for keyword in ['price', 'value', 'amount']):
            df[column] = df[column].astype(str).str.replace('$', '').str.replace(' ', '').str.replace(',', '').str.rstrip('.').str.strip()
        elif 'ltv' in col_lower:
            df[column] = df[column].apply(lambda x: str(int(float(x) * 100)) if isinstance(x, str) and x.replace('.', '', 1).isdigit() else str(x))

    df.replace(["nan", "Not Available", "null", "Null", "not available"], "not_found", inplace=True)
    return df


# Function to calculate accuracy between ground truth and predictions
def calculate_accuracy(gt_series, pred_series):
    return (gt_series == pred_series).mean() * 100


# Function to extract predictions from JSON files
def get_de_results_excel(json_directory):
    data = []
    for filename in os.listdir(json_directory):
        if filename.endswith('.json'):
            with open(os.path.join(json_directory, filename), 'r') as f:
                json_data = json.load(f)
            extracted_data = {field['Name'] + '_pred': field['PostProcessingValue'] for field in json_data['Value']['FieldData']}
            extracted_data['file_name'] = filename
            data.append(extracted_data)
    return pd.DataFrame(data)


def lowercase_string(x):
    return str(x).lower()


def get_merged_gt_pred_results(pred_df, gt_df, save_results_dir):
    gt_df.rename(columns={col: col + '_gt' for col in gt_df.columns if col != 'file_name'}, inplace=True)
    pred_df.rename(columns={col: col + '_pred' for col in pred_df.columns if col != 'file_name' and not col.endswith('_pred')}, inplace=True)
    
    gt_df['file_name'] = gt_df['file_name'].str.split('.').str[0].str.strip()
    pred_df['file_name'] = pred_df['file_name'].str.split('.pdf.json').str[0].str.strip()
    
    merged_df = pd.merge(gt_df, pred_df, on='file_name', how='inner')
    merged_df.replace({'': np.nan, None: np.nan}, inplace=True)
    merged_df.fillna('not_found', inplace=True)
    merged_df = merged_df.applymap(lowercase_string)
    
    # Reorder columns to place GT and Prediction side by side
    columns_order = ['file_name']
    for col in gt_df.columns:
        if col != 'file_name':
            pred_col = col.replace('_gt', '_pred')
            if pred_col in pred_df.columns:
                columns_order.extend([col, pred_col])
    
    merged_df = merged_df[columns_order]
    print(merged_df)
    # Save merged results
    merged_df.to_excel(f"{save_results_dir}/merged_results.xlsx", index=False)
    return merged_df

# Compute accuracy for all fields
def get_acc_all_fields(df):
    print(df)
    accuracy_results = {}
    
    for col in df.columns:
        if col.endswith('_gt'):
            pred_col = col[:-3] + '_pred'  # Expected prediction column
            if pred_col in df.columns:  # Ensure pred_col exists before computing accuracy
                accuracy_results[col[:-3]] = calculate_accuracy(df[col], df[pred_col])
            else:
                print(f"Warning: Missing column {pred_col} in predictions.")
    
    return pd.DataFrame(list(accuracy_results.items()), columns=['Field', 'Accuracy'])


if __name__ == '__main__':
    json_directory = "/Users/<USER>/Downloads/post closing/Mapping Team/Annotation and GT/GT/9000828 - Report And Certificate Of Loan Disbursemt -VA 26-1820/de_result"
    gt_excel_path = "/Users/<USER>/Downloads/post closing/Mapping Team/Annotation and GT/GT/9000828 - Report And Certificate Of Loan Disbursemt -VA 26-1820/transferred_Report and Certificate.xlsx"
    get_accuracy = True

    pred_results_df = get_de_results_excel(json_directory)
    save_results_dir = f'valon_result/save_results_{int(time.time())}'
    os.makedirs(save_results_dir, exist_ok=True)
    pred_results_df.to_excel(f'{save_results_dir}/extracted_results.xlsx', index=False)
    
    if get_accuracy:
        gt_df = pd.read_excel(gt_excel_path, sheet_name="Sheet1")
        print(gt_df)
        
        # Preprocess and clean data
        gt_df = format_df_price(gt_df)
        pred_results_df = format_df_price(pred_results_df)
        print(pred_results_df)
        # Merge ground truth and predictions
        merged_df = get_merged_gt_pred_results(pred_results_df, gt_df, save_results_dir)
        
        # Compute accuracy
        acc_df = get_acc_all_fields(merged_df)
        acc_df.to_excel(f"{save_results_dir}/accuracy_results.xlsx", index=False)
    
    print("Processing complete. Results saved.")
