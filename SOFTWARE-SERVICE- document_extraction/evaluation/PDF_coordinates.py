import cv2
import numpy as np
import json
from pdf2image import convert_from_path
from PIL import Image

def draw_bboxes_on_pdf(pdf_path, json_path, output_pdf_path):
    # Load JSON data
    with open(json_path, 'r') as f:
        data = json.load(f)
    
    # Convert PDF pages to images
    images = convert_from_path(pdf_path)
    annotated_images = []
    
    # Identify the correct structure of the JSON
    field_data = data["Value"].get("FieldData", [])
    
    for page_number, img in enumerate(images):
        img = np.array(img)
        img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)  # Convert to OpenCV format
        
        # Get page width and height
        page_height, page_width, _ = img.shape
        
        for field in field_data:
            if field.get("PageNumber", 0) - 1 != page_number:
                continue
            
            # Extract field coordinates, supporting both JSON formats
            coords = field.get("FieldValueCoordinates", field.get("FieldNameCoordinates", {}))
            x = int(coords.get("X", 0) * page_width)
            y = int(coords.get("Y", 0) * page_height)
            w = int(coords.get("Width", 0) * page_width)
            h = int(coords.get("Height", 0) * page_height)
            
            # if w > 0 and h > 0:  # Only draw if valid dimensions exist
            cv2.rectangle(img, (x, y), (x + w, y + h), (0, 255, 0), 2)
        
        # Convert back to PIL image format
        annotated_images.append(Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB)))
    
    # Save all annotated images as a single PDF
    if annotated_images:
        annotated_images[0].save(output_pdf_path, save_all=True, append_images=annotated_images[1:])

input_pdf = "/home/<USER>/docvufileshareuat/Hoogar/PC_pdf/borrower_authorization/pdf/Variant_1.pdf"
input_json = "/home/<USER>/docvufileshareuat/Valon/Note/FVC_de_results/Variant_1.pdf.json"
output_pdf = "/home/<USER>/docvufileshareuat/Valon/NPF_1003_new/pdf/CPID_BBOX.pdf"

# Example usage
draw_bboxes_on_pdf(input_pdf, input_json, output_pdf)
