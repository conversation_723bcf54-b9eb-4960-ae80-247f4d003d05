import os
import json
import numpy as np
import pandas as pd
import time
from dateutil import parser
from docvu_de_core.modules.DateFinder import DateFinder

def custom_remove_char(x):
    x = str(x).lower()
    if x.endswith('.0'):
        x = x.replace('.0', '')
    if x.startswith('s'):
        x = x.replace('s', '')
    return x
def format_df_price(df):
    for column in df.columns:
        if 'price' in column.lower() or 'value' in column.lower() or 'amount' in column.lower():
            df[column]  = df[column].apply(lambda x:str(x).replace('$', '').replace(' ', '').replace(',','').rstrip('.').strip())
            df[column]  = df[column].apply(custom_remove_char)
        elif 'ltv' in column.lower():
            print("Formatting LTV")
            df[column] = df[column].apply(
                lambda x: str(int(float(x)*100)) if isinstance(x, str) and x.replace('.', '', 1).isdigit() else str(x)
            )
    return df
            
def lowercase_string(x):
    return str(x).lower()

def calculate_accuracy(gt_series, pred_series):
    # Calculate accuracy based on exact match
    return (gt_series == pred_series).mean() * 100

def get_de_results_excel(json_directory):
    # List to store the extracted data
    data = []
    # Iterate through the JSON files in the directory
    for filename in os.listdir(json_directory):
        if filename.endswith('.json'):
            with open(os.path.join(json_directory, filename), 'r') as f:
                json_data = json.load(f)
            extracted_data = {}
            for extracted_field in json_data['Value']['FieldData']:
                key = extracted_field['Name']
                value = extracted_field['PostProcessingValue'] 
                extracted_data[key] = value
            extracted_data['file_name'] = filename
            data.append(extracted_data)
    # Create a pandas DataFrame from the extracted data
    df = pd.DataFrame(data)
    return df 

def get_merged_gt_pred_results(pred_df, gt_df, doc_name):
    gt_df['file_name'] = gt_df['file_name'].apply(lambda x: x.split('.')[0].strip())
    pred_df['file_name'] = pred_df['file_name'].apply(lambda x: x.split('.pdf.json')[0].strip())
    merged_df = pd.merge(gt_df, pred_df, how='inner', on='file_name', suffixes=['_gt', '_pred'])
    columns_to_keep = [col for col in merged_df.columns if '_gt' in col or '_pred' in col] + ['file_name']
    print("!!!!!!columns_to_keep", columns_to_keep)
    merged_df = merged_df[columns_to_keep]
    merged_df.replace({'': np.nan, None: np.nan}, inplace=True)
    merged_df = merged_df.fillna('not_found')
    merged_df = merged_df.applymap(lowercase_string)
    merged_df.to_excel("demo_res.xlsx", index=False)
    print("MERGED:", merged_df.dtypes)

    # Create compare columns and save to Excel with each _gt and _pred columns in separate sheets
    with pd.ExcelWriter('merged_df.xlsx', engine='xlsxwriter') as writer:
        sheet_names = set()
        for column in gt_df.columns:
            if column not in ['file_name', 'form_type']:
                gt_column = column + '_gt'
                pred_column = column + '_pred'
                compare_column = column + '_compare'
                if pred_column in merged_df.columns:
                    merged_df[compare_column] = merged_df[gt_column] == merged_df[pred_column]

                    # Shorten the sheet name if necessary and ensure uniqueness
                    sheet_name = f'{column}_comparison'
                    if len(sheet_name) > 31:
                        sheet_name = sheet_name[:28] + '...'

                    # Ensure the sheet name is unique
                    original_sheet_name = sheet_name
                    suffix = 1
                    while sheet_name.lower() in sheet_names:
                        sheet_name = f'{original_sheet_name[:25]}_{suffix:02}'
                        suffix += 1
                    sheet_names.add(sheet_name.lower())

                    merged_df[[gt_column, pred_column]].to_excel(writer, sheet_name=sheet_name, index=False)

    return merged_df

def get_acc_all_fields(df):
    accuracy_results = {}
    for column in df.columns:
        if column.endswith('_gt'):
            column_name = column[:-3]  # Get the base column name
            gt_column = column
            pred_column = f'{column_name}_pred'
            accuracy = calculate_accuracy(df[gt_column], df[pred_column])
            accuracy_results[column_name] = accuracy
            print(accuracy_results)
    # Convert accuracy_results to a DataFrame for easier manipulation
    accuracy_df = pd.DataFrame(list(accuracy_results.items()), columns=['Column', 'Accuracy'])
    return accuracy_df


if __name__ == '__main__':
    gt_available = False
    doc_name = "Sheet1"
    json_directory = "/Users/<USER>/Downloads/post closing/Mapping Team/Annotation and GT/GT/9000743 - Owners Affidavit/de_results"

    pred_results_df = get_de_results_excel(json_directory)
    save_results_dir = f'valon_extracted_results/save_results_{int(time.time())}'
    os.makedirs(save_results_dir, exist_ok=True)
    pred_results_df.to_excel(f'{save_results_dir}/{doc_name}_extracted_results.xlsx', index=False)
    print("PRED Types", pred_results_df.dtypes)

    if gt_available:
        gt_excel_path = "/Users/<USER>/Downloads/post closing/Mapping Team/Annotation and GT/GT/9000033 - Address Certification/transformed_gt.xlsx"  
        # Directory containing the JSON files

        gt_df = pd.read_excel(gt_excel_path, sheet_name = "Sheet1")
        print("GT_DF", gt_df)
        print("Gt_df_columns!!!", gt_df.columns)

        if doc_name == 'Note':
            gt_df['Original_Interest_Rate_VA_325503'] = gt_df['Original_Interest_Rate_VA_325503'].apply(lambda x: str(x*100))
        elif doc_name == 'UNDERWRITING_SUMMARY_1':
            gt_df['Original_LTV_VA_326039'] = gt_df['Original_LTV_VA_326039'].apply(lambda x: str(x*100))

        if doc_name == "PMI_CANC_DISCLOSURE":
            for column in gt_df.columns:
                if 'date' in column.lower():
                    print("column!!", column)
                    return_format = "%m/%d/%Y"  # Format for returning dates
                    df = DateFinder(return_format=return_format)
                    dates_list = gt_df[column].tolist()
                    extracted_dates_list = []
                    for text in dates_list:
                        if text:
                            extracted_date = df.get_dates_in_text(str(text), return_position_info=False, return_first=True)
                            extracted_dates_list.append(extracted_date)
                        else:
                            extracted_dates_list.append('not_found')
                    gt_df[column] = extracted_dates_list
        
        gt_df = format_df_price(gt_df)
        # Apply strip() and lower() to all string columns
        gt_df = gt_df.applymap(lambda x: x.strip() if isinstance(x, str) else str(x))
        gt_df.replace("nan", "not_found", inplace=True)
        gt_df.replace("Not Available", "not_found", inplace=True)
        pred_results_df = format_df_price(pred_results_df)
        print("HEASDER",pred_results_df.head())
        print("GTHEADER", gt_df.head())
        merged_df = get_merged_gt_pred_results(pred_results_df, gt_df, doc_name=doc_name)
        merged_df.to_excel(f'{save_results_dir}/{doc_name}_merged.xlsx', index=False)
        acc_df = get_acc_all_fields(merged_df)
        acc_df.to_excel(f'{save_results_dir}/{doc_name}_acc_results.xlsx', index=False)
        print(acc_df)
        print("RESULTS SAVED AT", save_results_dir)