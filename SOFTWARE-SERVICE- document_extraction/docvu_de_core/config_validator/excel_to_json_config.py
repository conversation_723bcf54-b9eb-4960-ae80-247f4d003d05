import pandas as pd
import json
from config_validator import NeewConfigValidator

class ExcelToJsonConverter:
    def __init__(self, excel_file_path, json_file_name):
        self.excel_file_path = excel_file_path
        self.json_file_name = json_file_name
        self.validator = NeewConfigValidator()
        self.has_errors = False

    def convert_to_json(self):
        df = pd.read_excel(self.excel_file_path)
        df = df.fillna('')

        config_json = []
        for i in range(df.shape[0]):
            if df.iloc[i]['Page'] == "":
                continue
            item = {
                "key": df.iloc[i]['key'].strip(),
                "direction": df.iloc[i]['value_direction'].strip().lower(),
                "type": df.iloc[i]['type'].strip().lower().replace(" ", ""),
                "multi_line_value": False,
                "end_identifier": df.iloc[i]['end_identifier'].strip(),
                "start_identifier": df.iloc[i]['start_identifier'].strip(),
                "possible_page_numbers": [int(df.iloc[i]['Page'])],
                "field_id": int(df.iloc[i]['field_id']),
                "field_name": df.iloc[i]['Field Name'].strip(),
                "document_id": int(df.iloc[i]['document_id'])
            }
            if item['direction'].lower() == 'bottom':
                item['direction'] = 'down'

            if df.iloc[i]['multi_line_value'].lower() == "yes":
                item['multi_line_value'] = True

            if df.iloc[i]['type'].strip().lower().replace(" ", "") in ['omr', 'checkbox']:
                item["additional_info"] = {
                        "field_options": df.iloc[i]['field_options'].split(",")
                    }

            if df.iloc[i]['nth_line']:
                if "additional_info" in item:
                    item["additional_info"]["nth_line"] = int(df.iloc[i]['nth_line'])
                else:
                    item["additional_info"] = {
                            "nth_line": int(df.iloc[i]['nth_line'])
                        }

            # Validate each item
            is_valid, error_message = self.validator.parser_config(item, i)
            if not is_valid:
                print(f"Error parsing config at row {i+1}: {error_message}")
                self.has_errors = True
                break
            
            is_valid, error_message = self.validator.validate_valid_data(item, i)
            if not is_valid:
                print(f"Validation failed for config at row {i+1}: {error_message}")
                self.has_errors = True
                break

            config_json.append(item)

        # Write JSON file only if there were no errors
        if not self.has_errors:
            with open(f"./de_config/{self.json_file_name}.json", "w") as f:
                f.write(json.dumps(config_json))

# Example usage:
if __name__ == "__main__":
    converter = ExcelToJsonConverter("/home/<USER>/secondry_drive/ibrahim/Data_Extraction/SOFTWARE-SERVICE-%20document_extraction/trial/newcheck_validator.xlsx", "bot1003_de")
    converter.convert_to_json()
