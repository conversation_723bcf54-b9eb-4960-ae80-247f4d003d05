import json
from typing import List, Tu<PERSON>, Dict
from docvu_de_core.page_info_parser.BigFontClusterer import BigFontClusterer


class Classify:
    def __init__(self, config):
        if isinstance(config, str):
            with open(config, 'r') as f:
                self.config = json.load(f)
        elif isinstance(config, dict):
            self.config = config

        self.max_upper_block = self.config.get('max_upper_block', 3)
        self.use_upper_split_percentage = self.config.get('use_upper_split_percentage', 0.45)
        self.max_lines_for_header = self.config.get('max_lines_for_header', 5)
        self.max_upper_lines_for_key_search = self.config.get('max_upper_lines_for_key_search', 8)
        self.debug = self.config.get('debug', True)

    def sort_list(self, l: List[Tuple]) -> List[Tuple]:
        def is_upper_case(s: str) -> bool:
            return s.isupper()

        sorted_list = sorted(l, key=lambda x: (not is_upper_case(x[2]), -x[1], x[0]))
        return sorted_list

    def get_upper_blocks_data_from_all_pages(self, merged_page_json: str) -> Tuple[
        Dict[int, List[str]], Dict[int, List[int]]]:
        with open(merged_page_json) as temp_f:
            merged_pages_dict = json.loads(temp_f.read())

        upper_pages = {}
        text_height = {}
        page_count = merged_pages_dict.keys()
        for page in page_count:
            block_text = []
            height = []
            found_end = False
            page_json_data = merged_pages_dict[str(page)]['COMPOSED_BLOCKS'] if isinstance(merged_pages_dict[str(page)],
                                                                                           dict) else merged_pages_dict[
                str(page)]
            for index, block_data in enumerate(page_json_data):
                img_height = int(block_data["IMAGE_HEIGHT"]) if "IMAGE_HEIGHT" in block_data.keys() else -1
                max_upper_vpos_permissible = int(img_height * self.use_upper_split_percentage)
                if index > self.max_upper_block:
                    continue
                for block in block_data["TEXT_BLOCK"]:
                    text_lines = block["TEXT_LINE"]
                    for lines in text_lines:
                        strings = lines["STRING"]
                        for string in strings:
                            if img_height != -1 and int(string["VPOS"]) > max_upper_vpos_permissible:
                                continue
                            if found_end:
                                break
                            block_text.append(string["text"])
                            height.append(string["HEIGHT"])
                        if found_end:
                            break
                    if found_end:
                        break
                if found_end:
                    break
            if found_end:
                break
            upper_pages[int(page)] = block_text
            text_height[int(page)] = height
        return upper_pages, text_height

    def header(self, block_texts: Dict[int, List[str]], block_text_height: Dict[int, List[int]], config: Dict) -> Tuple[bool, int, int]:
        return self.check_section(block_texts, block_text_height, config)

    def body(self, block_texts: Dict[int, List[str]], block_text_height: Dict[int, List[int]], config: Dict) -> Tuple[bool, int, int]:
        return self.check_section(block_texts, block_text_height, config)

    def footer(self, block_texts: Dict[int, List[str]], block_text_height: Dict[int, List[int]], config: Dict) -> Tuple[bool, int, int]:
        return self.check_section(block_texts, block_text_height, config)

    def big_font(self, block_texts: Dict[int, List[str]], config: Dict) -> List[str]:
        return self.apply_big_font_clusterer(block_texts, config)

    def check_section(self, block_texts: Dict[int, List[str]], block_text_height: Dict[int, List[int]], config: Dict) -> Tuple[bool, int, int]:
        include_strings = config.get('include_strings', [])
        exclude_strings = config.get('exclude_strings', [])
        length_comparison = config.get('length_comparison', False)

        primary_keys = []
        page_stats = []
        num_pages = len(block_texts)

        for (page, text), (_, height) in zip(block_texts.items(), block_text_height.items()):
            text_height_list = list(zip(text, height))
            for idx, (s, h) in enumerate(text_height_list):
                if any(v.lower() in s.strip().lower() for v in include_strings):
                    if length_comparison and len(s.strip()) == len(include_strings[0].strip()):
                        if (len(text_height_list) > idx + 1):
                            s_next, h_next = text_height_list[idx + 1]
                            if not any(ex_str in s_next.strip().lower() for ex_str in exclude_strings):
                                primary_keys.append([int(page), int(h), s.strip()])
                        else:
                            primary_keys.append([int(page), int(h), s.strip()])
                    elif any(v.lower() == s.strip().lower() for v in include_strings):
                        primary_keys.append([int(page), int(h), s.strip()])
                    page_stats.append([int(page), int(h), s.strip()])

        primary_keys = self.sort_list(primary_keys)
        page_stats = self.sort_list(page_stats)

        if self.debug:
            print(page_stats)
            print(primary_keys)

        if primary_keys:
            return True, primary_keys[0][0], num_pages
        elif page_stats:
            return True, page_stats[0][0], num_pages
        else:
            return False, -1, num_pages

    def apply_big_font_clusterer(self, block_texts: Dict[int, List[str]], config: Dict) -> List[str]:
        big_font_config = config.get('big_font', {})
        height_threshold = big_font_config.get('height_threshold', 0.5)
        num_clusters = big_font_config.get('num_clusters', 3)

        big_font_clusterer = BigFontClusterer(height_threshold=height_threshold, num_clusters=num_clusters)

        # Extract features and cluster based on height
        features = big_font_clusterer.extract_features(block_texts)
        labels = big_font_clusterer.cluster_text_lines(features)
        large_font_texts = big_font_clusterer.extract_large_font_text(block_texts, labels)

        return [text for text in large_font_texts if any(
                inc.lower() in text.lower() for inc in big_font_config.get('include_strings', [])
        ) and not any(
                exc.lower() in text.lower() for exc in big_font_config.get('exclude_strings', [])
        )]

    def classify_document(self, block_texts: Dict[int, List[str]], block_text_height: Dict[int, List[int]]) -> Dict:
        for doc_type, sections in self.config['document_types'].items():
            return_value = sections.get('return')
            all_conditions_met = True

            for section_name, section_config in sections.items():
                if section_name == 'return':
                    continue
                method = getattr(self, section_name, None)
                if method:
                    result = method(block_texts, block_text_height, section_config) if section_name != 'big_font' else method(block_texts, section_config)
                    if isinstance(result, tuple) and not result[0]:  # If any condition in a section fails
                        all_conditions_met = False
                        break
                    elif isinstance(result, list) and not result:  # If large fonts were not detected
                        all_conditions_met = False
                        break

            if all_conditions_met:
                return {
                    'document_type': return_value,
                    'section': section_name,
                    'starting_page': result[1] if isinstance(result, tuple) else None,
                    'total_pages': result[2] if isinstance(result, tuple) else None,
                    'large_font_texts': result if isinstance(result, list) else None
                }

        return {
            'document_type': self.config.get('default_return'),
            'section': 'none',
            'starting_page': -1,
            'total_pages': len(block_texts)
        }

    def process_form(self, combined_json_path: str) -> Dict:
        upper_block_text, upper_block_text_height = self.get_upper_blocks_data_from_all_pages(combined_json_path)
        return self.classify_document(upper_block_text, upper_block_text_height)


# Usage example

# Usage example
if __name__ == '__main__':
    config = {
        "max_upper_block": 3,
        "use_upper_split_percentage": 0.45,
        "max_lines_for_header": 5,
        "max_upper_lines_for_key_search": 8,
        "debug": True,
        "default_return": "docvu_de_core/de_config/9000001-1003_de.json",
        "document_types": {
            "old_form": {
                "return": "docvu_de_core/de_config/9000001-1003_de.json",
                "header": {
                    "include_strings": ["section 1","section 2", "section3", "personal information"],
                    "exclude_strings": [],
                    "length_comparison": False
                },
                "body": {
                    "include_strings": ["section 1","section 2", "section3", "personal information"],
                    "exclude_strings": [],
                    "length_comparison": False
                }
            }
        }
    }

    cls = Classify(config)

    print("-" * 50)
    json_path = '/home/<USER>/mohith_data_management/GIT_CLONE/SOFTWARE-SERVICE-%20document_extraction/ocr_output/07_1003_Submission_0608_kofax_ocr/combined.json'
    result = cls.process_form(combined_json_path=json_path)
    print(result)