class ModelOutput:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

    def __str__(self):
        return str(self.to_dict())

    def to_dict(self):
        return {attr: getattr(self, attr) for attr in dir(self)
                if not callable(getattr(self, attr)) and not attr.startswith("__")}


class ModelInput:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

    def __str__(self):
        return str(self.to_dict())

    def to_dict(self):
        return {attr: getattr(self, attr) for attr in dir(self)
                if not callable(getattr(self, attr)) and not attr.startswith("__")}