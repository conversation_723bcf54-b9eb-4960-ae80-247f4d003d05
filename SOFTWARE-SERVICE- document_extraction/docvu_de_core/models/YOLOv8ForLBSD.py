from docvu_de_core import yolo_model_path
from ultralytics import YOLO
from matplotlib import pyplot as plt
import cv2
import os
import torch
import json


class YOLOv8ForLBSD(torch.nn.Module):
    def __init__(self, logger, model_path=None, lbsd_classes=None, from_pretrained=True):
        """
        Initialize YOLOv8 model for LBSD (Signature, Barcode, Logo, Date, Seal, Seal Date, Initials) detection.

        Args:
            logger: Logger for logging events.
            model_path: Path to the YOLO model, if not provided it will load from yolo_model_path.
            lbsd_classes: Class IDs of the LBSD objects to be detected. Default is [0, 1, 2, 3, 4, 5, 6].
            from_pretrained: Whether to load the model from pretrained weights or from scratch.
        """
        super(YOLOv8ForLBSD, self).__init__()
        self.logger = logger
        self.model_path = model_path if model_path else yolo_model_path
        self.lbsd_classes = lbsd_classes if lbsd_classes else [0, 1, 2, 3, 4, 5, 6]  # Default classes: 0=Signature, 1=Barcode, 2=Logo, 3=Date, 4=Seal, 5=Seal Date, 6=Initials

        # Load the model (either pretrained or from scratch)
        if from_pretrained:
            self.model = self.load_model()
        else:
            self.model = self.initialize_model()

    def load_model(self):
        """
        Load the YOLO model from the given path.

        Returns:
            Loaded YOLO model.
        """
        model = YOLO(self.model_path)
        self.logger.info("YOLO model loaded")
        print("YOLO model loaded")
        return model

    def initialize_model(self):
        """
        Initialize the YOLO model from scratch.

        Returns:
            Initialized YOLO model.
        """
        # Assuming YOLO can initialize from scratch with a base configuration
        model = YOLO()  # You might need to adjust this depending on YOLO initialization behavior
        self.logger.info("YOLO model initialized from scratch")
        return model

    @classmethod
    def from_pretrained(cls, pretrained_model_path, *model_args, **kwargs):
        """
        Load a pretrained model from a saved directory.

        Args:
            pretrained_model_path: Path to the directory containing model weights and config.
            logger: Logger instance for logging.

        Returns:
            YOLOv8ForLBSD instance loaded with pretrained weights and config.
        """
        logger = kwargs.get("logger")
        # Initialize the model with configuration settings
        model = cls(logger=logger,
                    model_path=os.path.join(pretrained_model_path, "model.pt"),
                    from_pretrained=True)

        # Load model weights
        logger.info(f"Model loaded from {os.path.join(pretrained_model_path, 'model.pt')}")

        return model

    def forward(self, image_path, output_path=None):
        """
        Perform inference on the given image and save the output if required.

        Args:
            image_path: Path to the image for inference.
            output_path: Optional path to save the output image with bounding boxes.

        Returns:
            Results: Inference results with bounding boxes and classes.
        """

        # Colors for the new and existing classes
        colors = [
            (0, 255, 0),    # Green for class 0 (Signature)
            (0, 0, 255),    # Blue for class 1 (Barcode)
            (255, 0, 0),    # Red for class 2 (Logo)
            (255, 255, 0),  # Yellow for class 3 (Date)
            (255, 0, 255),  # Magenta for class 4 (Seal)
            (0, 255, 255),  # Cyan for class 5 (Seal Date)
            (128, 128, 0)   # Olive for class 6 (Initials) - This is the new class "Initials"
        ]

        results = self.model([image_path])  # Run batched inference on a list of images
        orig_image = results[0].orig_img  # Get the original image
        bboxes = results[0].boxes  # Get bounding boxes

        for bbox in bboxes:  # Draw bounding boxes
            klass = int(bbox.cls.item())
            conf = bbox.conf
            xyxy = bbox.xyxy

            top = (int(xyxy[0][0]), int(xyxy[0][1]))
            bottom = (int(xyxy[0][2]), int(xyxy[0][3]))
            orig_image = cv2.rectangle(orig_image, top, bottom, colors[klass], 4)

            # Add confidence score behind the bounding box
            label = f"Conf: {conf.item():.2f}"
            text_color = (244, 67, 54)
            orig_image = cv2.putText(orig_image, label, (top[0] - 120, top[1]), cv2.FONT_HERSHEY_SIMPLEX, 0.7,
                                     text_color, 3)

        # Save the image if output path is provided
        if output_path:
            plt.imsave(output_path, orig_image)

        return results

    def get_detected_objects(self, image_path, output_path=None):
        """
        Get the detected LBSD objects with bounding boxes and confidence.

        Args:
            image_path: Path to the image for inference.
            output_path: Optional path to save the output image with bounding boxes.

        Returns:
            List of dictionaries containing bbox, confidence, class ID, and detection status.
        """

        detected_objects = []
        results = self.forward(image_path, output_path=output_path)

        for bbox in results[0].boxes:
            bbox_dict = {
                "bbox": {
                    "x1": int(bbox.xyxy[0][0]),
                    "y1": int(bbox.xyxy[0][1]),
                    "x2": int(bbox.xyxy[0][2]),
                    "y2": int(bbox.xyxy[0][3])
                },
                "confidence": bbox.conf.item(),
                "class_id": int(bbox.cls.item()),
                "detected": int(bbox.cls.item()) in self.lbsd_classes
            }
            detected_objects.append(bbox_dict)

        detected_objects = sorted(detected_objects, key=lambda x: (x['bbox']['x1'], x['bbox']['y1']))
        return detected_objects
