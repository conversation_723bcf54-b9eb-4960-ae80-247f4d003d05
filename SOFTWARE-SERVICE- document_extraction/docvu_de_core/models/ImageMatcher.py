import tensorflow as tf
import numpy as np
import os


class ImageMatcher:
    def __init__(self, model_path=None, quantize=True, from_pretrained=True):
        """
        Initialize the ImageMatcher using a TFLite model.

        Args:
            model_path: Path to the model file (TFLite format).
            quantize: Whether to enable quantization.
            from_pretrained: If True, load from the pretrained model file.
        """
        self.model_path = model_path
        self.quantize = quantize
        self.interpreter = None

        if from_pretrained:
            self.interpreter = self.load_pretrained()
        else:
            self.interpreter = self.initialize_interpreter()

    def initialize_interpreter(self):
        """
        Initialize the TFLite interpreter without loading pretrained weights.

        Returns:
            Initialized TFLite interpreter.
        """
        if not self.model_path or not os.path.exists(self.model_path):
            raise ValueError(f"Model path {self.model_path} does not exist.")

        interpreter = tf.lite.Interpreter(model_path=self.model_path)
        interpreter.allocate_tensors()
        print("TFLite interpreter initialized from scratch.")
        return interpreter

    def load_pretrained(self):
        """
        Load the pretrained TFLite model.

        Returns:
            Pretrained TFLite interpreter.
        """
        if not self.model_path or not os.path.exists(self.model_path):
            raise ValueError(f"Model path {self.model_path} does not exist.")

        interpreter = tf.lite.Interpreter(model_path=self.model_path)
        interpreter.allocate_tensors()
        print(f"Pretrained TFLite model loaded from {self.model_path}")
        return interpreter

    @classmethod
    def from_pretrained(cls, pretrained_model_path, **kwargs):
        """
        Load a pretrained model from the saved directory.

        Args:
            pretrained_model_path: Path to the directory containing the saved model configuration.

        Returns:
            A ImageMatcher instance with the pretrained TFLite model loaded.
        """
        return cls(model_path=os.path.join(pretrained_model_path, 'model.tflite'),
                   quantize=False,
                   from_pretrained=True)

    def get_input_details(self):
        """
        Get the input tensor details for the TFLite model.

        Returns:
            Input tensor details for the TFLite model.
        """
        input_details = self.interpreter.get_input_details()
        return input_details

    def get_output_details(self):
        """
        Get the output tensor details for the TFLite model.

        Returns:
            Output tensor details for the TFLite model.
        """
        output_details = self.interpreter.get_output_details()
        return output_details

    def get_embedding(self, image):
        """
        Get the image embedding for a given input image.

        Args:
            image: Input image to be embedded (preprocessed as needed).

        Returns:
            Embedding result from the TFLite model.
        """
        if self.interpreter is None:
            raise RuntimeError("TFLite interpreter has not been initialized.")

        input_details = self.get_input_details()
        output_details = self.get_output_details()

        # Resize the image to the model's input shape
        input_shape = input_details[0]['shape']
        image = np.array(image).astype(np.float32)
        image = np.resize(image, (input_shape[1], input_shape[2], input_shape[3]))

        # Set the tensor to the input image
        self.interpreter.set_tensor(input_details[0]['index'], [image])
        self.interpreter.invoke()

        # Get the output (embedding)
        output_data = self.interpreter.get_tensor(output_details[0]['index'])
        embedding = np.squeeze(output_data)
        return embedding

    def compare_images(self, image1, image2):
        """
        Compare two images using their embeddings.

        Args:
            image1: First input image.
            image2: Second input image.

        Returns:
            Cosine similarity between the two image embeddings.
        """
        embedding1 = self.get_embedding(image1)
        embedding2 = self.get_embedding(image2)

        # Compute cosine similarity
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)
        similarity = dot_product / (norm1 * norm2)

        print(f"Cosine similarity: {similarity}")
        return similarity

    def is_match(self, image1, image2, threshold=0.8):
        """
        Determine if two images are considered a match based on a similarity threshold.

        Args:
            image1: First input image.
            image2: Second input image.
            threshold (float): The similarity threshold to determine a match. Default is 0.8.

        Returns:
            (bool, float): A tuple containing a boolean indicating if the images match
            and the cosine similarity score.
        """
        similarity = self.compare_images(image1, image2)

        if similarity >= threshold:
            print(f"The images are a match with similarity: {similarity}")
            return True, similarity
        else:
            print(f"The images are not a match. Similarity: {similarity}")
            return False, similarity