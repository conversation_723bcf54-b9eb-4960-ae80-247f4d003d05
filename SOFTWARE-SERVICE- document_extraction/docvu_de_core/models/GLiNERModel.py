import torch.nn as nn
import os
from gliner import GLiNER, GLiNERConfig
from docvu_de_core.models.ModelIO import ModelInput, ModelOutput


class GLiNERModel(nn.Module):
    def __init__(self, config, from_pretrained=True):
        super(GLiNERModel, self).__init__()
        self.config = config

        # Initialize the model
        if from_pretrained:
            self.model = GLiNER.from_pretrained(self.config.MODEL_TYPE)
        else:

            gliner_config = GLiNERConfig()
            # Initialize without pretrained model, passing the config
            self.model = GLiNER(gliner_config)

    def forward(self, text, labels):
        """
        Extract entities from the input text based on the provided labels.
        Args:
            text: The input text from which to extract entities.
            labels: List of labels (e.g., ["person", "award", "date", ...]).
        Returns:
            ModelOutput: An instance containing the extracted entities.
        """
        entities = self.model.predict_entities(text, labels)
        model_output = ModelOutput(entities=entities)
        return model_output

    def predict_entities(self, text, labels):
        """
        Extract entities from the input text using the model.
        Args:
            text: The text for entity extraction.
            labels: List of entity labels to extract (e.g., ["person", "award"]).
        Returns:
            List of extracted entities with their labels.
        """
        # Perform the entity extraction
        entities = self.model.predict_entities(text, labels)
        return entities

    def save_pretrained(self, save_dir):
        """
        Save the model to the specified directory.
        Args:
            save_dir (str): Directory to save the model.
        """
        os.makedirs(save_dir, exist_ok=True)
        print(f"Saving model to {save_dir}")
        # GLiNER does not have a direct save method like T5, but you could save configuration if needed.

    @classmethod
    def from_pretrained(cls, model_dir, logger=None, device="cpu"):
        """
        Load the pretrained GLiNER model from the specified directory.
        Args:
            model_dir (str): Path to the directory containing the pre-trained model.
            logger: Optional logger for logging messages.
            device (str): The device to load the model onto, e.g., 'cpu' or 'cuda'.
        """
        # Log loading if a logger is provided
        if logger:
            logger.info(f"Loading pretrained model from {model_dir}")
        else:
            print(f"Loading pretrained model from {model_dir}")

        # GLiNER loads from Huggingface directly using the model directory or URL
        model = GLiNER.from_pretrained(model_dir)

        # Return the class instance with the loaded model
        instance = cls(config=None, from_pretrained=False)  # Passing config=None for simplicity
        instance.model = model  # Assign the loaded model to the instance
        instance.to(device)  # Move the model to the desired device

        return instance


# Example usage of from_pretrained
if __name__ == "__main__":
    model_dir = "C:/Users/<USER>/.models_cache/docvu-gliner-model/docvu-gliner-model-2024.11.29.1/"  # Change this to the path where your model is stored
    logger = None  # You can provide a logger if needed
    device = "cpu"  # Change to "cuda" if using GPU

    # Load pretrained model
    model = GLiNERModel.from_pretrained(model_dir, logger=logger, device=device)

    # Now you can use the model for entity extraction
    # text = "Senior Chairman of the Board tide guaranty company Authorized Countersignature Princeton, New Jersey Chairman of the Board President"
    text = "Signed under seal for the Comnanv, but this Policy is to be valid only when it bears an authorized countersignature. Chairman of the Board Countersigned: STEWART TITLE GUARANTY COMPANY Authorized signature Nova Title Agency Company 510 East Calhoun Street Anderson, SC 29621  City, State Policy Serial No. O-9736-264398 ALTA Residential Title Insurance Policy 1987 "
    labels = ["organization"]
    entities = model.predict_entities(text, labels)

    for entity in entities:
        print(entity["text"], "=>", entity["label"])

