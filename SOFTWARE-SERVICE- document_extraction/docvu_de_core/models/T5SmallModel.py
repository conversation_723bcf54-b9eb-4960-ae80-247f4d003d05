import torch
import torch.nn as nn
import os
import json
from transformers import T5ForConditionalGeneration, T5Tokenizer, T5Config
from docvu_de_core.models.ModelIO import ModelInput, ModelOutput  # Assuming you have these classes in model/ModelIO.py


def correct_state_dict_keys(pretrained_state_dict, model_state_dict):
    """
    Correct the keys in the pretrained state_dict to match the model's expected keys.

    Args:
        pretrained_state_dict (dict): The state_dict loaded from the pretrained model.
        model_state_dict (dict): The state_dict of the current model for reference.

    Returns:
        dict: The corrected state_dict with keys matching the model's state_dict.
    """
    corrected_state_dict = {}

    # Mapping to adjust keys from the pretrained state_dict to the model's state_dict
    key_mapping = {
        "shared.": "model.shared.",
        "encoder.": "model.encoder.",
        "decoder.": "model.decoder.",
        "lm_head.weight": "model.lm_head.weight",
    }

    for pretrained_key in pretrained_state_dict.keys():
        new_key = pretrained_key

        # Check for key replacements based on the mapping
        for old_key_prefix, new_key_prefix in key_mapping.items():
            if pretrained_key.startswith(old_key_prefix):
                new_key = pretrained_key.replace(old_key_prefix, new_key_prefix, 1)
                break

        # Ensure only matching keys are transferred
        if new_key in model_state_dict:
            corrected_state_dict[new_key] = pretrained_state_dict[pretrained_key]
        else:
            print(f"Skipping key {new_key} as it doesn't exist in the model's state_dict.")

    return corrected_state_dict



class T5SmallModel(nn.Module):
    def __init__(self, config, from_pretrained=True):
        super(T5SmallModel, self).__init__()
        self.config = config

        # Initialize the model and tokenizer
        if from_pretrained:
            self.model = T5ForConditionalGeneration.from_pretrained(self.config.MODEL_TYPE)
            self.tokenizer = T5Tokenizer.from_pretrained(self.config.MODEL_TYPE)

            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

        else:
            t5_config = T5Config()
            self.model = T5ForConditionalGeneration(t5_config)

    def forward(self, input_ids, attention_mask, labels):
        """
        Forward pass through the model.
        Args:
            input_ids: Tensor of input token IDs.
            attention_mask: Tensor of attention masks.
            labels: Tensor of labels for sequence generation.
        Returns:
            ModelOutput: An instance of ModelOutput containing the model's outputs and loss (if labels provided).
        """
        outputs = self.model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels
        )

        # Creating a ModelOutput instance
        model_output = ModelOutput(
                logits=outputs.logits,
                loss=outputs.loss if labels is not None else None
        )

        return model_output

    def generate_text(self, input_text, max_length=50, num_beams=3, temperature=1.0):
        """
        Generate text based on input text using the fine-tuned model.
        Args:
            input_text: The initial text to start generating from.
            max_length: Maximum length of the generated sequence.
            num_beams: Number of beams for beam search.
            temperature: Sampling temperature.
        Returns:
            Generated text.
        """
        # Ensure the model is in evaluation mode
        self.model.eval()

        # Tokenize the input text and create an attention mask
        inputs = self.tokenizer(input_text, return_tensors='pt', truncation=True, max_length=self.config.MAX_SEQ_LENGTH)
        input_ids = inputs['input_ids'].to(self.model.device)
        attention_mask = inputs['attention_mask'].to(self.model.device)

        # Generate output tokens with the attention mask
        output_ids = self.model.generate(
                input_ids,
                attention_mask=attention_mask,
                max_length=max_length,
                num_beams=num_beams,
                temperature=temperature,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                decoder_start_token_id=self.tokenizer.pad_token_id,  # Set decoder start token
                use_cache=True  # Speed up generation
        )

        # Decode only the generated tokens
        return self.tokenizer.decode(output_ids[0], skip_special_tokens=True)

    def load_pretrained_weights(self, pretrained_model_path):
        """
        Load weights from a pretrained T5 model.
        Args:
            pretrained_model_path (str): Path to the .pt file of the pretrained model.
        """
        print('Loading pretrained weights from:', pretrained_model_path)
        pretrained_weights = torch.load(pretrained_model_path)
        self.model.load_state_dict(pretrained_weights, strict=False)

    def save_pretrained(self, save_dir):
        """
        Save the model, tokenizer, and configuration files to the specified directory.
        Args:
            save_dir (str): Directory to save the model files.
        """
        os.makedirs(save_dir, exist_ok=True)

        config_dict = self.config.to_dict()
        config_dict["tokenizer"] = 'tokenizer'
        config_dict["model"] = 'model.pt'

        # Save the tokenizer
        self.tokenizer.save_pretrained(os.path.join(save_dir, config_dict['tokenizer']))

        # Save the model state dict
        torch.save(self.model.state_dict(), os.path.join(save_dir, config_dict['model']))

        # Save configuration (arguments)
        with open(os.path.join(save_dir, 'config.json'), 'w') as f:
            json.dump(config_dict, f)

    @classmethod
    def from_pretrained(cls, pretrained_model_path: str, *model_args, **kwargs):
        """
        Instantiate a pretrained model from a pre-trained model configuration.
        Args:
            pretrained_model_path (str): Path to the directory containing the pre-trained model files.
        """
        # Load configuration (arguments)
        print("Loading pretrained model from", pretrained_model_path)
        with open(os.path.join(pretrained_model_path, 'config.json'), 'r') as f:
            config = json.load(f)
        config['tokenizer'] = os.path.join(pretrained_model_path, config['tokenizer'])
        config['model'] = os.path.join(pretrained_model_path, config['model'])

        # Initialize the model with the loaded configuration
        model = cls(ModelInput(**config), from_pretrained=False)

        # Load the tokenizer
        model.tokenizer = T5Tokenizer.from_pretrained(config['tokenizer'])
        if model.tokenizer.pad_token is None:
            model.tokenizer.pad_token = model.tokenizer.eos_token

        # Load the model weights
        pretrained_weights = torch.load(config['model'], map_location=kwargs.get('device', 'cuda'))

        # Correct the state_dict keys
        corrected_weights = correct_state_dict_keys(pretrained_weights, model.state_dict())

        # Load the corrected state_dict into the model
        model.load_state_dict(corrected_weights, strict=True)

        return model