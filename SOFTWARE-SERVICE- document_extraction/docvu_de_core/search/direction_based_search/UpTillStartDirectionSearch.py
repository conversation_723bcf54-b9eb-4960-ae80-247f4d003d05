from docvu_de_core.search.direction_based_search.BaseSearchOption import BaseSearchOption
from docvu_de_core.io import <PERSON><PERSON>eyFieldsOutputFormat
from docvu_de_core.search import <PERSON><PERSON>eyFieldHelper
from docvu_de_core.utils import logger


class UpTillStartDirectionSearch(BaseSearchOption):
    def __init__(self, debug=False):
        super().__init__()
        self.helper = SearchKeyFieldHelper(logger)
        self.debug = debug

    def get_value_in_up_till_start_direction(self, blocks_data, tl_id, ts_id, found_field_data, field_info, field_key):
        possible_answer_list = []
        found_value = False
        # Continue searching the data from the place where key was found
        for block_data in blocks_data:
            for block in block_data:
                for tl, text_line in enumerate(block["TEXT_LINE"]):
                    for ts, text_string in enumerate(text_line["STRING"]):
                        # for the value consider everything in the line.
                        if (text_string["text"].lower() == found_field_data["text"].lower() and
                            tl == tl_id and ts == ts_id):
                            found_value = True
                            break
                        else:
                            possible_answer_list.append(text_string)
                        if found_value:
                            break
                    if found_value:
                        break
                if found_value:
                    break
            if found_value:
                break
        if possible_answer_list:
            for st_identifier in field_info["start_identifier"]:
                match = [v[0] for v in enumerate(possible_answer_list)
                         if st_identifier.lower() in v[1]["text"].lower()]
                if match:
                    # If a match is found, return the concatenated text from that point onwards
                    combined_text = " ".join(v["text"] for v in possible_answer_list[match[0]:])
                    # Also return the sublist of elements starting from the match
                    matching_elements = possible_answer_list[match[0]:]
                    # return combined_text, matching_elements
                    bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(matching_elements)
                    return SearchKeyFieldsOutputFormat(value=combined_text,
                                                       search_next_page=False,
                                                       success=True,
                                                       elements=matching_elements,
                                                       is_table=False,
                                                       line_number=line_number,
                                                       bbox=bbox,
                                                       field_key=field_key)

                # If no identifier is matched, return the text and the last element in the list
                last_element_text = possible_answer_list[-1]["text"]
                last_element = possible_answer_list[-1]
                # return last_element_text, last_element
                return SearchKeyFieldsOutputFormat(value=last_element_text,
                                                   search_next_page=False,
                                                   success=True,
                                                   elements=last_element,
                                                   is_table=False,
                                                   line_number=last_element['LINE_NUMBER'],
                                                   bbox=[last_element['HPOS'],
                                                                last_element['VPOS'],
                                                                last_element['END_HPOS'],
                                                                last_element['END_VPOS']],
                                                   field_key=field_key)
        else:
            return SearchKeyFieldsOutputFormat(value=None,
                                               search_next_page=False,
                                               success=False,
                                               elements=None,
                                               is_table=False,
                                               line_number=None,
                                               bbox=None,
                                               field_key=field_key)

