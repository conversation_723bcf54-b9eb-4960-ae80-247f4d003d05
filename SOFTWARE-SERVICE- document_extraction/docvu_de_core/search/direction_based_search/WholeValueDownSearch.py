from docvu_de_core.search.direction_based_search.BaseSearchOption import BaseSearchOption
from docvu_de_core.search import <PERSON><PERSON><PERSON><PERSON>ieldHelper
from docvu_de_core.utils import logger
from docvu_de_core.io import OutputFormat, Search<PERSON>eyFieldsOutputFormat
from docvu_de_core.modules import MSTGraph
from docvu_de_core.utils import ElementUtils


class WholeValueDownSearch(BaseSearchOption):
    def __init__(self, debug=False):
        super().__init__()
        self.helper = SearchKeyFieldHelper(logger)
        self.debug = debug

    def get_multi_page_data_with_start_end_identifiers(self, whole_page_data, field_info, search_after,
                                                       use_start_identifier=False, use_key=False):
        consider_data = False
        possible_answer_list = []
        secondary_possible_answer_list = []
        third_possible_answer_list = []
        start_identifier_vpos_info = []
        key_vpos_info = []
        for ts_id, text_string in enumerate(whole_page_data):
            if (not consider_data and self.helper.process_utils.check_if_key_found_is_individual(
                    [text_string["text"]], field_info["start_identifier"])[0]):
                consider_data = True
                continue
            if use_key and self.helper.process_utils.get_match(field_info["key"], text_string["text"]):
                key_vpos_info.append(text_string["VPOS"])
                break

            if self.helper.process_utils.check_if_key_found_is_individual(
                    [text_string["text"]], field_info["start_identifier"])[0]:
                start_identifier_vpos_info.append(text_string["VPOS"])

            if not consider_data and text_string["VPOS"] > search_after:
                if text_string:
                    secondary_possible_answer_list.append(text_string)
            if consider_data:
                if text_string:
                    possible_answer_list.append(text_string)
        if use_start_identifier:
            vpos_info = start_identifier_vpos_info
        else:
            vpos_info = key_vpos_info if key_vpos_info else start_identifier_vpos_info
        if vpos_info:
            if len(vpos_info) > 1:
                mst_graph = MSTGraph(len(vpos_info))
                mst_graph.add_edge(vpos_info)
                clustered_points = mst_graph.cluster_using_mst()
                sorted_key = sorted(clustered_points)
                search_after = clustered_points[sorted_key[0]][-1]
            else:
                search_after = vpos_info[0]
            for ts_id, text_string in enumerate(whole_page_data):
                if text_string["VPOS"] > search_after:
                    if text_string:
                        third_possible_answer_list.append(text_string)

        if self.debug:
            print("1. possible_answer_list: ", possible_answer_list)
            print("1. secondary_possible_answer_list: ", secondary_possible_answer_list)
            print(f"\n VPOS INFO = {vpos_info}")
        if third_possible_answer_list:
            if self.debug:
                print("third_possible_answer_list =", third_possible_answer_list)
            return third_possible_answer_list
        else:
            if self.debug:
                print("possible_answer_list: ", possible_answer_list)
                print("secondary_possible_answer_list: ", secondary_possible_answer_list)
        return possible_answer_list if possible_answer_list else secondary_possible_answer_list

    def get_whole_value_down(self, whole_page_data, found_tl_id, found_ts_id, found_field_data, field_info, field_key):
        possible_answer_list = []
        consider_data = False
        found_end = False
        start_searching = True
        # Continue searching the data from the place where key was found
        for i, item in enumerate(whole_page_data):
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    for ts_id, text_string in enumerate(text_line["STRING"]):
                        if (text_string["text"].lower() == found_field_data["text"].lower() and
                                found_tl_id == tl_id and found_ts_id == ts_id):
                            consider_data = True
                            start_searching = text_string['VPOS']
                            if not ("include_key" in field_info.keys() and field_info["include_key"]):
                                continue
                        if consider_data:
                            include_footer = False
                            if ("probable_type" in field_info.keys()
                                and field_info["probable_type"] == "Footer"):
                                include_footer = True
                            if self.helper.process_utils.get_match(field_info["end_identifier"], text_string["text"]):
                                found_end = True
                            elif not include_footer and self.helper.is_in_footer(text_string):
                                found_end = True
                            else:
                                if "exclude_key" in field_info.keys() and field_info["exclude_key"]:
                                    if not self.helper.process_utils.get_match(field_info["key"], text_string["text"]):
                                        if text_string:
                                            possible_answer_list.append(text_string)
                                else:
                                    if text_string:
                                        possible_answer_list.append(text_string)
                        if found_end:
                            break
                    if found_end:
                        break
                if found_end:
                    break
            if found_end:
                break
        if self.debug:
            print("get_whole_value_down | Possible Answer List in DOWN: ", possible_answer_list)
        if possible_answer_list:
            #sorted_list = self.helper.process_utils.rearrange_based_on_vpos_hpos(possible_answer_list)
            sorted_list = possible_answer_list
            # if self.debug:
            if self.debug:
                print("get_whole_value_down | Possible Sorted Answer List in DOWN: ", sorted_list)
            if ("additional_info" in field_info.keys() and
                ("is_in_center" in field_info["additional_info"].keys() and
                 field_info["additional_info"]["is_in_center"] and "has_keyword" in field_info["additional_info"])):
                proximity_text = []
                proximity_dist = []
                proximity_elements = []
                for index1, lst1 in enumerate(sorted_list):
                    for lst2 in sorted_list[index1 + 1:]:
                        match_found = [True if k.lower() in lst1["text"].lower() else False
                                       for k in field_info["additional_info"]["has_keyword"]]
                        if self.debug:
                            print(lst1["text"], "--", lst2["text"], "--",
                              self.helper.dist_utils.get_centroid_dist(lst1, lst2), "--",
                              self.helper.dist_utils.get_vertical_distance(lst1, lst2), "--",
                              self.helper.dist_utils.get_horizontal_distance(lst1, lst2), "--",
                              self.helper.dist_utils.get_distance(lst1, lst2), "--", match_found)

                        if any(match_found):
                            proximity_text.append(lst1["text"] + " " + lst2["text"])
                            proximity_elements.append((lst1, lst2))
                            proximity_dist.append(self.helper.dist_utils.get_centroid_dist(lst1, lst2))
                if proximity_dist:
                    # return (proximity_text[proximity_dist.index(min(proximity_dist))],
                    #        proximity_elements[proximity_dist.index(min(proximity_dist))], False)
                    proximity_element = proximity_elements[proximity_dist.index(min(proximity_dist))]
                    return OutputFormat(value=proximity_text[proximity_dist.index(min(proximity_dist))],
                            search_next_page=False,
                            success=True,
                            elements=proximity_element,
                            is_table=False,
                            line_number=proximity_element['LINE_NUMBER'],
                            bbox=[proximity_element['HPOS'], proximity_element['VPOS'], proximity_element['END_HPOS'],
                                  proximity_element['END_VPOS']],
                            field_key=field_key,
                            is_text_merged = False)

            if "use_mst" in field_info.keys() and field_info["use_mst"]:
                use_start_identifier = False
                if "use_mst_for_start_identifiers" in field_info.keys() and field_info["use_mst_for_start_identifiers"]:
                    use_start_identifier = True
                ans_value = self.get_multi_page_data_with_start_end_identifiers(
                    possible_answer_list, field_info, start_searching, use_start_identifier=use_start_identifier)
                # return ans_value, False
                bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(possible_answer_list)
                return OutputFormat(value=self.helper.process_utils.get_string_from_elements(ans_value),
                                    search_next_page=False,
                                    success=True,
                                    elements=ans_value,
                                    is_table=False,
                                    line_number=line_number,
                                    bbox=bbox,
                                    field_key=field_key,
                                    is_text_merged=False)
            else:
                new_text_line_string = " ".join(pal['text'] for pal in possible_answer_list)
                temp_field_value, remaining_string_start_index, remaining_string_end_index = (
                    self.helper.process_utils.get_remaining_text(new_text_line_string,
                                                          field_info,
                                                          use_key=False))
                possible_answer_list = ElementUtils.filter_elements_by_string_indices(possible_answer_list,
                                                                                      remaining_string_start_index,
                                                                                      remaining_string_end_index)
                if field_info.get("start_identifier_down"):
                    prev_pal = possible_answer_list[0]
                    for idx, pal in enumerate(possible_answer_list[1:]):
                        if not self.helper.dist_utils.on_same_horizontal_line(prev_pal, pal):
                            possible_answer_list = possible_answer_list[idx + 1:]
                            break
                        prev_pal = pal
                    bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(possible_answer_list)
                    return OutputFormat(value=self.helper.process_utils.get_string_from_elements(possible_answer_list),
                                        search_next_page=False,
                                        success=True,
                                        elements=possible_answer_list,
                                        is_table=False,
                                        line_number=line_number,
                                        bbox=bbox,
                                        field_key=field_key,
                                        is_text_merged=False)

            new_list = []
            count = 0
            is_text_merged = False
            end_identifier_index = []
            start_identifier_index = []
            total_string = ' '.join(pal['text'] for pal in possible_answer_list)
            _, start_idx, end_idx = self.helper.process_utils.get_remaining_text(total_string, field_info,
                                           use_start_identifier=True,
                                           use_end_identifier=True,
                                           use_key=False)
            final_list = ElementUtils.filter_elements_by_string_indices(possible_answer_list, start_idx, end_idx)
            if "multi_line_value" in field_info.keys() and field_info["multi_line_value"]:
                # return final_list, is_text_merged
                value = ''
                for fl in final_list:
                    value += fl['text'] + " "
                value = value.strip()
                bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(final_list)
                return OutputFormat(value=value,
                                    search_next_page=False,
                                    success=True,
                                    elements=final_list,
                                    is_table=False,
                                    line_number=line_number,
                                    bbox=bbox,
                                    field_key=field_key,
                                    is_text_merged=is_text_merged)
            else:
                answer_list = [final_list[0]]
                i = 1
                while "consider_next_line_on_delimiter" in field_info and \
                        field_info['consider_next_line_on_delimiter'] and \
                        any(answer_list[-1]['text'].endswith(d) for d in [',', ';', ':']):
                    if len(final_list) <= i:
                        break
                    answer_list.append(final_list[i])
                    i += 1
                bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(answer_list)
                value = ''
                for fl in answer_list:
                    value += fl['text'] + " "
                value = value.strip()
                return SearchKeyFieldsOutputFormat(value=value,
                                                   search_next_page=False,
                                                   success=True,
                                                   elements=answer_list,
                                                   is_table=False,
                                                   line_number=line_number,
                                                   bbox=bbox,
                                                   field_key=field_key,
                                                   is_text_merged=is_text_merged)
        return OutputFormat(value=None,
                            search_next_page=False,
                            success=False,
                            elements=None,
                            is_table=False,
                            line_number=None,
                            bbox=None,
                            field_key = field_key,
                            is_text_merged=None)