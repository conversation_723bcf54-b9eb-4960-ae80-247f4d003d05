from docvu_de_core.search.direction_based_search.BaseSearchOption import Base<PERSON>earchOption
from docvu_de_core.io import <PERSON><PERSON>ey<PERSON>ieldsOutputFormat
from docvu_de_core.search import <PERSON><PERSON>eyFieldHelper
from docvu_de_core.utils import logger


class MultiPageSearch(BaseSearchOption):
    def __init__(self, debug=False):
        super().__init__()
        self.helper = SearchKeyFieldHelper(logger)
        self.debug = debug

    @staticmethod
    def check_if_end_of_exceptions(field_info, text_string):
        for s in field_info["end_identifier"]:
            if s == "":
                continue
            match_found = [s.lower() in text_string.lower()]
            #Making this to duplicate to ignore multipage checking
            if any(match_found) and s.lower() == "//end of exceptions//":
                return True
        return False

    def extract_from_multi_page(self, whole_page_data, found_tl_id, found_ts_id, found_field_data, field_info, field_key):
        possible_answer_list = []
        consider_data = False
        found_end = False
        continue_search = True
        # Continue searching the data from the place where key was found
        for i, item in enumerate(whole_page_data):
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    for ts_id, text_string in enumerate(text_line["STRING"]):
                        if (text_string["text"].lower() == found_field_data["text"].lower() and
                                found_tl_id == tl_id and found_ts_id == ts_id):
                            consider_data = True
                            if not ("include_key" in field_info.keys() and field_info["include_key"]):
                                continue
                        if consider_data:
                            ## Checking for end_identifier is present in individual position
                            if "individual_end_identifier" in field_info.keys() and field_info["individual_end_identifier"]:
                                found_end = self.helper.process_utils.is_identifier_at_start(field_info["end_identifier"], text_string["text"])
                                ## Checking individual_end_identifier for hpos based position
                                if found_end and ("hpos_aligned_end_identifier" in field_info.keys() and field_info["hpos_aligned_end_identifier"]):
                                    hpos_aligned_end_identifier = self.helper.process_utils.individual_hpos_aligned_identifier(text_string)
                                    found_end = hpos_aligned_end_identifier
                            ## Checking end_identifier is present in text string
                            else:
                                found_end = self.helper.process_utils.get_match(field_info["end_identifier"],text_string["text"])

                            if field_info.get("terminating_keyword") and self.helper.process_utils.get_match(field_info["terminating_keyword"],text_string["text"]):
                                f_index = self.helper.process_utils.find_index_using_identifiers(field_info["terminating_keyword"],
                                                                            text_string["text"])
                                text_string["text"] = text_string["text"][:f_index]

                            if found_end:
                                if field_info.get("additional_info", {}).get("stop_on_end_identifier"):
                                    continue_search = False
                                # Additional condition to stop search based on exception check
                                if self.check_if_end_of_exceptions(field_info, text_string["text"]):
                                    continue_search = False
                            elif field_info.get("exclude_footer") and self.helper.is_in_footer(text_string):
                                found_end = True
                            else:
                                if text_string:
                                    possible_answer_list.append(text_string)
                        if found_end:
                            break
                    if found_end:
                        break
                if found_end:
                    break
            if found_end:
                break

        sorted_list = self.helper.process_utils.rearrange_based_on_vpos_hpos(possible_answer_list)
        new_list = []
        count = 0
        is_text_merged = False
        for index, slist in enumerate(sorted_list):
            if index == 0:
                new_list.append(slist)
            else:
                curr_list_vpos = int(slist["VPOS"])
                prev_list_vpos = int(new_list[count]["VPOS"])
                if curr_list_vpos - prev_list_vpos <= 15:
                    new_list[count]["text"] += " " + slist["text"]
                    new_list[count]["HPOS"] = min(new_list[count]["HPOS"], slist["HPOS"])
                    new_list[count]["VPOS"] = min(new_list[count]["VPOS"], slist["VPOS"])
                    new_list[count]["END_HPOS"] = max(new_list[count]["END_HPOS"], slist["END_HPOS"])
                    new_list[count]["END_VPOS"] = max(new_list[count]["END_VPOS"], slist["END_VPOS"])
                    new_list[count]["LINE_NUMBER"] = max(new_list[count]["LINE_NUMBER"], slist["LINE_NUMBER"])
                    is_text_merged = True
                else:
                    new_list.append(slist)
                    count += 1
        # return new_list, is_text_merged, continue_search
        bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(new_list)
        return SearchKeyFieldsOutputFormat(value=self.helper.process_utils.get_string_from_elements(new_list),
                                           search_next_page=continue_search,
                                           success=True,
                                           elements=new_list,
                                           is_table=True,
                                           line_number=line_number,
                                           bbox=bbox,
                                           field_key=field_key,
                                           is_text_merged=is_text_merged)