from docvu_de_core.search.direction_based_search.BaseSearchOption import Base<PERSON>earchOption
from docvu_de_core.io import OutputFormat
from docvu_de_core.search import Search<PERSON>ey<PERSON>ieldHelper
from docvu_de_core.utils import logger


class WholePageTextSearch(BaseSearchOption):
    def __init__(self, debug=False):
        super().__init__()
        self.helper = SearchKeyFieldHelper(logger)
        self.debug = debug

    def get_whole_page_text(self, whole_page_data, found_tl_id, found_ts_id, found_field_data, field_info, field_key):
        possible_answer_list = []
        consider_data = False
        found_end = False
        # Continue searching the data from the place where key was found
        for i, item in enumerate(whole_page_data):
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    for ts_id, text_string in enumerate(text_line["STRING"]):
                        if (text_string["text"].lower() == found_field_data["text"].lower() and
                                found_tl_id == tl_id and found_ts_id == ts_id):
                            consider_data = True
                            if self.debug:
                                print('text_string["text"] =', text_string["text"])
                            if not ("include_key" in field_info.keys() and field_info["include_key"]):
                                continue
                            if ("probable_place" in field_info.keys() and
                                    field_info["probable_place"].lower() == 'individual'):
                                check_if_individual = (
                                    self.helper.process_utils.check_if_key_found_is_individual(
                                        [text_string["text"]], field_info["key"])[0])
                                if self.debug:
                                    print(f"Got Value of check_if_individual = {check_if_individual}")
                                if not check_if_individual:
                                    consider_data = False
                                    continue
                        if consider_data:
                            if field_info.get("exclude_header", False) and self.helper.is_on_page_boundary(text_string):
                                continue
                            include_footer = False
                            if ("probable_type" in field_info.keys()
                                    and field_info["probable_type"] == "Footer"):
                                include_footer = True
                            if self.helper.process_utils.get_match(field_info["end_identifier"], text_string["text"]):
                                if self.debug:
                                    print(
                                        'get_whole_page_text | if self.helper.process_utils.get_match(field_info["end_identifier"], text_string["text"])"',
                                        self.helper.process_utils.get_match(field_info["end_identifier"],
                                                                            text_string["text"]))
                                    print('get_whole_page_text | text_string["text"] =', text_string["text"])
                                    print('get_whole_page_text | field_info["end_identifier"] =',
                                          field_info["end_identifier"])
                                found_end = True
                            elif field_info.get("exclude_footer") and self.helper.is_in_footer(text_string):
                                found_end = True
                            else:
                                if text_string:
                                    possible_answer_list.append(text_string)
                        if found_end:
                            break
                    if found_end:
                        break
                if found_end:
                    break
            if found_end:
                break
        # if self.debug:
        if self.debug:
            print("get_whole_page_text | Possible Answer List in DOWN: ", possible_answer_list)
        # return possible_answer_list
        value = self.helper.process_utils.get_string_from_elements(possible_answer_list)
        bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(possible_answer_list)
        return OutputFormat(value=value,
                            search_next_page=False,
                            success=True,
                            elements=possible_answer_list,
                            is_table=False,
                            line_number=line_number,
                            bbox=bbox,
                            field_key=field_key)