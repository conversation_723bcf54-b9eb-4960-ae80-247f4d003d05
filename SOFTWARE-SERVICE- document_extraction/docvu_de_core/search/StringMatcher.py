from fuzzywuzzy import fuzz, process
from docvu_de_core.io import OutputFormat

class StringMatcher:
    """
    A class to perform string matching on OCR-extracted text considering potential OCR errors.
    The class can perform either substring matching, plain string matching, or fuzzy matching with adaptive thresholding.
    """

    def __init__(self, min_threshold=70, max_threshold=90, length_threshold=5):
        """
        Initialize the string matcher.

        Parameters:
        min_threshold (int): Minimum threshold for long strings.
        max_threshold (int): Maximum threshold for short strings.
        length_threshold (int): The string length at which the threshold starts to decrease.
        """
        self.min_threshold = min_threshold
        self.max_threshold = max_threshold
        self.length_threshold = length_threshold

    def pre_process_text(self, text):
        """
        Preprocess the text by converting to lowercase, removing spaces, and keeping only alphanumeric characters and specific symbols.

        Parameters:
        text (str): The text to preprocess.

        Returns:
        str: The preprocessed text.
        """
        text = text.lower().replace(" ", "")
        text = "".join(c for c in text if (c.isalnum() or c in ["$", "\n"]))
        return text

    def calculate_adaptive_threshold(self, text_length):
        """
        Calculate the adaptive threshold based on the length of the string.

        Parameters:
        text_length (int): Length of the string to match.

        Returns:
        int: The adaptive threshold for fuzzy matching.
        """
        if text_length <= self.length_threshold:
            # For very short strings, use the maximum threshold
            return self.max_threshold
        else:
            # For longer strings, reduce the threshold linearly
            threshold_range = self.max_threshold - self.min_threshold
            length_factor = min(text_length - self.length_threshold, self.length_threshold) / self.length_threshold
            adaptive_threshold = self.max_threshold - (threshold_range * length_factor)
            return int(adaptive_threshold)

    def fuzzy_match_ocr(self, ocr_text, keys):
        """
        Perform fuzzy matching on the OCR-extracted text using adaptive thresholding, with a focus on substring matching.

        Parameters:
        ocr_text (str): The text extracted from OCR that needs to be matched.
        keys (list of str): The list of correct keys to match against.

        Returns:
        tuple:
            bool: Whether a match was found that meets the adaptive threshold.
            str: The best-matched key or None if no match is found above the threshold.
        """
        # Preprocess both the OCR text and keys
        ocr_text = self.pre_process_text(ocr_text)
        keys = [self.pre_process_text(key) for key in keys]

        # Calculate the adaptive threshold based on the length of the OCR text
        adaptive_threshold = self.calculate_adaptive_threshold(len(ocr_text))

        # Perform fuzzy matching using fuzzywuzzy's process.extractOne
        best_match, score = process.extractOne(ocr_text, keys, scorer=fuzz.partial_ratio)

        # Check if any key is a substring of the OCR text
        found_match = any(k in ocr_text for k in keys)

        # Determine if the match meets the threshold or if a substring match is found
        if score >= adaptive_threshold or found_match:
            return True, best_match if found_match is None else [k for k in keys if k in ocr_text][0]
        else:
            return False, None

    def check_if_key_found_is_individual(self, present_str, key_str, return_found_key=False):
        """
        Check if the key is found as an individual key in the present string, with a focus on substring and fuzzy matching.

        Parameters:
        present_str (list of str): The list of present strings to check against.
        key_str (list of str): The list of key strings to search for.
        return_found_key (bool): If True, return the found key along with the match result.

        Returns:
        tuple: (bool, int, str or None) - Whether the key was found, the end index of the key, and the found key if requested.
        """
        for pstr in present_str:
            pstr_processed = self.pre_process_text(pstr)
            for k in key_str:
                k_processed = self.pre_process_text(k)
                index = pstr_processed.find(k_processed)
                # Perform partial fuzzy matching if substring matching is not exact
                score = fuzz.partial_ratio(pstr_processed, k_processed)
                if index == 0 or score >= self.calculate_adaptive_threshold(len(k_processed)):
                    if return_found_key:
                        return True, index + len(k_processed), k
                    else:
                        return True, index + len(k_processed)
        if return_found_key:
            return False, 0, None
        else:
            return False, 0

    def match_key(self, ocr_text, keys, use_partial_matching=True, check_individual=False):
        """
        Match the OCR-extracted text against a list of keys using either substring matching, plain string matching, or fuzzy matching.

        Parameters:
        ocr_text (str): The text extracted from OCR that needs to be matched.
        keys (list of str): The list of correct keys to match against.
        use_partial_matching (bool): If True, use fuzzy matching; if False, use plain string matching.
        check_individual (bool): If True, perform individual key matching.

        Returns:
        tuple:
            bool: Whether a match was found.
            str: The matched key or None if no match is found.
        """
        if check_individual:
            is_matched, _, matched_key = self.check_if_key_found_is_individual([ocr_text], keys, return_found_key=True)
            return is_matched, matched_key

        if use_partial_matching:
            # Use fuzzy matching with adaptive thresholding and substring matching
            return self.fuzzy_match_ocr(ocr_text, keys)
        else:
            # Preprocess the OCR text for plain string matching
            ocr_text = self.pre_process_text(ocr_text)
            keys = [self.pre_process_text(key) for key in keys]

            # Check if any key is a substring of the OCR text
            found_match = any(k in ocr_text for k in keys)

            if found_match:
                matched_key = [k for k in keys if k in ocr_text][0]
                return True, matched_key
            else:
                return False, None

    def __call__(self, ocr_text, keys, use_partial_matching=True, check_individual=False):
        found_key, key_text = self.match_key(ocr_text, keys, use_partial_matching=use_partial_matching, check_individual=check_individual)

        return OutputFormat(found_key=found_key,
                            key_text=key_text)


# Example usage
if __name__ == "__main__":
    matcher = StringMatcher()

    ocr_texts = ["TX", "Polic Holder", "Insuranc Polic", "Dae of Birth"]
    keys = ["TX", "Policy Holder", "Insurance Policy", "Date of Birth"]

    for ocr_text in ocr_texts:
        is_matched, matched_key = matcher.match_key(ocr_text, keys, use_partial_matching=True, check_individual=True)
        print(f"OCR Text: '{ocr_text}' -> Is Matched: {is_matched}, Matched Key: '{matched_key}'")