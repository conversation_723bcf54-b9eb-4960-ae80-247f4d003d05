class MSTGraph:
    def __init__(self, vertices):
        self.vertices = vertices
        self.graph = []

    def add_edge(self, points):
        u = 0
        v = 0
        for index, p in enumerate(points):
            v += 1
            if index > 0:
                if p - points[index-1] > 100:
                    u += 1
                    v = 1
            self.graph.append([u, v, p])

    def find(self, parent, i):
        if parent[i] != i:
            parent[i] = self.find(parent, parent[i])
        return parent[i]

    @staticmethod
    def union(parent, rank, x, y):
        if rank[x] < rank[y]:
            parent[x] = y
        elif rank[x] > rank[y]:
            parent[y] = x
        else:
            parent[y] = x
            rank[x] += 1

    def cluster_using_mst(self):
        result = []
        i = 0
        e = 0
        self.graph = sorted(self.graph,
                            key=lambda item: item[2])
        parent = []
        rank = []
        for node in range(self.vertices):
            parent.append(node)
            rank.append(0)
        while e < self.vertices - 1:
            if i == self.vertices:
                break
            u, v, w = self.graph[i]
            i = i + 1
            x = self.find(parent, u)
            y = self.find(parent, v)

            if x != y:
                e = e + 1
                result.append([u, v, w])
                self.union(parent, rank, x, y)
                if e == self.vertices - 1:
                    break

        minimum_cost = 0
        clustered_points = {}
        for u, v, weight in result:
            minimum_cost += weight
            if u in clustered_points.keys():
                clustered_points[u].append(weight)
            else:
                clustered_points[u] = [weight]
        print("Minimum Cost in Tree = {}".format(minimum_cost))
        print("Clustered Points = {}".format(clustered_points))
        return clustered_points


if __name__ == '__main__':
    points = [299, 363, 464, 1581, 1584, 1600, 1654, 1675]
    g = MSTGraph(len(points))
    g.add_edge(points)
    g.cluster_using_mst()

    d = {'a': [1, 2, 3], 'c': ['one', 'two'], 'b': ['blah', 'bhasdf', 'asdf'], 'd': ['asdf', 'wer', 'asdf', 'zxcv']}
    d1 = sorted(d)
    print(d, d1)

