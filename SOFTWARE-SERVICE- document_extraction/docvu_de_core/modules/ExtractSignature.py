import cv2
import os
from docvu_de_core.io import ObjectDetectionOutputFormat
from docvu_de_core.models import ModelManager
# from config import *

class ExtractSignature:
    def __init__(self, logger):
        self.logger = logger

    def compute_iou(self, boxA, boxB):
        xA = max(boxA["x1"], boxB["x1"])
        yA = max(boxA["y1"], boxB["y1"])
        xB = min(boxA["x2"], boxB["x2"])
        yB = min(boxA["y2"], boxB["y2"])

        interArea = max(0, xB - xA) * max(0, yB - yA)
        if interArea == 0:
            return 0.0

        boxAArea = (boxA["x2"] - boxA["x1"]) * (boxA["y2"] - boxA["y1"])
        boxBArea = (boxB["x2"] - boxB["x1"]) * (boxB["y2"] - boxB["y1"])
        # Check if either of the areas is zero to prevent division by zero
        if boxAArea == 0 or boxBArea == 0:
            return 0.0

        iou = interArea / float(boxAArea + boxBArea - interArea)
        return iou

    def extract_signature_details(self, image_path, output_dir=None, return_cropped_regions=False):
        bounding_boxes = []
        class_labels = []
        confidences = []
        crops = []

        try:
            detected_objects = ModelManager.yolo_v8_for_lbsd_model.get_detected_objects(image_path, output_path=image_path.replace('.jpg', '_lbsd.jpg'))
        except Exception as e:
            self.logger.error(f"Error during signature detection: {e}")
            return ObjectDetectionOutputFormat(
                success=False,
                message="Error during detection.",
                image_path=image_path
            )

        signature_objects = [obj for obj in detected_objects if obj["class_id"] == 0 and obj["detected"]]

        selected_signature_objects = []
        iou_threshold=0.5

        for i, bboxA in enumerate(signature_objects):
            keep = True  # Flag to check if bboxA should be kept
        
            for j, bboxB in enumerate(signature_objects):
                if i != j:  # Don't compare the bounding box with itself
                    iou = self.compute_iou(bboxA['bbox'], bboxB['bbox'])
                
                # If IoU is above the threshold, compare areas and choose the larger one
                    if iou > iou_threshold:
                        bboxA_area = (bboxA['bbox']['x2'] - bboxA['bbox']['x1']) * (bboxA['bbox']['y2'] - bboxA['bbox']['y1'])
                        bboxB_area = (bboxB['bbox']['x2'] - bboxB['bbox']['x1']) * (bboxB['bbox']['y2'] - bboxB['bbox']['y1'])
                        
                        # Keep the larger area box and discard the smaller one
                        if bboxA_area < bboxB_area:
                            keep = False
                            break
        
            if keep:
                selected_signature_objects.append(bboxA)  

        for obj in selected_signature_objects:
            bounding_boxes.append(obj["bbox"])
            class_labels.append("signature")
            confidences.append(obj["confidence"])

            if return_cropped_regions:
                orig_image = cv2.imread(image_path)
                if orig_image is None:
                    self.logger.error(f"Unable to read image: {image_path}")
                    continue

                bbox = obj["bbox"]
                x1, y1, x2, y2 = bbox["x1"], bbox["y1"], bbox["x2"], bbox["y2"]
                cropped_image = orig_image[y1:y2, x1:x2]

                if output_dir:
                    os.makedirs(output_dir, exist_ok=True)
                    cropped_path = os.path.join(output_dir, f"signature_{len(crops) + 1}.jpg")
                    cv2.imwrite(cropped_path, cropped_image)
                    crops.append(cropped_path)
                else:
                    crops.append(cropped_image)

        return ObjectDetectionOutputFormat(
            success=True,
            message=f"Detected {len(selected_signature_objects)} signature(s).",
            bounding_boxes=bounding_boxes,
            class_labels=class_labels,
            confidences=confidences,
            image_path=image_path,
            crops=crops
        )
    
if __name__== "__main__":
    # ModelManager.load_config(model_config)
    ModelManager.load(None, load_yolo_v8_for_lbsd_model=True, overwrite= False)