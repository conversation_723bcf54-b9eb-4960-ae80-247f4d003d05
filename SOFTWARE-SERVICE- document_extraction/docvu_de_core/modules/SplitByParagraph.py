import numpy as np
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.utils.element_utils import ElementUtils


class SplitByParagraph:
    def __init__(self, vpos_threshold=None, auto_split_algo=None):
        """
        Initialize the splitter with options for manual or automatic VPOS threshold.

        :param vpos_threshold: The maximum vertical distance between lines to consider them part of the same paragraph.
                               If None, auto-split will be used based on the specified algorithm.
        :param auto_split_algo: The algorithm to use for auto-split ('mean' or 'kmeans').
                                Only used if vpos_threshold is None.
        """
        self.vpos_threshold = vpos_threshold
        self.auto_split_algo = auto_split_algo

    def split_text(self, structured_elements):
        """
        Split the structured data into paragraphs based on the vertical position (VPOS) distance.

        :param structured_elements: List of dictionaries containing text and bounding box data.
        :return: An OutputFormat object containing bounding boxes, line numbers, points (text content), and success
        flag.
        """
        if self.vpos_threshold is None and self.auto_split_algo is not None:
            try:
                self.vpos_threshold = self.auto_split(structured_elements)
            except Exception as e:
                # If auto-split fails, return everything as one paragraph
                print(f"Auto-split error: {e}")
                para = ' '.join(se['text'] for se in structured_elements)
                bbox, line_number = ElementUtils.get_bbox_and_line_number(structured_elements)
                paragraphs = [[para, bbox, line_number]]
                return self.create_output_format(paragraphs, False, structured_elements)

        paragraphs = []
        buffer = []
        buffer_bbox = []
        buffer_line_numbers = []
        prev_vpos = None
        split_occurred = False

        for element in structured_elements:
            text = element['text']
            vpos = element['VPOS']
            bbox = (element['HPOS'], element['VPOS'], element['END_HPOS'], element['END_VPOS'])
            line_number = element['LINE_NUMBER'] if 'LINE_NUMBER' in element else float('inf')

            if prev_vpos is not None and (vpos - prev_vpos) > self.vpos_threshold:
                if buffer:
                    paragraph_text = ' '.join(buffer).strip()
                    if paragraph_text:
                        paragraphs.append((
                            paragraph_text,
                            (min(b[0] for b in buffer_bbox), min(b[1] for b in buffer_bbox),
                             max(b[2] for b in buffer_bbox), max(b[3] for b in buffer_bbox)),
                            min(buffer_line_numbers)
                        ))
                    buffer = []
                    buffer_bbox = []
                    buffer_line_numbers = []
                    split_occurred = True

            buffer.append(text)
            buffer_bbox.append(bbox)
            buffer_line_numbers.append(line_number)
            prev_vpos = vpos

        if buffer:
            paragraph_text = ' '.join(buffer).strip()
            if paragraph_text:
                paragraphs.append((
                    paragraph_text,
                    (min(b[0] for b in buffer_bbox), min(b[1] for b in buffer_bbox),
                     max(b[2] for b in buffer_bbox), max(b[3] for b in buffer_bbox)),
                    min(buffer_line_numbers)
                ))

        return self.create_output_format(paragraphs, split_occurred, structured_elements)

    def create_output_format(self, paragraphs, success, elements):
        """
        Create the output format object from paragraphs.

        :param paragraphs: List of paragraphs with their bounding boxes and line numbers.
        :param success: Boolean flag indicating if the split was successful.
        :return: PostProcessOutputFormat object.
        """
        points = []
        bboxes = []
        line_numbers = []
        for paragraph in paragraphs:
            points.append(paragraph[0])
            bboxes.append(paragraph[1])
            line_numbers.append(paragraph[2])

        return PostProcessOutputFormat(
                bbox=bboxes,
                line_number=line_numbers,
                value=points,
                success=success,
                elements=elements
        )

    def auto_split(self, structured_elements):
        """
        Automatically determine the VPOS threshold using the specified algorithm.

        :param structured_elements: List of dictionaries containing text and bounding box data.
        :return: The automatically determined VPOS threshold.
        """
        vpos_differences = [
            structured_elements[i + 1]['VPOS'] - structured_elements[i]['VPOS']
            for i in range(len(structured_elements) - 1)
        ]

        if self.auto_split_algo == 'mean':
            return np.mean(vpos_differences)
        elif self.auto_split_algo == 'kmeans':
            return self.kmeans_threshold(vpos_differences)
        else:
            raise ValueError("Invalid auto_split_algo. Use 'mean' or 'kmeans'.")

    def kmeans_threshold(self, vpos_differences):
        """
        Determine the VPOS threshold using K-means clustering.

        :param vpos_differences: List of VPOS differences between consecutive elements.
        :return: The VPOS threshold determined by K-means clustering.
        """
        vpos_differences = np.array(vpos_differences).reshape(-1, 1)
        max_clusters = min(len(vpos_differences), 10)  # Set a reasonable maximum number of clusters

        best_k = 2
        best_score = -1
        best_centers = np.array([])

        for k in range(2, max_clusters + 1):
            kmeans = KMeans(n_clusters=k, random_state=0).fit(vpos_differences)
            score = silhouette_score(vpos_differences, kmeans.labels_)

            if score > best_score:
                best_score = score
                best_k = k
                best_centers = kmeans.cluster_centers_

        if best_centers.size == 0 or len(best_centers) < 2:
            raise ValueError("K-means clustering failed to determine valid centers.")

        centers = sorted(best_centers.flatten())
        threshold = (centers[-2] + centers[-1]) / 2  # Choose threshold between the two largest centers
        vpos_differences = np.array(vpos_differences)
        mean_diff = np.mean(vpos_differences).item()
        if abs(threshold - mean_diff) <  5:
            threshold = max(vpos_differences) * 2
        return threshold

    def __call__(self, **kwargs):
        input_data = kwargs.get('elements')
        return self.split_text(input_data)


# Example usage:
if __name__ == "__main__":
    structured_elements = [
        {'text': 'EXHIBIT A', 'HPOS': 753, 'VPOS': 389, 'WIDTH': 181, 'HEIGHT': 24, 'END_HPOS': 934, 'END_VPOS': 413,
         'LINE_NUMBER': 6},
        {'text': 'LEGAL DESCRIPTION', 'HPOS': 677, 'VPOS': 428, 'WIDTH': 325, 'HEIGHT': 24, 'END_HPOS': 1002,
         'END_VPOS': 452, 'LINE_NUMBER': 7},
        {'text': 'Parcel 1', 'HPOS': 162, 'VPOS': 496, 'WIDTH': 87, 'HEIGHT': 20, 'END_HPOS': 249, 'END_VPOS': 516,
         'LINE_NUMBER': 8},
        {'text': 'Lot 15, Sandra Villas, according to Book 266 of Maps, Page 4, records of Maricopa County, Arizona.',
         'HPOS': 162, 'VPOS': 560, 'WIDTH': 1224, 'HEIGHT': 20, 'END_HPOS': 1386, 'END_VPOS': 580, 'LINE_NUMBER': 9},
        {'text': 'Parcel 2', 'HPOS': 162, 'VPOS': 624, 'WIDTH': 85, 'HEIGHT': 20, 'END_HPOS': 247, 'END_VPOS': 644,
         'LINE_NUMBER': 10},
        {
            'text': 'Together with an Easement for ingress, egress over the East 8.5 feet of Lot 16, Sandra Villas, '
                    'accroding to',
            'HPOS': 160, 'VPOS': 688, 'WIDTH': 1294, 'HEIGHT': 20, 'END_HPOS': 1454, 'END_VPOS': 708,
            'LINE_NUMBER': 11},
        {
            'text': 'Book 266 of Maps, Page 4, records of Maricopa County, Arizona, as disclosed to Recorders No. '
                    '85-054108',
            'HPOS': 162, 'VPOS': 720, 'WIDTH': 1303, 'HEIGHT': 20, 'END_HPOS': 1465, 'END_VPOS': 740, 'LINE_NUMBER': 12}
    ]

    splitter_mean = SplitByParagraph(vpos_threshold=None, auto_split_algo='mean')
    sections_mean = splitter_mean.split_text(structured_elements)
    print("Mean-based auto-split:")
    print(f"BBoxes: {sections_mean.bbox}")
    print(f"Line Numbers: {sections_mean.line_number}")
    print(f"Points: {sections_mean.value}")
    print(f"Success: {sections_mean.success}")

    splitter_kmeans = SplitByParagraph(vpos_threshold=None, auto_split_algo='kmeans')
    sections_kmeans = splitter_kmeans.split_text(structured_elements)
    print("K-means-based auto-split:")
    print(f"BBoxes: {sections_kmeans.bbox}")
    print(f"Line Numbers: {sections_kmeans.line_number}")
    print(f"Points: {sections_kmeans.value}")
    print(f"Success: {sections_kmeans.success}")
