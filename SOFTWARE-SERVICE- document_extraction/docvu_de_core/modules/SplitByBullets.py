from docvu_de_core.utils.TextPreprocessor import TextPreprocessor
from docvu_de_core.utils.OCRUtils import OCRUtils
from docvu_de_core.io import OutputFormat
import re
import math

DEBUG = False

class SplitByBullets:

    max_supported_bullet_number = 150

    @classmethod
    def remove_bullet_points(cls, points):
        # Regular expression to match bullet points like "1.", "2.", etc. only if they are at the start
        bullet_pattern = re.compile(r'^\s*\d{1,3}\.\s*')

        # Remove the bullet point from each point if it matches the pattern at the start
        cleaned_points = [bullet_pattern.sub('', point, count=1) for point in points]

        return cleaned_points

    @staticmethod
    def filter_invalid_bboxes(bullet_points_str, bullet_points_bbox, bullet_points_line_numbers):
        valid_bullet_points_str = []
        valid_bullet_points_bbox = []
        valid_bullet_points_line_numbers = []

        for i in range(len(bullet_points_bbox)):
            start_hpos, start_vpos, end_hpos, end_vpos = bullet_points_bbox[i]
            if (not math.isinf(start_hpos) and start_hpos >= 0 and
                    not math.isinf(start_vpos) and start_vpos >= 0 and
                    not math.isinf(end_hpos) and end_hpos >= 0 and
                    not math.isinf(end_vpos) and end_vpos >= 0 and
                    start_hpos < end_hpos and start_vpos < end_vpos):
                valid_bullet_points_str.append(bullet_points_str[i])
                valid_bullet_points_bbox.append(bullet_points_bbox[i])
                valid_bullet_points_line_numbers.append(bullet_points_line_numbers[i])

        return valid_bullet_points_str, valid_bullet_points_bbox, valid_bullet_points_line_numbers

    @classmethod
    def split_by_bullets_from_list_of_line_str(cls, lines):
        """
        Consolidates lines from OCR output into bullet points, combining continuation lines.

        Parameters:
        - lines (list of str): The OCR output lines.

        Returns:
        - list of str: The consolidated bullet points.
        """
        bullet_points = []
        current_bullet_point = ""
        is_first_line = True

        for line in lines:
            if is_first_line:
                is_first_line = False
                if len(line) > 1:
                # Check if the line starts with a number followed by a dot or whitespace
                    if (not line.lstrip().startswith(tuple(str(i) for i in range(1, 101))) and
                            ((line.lstrip()[2] == '.' or line.lstrip()[2].isspace()) or
                             (line.lstrip()[1] == '.' or line.lstrip()[1].isspace()))):
                        continue

            stripped_line = line.lstrip()  # Remove leading whitespace
            if any(stripped_line.startswith(f"{i}.") or stripped_line.startswith(f"{i} ") for i in range(1, 100)):
                # Remove the numeric prefix and the following dot or space
                line_without_number = stripped_line.split(' ', 1)[-1].lstrip('.').lstrip()
                if current_bullet_point:
                    # Append the current bullet point and start a new one without the numeric prefix
                    bullet_points.append(current_bullet_point.strip())
                    current_bullet_point = line_without_number
                else:
                    # Start a new bullet point without the numeric prefix
                    current_bullet_point = line_without_number
            else:
                current_bullet_point += " " + line

        if current_bullet_point:
            bullet_points.append(current_bullet_point.strip())

        return bullet_points

    @classmethod
    def handle_special_cases(cls, lines):
        """
        Handles special cases where text contains numbers that are not bullet points.
        Merges lines that were incorrectly split due to these numbers.
        """
        merged_lines = []
        i = 0
        while i < len(lines):
            line = lines[i]
            if len(line) > 1:  # Check if line has more than one segment
                text = line[0]['text'].strip()
                if text.isdigit():  # Check if the first segment is a digit
                    number = int(text)
                    if number > cls.max_supported_bullet_number or (
                            merged_lines and
                            number != merged_lines[-1][0]['text'] + 1 and
                            (i + 1 < len(lines) and lines[i + 1][0]['text'].isdigit())
                    ):
                        # Merge with the previous line if it's not a valid bullet point
                        merged_lines[-1].extend(line)
                    else:
                        # Treat as a new bullet point
                        merged_lines.append(line)
                else:
                    # Treat as a part of the current bullet point
                    merged_lines.append(line)
            else:
                merged_lines.append(line)
            i += 1
        return merged_lines

    @classmethod
    def is_bullet_point(cls, text):
        # Trim leading and trailing whitespace
        text = text.strip()

        # Regular expression to match bullet points like "1.", "2.", "1 ", "2 "
        # but not "1.x"
        bullet_pattern = re.compile(r'^(\d{1,3})([\.\s])(.*)$')

        # Check if the text matches the bullet point pattern
        match = bullet_pattern.match(text)
        if match:
            # Extract the number at the start
            number_str = match.group(1).strip()
            separator = match.group(2)
            following_text = match.group(3)

            # Check if the separator is '.' followed by a space or if the separator is a space
            if separator == '.' and (following_text == '' or following_text[0].isspace()):
                try:
                    number = int(number_str)
                    # Ensure the number is within the supported bullet number range
                    if 1 <= number < cls.max_supported_bullet_number:
                        return True, number
                except ValueError:
                    return False, None
            elif separator.isspace():
                try:
                    number = int(number_str)
                    # Ensure the number is within the supported bullet number range
                    if 1 <= number < cls.max_supported_bullet_number:
                        return True, number
                except ValueError:
                    return False, None

        return False, None


    @classmethod
    def find_last_bullet_point(cls, data_list):
        last_bullet_index = -1
        for i, item in enumerate(data_list):
            if 'text' in item[0]:
                text = item[0]['text'].strip()
                # Check if the text starts with a digit followed by a dot
                if text:
                    is_bullet_point, point_int = cls.is_bullet_point(text)
                    if is_bullet_point:
                        last_bullet_index = i
        return last_bullet_index if last_bullet_index != -1 else None

    @classmethod
    def find_first_bullet_point(cls, data_list):
        last_bullet_index = len(data_list) - 1
        for i, item in enumerate(data_list):
            if 'text' in item[0]:
                text = item[0]['text'].strip()
                # Check if the text starts with a digit followed by a dot
                if text:
                    is_bullet_point, point_int = cls.is_bullet_point(text)
                    if is_bullet_point:
                        last_bullet_index = i
                        return last_bullet_index
        return last_bullet_index

    @classmethod
    def trim_before(cls, bullet_points):
        new_points = []
        found_int, point_int = cls.is_bullet_point(bullet_points[cls.find_first_bullet_point(bullet_points)][0]['text'])
        if found_int and point_int > 4:
            return bullet_points
        for idx, bp in enumerate(bullet_points):
            is_bullet_point, point_int = cls.is_bullet_point(bp[0]['text'])
            if is_bullet_point:
                new_points = bullet_points[idx:]
                if point_int == 1:
                    return new_points
                ## remove points before
                if idx-point_int < -1 or idx == 0:
                    return new_points
                for i in range(idx-1, idx-point_int, -1):
                    new_points.insert(0, bullet_points[i])
                return new_points
        return bullet_points

    @classmethod
    def merge_points(cls, bullet_points):
        new_bullet_points = []
        for bp in bullet_points:
            new_bullet_points.extend(bp)
        return new_bullet_points

    @classmethod
    def consolidate_bullet_points(cls, paragraphs):
        consolidated = []
        current_bullet = []
        current_bullet_number = None

        for idx, bullet_point in enumerate(paragraphs):
            bullet_text = ' '.join([item['text'] for item in bullet_point])
            is_bullet, number = cls.is_bullet_point(bullet_text)

            if is_bullet:
                if current_bullet and (current_bullet_number is None or number != current_bullet_number + 1):
                    consolidated.append(current_bullet)
                    current_bullet = []
                    current_bullet_number = number
                current_bullet.extend(bullet_point)
            else:
                if current_bullet:
                    current_bullet.extend(bullet_point)
                else:
                    consolidated.append(bullet_point)

            # Handle the case when the last bullet point is reached
            if idx == len(paragraphs) - 1 and current_bullet:
                consolidated.append(current_bullet)

        return consolidated


    @classmethod
    def merge_if_no_bullet_found(cls, bullet_points):
        found_bullet = False
        for idx, bp in enumerate(bullet_points):
            is_bullet_point, point_int = cls.is_bullet_point(bp[0]['text'])
            if is_bullet_point:
                found_bullet = True
                break
        if not found_bullet:
            return [cls.merge_points(bullet_points)]
        else:
            return bullet_points

    @classmethod
    def merge_from_bottom_till_last_found(cls, bullet_points):
        prev_found_number_idx = cls.find_last_bullet_point(bullet_points)
        if prev_found_number_idx is not None and len(bullet_points) > (prev_found_number_idx - 1):
            to_merge = bullet_points[prev_found_number_idx:]
            bullet_points = bullet_points[:prev_found_number_idx]
            last_bullet = []
            for tm in to_merge:
                last_bullet.extend(tm)
            bullet_points.append(last_bullet)
        return bullet_points

    @classmethod
    def split_by_bullets_from_list_of_line_lists(cls, lines, vpos_threshold=120):
        function_name = 'split_by_bullets_from_list_of_line_lists'
        if not lines or len(lines) == 0:
            return []
        for i in range(len(lines)):
            if not lines[i]:
                return []

        all_end_hpos = [phrase['END_HPOS'] for line in lines for phrase in line]
        max_hpos = max(all_end_hpos)

        ## check if HPOS of the very 1st line is > 30% of aprox width then dont consider it
        approx_width = max_hpos
        if (lines[0][0]['HPOS']) > (approx_width * (3.5/10)) and len(lines) > 1:
            lines = lines[1:] if len(lines) > 1 else []

        all_point_wop_hpos = []
        # Iterate over each line in lines
        for line in lines:
            # Iterate over each phrase in the current line
            is_bullet_point, point_int = cls.is_bullet_point(line[0]['text'])
            if is_bullet_point:
                if len(line) > 1:
                    found_int = False
                    for phrase in line:
                        is_bullet_point, point_int = cls.is_bullet_point(line[0]['text'])
                        if not found_int and  is_bullet_point:
                            found_int = True
                            if len(phrase['WORDS']) > 1:
                                all_point_wop_hpos.append(phrase['WORDS'][1]['HPOS'])
                                break
                        else:
                            if found_int:
                                all_point_wop_hpos.append(phrase['WORDS'][0]['HPOS'])
                                break
                else:
                    if line[0]['WORDS'][0]['text'].strip() in \
                            [f"{i}." for i in range(SplitByBullets.max_supported_bullet_number)] \
                            and ((line[0]['WORDS'][0]['HPOS'] + line[0]['WORDS'][0]['WIDTH']) - \
                            line[0]['WORDS'][0]['HPOS']) > 35 and len(line[0]['WORDS']) > 1:
                        all_point_wop_hpos.append(line[0]['WORDS'][1]['HPOS'])
        all_hpos = [phrase['HPOS'] for line in lines for phrase in line]

        min_hpos = int(min(all_point_wop_hpos) if len(all_point_wop_hpos) != 0 else min(all_hpos))
        min_point_hpos = min(all_hpos)

        bullet_points = []
        current_bullet_point = []
        is_first_line = True
        is_possible_next_bullet = True
        last_vpos = 0  # To track the last VPOS of bullet points
        prev_found_number = None
        prev_found_number_idx = 0
        is_prev_point_without_number = False
        first_found_line_number = None
        processing_first_bullet = False

        for ldx, line in enumerate(lines):
            if DEBUG:
                print(''.join(l['text'] for l in line))
            current_vpos = line[0]['VPOS']

            is_bullet_point, point_int = cls.is_bullet_point(line[0]['text'])

            if is_bullet_point:
                if point_int == 3:
                    print('hi')

            # Skip lines that are too far vertically from the last bullet point
            if not is_bullet_point and last_vpos != 0 and (current_vpos - last_vpos > vpos_threshold):
                if DEBUG:
                    print('last_vpos =', last_vpos)
                    print('current_vpos =', current_vpos)
                continue

            if DEBUG:
                print('is_bullet_point =', is_bullet_point)
                print('point_int =', point_int)

            if is_first_line:
                # Check if the line starts with a number followed by a dot or whitespace
                if not is_bullet_point:
                    first_found_line_number = None
                else:
                    first_found_line_number = point_int
                    processing_first_bullet = True
                    is_first_line = False
                    last_vpos = current_vpos  # Update last VPOS on valid bullet point


            if (line[0]['HPOS'] - min_hpos) > 50:
                is_possible_next_bullet = False
            elif (line[0]['HPOS'] - min_hpos) < -2 and bullet_points:
                is_possible_next_bullet = True

            '''if is_bullet_point:
                    if min_point_hpos + 10 < line[0]['HPOS']:
                        is_bullet_point = False
                    if prev_found_number is not None and prev_found_number + 1 == point_int:
                        is_possible_next_bullet = True'''

            if is_bullet_point or is_possible_next_bullet:
                ## 1. Get the bullet number
                bullet_number = point_int

                if DEBUG:
                    print('bullet_number =', bullet_number)

                ## 2. Get the line by removing the numbeer

                ## 3. set is_possible_next_bullet to false, as processing it
                is_possible_next_bullet = False

                ## 4. previous bullet is without number and current found bullet number is
                ##    just the next (+1) number of the previous found bullet,
                ##    then merge till current found bullet, previous found bullet
                if bullet_number is not None and bullet_points and is_prev_point_without_number \
                        and prev_found_number is not None \
                        and (bullet_number - prev_found_number) == 1:
                    if DEBUG:
                        print('bullet_number =', bullet_number)
                        print('prev_found_number =', )
                    remaining_bullet_point = current_bullet_point
                    current_bullet_point = []
                    for bullet_point in bullet_points[prev_found_number_idx: ]:
                        current_bullet_point.extend(bullet_point)
                    current_bullet_point.extend(remaining_bullet_point)
                    bullet_points = bullet_points[:prev_found_number_idx]

                ## 5. Add current bullet point to all bullet points
                if current_bullet_point:
                    # Append the current bullet point and start a new one without the numeric prefix
                    if processing_first_bullet:
                        processing_first_bullet = False
                        if len(bullet_points) >= (first_found_line_number + 1):
                            if current_bullet_point:
                                bullet_points = bullet_points[-1 * (first_found_line_number - 2):]
                            else:
                                bullet_points = bullet_points[-1 * (first_found_line_number + 1):]

                    bullet_points.append(current_bullet_point)
                    current_bullet_point = line
                else:
                    # Start a new bullet point without the numeric prefix
                    current_bullet_point = line

                ## 6. Store prev_found_number_idx to be used while merging if next fount bullet is without number
                if  bullet_points:
                    prev_found_number_idx = cls.find_last_bullet_point(bullet_points)

                ## 7. Update previous found bullet number as well for tracking
                if (bullet_number is not  None) and (prev_found_number is None or prev_found_number < bullet_number) \
                        and bullet_number < SplitByBullets.max_supported_bullet_number:
                    prev_found_number = bullet_number
                else:
                    prev_found_number = prev_found_number

                ## 8. Track if bullet points are encountered without number
                if bullet_number is None:
                    is_prev_point_without_number = True
                else:
                    if bullet_number > SplitByBullets.max_supported_bullet_number \
                            and (prev_found_number is not None and prev_found_number < bullet_number):
                        is_prev_point_without_number = True
                    else:
                        is_prev_point_without_number = False
            else:
                current_bullet_point.extend(line)

            if (max_hpos - (line[-1]['HPOS'] + line[-1]['WIDTH'])) > 80:
                is_possible_next_bullet = True
            else:
                is_possible_next_bullet = False
            last_vpos = current_vpos  # Update last VPOS after processing line

        if current_bullet_point:
            bullet_points.append(current_bullet_point)

        ## trim bullet points before
        bullet_points = cls.trim_before(bullet_points)

        ## merge bullets till last found bullet point from bottom
        bullet_points = cls.merge_from_bottom_till_last_found(bullet_points)

        ## merge all points if no bullet is found
        bullet_points = cls.merge_if_no_bullet_found(bullet_points)

        bullet_points_str = []
        bullet_points_bbox = []
        bullet_points_line_numbers = []
        for bullet_point in bullet_points:
            # bbox, line_no = cls.pu.get_bbox_and_line_number(bullet_points)
            bp_str = ''
            start_vpos = float('inf')
            start_hpos = float('inf')
            end_vpos = float('-inf')
            end_hpos = float('-inf')
            line_no = float('inf')
            for line in bullet_point:
                bp_str += ' ' + line['text']
                start_vpos = min(start_vpos, line['VPOS'])
                start_hpos = min(start_hpos, line['HPOS'])
                end_vpos = max(end_vpos, line['END_VPOS'])
                end_hpos = max(end_hpos, line['END_HPOS'])
                line_no = min(line_no, line['LINE_NUMBER'])
            bullet_points_str.append(bp_str)

            # Check and update each value if it is infinity or negative infinity
            start_hpos = 0 if math.isinf(start_hpos) or start_hpos < 0 else start_hpos
            start_vpos = 0 if math.isinf(start_vpos) or start_vpos < 0 else start_vpos
            end_hpos = 0 if math.isinf(end_hpos) or end_hpos < 0 else end_hpos
            end_vpos = 0 if math.isinf(end_vpos) or end_vpos < 0 else end_vpos

            # Append the updated values to bullet_points_bbox
            bullet_points_bbox.append((start_hpos, start_vpos, end_hpos, end_vpos))
            bullet_points_line_numbers.append(line_no)

        bullet_points_str, bullet_points_bbox, bullet_points_line_numbers = \
            cls.filter_invalid_bboxes(bullet_points_str, bullet_points_bbox, bullet_points_line_numbers)
        return bullet_points_str, bullet_points_bbox, bullet_points_line_numbers


    @classmethod
    def split_by_bullets(cls, lines, sort=True):
        """
        Consolidates lines from OCR output into bullet points, combining continuation lines.

        Parameters:
        - lines (list of str): The OCR output lines.

        Returns:
        - list of str: The consolidated bullet points.
        """
        if lines is None or len(lines) == 0:
            return OutputFormat(value=None,
                                search_next_page=False,
                                success=False,
                                elements=None,
                                is_table=True,
                                line_number=None,
                                bbox=None)
        elif isinstance(lines[0], str):
            return cls.split_by_bullets_from_list_of_line_str(lines)
        elif isinstance(lines[0], list):
            bullet_points_str, bullet_points_bbox, bullet_points_line_numbers = \
                cls.split_by_bullets_from_list_of_line_lists(lines)
            return OutputFormat(value = bullet_points_str,
                            search_next_page = False,
                            success=True,
                            elements = [elem for line in lines for elem in line],
                            is_table = True,
                            line_number = bullet_points_line_numbers,
                            bbox = bullet_points_bbox)
        elif isinstance(lines[0], dict):
            lines = OCRUtils.sort_and_merge_ocr_data(lines, merge_lines = False) if sort else lines
            bullet_points_str, bullet_points_bbox, bullet_points_line_numbers = cls.split_by_bullets_from_list_of_line_lists(
                lines)
            return OutputFormat(value=bullet_points_str,
                                search_next_page=False,
                                success=True,
                                elements=[elem for line in lines for elem in line],
                                is_table=True,
                                line_number=bullet_points_line_numbers,
                                bbox=bullet_points_bbox)
        else:
            return OutputFormat(value=None,
                                search_next_page=False,
                                success=False,
                                elements=None,
                                is_table=True,
                                line_number=None,
                                bbox=None)

    # Example usage

if __name__ == '__main__':
    lines = [
        [{"text": "Preamble text not a bullet", "HPOS": 50, "VPOS": 80, "WIDTH": 200, "END_HPOS": 250, "END_VPOS": 100}],
        [{"text": "1.", "HPOS": 100, "VPOS": 100, "WIDTH": 30, "END_HPOS": 130, "END_VPOS": 120},
         {"text": "First item details", "HPOS": 130, "VPOS": 100, "WIDTH": 170, "END_HPOS": 300, "END_VPOS": 120}],
        [{"text": "continuing the above item.", "HPOS": 100, "VPOS": 120, "WIDTH": 200, "END_HPOS": 300, "END_VPOS": 140}],
        [{"text": "2.", "HPOS": 100, "VPOS": 140, "WIDTH": 30, "END_HPOS": 130, "END_VPOS": 160},
         {"text": "Second item starts here.", "HPOS": 130, "VPOS": 140, "WIDTH": 180, "END_HPOS": 310, "END_VPOS": 160}],
        [{"text": "Additional details of second item.", "HPOS": 100, "VPOS": 160, "WIDTH": 250, "END_HPOS": 350, "END_VPOS": 180}]
    ]


    bullet_points, bullet_point_texts = SplitByBullets.split_by_bullets_from_list_of_line_lists(lines)
    print("Bullet Points:")
    print(bullet_points)
    print("Bullet Point Texts (Detailed):")
    print(bullet_point_texts)