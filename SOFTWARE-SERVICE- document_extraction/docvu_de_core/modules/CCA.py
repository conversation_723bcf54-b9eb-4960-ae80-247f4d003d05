import cv2
import numpy as np

class CCA:
    def __init__(self, image_path):
        self.image_path = image_path
        self.image = cv2.imread(image_path)
        if self.image is None:
            raise ValueError(f"Could not load image from path: {image_path}")
        self.gray_image = cv2.cvtColor(self.image, cv2.COLOR_BGR2GRAY)

    def detect_strikes(self):
        # Invert the grayscale image
        inverted_image = cv2.bitwise_not(self.gray_image)

        # Apply a binary threshold to get a binary image
        _, binary_image = cv2.threshold(inverted_image, 150, 255, cv2.THRESH_BINARY)

        # Perform connected component analysis
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary_image, connectivity=8)

        bboxes = []
        for i in range(1, num_labels):  # Start from 1 to skip the background component
            x, y, w, h, area = stats[i]
            aspect_ratio = w / float(h)

            # Filter components that are likely to be strikethrough lines based on aspect ratio and size
            if aspect_ratio > 5 and 10 < area < 1000:
                bboxes.append((x, y, x + w, y + h))

        return bboxes

    def is_strikethrough(self, bbox,
                         aspect_ratio_th=15,
                         area_max_th=2000):
        x1, y1, x2, y2 = bbox

        # Ensure bounding box coordinates are valid
        if x1 >= x2 or y1 >= y2 or x1 < 0 or y1 < 0 or x2 > self.gray_image.shape[1] or y2 > self.gray_image.shape[0]:
            return False

        roi = self.gray_image[y1:y2, x1:x2]
        color_roi = self.image[y1:y2, x1:x2].copy()

        # Invert the grayscale ROI
        inverted_roi = cv2.bitwise_not(roi)

        # Apply a binary threshold to get a binary image
        _, binary_roi = cv2.threshold(inverted_roi, 150, 255, cv2.THRESH_BINARY)

        # Perform connected component analysis on the ROI
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary_roi, connectivity=8)
        strikethrough_detected = False

        for i in range(1, num_labels):  # Start from 1 to skip the background component
            x, y, w, h, area = stats[i]
            aspect_ratio = w / float(h)

            # Check if the component is a horizontal line that crosses the entire width of the ROI
            if aspect_ratio > aspect_ratio_th and area > area_max_th and w >= (x2 - x1) * 0.9:
                cv2.rectangle(color_roi, (x, y), (x + w, y + h), (0, 255, 0), 2)
                strikethrough_detected = True

        return strikethrough_detected

    def draw_bboxes(self, bboxes):
        for (x1, y1, x2, y2) in bboxes:
            cv2.rectangle(self.image, (x1, y1), (x2, y2), (0, 255, 0), 2)
        cv2.imshow("Strikethrough Detection", self.image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()

if __name__ == '__main__':
    # Example usage:
    detector = CCA("../../7.jpg")

    # Check a specific bounding box for strikethrough
    bbox = (196, 324, 196, 344)  # replace these with actual coordinates from OCR
    try:
        is_strikethrough = detector.is_strikethrough(bbox)
        print(f"Is the region strikethrough? {'Yes' if is_strikethrough else 'No'}")
    except ValueError as e:
        print(f"Error: {e}")