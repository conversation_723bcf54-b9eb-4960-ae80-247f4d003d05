import cv2
import os
from docvu_de_core.io import ObjectDetectionOutputFormat
from docvu_de_core.models import ModelManager

class ExtractSeal:
    def __init__(self, logger):
        """
        Initializes the ExtractSeal class with a logger instance.
        
        Parameters:
        logger (object): Logger instance for logging messages and errors.
        """
        self.logger = logger

    def extract_seal_details(self, image_path, output_dir=None, return_cropped_regions=False):
        """
        Extracts seal details from the given image using a YOLOv8 model.
        
        Parameters:
        image_path (str): Path to the input image.
        output_dir (str, optional): Directory to save cropped seal images. Defaults to None.
        return_cropped_regions (bool, optional): Whether to return cropped seal images. Defaults to False.
        
        Returns:
        ObjectDetectionOutputFormat: Contains detected seal details, including bounding boxes,
        class labels, confidences, and optionally cropped seal images.
        """
        bounding_boxes = []  # List to store bounding boxes of detected seals.
        class_labels = []    # List to store class labels ("seal").
        confidences = []     # List to store confidence scores.
        crops = []           # List to store cropped seal images or their paths.

        try:
            # Perform object detection using the YOLOv8 model.
            detected_objects = ModelManager.yolo_v8_for_lbsd_model.get_detected_objects(image_path, output_path=image_path.replace('.jpg', '_lbsd.jpg'))
        except Exception as e:
            # Log the error and return a failure response.
            self.logger.error(f"Error during seal detection: {e}")
            return ObjectDetectionOutputFormat(
                success=False,
                message="Error during detection.",
                image_path=image_path
            )

        # Filter only seal objects (class_id == 4 and detected flag is True)
        seal_objects = [obj for obj in detected_objects if obj["class_id"] == 4 and obj["detected"]]

        for obj in seal_objects:
            bounding_boxes.append(obj["bbox"])
            class_labels.append("seal")
            confidences.append(obj["confidence"])

            if return_cropped_regions:
                # Read the original image
                orig_image = cv2.imread(image_path)
                if orig_image is None:
                    self.logger.error(f"Unable to read image: {image_path}")
                    continue

                # Extract bounding box coordinates
                bbox = obj["bbox"]
                x1, y1, x2, y2 = bbox["x1"], bbox["y1"], bbox["x2"], bbox["y2"]
                cropped_image = orig_image[y1:y2, x1:x2]  # Crop the detected seal region

                if output_dir:
                    # Create the output directory if it doesn't exist
                    os.makedirs(output_dir, exist_ok=True)
                    cropped_path = os.path.join(output_dir, f"seal_{len(crops) + 1}.jpg")
                    cv2.imwrite(cropped_path, cropped_image)  # Save the cropped image
                    crops.append(cropped_path)  # Store the cropped image path
                else:
                    crops.append(cropped_image)  # Store the cropped image directly

        return ObjectDetectionOutputFormat(
            success=True,
            message=f"Detected {len(seal_objects)} seal(s).",
            bounding_boxes=bounding_boxes,
            class_labels=class_labels,
            confidences=confidences,
            image_path=image_path,
            crops=crops
        )

if __name__ == "__main__":
    # Load the YOLOv8 model for seal detection
    ModelManager.load(None, load_yolo_v8_for_lbsd_model=True, overwrite=False)