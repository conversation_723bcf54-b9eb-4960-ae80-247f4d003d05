import time
from pprint import pprint
from typing import List, Dict
import re

from pprint import pprint
from typing import List, Dict
import re

DEBUG = False

class TableExtractor:
    """
    A class for extracting table information from OCR data. It groups text elements into rows based on their vertical position (VPOS)
    and uses various heuristics to determine potential table rows and boundaries.
    """

    TABLE_SEPARATORS = ['|']

    def __init__(self, 
                    vpos_tolerance: int = 5, 
                    table_gap_threshold: int = 100, 
                    string_spacing_threshold: int = 250,
                    min_area_threshold: int = 10000,
                    merge_closeby_rows: bool = True):
        """
        Initializes the TableExtractor with given tolerances and thresholds.
        """
        self.vpos_tolerance = vpos_tolerance
        self.table_gap_threshold = table_gap_threshold
        self.string_spacing_threshold = string_spacing_threshold
        self.merge_closeby_rows = merge_closeby_rows
        self.nonsensical_content_pattern = re.compile(r'^[r_!.u]{2,}$')
        self.min_area_threshold = min_area_threshold

    def group_elements_into_rows(self, all_strings, vpos_tolerance = None):
        """
        Group text elements into rows based on VPOS with a given tolerance.
        """
        vpos_tolerance = self.vpos_tolerance if vpos_tolerance is None else vpos_tolerance
        lines = []
        if len(all_strings) == 0:
            return lines
        current_line = [all_strings[0]]  # Start with the first string

        for i in range(len(all_strings) - 1):
            current_string, next_string = all_strings[i], all_strings[i + 1]
            if (next_string["VPOS"] - current_string["VPOS"]) <= vpos_tolerance:
                current_line.append(next_string)
            else:
                lines.append(current_line)
                current_line = [next_string]
        lines.append(current_line)  # Add the last line

        return lines

    def is_line_a_potential_row(self, line, table = None, string_spacing_threshold=None):
        """
        Determine if a line is a potential row based on the spacing between strings
        and the similarity in the number of strings compared to the previous row.
        """
        function_name = 'is_line_a_potential_row'
        
        string_spacing_threshold = self.string_spacing_threshold if string_spacing_threshold is None else string_spacing_threshold
        spacings = [line[i+1]['HPOS'] - (line[i]['HPOS'] + line[i]['WIDTH']) for i in range(len(line)-1)]

        if DEBUG:
            print(function_name, 'spacings =', spacings)
            
        if table is None or len(table) < 1:
            if any(spacing < string_spacing_threshold for spacing in spacings):
                return True
        else:
            boundry = self.calculate_boundary(table[-1])
            if table is None or len(table) < 2:
                if DEBUG:
                    print(function_name, 'max vpos = ', abs(max([(line[i]['VPOS'] + line[i]['HEIGHT']) for i in range(len(line))])))
                    print(function_name, 'self.table_gap_threshold =', self.table_gap_threshold)
                    print(function_name, 'boundry =',  boundry)
                if not spacings:
                    if DEBUG:
                        print(function_name, 'no_spacing')
                        print(function_name, " (min([(line[i]['HPOS'] ) for i in range(len(line))]) - boundry[0])",  (min([(line[i]['HPOS'] ) for i in range(len(line))]) - boundry[0]))
                        print(function_name, "(boundry[2] - max([(line[i]['END_HPOS'] ) for i in range(len(line))])", (boundry[2] - max([(line[i]['END_HPOS'] ) for i in range(len(line))])))
                    if abs(max([(line[i]['VPOS'] + line[i]['HEIGHT']) for i in range(len(line))]) - boundry[3]) <= self.table_gap_threshold // 1.5:
                        if DEBUG:
                            print(function_name, 'so_close')
                        return True
                    elif min([(line[i]['HPOS'] ) for i in range(len(line))]) - boundry[0] > 30 and (boundry[2] - max([(line[i]['END_HPOS'] ) for i in range(len(line))]) > 30):
                        if DEBUG:
                            print(function_name, 'inside_boundary')
                        return True
                elif any(spacing < string_spacing_threshold for spacing in spacings) or \
                    abs(max([(line[i]['VPOS'] + line[i]['HEIGHT']) for i in range(len(line))]) - boundry[3]) <= self.table_gap_threshold // 1.5:
                    return True
            else:
                av_spacing = self.calculate_average_row_spacing(table)
                if DEBUG:
                    print(function_name, 'max vpos = ', min([(line[i]['VPOS']) for i in range(len(line))]))
                    print(function_name, 'av_spacing =', av_spacing)
                    print(function_name, 'boundry =',  boundry)
                    print(function_name, 'substract =',  min([(line[i]['VPOS']) for i in range(len(line))]) - boundry[3])
                if any(spacing < string_spacing_threshold for spacing in spacings) or \
                    abs(min([(line[i]['VPOS']) for i in range(len(line))]) - boundry[3]) <= av_spacing + 5:
                    return True
        return False

    def is_potential_table_end(self, current_line, prev_line, table = None):
        """
        Determine if the gap between the current line and the previous line
        suggests the start of a new potential table.
        """
        function_name = 'is_potential_table_end'
        if not prev_line:
            return False  # Always treat the first line as part of the first table
        
        gap = min(cl['VPOS'] for cl in current_line) - max(pl['VPOS'] + pl['HEIGHT'] for pl in prev_line)
        
        if DEBUG:
            print(function_name, 'gap =', gap)
            print(function_name, 'current_line =', current_line)
            print(function_name, 'prev_line =', prev_line)
            print(function_name, "min(cl['VPOS'] for cl in current_line) =", min(cl['VPOS'] for cl in current_line))
            print(function_name, "max(pl['VPOS'] + pl['HEIGHT'] for pl in prev_line) =", max(pl['VPOS'] + pl['HEIGHT'] for pl in prev_line))
        
        if not table or gap > self.table_gap_threshold:
            return gap > self.table_gap_threshold
        elif table:
            HPOS_list = []
            VPOS_list = []
            endHPOS_list = []
            endVPOS_list = []
            for row in table:
                HPOS, VPOS, endHPOS, endVPOS = self.calculate_boundary(row)
                HPOS_list.append(HPOS)
                VPOS_list.append(VPOS)
                endHPOS_list.append(endHPOS)
                endVPOS_list.append(endVPOS)
            min_HPOS, min_VPOS, max_endHPOS, max_endVPOS = min(HPOS_list), min(VPOS_list), max(endHPOS_list), max(endVPOS_list)
            table_width = max_endHPOS - min_HPOS                        
            
            line_width =  max(pl['END_HPOS'] for pl in prev_line) - min(cl['HPOS'] for cl in current_line)
            
            if DEBUG:
                print('line_width', line_width)
                print('table_width', table_width)
                print('(table_width / line_width)', (table_width / line_width), (table_width / line_width) < 0.6 and len(table) <= 1)
                print('current_line =11 ', current_line)
                print('table =11 ', table)
            return (table_width / line_width if line_width != 0 else 0.01) < 0.6 and len(table) <= 1
        else:
            return gap > self.table_gap_threshold
            
    def split_cell_by_str(self, col, separator):
        texts = col['text'].split(separator)
        words = [[]]
        if separator == ' ':
            words = [[word] for word in col['WORDS']]
        for word in col['WORDS']:
            if separator in word['text']:
                sub_words = word['text'].split(separator)
                for i, sub_text in enumerate(sub_words):
                    new_word = word.copy()
                    new_word['text'] = sub_text
                    words[-1].append(new_word)
                    if i < len(sub_words) - 1:
                        words.append([])
            else:
                words[-1].append(word)
        return zip(texts, words)

    def split_col(self, col):
        for ts in TableExtractor.TABLE_SEPARATORS:
            if ts in col['text']:
                texts = col['text'].split(ts)     
                texts_and_words = self.split_cell_by_str(col, ts)
                ## TODO add words          
                total_chars = len(col['text'])
                total_width = col['WIDTH']
                hpos = col['HPOS']
                vpos = col['VPOS']
                covered_width = 0
                score = 100
                temp_next_string = []
                for tnw in texts_and_words:
                    col_chars = len(tnw)
                    col_fraction = col_chars / total_chars
                    col_width = int(total_width * col_fraction)
                    temp_next_string.append({'HPOS': hpos, 
                                            'VPOS': vpos, 
                                            'WIDTH': col_width, 
                                            'HEIGHT': col['HEIGHT'], 
                                            'text': tnw[0], 
                                            'WORDS': tnw[1],
                                            'SCORE': score})
                    hpos += col_width
                col = temp_next_string
                break
        return col

    def merger_col(self, current_row, table):
        merged_current_row = [current_row[0]]
        for j in range(1, len(current_row)):
            if DEBUG:
                print(1, 'merger_col', 'j =', j)
                print(2, 'merger_col', 'merged_current_row =', merged_current_row)
                print(3, 'merger_col', 'current_row =', current_row)
                print(4, 'merger_col', "current_row[j]['HPOS'] =", current_row[j]['HPOS'])
                print(5, 'merger_col', "merged_current_row[-1]['WIDTH'] =", merged_current_row[-1]['WIDTH'])
                print(6, 'merger_col', "merged_current_row[-1]['HPOS'] =", merged_current_row[-1]['HPOS'])
                print(7, 'merger_col', "current_row[j]['HPOS'] - (merged_current_row[-1]['HPOS'] + merged_current_row[-1]['WIDTH']) =", current_row[j]['HPOS'] - (merged_current_row[-1]['HPOS'] + merged_current_row[-1]['WIDTH']))
                print(8, 'merger_col', "current_row[j] =", current_row[j])
                print(9, 'merger_col', "merged_current_row[-1] =", merged_current_row[-1])
            if current_row[j]['HPOS'] - (merged_current_row[-1]['HPOS'] + merged_current_row[-1]['WIDTH']) < 60:
                merged_current_row[-1] = {'text' : merged_current_row[-1]['text'] + current_row[j]['text'], 
                                                    'VPOS' : merged_current_row[-1]['VPOS'], 
                                                    'HPOS' : merged_current_row[-1]['HPOS'],                                                     
                                                    'WIDTH' : current_row[j]['HPOS'] + current_row[j]['WIDTH'] - merged_current_row[-1]['HPOS'] , 
                                                    'HEIGHT' : max([merged_current_row[-1]['HEIGHT'], current_row[j]['HEIGHT']]),
                                                    'WORDS' :  merged_current_row[-1]['WORDS'] + current_row[j]['WORDS']}
            else:
                merged_current_row.append(current_row[j])
        return merged_current_row if len(merged_current_row) > 0 else current_row

    def find_partial_table_rows(self, lines, table, boundary=None):
        """
        Identify sequences of strings in a line as part of the table row where the horizontal gap between
        consecutive strings is less than the threshold. Also, returns the boundary of the row if no boundary
        is provided; otherwise, checks if the row falls within the provided boundary.
        """
        new_tables = []
        row_boundaries = []
        
        if DEBUG:
            print('boundary =', boundary)        
            print('lines =', lines)        

        for idx, line in enumerate(lines):
            current_row = []
            first_string = self.split_col(line[0])
            if isinstance(first_string, list):
                current_row.extend(first_string)
            else:
                current_row.append(first_string)
            
            if DEBUG:
                print(0, 'find_partial_table_rows', current_row)
                if boundary is not None:
                    print('0.0.1', 'boundary =', boundary)
                    print('0.0.2', ' self.calculate_boundary(current_row) =', self.calculate_boundary(current_row))

            for i in range(len(line) - 1):
                if DEBUG:
                    print(0.1, 'find_partial_table_rows', line)
                current_string = line[i]
                next_string = line[i + 1]
                gap = next_string['HPOS'] - (current_string['HPOS'] + current_string['WIDTH'])

                next_string = self.split_col(next_string)

                if gap < self.string_spacing_threshold and gap > 50:
                    # The gap is small enough to consider this part of the current row            
                    if isinstance(next_string, list):
                        current_row.extend(next_string)    
                    else:
                        current_row.append(next_string)
                elif boundary is not None and len(boundary) > 0 and self.calculate_boundary(current_row)[0] >= boundary[0][0] and self.calculate_boundary(current_row)[2] < boundary[0][2]:
                    if isinstance(next_string, list):
                        current_row.extend(next_string)    
                    else:
                        current_row.append(next_string)
                else:
                    if DEBUG:
                        print(100, len(current_row), current_row)
                    # If the gap is too large, finalize the current row before starting a new one
                    if len(current_row) > 1:  # Only consider rows with more than one string
                        current_boundary = self.calculate_boundary(current_row)
                        if not boundary:
                            if DEBUG:
                                print(1, 'cr', current_row)
                            merged_current_row = self.merger_col(current_row, table)
                            new_tables.append(merged_current_row)
                            row_boundaries.append(current_boundary)
                        elif self.is_boundary_similar(current_boundary, boundary[idx]):
                            if DEBUG:
                                print(2, 'cr', current_row)
                            merged_current_row = self.merger_col(current_row, table)
                            new_tables.append(merged_current_row)
                            row_boundaries.append(current_boundary)
                        else:
                            if DEBUG:
                                print(3, 'cr', current_row)
                            start_idx = 0
                            end_idx = len(current_row)
                            for jdx in range(len(current_row)  - 1):
                                current_boundary = self.calculate_boundary(current_row[jdx:])   
                                if self.is_start_boundary_similar(current_boundary, boundary[idx]):
                                    start_idx = jdx
                            for jdx in range(len(current_row) - 1, 0, -1):
                                current_boundary = self.calculate_boundary(current_row[:jdx])   
                                if self.is_end_boundary_similar(current_boundary, boundary[idx]):
                                    end_idx = jdx
                            if start_idx < end_idx <= len(current_row):
                                current_row =current_row[start_idx : end_idx]
                                merged_current_row = self.merger_col(current_row, table)
                                new_tables.append(merged_current_row)
                                row_boundaries.append(current_boundary)

                                                     
                    if isinstance(next_string, list):
                        current_row = next_string
                    else:
                        current_row = [next_string]  # Start a new row

            # Check the last row
            if len(current_row) > 1:
                if DEBUG:
                    print(1, 'find_partial_table_rows', current_row)
                current_boundary = self.calculate_boundary(current_row)
                if not boundary:
                    if DEBUG:
                        print(2, 'find_partial_table_rows', current_row)
                    merged_current_row = self.merger_col(current_row, table)
                    new_tables.append(merged_current_row)
                    row_boundaries.append(current_boundary)
                elif self.is_boundary_similar(current_boundary, boundary[idx]):
                    if DEBUG:
                        print(3, 'find_partial_table_rows', current_row)
                    merged_current_row = self.merger_col(current_row, table)
                    new_tables.append(merged_current_row)
                    row_boundaries.append(current_boundary)
                else:
                    if DEBUG:
                        print(4, 'find_partial_table_rows', current_row)
                    start_idx = 0
                    end_idx = len(current_row)
                    for jdx in range(len(current_row)  - 1):
                        current_boundary = self.calculate_boundary(current_row[jdx:])   
                        if self.is_start_boundary_similar(current_boundary, boundary[idx]):
                            start_idx = jdx
                    for jdx in range(len(current_row) - 1, 0, -1):
                        current_boundary = self.calculate_boundary(current_row[:jdx])  
                        if DEBUG:
                            print(4.1, 'find_partial_table_rows', 'jdx =', jdx, 'self.is_end_boundary_similar(current_boundary, boundary[idx], is_last_col = jdx == (len(current_row) - 1)) =', self.is_end_boundary_similar(current_boundary, boundary[idx], is_last_col = jdx == (len(current_row) - 1))) 
                        if self.is_end_boundary_similar(current_boundary, boundary[idx], is_last_col = jdx == (len(current_row) - 1)):
                            end_idx = jdx
                    if DEBUG:
                        print(5, 'find_partial_table_rows', 'start_idx, end_idx', 'len(current_row)', start_idx, end_idx, len(current_row))
                    if start_idx < end_idx <= len(current_row):
                        current_row =current_row[start_idx : end_idx + 1]
                        if DEBUG:
                            print(5.0,'find_partial_table_rows', 'current_row =', current_row)
                        merged_current_row = self.merger_col(current_row, table)
                        new_tables.append(merged_current_row)
                        row_boundaries.append(current_boundary)
            elif len(current_row) == 1 and table is not None and len(table) > 0:
                if DEBUG:
                    print(5.1, 'find_partial_table_rows', 'current_row =', current_row)
                if self.are_very_close(current_row, table[-1]):
                    if DEBUG:
                        print(5.2, 'find_partial_table_rows', 'current_row =', current_row)
                    merged_current_row = [current_row[0]]
                    merged_current_row = self.merger_col(current_row, table)
                    new_tables.append(merged_current_row)
                    current_boundary = self.calculate_boundary(current_row) 
                    row_boundaries.append(current_boundary)

        if boundary:
            if DEBUG:
                print(6, 'find_partial_table_rows', 'new_tables =', new_tables)
            return new_tables, row_boundaries
        else:
            if DEBUG:
                print(7, 'find_partial_table_rows', 'new_tables =', new_tables)            
            # If no boundary was given, return all found rows and their calculated boundaries
            return new_tables, row_boundaries

    def is_valid_th(self, th):
        for heading in th:
            if len(heading['text']) > 30:
                if True:
                    if DEBUG:
                        print("heading['text'] =", heading['text'])
                return False
        for heading in th:
            if heading['text'].strip().lower() in ['yes', 'no']:
                if DEBUG:
                    print("heading['text'] =", heading['text'])
                return False
        return True

    def filter_invalid_tables(self, tables):
        """
        Filters out tables that are unlikely to be actual tables based on various heuristics.
        """
        valid_tables = []

        for table in tables:
            # Heuristic 1: Table must have more than one row and one column
            if len(table['items']) <= 1 or all(len(row) <= 1 for row in table['items']):
                continue

            # Heuristic 2: Check for minimal dimensions
            if table['WIDTH'] < 10 or table['HEIGHT'] < 10:
                continue

            # Heuristic 3: Filter based on content
            has_valid_content = False
            for row in table['items']:
                for item in row:
                    text = item['text'].strip()
                    if text and len(text) > 1:  # Assuming valid content has more than one character
                        has_valid_content = True
                        break
                if has_valid_content:
                    break

            if not has_valid_content:
                continue

            valid_tables.append(table)

        return valid_tables

    def extract_and_filter_tables(self, extracted_tables: List[Dict]) -> List[Dict]:
        """
        Combines extraction and filtering processes.
        """
        if DEBUG:
            print(1, 'extract_and_filter_tables')
            pprint(extracted_tables)
        extracted_tables = self.merge_close_rows(extracted_tables)
        
        if DEBUG:
            print(2, 'extract_and_filter_tables')
            pprint(extracted_tables)
        extracted_tables = self.filter_invalid_tables_1(extracted_tables)
        
        if DEBUG:
            print(3, 'extract_and_filter_tables')
            pprint(extracted_tables)
        extracted_tables = self.filter_invalid_tables_2(extracted_tables)
        
        if DEBUG:
            print(4, 'extract_and_filter_tables')
            pprint(extracted_tables)
        extracted_tables = self.filter_invalid_tables_3(extracted_tables)
                
        
        if DEBUG:
            print(5, 'extract_and_filter_tables')
            pprint(extracted_tables)
        return extracted_tables
    
    def merge_close_rows(self, tables):
        """
        Merges rows in the tables that are closer than the typical row spacing, indicating they are part of the same cell.
        """
        merged_tables = []
        for table in tables:
            av_spacing = self.calculate_average_row_spacing(table['items'])
            new_items = []
            new_values = []
            skip_next = False
            for i in range(len(table['items']) - 1):
                if skip_next:
                    skip_next = False
                    continue
                
                current_row = table['items'][i]
                current_value = table['values'][i]
                next_row = table['items'][i + 1]
                # Calculate vertical distance between current row's bottom and next row's top
                distance = next_row[0]['VPOS'] - (current_row[0]['VPOS'] + current_row[0]['HEIGHT'])          
                
                if DEBUG:
                    print('av_spacing =', av_spacing)      
                    print('distance =', distance)
                    print('current_row =', current_row)
                    print('next_row =', next_row)
                
                # Heuristic to determine if rows are part of the same cell
                if distance < av_spacing / 1.3:
                    if DEBUG:
                        print('distance, av_spacing =', distance, av_spacing / 2)
                    # Merge current and next row
                    merged_row, merged_value = self.merge_two_rows(current_row, next_row)
                    if DEBUG:
                        pprint(merged_row)
                    new_items.append(merged_row)
                    new_values.append(merged_value)
                    skip_next = True
                else:
                    new_items.append(current_row)
                    new_values.append(current_value)
            
            # Handle the last row if not already merged
            if not skip_next:
                new_items.append(table['items'][-1])
                new_values.append(table['values'][-1])
            
            # Update the table with merged rows
            table['items'] = new_items
            table['values'] = new_values
            merged_tables.append(table)
        return merged_tables

    def calculate_average_row_spacing(self, items):
        """
        Calculates the average vertical spacing between rows in a table.
        """
        spacings = []
        for i in range(len(items) - 1):
            spacing = min(items[i + 1][j]['VPOS'] for j in range(len(items[i + 1]))) - max((items[i][j]['VPOS'] + items[i][0]['HEIGHT']) for j in range(len(items[i])))
            spacings.append(spacing)
        return sum(spacings) / len(spacings) if spacings else 0

    def merge_two_rows(self, row1, row2):
        """
        Merges two rows into one, combining the text of vertically aligned cells.
        Assumes rows are lists of cell dictionaries.
        """
        # This is a simplified merge function; actual implementation may need to account for cell alignments
        merged_row = []
        merged_value = []
        for cell1, cell2 in zip(row1, row2):
            merged_cell = {
                'HPOS': min(cell1['HPOS'], cell2['HPOS']),
                'VPOS': min(cell1['VPOS'], cell2['VPOS']),
                'WIDTH': max(cell1['WIDTH'], cell2['WIDTH']),
                'HEIGHT': cell1['HEIGHT'] + cell2['HEIGHT'],
                'text': cell1['text'] + " " + cell2['text'],
                'WORDS': cell1['WORDS'] + cell2['WORDS'],
                'SCORE': min(cell1.get('SCORE', 100), cell2.get('SCORE', 100))  # Optional: Handle SCORE
            }
            merged_row.append(merged_cell)
            merged_value.append(merged_cell['text'])
        return merged_row, merged_value

    def has_valid_content(self, items: List[List[Dict]]) -> bool:
        """
        Checks if a significant portion of the content within a table is valid.
        """
        ## if last col is empty remove it
        last_row = items[-1]
        if all(len(lr['text'].strip()) == 0 for lr in last_row):
            items.pop(-1)
            
        valid_items_count = 0
        total_items_count = 0
        for row in items:
            for cell in row:
                total_items_count += 1
                if self.is_content_valid(cell['text']):
                    valid_items_count += 1
        if DEBUG:
            print('valid_items_count =', valid_items_count)
            print('total_items_count =', total_items_count)
            print('0.60 * total_items_count =', 0.60 * total_items_count)
        return valid_items_count >= 0.60 * total_items_count  # 60% valid content threshold

    def is_content_valid(self, text: str) -> bool:
        """
        Checks if a text is considered valid content, based on length and regex pattern.
        """
        return bool(text.strip()) and not self.nonsensical_content_pattern.match(text.strip())

    def filter_invalid_tables_1(self, tables: List[Dict]) -> List[Dict]:
        """
        Filters out tables that fail the basic content and regex checks.
        """
        return [table for table in tables if self.has_valid_content(table['items'])]

    def filter_invalid_tables_2(self, tables: List[Dict]) -> List[Dict]:
        """
        Analyzes the HPOS and VPOS information to filter out irregular tables.
        """
        return [table for table in tables if not self.calculate_row_length_discrepancy(table)]

    def calculate_row_length_discrepancy(self, table: Dict) -> bool:
        """
        Determines if there's a significant discrepancy in the length of rows in the table.
        """
        row_lengths = [len(row) for row in table['items']]
        if not row_lengths:
            return True  # Treat empty tables as having a discrepancy
        
        max_length = max(row_lengths)
        min_length = min(row_lengths)
        discrepancy_threshold = max_length * 0.5
        return (max_length - min_length) > discrepancy_threshold

    def filter_invalid_tables_3(self, tables: List[Dict]) -> List[Dict]:
        """
        Filters out tables based on additional spatial consistency checks and improved content validation.
        This stage might include more advanced checks, such as verifying the alignment of cells within rows or columns
        and ensuring the table's overall structure matches expected patterns.
        """
        # Placeholder for additional filtering logic based on spatial consistency and other advanced criteria
        valid_tables = [table for table in tables if self.perform_advanced_spatial_checks(table)]
        return valid_tables

    def perform_advanced_spatial_checks(self, table: Dict) -> bool:
        """
        Performs advanced spatial checks on a table to ensure its structure and alignment
        are consistent with valid table patterns. This includes checking for consistent
        row heights, column widths, and alignment of cells.
        """
        if not table['items']:
            return False  # Discard empty tables

        # Check for consistent row heights
        row_heights = [self.calculate_average_row_height(row) for row in table['items']]
        # if not self.are_values_consistent(row_heights):
        #    if DEBUG:
        #        print('values are not consistent', row_heights)
        #    return False

        # Check for consistent column alignments
        if not self.are_columns_aligned(table['items']):
            if DEBUG:
                print('columns are not aligned')
            return False

        table_area = table['WIDTH'] * table['HEIGHT']

        # Check if the table's area meets the minimum threshold
        return table_area >= self.min_area_threshold

    def calculate_average_row_height(self, row: List[Dict]) -> float:
        """
        Calculates the average height of a row in the table.
        """
        if not row:
            return 0
        total_height = sum(cell['HEIGHT'] for cell in row)
        return total_height / len(row)

    def are_values_consistent(self, values: List[float], threshold: float = 0.2) -> bool:
        """
        Checks if a list of values are consistent within a given threshold.
        Consistency is defined as the standard deviation being below a certain threshold
        relative to the mean.
        """
        if not values:
            return False
        mean_value = sum(values) / len(values)
        variance = sum((x - mean_value) ** 2 for x in values) / len(values)
        standard_deviation = variance ** 0.5
        return (standard_deviation / mean_value) <= threshold

    def are_columns_aligned(self, items: List[List[Dict]], alignment_threshold: int = 10) -> bool:
        """
        Checks if columns in the table are aligned by comparing the HPOS (horizontal position)
        of cells in the same column across different rows.
        """
        # Assuming the table has a regular structure, use the first row to determine the number of columns
        num_columns = len(items[0])
        for col_index in range(num_columns):
            column_positions = [row[col_index]['HPOS'] for row in items if len(row) > col_index]
            if not self.are_values_consistent(column_positions, threshold=alignment_threshold):
                return False
        return True

    def check_horizontal_overlap(self, a, b, threshold=0.6):
        # Calculate the horizontal overlap
        start_max = max(a['HPOS'], b['HPOS'])
        end_min = min(a['HPOS'] + a['WIDTH'], b['HPOS'] + b['WIDTH'])
        overlap_width = max(0, end_min - start_max)
        
        # Calculate the smaller width of the two elements
        min_width = min(a['WIDTH'], b['WIDTH'])
        
        # Check if overlap is greater than the threshold of the smaller width
        overlap_ratio = overlap_width / min_width if min_width else 0
        
         # Check if A is subset of B or vice versa horizontally
        a_inside_b_horizontally = (a['HPOS'] >= b['HPOS']) and ((a['HPOS'] + a['WIDTH']) <= (b['HPOS'] + b['WIDTH']))
        b_inside_a_horizontally = (b['HPOS'] >= a['HPOS']) and ((b['HPOS'] + b['WIDTH']) <= (a['HPOS'] + a['WIDTH']))
    
        return overlap_ratio > threshold or a_inside_b_horizontally or b_inside_a_horizontally

    def refine_table(self, table):        
        cols = [len(tr) for tr in table]
        if len(cols) < 2:
            if DEBUG:
                print('cols =', cols)
            return []
        elif len(cols) == 2 and cols[0] == cols[1]:
            if abs((table[0][0]['HPOS'] + table[0][0]['WIDTH']) - (table[1][0]['HPOS'] + table[1][0]['WIDTH'])) > 150:
                if DEBUG:
                    print('###', 0, "refine_table")
                    print('###', 0, "refine_table", "cols =", cols)
                return []
        
        if any(len(cv['text'].strip()) == 0 for cv in table[0]):
            if DEBUG:
                print('###', 0.1, "refine_table")
            return []
        
        gt1c = 0
        for c in cols:
            if c > 1:
                gt1c += 1
        if gt1c == 1:
            if DEBUG:
                print('###', 0.2, "refine_table")
            return []

        if any(c != cols[0] for c in cols):            
            for ti in range(1, len(table)):
                if DEBUG:
                    print('########', 'ti =', ti)
                row_cells = table[ti]
                row_cells_count = len(row_cells)

                prev_row_cells = table[ti -1]
                prev_row_cells_count = len(prev_row_cells)
                
                if DEBUG:
                    print('prev_row_cells_count =', prev_row_cells_count)
                    print('row_cells_count =', row_cells_count)
                    print('row_cells =', row_cells)
                    print('prev_row_cells =', prev_row_cells)

                if row_cells_count == prev_row_cells_count:
                    continue
                elif row_cells_count < prev_row_cells_count:                    
                    rci = 0                    
                    new_row_cells = []
                    for k, prc in enumerate(prev_row_cells):
                        if rci >= len(row_cells):
                            if len(new_row_cells) == len(prev_row_cells):
                                break
                            else:
                                new_row_cells.append({'text' : '', 
                                                    'VPOS' : min(rc['VPOS'] for rc in row_cells), 
                                                    'HPOS' : prc['HPOS'], 
                                                    'WIDTH' : prc['WIDTH'], 
                                                    'HEIGHT' : max(rc['HEIGHT'] for rc in row_cells),
                                                    'WORDS' : []})
                                continue
                        if k!= 0:
                            prc_prev_col = prev_row_cells[k-1]
                        if DEBUG:
                            print('rci =', rci)
                            print('k =', k)    
                            if k != 0:
                                print('prc_prev_col =', prc_prev_col)                    
                                print("prc['HPOS'] =", prc['HPOS'])
                                print("row_cells[rci]['HPOS'] =", row_cells[rci]['HPOS'])
                                print("prc_prev_col['HPOS'] =", prc_prev_col['HPOS'])
                                print("prc_prev_col['WIDTH'] =", prc_prev_col['WIDTH'])
                                print("(prc['HPOS'] + prc_prev_col['HPOS'] + prc_prev_col['WIDTH']) / 2) =", (prc['HPOS'] + prc_prev_col['HPOS'] + prc_prev_col['WIDTH']) / 2)
                                print("(row_cells[rci]['HPOS'] - ((prc['HPOS'] + prc_prev_col['HPOS'] + prc_prev_col['WIDTH']) / 2)) =", (row_cells[rci]['HPOS'] - ((prc['HPOS'] + prc_prev_col['HPOS'] + prc_prev_col['WIDTH']) / 2)))
                        if rci >= len(row_cells):
                            if DEBUG:
                                # print(5, 'refine_table', 'row_cells[rci] =', row_cells[rci])
                                print(5, 'refine_table', 'prc =', prc)
                            new_row_cells.append({'text' : '', 
                                                    'VPOS' : min(rc['VPOS'] for rc in row_cells), 
                                                    'HPOS' : prc['HPOS'], 
                                                    'WIDTH' : prc['WIDTH'], 
                                                    'HEIGHT' : max(rc['HEIGHT'] for rc in row_cells),
                                                    'WORDS' : []})
                        elif k == 0 and abs(prc['HPOS'] - row_cells[rci]['HPOS']) < 25:
                            new_row_cells.append(row_cells[rci])
                            rci += 1
                        elif k != 0 and \
                            (row_cells[rci]['HPOS'] - ((prc['HPOS'] + prc_prev_col['HPOS'] + prc_prev_col['WIDTH']) / 2)) >= 0 and \
                            (row_cells[rci]['HPOS'] - ((prc['HPOS'] + prc_prev_col['HPOS'] + prc_prev_col['WIDTH']) / 2)) <= 85:
                            if DEBUG:
                                print(4, 'refine_table', 'row_cells[rci] =', row_cells[rci])
                                print(4, 'refine_table', 'prc =', prc)
                            new_row_cells.append(row_cells[rci])
                            rci += 1
                        elif self.check_horizontal_overlap(row_cells[rci], prc):
                            if DEBUG:
                                print(3, 'refine_table', 'row_cells[rci] =', row_cells[rci])
                                print(3, 'refine_table', 'prc =', prc)
                            new_row_cells.append(row_cells[rci])
                            rci += 1
                        else:
                            if DEBUG:
                                print(6, 'refine_table', 'row_cells[rci] =', row_cells[rci])
                                print(6, 'refine_table', 'prc =', prc)
                            new_row_cells.append({'text' : '', 
                                                    'VPOS' : min(rc['VPOS'] for rc in row_cells), 
                                                    'HPOS' : prc['HPOS'], 
                                                    'WIDTH' : prc['WIDTH'], 
                                                    'HEIGHT' : max(rc['HEIGHT'] for rc in row_cells),
                                                    'WORDS' : []})
                    table[ti] = new_row_cells
                    if DEBUG:
                        print('table =')
                        pprint(table)
                else:
                    prci = 0
                    new_prev_row_cells = []
                    for k, rc in enumerate(row_cells):
                        if prci >= len(prev_row_cells):
                            break
                        if k!= 0:
                            rc_prev_col = row_cells[k-1]
                        if prci  >= len(prev_row_cells):
                            new_prev_row_cells.append({'text' : '', 
                                                    'VPOS' : min(prc['VPOS'] for prc in prev_row_cells), 
                                                    'HPOS' : rc['HPOS'], 
                                                    'WIDTH' : rc['WIDTH'], 
                                                    'HEIGHT' : max(prc['HEIGHT'] for prc in prev_row_cells),
                                                    'WORDS' : []})
                        elif abs(rc['HPOS'] - prev_row_cells[prci]['HPOS']) < 25:
                            if DEBUG:
                                print(1, 'refine_table', 'prev_row_cells[prci] =', prev_row_cells[prci])
                                print(1, 'refine_table', 'rc =', rc)
                            new_prev_row_cells.append(prev_row_cells[prci])
                            prci += 1
                        elif k != 0 and \
                            (prev_row_cells[prci]['HPOS'] - ((rc['HPOS'] + rc_prev_col['HPOS'] + rc_prev_col['WIDTH']) / 2)) >= 0 and \
                            (prev_row_cells[prci]['HPOS'] - ((rc['HPOS'] + rc_prev_col['HPOS'] + rc_prev_col['WIDTH']) / 2)) <= 85:
                            if DEBUG:
                                print(1.1, 'refine_table', 'prev_row_cells[prci] =', prev_row_cells[prci])
                                print(1.1, 'refine_table', 'prci =', prci)
                            new_prev_row_cells.append(prev_row_cells[prci])
                            prci += 1
                        elif k == len(row_cells) - 1 and \
                                (self.check_horizontal_overlap(prev_row_cells[prci], rc)):
                            if DEBUG:
                                print(2, 'refine_table', 'prev_row_cells[prci] =', prev_row_cells[prci])
                                print(2, 'refine_table', 'rc =', rc)
                            new_prev_row_cells.append(prev_row_cells[prci])
                            prci += 1
                        else:
                            new_prev_row_cells.append({'text' : '', 
                                                    'VPOS' : min(prc['VPOS'] for prc in prev_row_cells), 
                                                    'HPOS' : rc['HPOS'], 
                                                    'WIDTH' : rc['WIDTH'], 
                                                    'HEIGHT' : max(prc['HEIGHT'] for prc in prev_row_cells),
                                                    'WORDS' : []})
                    table[ti - 1] = new_prev_row_cells
        
        if len(table) >= 3:
            last_row = table[-1]
            if all(len(lr['text'].strip()) == 0 for lr in last_row):
                table.pop(-1)
        return table

    def merger_with_prev_row(self, line, prev_line):
        line_idx = 0

        for jdx in range(len(prev_line)  - 1):
            if line_idx >= len(line):
                continue
            if abs(line[line_idx]['HPOS'] - prev_line[jdx]['HPOS']) < 20:
                prev_line[jdx]['HEIGHT'] += (line[line_idx]['HEIGHT'] + abs(line[line_idx]['VPOS'] - (prev_line[jdx]['VPOS'] + prev_line[jdx]['HEIGHT'] )))
                prev_line[jdx]['WIDTH'] = max(prev_line[jdx]['WIDTH'], line[line_idx]['WIDTH'])
                prev_line[jdx]['text'] += f' {line[line_idx]["text"]}'
                line_idx += 1

        return [prev_line]

    def calculate_boundary(self, row):
        """
        Calculate the boundary (HPOS, VPOS, endHPOS, endVPOS) for a given row.
        """
        HPOS = min(item['HPOS'] for item in row)
        VPOS = min(item['VPOS'] for item in row)
        endHPOS = max(item['HPOS'] + item['WIDTH'] for item in row)
        endVPOS = max(item['VPOS'] + item['HEIGHT'] for item in row)
        return HPOS, VPOS, endHPOS, endVPOS

    def is_boundary_similar(self, boundary1, boundary2, tolerance=90):
        """
        Check if two boundaries are similar, within a certain tolerance.
        """    
        hpos_simlar = abs(boundary2[0] - boundary1[0]) <= tolerance
        end_hpos_simlar = abs(boundary2[2] - boundary1[2]) <= tolerance
        return hpos_simlar and end_hpos_simlar

    def is_start_boundary_similar(self, boundary1, boundary2, tolerance=30):
        """
        Check if two boundaries are similar, within a certain tolerance.
        """
        hpos_simlar = abs(boundary1[0] - boundary2[0]) <= tolerance
        return hpos_simlar

    def is_end_boundary_similar(self, boundary1, boundary2, is_last_col = False, tolerance=100):
        """
        Check if two boundaries are similar, within a certain tolerance.
        """
        end_hpos_simlar = abs(boundary1[2] - boundary2[2]) <= tolerance // 3 if not is_last_col else tolerance 
        return end_hpos_simlar

    def are_very_close(self, current_line, prev_line):
        if DEBUG:
            print('#########', prev_line)
            print('#########', current_line)
            print(max(pl['VPOS'] + pl['HEIGHT'] for pl in prev_line))
            print(min(cl['VPOS'] for cl in current_line))
            print('#########', (max(pl['VPOS'] + pl['HEIGHT'] for pl in prev_line) - min(cl['VPOS'] for cl in current_line)))
        if (max(pl['VPOS'] + pl['HEIGHT'] for pl in prev_line) - min(cl['VPOS'] for cl in current_line)) < 30:
            return True
        return False

    def construct_tables_with_advanced_logic(self, 
                                                lines, 
                                                col_spacing_threshold=150):
        """
        Construct tables with advanced logic including partial row handling.
        """
        function_name = 'construct_tables_with_advanced_logic'
        tables = []
        current_table = []
        boundary = None

        for i, line in enumerate(lines):
            if DEBUG:
                print(function_name, 1, line)
            if len(line) <= 1 and boundary is None:
                continue

            prev_line = None
            
            if i > 0:
                if DEBUG:
                    print(function_name, 2, line)
                prev_line = lines[i - 1]
                if self.is_potential_table_end(line, prev_line, current_table):
                    if DEBUG:
                        print(function_name, 3, line)
                    if len(current_table) <= 1:    
                        current_table = []
                        boundary = None
                    else:      
                        current_table = self.refine_table(current_table)           
                        tables.append(current_table)
                        current_table = []
                        boundary = None
                                       
                if self.is_line_a_potential_row(line, table = current_table):    
                    if DEBUG:
                        print(function_name, 4, line)                
                    partial_rows, col_boundary = self.find_partial_table_rows([line], current_table, boundary)
                    if DEBUG:
                        print(function_name, 5, 'partial_rows = ', partial_rows)
                    if len(current_table) == 0 and len(partial_rows) >= 1 and not self.is_valid_th(partial_rows[0]):
                        if DEBUG:
                            print(function_name, 6, self.is_valid_th(partial_rows[0]))
                        continue                        
                    if self.merge_closeby_rows and self.are_very_close(line, prev_line) and len(current_table) > 0:
                         if DEBUG:
                             print(6.5, self.are_very_close(line, prev_line))
                         partial_rows = self.merger_with_prev_row(partial_rows[0], current_table[-1])
                         current_table[-1] = partial_rows[0]
                    else:
                        if len(current_table) == 0:
                            boundary = col_boundary
                        current_table.extend(partial_rows)
                        if DEBUG:
                            print(function_name, 7, 'current_table = ',)
                            pprint(current_table)
                else:
                    if DEBUG:
                        print(function_name, 8, 'current_table = ')
                        pprint(current_table)
                    current_table = self.refine_table(current_table)           
                    if DEBUG:
                        print(function_name, 9, 'current_table = ')
                        pprint(current_table)
                    if len(current_table) > 1:  # Finalize the current table
                        tables.append(current_table)
                    current_table = []
                    boundary = None
            else:
                # For the first line, assume it's a potential start of a table
                partial_rows, boundary = self.find_partial_table_rows([line], current_table, boundary)
                if len(current_table) == 0 and len(partial_rows) >= 1 and not self.is_valid_th(partial_rows[0]):
                        continue
                if prev_line is None:
                    continue
                if self.merge_closeby_rows and self.are_very_close(line, prev_line) and len(current_table) > 0:
                    partial_rows = self.merger_with_prev_row(partial_rows[0], current_table[-1])
                    current_table[-1] = partial_rows[0]
                else:
                    current_table.extend(partial_rows)

        if len(current_table) > 1:  # Add the last table
            if DEBUG:
                print(function_name, 10, 'current_table = ')
                pprint(current_table)
            current_table = self.refine_table(current_table)   
            if DEBUG:
                print(function_name, 11, 'current_table = ')
                pprint(current_table)
            tables.append(current_table)       

        return tables

    def extract_tables_with_refined_row_columns(self, all_strings):
        """
        Extract tables with a refined strategy for identifying rows and columns.
        """
        # Group elements into lines (potential rows) based on VPOS tolerance
        lines = self.group_elements_into_rows(all_strings)
        
        # Construct tables from these lines, ensuring rows are properly formed with multiple columns
        tables = self.construct_tables_with_advanced_logic(lines)

        # Format the output to include tables with rows having multiple columns
        structured_tables = []
        for table in tables:
            if not table:
                continue

            formatted_table = {
                'HPOS' : table[0][0]['HPOS'],
                'VPOS' : table[0][0]['VPOS'],
                'WIDTH' : max([sum([item.get('WIDTH', 0) for item in row]) for row in table]),
                'HEIGHT' : (table[-1][-1]['VPOS'] + table[-1][-1]['WIDTH']) - table[0][0]['VPOS'],
                'values': [[item['text'] for item in row] for row in table],
                'items' : [[item for item in row] for row in table]
            }
            structured_tables.append(formatted_table)

        structured_tables = self.extract_and_filter_tables(structured_tables)

        return structured_tables

    def extract_tables_with_row_patterns(self, ocr_data):
        # Flatten and sort the OCR data by VPOS, then HPOS
        all_strings = []
        for cblock in ocr_data:
            if 'TEXT_BLOCK' not in cblock.keys():
                print(1)
                print(cblock)
            for text_block in cblock["TEXT_BLOCK"]:
                for text_line in text_block["TEXT_LINE"]:
                    for string in text_line["STRING"]:
                        all_strings.append(string)

        extracted_tables = self.extract_tables_with_refined_row_columns(all_strings)

        return extracted_tables

if __name__ == "__main__":
    import json
    stat = time.time()
    
    DEBUG = True
    
    pdf_name = 'STEWARTECM_PROD000057039915'
    ocr_path = f'/Users/<USER>/Repos/SOFTWARE-SERVICE-%20document_extraction/ocr_output/ECM_PROD000012378277/combined.json'
    

    print(ocr_path)
    with  open(ocr_path, 'r') as f:
        all_ocr_data = json.loads(f.read())
    ocr_data = all_ocr_data['1']['COMPOSED_BLOCKS']
    
    extractor = TableExtractor(vpos_tolerance = 7, 
                                table_gap_threshold = 77, 
                                string_spacing_threshold = 350,
                                min_area_threshold = 20000,
                                merge_closeby_rows = False)
    extracted_tables = extractor.extract_tables_with_row_patterns(ocr_data)
    pprint(extracted_tables)

    print(f'total time = {time.time() - stat}')