from fuzzywuzzy import fuzz
from fuzzywuzzy import process


class AdaptiveFuzzyMatcher:
    """
    A class to perform fuzzy matching on OCR-extracted text considering potential OCR errors.
    The class uses adaptive thresholding based on the length of the text to improve matching accuracy.
    """

    def __init__(self, min_threshold=70, max_threshold=90, length_threshold=5):
        """
        Initialize the adaptive fuzzy matcher.

        Parameters:
        min_threshold (int): Minimum threshold for long strings.
        max_threshold (int): Maximum threshold for short strings.
        length_threshold (int): The string length at which the threshold starts to decrease.
        """
        self.min_threshold = min_threshold
        self.max_threshold = max_threshold
        self.length_threshold = length_threshold

    def calculate_adaptive_threshold(self, text_length):
        """
        Calculate the adaptive threshold based on the length of the string.

        Parameters:
        text_length (int): Length of the string to match.

        Returns:
        int: The adaptive threshold for fuzzy matching.
        """
        if text_length <= self.length_threshold:
            # For very short strings, use the maximum threshold
            return self.max_threshold
        else:
            # For longer strings, reduce the threshold linearly
            threshold_range = self.max_threshold - self.min_threshold
            length_factor = min(text_length - self.length_threshold, self.length_threshold) / self.length_threshold
            adaptive_threshold = self.max_threshold - (threshold_range * length_factor)
            return int(adaptive_threshold)

    def fuzzy_match_ocr(self, ocr_text, keys):
        """
        Perform fuzzy matching on the OCR-extracted text using adaptive thresholding.

        Parameters:
        ocr_text (str): The text extracted from OCR that needs to be matched.
        keys (list of str): The list of correct keys to match against.

        Returns:
        tuple:
            bool: Whether a match was found that meets the adaptive threshold.
            str: The best-matched key or None if no match is found above the threshold.
        """
        # Calculate the adaptive threshold based on the length of the OCR text
        adaptive_threshold = self.calculate_adaptive_threshold(len(ocr_text))

        # Perform fuzzy matching using fuzzywuzzy's process.extractOne
        best_match, score = process.extractOne(ocr_text, keys, scorer=fuzz.ratio)

        # Determine if the match meets the threshold
        if score >= adaptive_threshold:
            return True, best_match
        else:
            return False, None


# Example usage
if __name__ == "__main__":
    matcher = AdaptiveFuzzyMatcher()

    ocr_texts = ["TX", "Polic Holder", "Insuranc Polic", "Dae of Birth"]
    keys = ["TX", "Policy Holder", "Insurance Policy", "Date of Birth"]

    for ocr_text in ocr_texts:
        is_matched, matched_key = matcher.fuzzy_match_ocr(ocr_text, keys)
        print(f"OCR Text: '{ocr_text}' -> Is Matched: {is_matched}, Matched Key: '{matched_key}'")