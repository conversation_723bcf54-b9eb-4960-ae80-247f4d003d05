class ExtractedDocument:
    def __init__(self, reference_no, field_data, table_data):
        """
        Initializes a new instance of the ExtractedDocument class, which represents all extracted data from a single document.

        :param reference_no: A unique identifier or reference number for the document.
        :param field_data: A list of BaseFieldData instances, each representing key-value pair extraction results.
        :param table_data: A list of BaseTableData instances, each representing extracted table data.
        """
        self.reference_no = reference_no
        self.field_data = field_data  # Expecting a list of BaseFieldData instances
        self.table_data = table_data  # Expecting a list of BaseTableData instances

    def to_dict(self):
        """
        Converts the extracted document instance into a dictionary format that can be easily serialized into JSON.

        :return: A dictionary representation of the extracted document.
        """
        return {
            "ReferenceNo": self.reference_no,
            "FieldData": [fd.to_dict() for fd in self.field_data],
            "TableData": [td.to_dict() for td in self.table_data]
        }

    def __str__(self):
        """
        Generates a pretty-printed string representation of the extracted document for debugging and visualization.

        :return: A string representation of the extracted document.
        """
        return ""
