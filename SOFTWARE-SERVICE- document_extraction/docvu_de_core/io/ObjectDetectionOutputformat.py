from docvu_de_core.io.OutputFormat import OutputFormat

class ObjectDetectionOutputFormat(OutputFormat):
    def __init__(self, data=None, success=True, message=None, bounding_boxes=None, class_labels=None, confidences=None, image_path=None, crops=None, **kwargs):
        super().__init__(data=data, success=success, message=message, **kwargs)
        self.bounding_boxes = bounding_boxes if bounding_boxes is not None else []
        self.class_labels = class_labels if class_labels is not None else []
        self.confidences = confidences if confidences is not None else []
        self.image_path = image_path
        self.crops = crops if crops is not None else []

    def to_dict(self):
        output_dict = super().to_dict()
        output_dict.update({
            'bounding_boxes': self.bounding_boxes,
            'class_labels': self.class_labels,
            'confidences': self.confidences,
            'image_path': self.image_path,
            'crops': self.crops
        })
        return output_dict