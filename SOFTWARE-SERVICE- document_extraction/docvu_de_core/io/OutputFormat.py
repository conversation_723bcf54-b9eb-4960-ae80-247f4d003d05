class OutputFormat:
    def __init__(self, data=None, success=True, message=None, **kwargs):
        """
        Initializes a new instance of the OutputFormat class with optional extra keyword arguments.

        :param data: The data to be encapsulated by the OutputFormat. Could be of any type.
        :param success: <PERSON><PERSON>an indicating whether the operation was successful.
        :param message: Optional message, typically used for providing details on errors or additional context.
        :param kwargs: Additional keyword arguments that are set as attributes of the instance.
        """
        self.success = success
        self.message = message
        self.data = data

        # Dynamically set each keyword argument as an attribute of the instance
        for key, value in kwargs.items():
            setattr(self, key, value)

    def to_dict(self):
        """
        Converts the OutputFormat instance into a dictionary format, including any dynamically set attributes.

        :return: A dictionary representation of the OutputFormat.
        """
        output_dict = {
            'success': self.success,
            'message': self.message,
            'data': self._data_to_dict(self.data)
        }
        # Add all dynamically set attributes to the dictionary
        for attr in vars(self):
            if attr not in ['success', 'message', 'data']:
                output_dict[attr] = getattr(self, attr)
        return output_dict

    def _data_to_dict(self, data):
        """
        A helper method to handle various types of data structures, converting them to dictionaries if necessary.

        :param data: The data to convert.
        :return: The data in a dictionary format if it is not a simple type.
        """
        if isinstance(data, (dict, list, tuple, set)):
            if isinstance(data, (list, tuple, set)):
                return [self._data_to_dict(item) for item in data]
            elif isinstance(data, dict):
                return {key: self._data_to_dict(value) for key, value in data.items()}
            return data
        elif hasattr(data, 'to_dict'):
            return data.to_dict()
        return data

    def __str__(self):
        """
        Returns a string representation of the OutputFormat instance, making it easier to debug.

        :return: A string representation of the OutputFormat.
        """
        import json
        return json.dumps(self.to_dict(), indent=4, sort_keys=True, default=str)


# Example usage:
if __name__ == '__main__':
    data_example = {'key': 'value', 'numbers': [1, 2, 3]}
    output = OutputFormat(data=data_example, success=True, message="Data processed successfully.",
                          additional_attr="Extra details")
    print(output)
