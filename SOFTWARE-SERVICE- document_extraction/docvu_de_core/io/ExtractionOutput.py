class ExtractionOutput:
    def __init__(self, file_id, status, job_id, documents, inference_time, exception=None):
        """
        Initializes a new instance of the ExtractionOutput class.
        
        :param file_id: A unique identifier for the file.
        :param status: The status of the extraction process.
        :param job_id: A unique identifier for the job.
        :param documents: A list of ExtractedDocument instances representing the extracted documents.
        :param inference_time: The time taken for the extraction process.
        :param exception: Any exceptions encountered during the extraction process.
        """
        self.file_id = file_id
        self.status = status
        self.job_id = job_id
        self.documents = documents
        self.inference_time = inference_time
        self.exception = exception

    def to_dict(self):
        """
        Converts the extraction output into a dictionary format for serialization.
        
        :return: A dictionary representation of the extraction output.
        """
        return {
            "FileId": self.file_id,
            "Status": self.status,
            "JobId": self.job_id,
            "ExtractedDocument": [document.to_dict() for document in self.documents],
            "InferenceTime": self.inference_time,
            "Exception": self.exception
        }

    def __str__(self):
        # Pretty print the extraction output for debugging purposes
        from pprint import pprint
        pprint(self.to_dict())
        return ""
