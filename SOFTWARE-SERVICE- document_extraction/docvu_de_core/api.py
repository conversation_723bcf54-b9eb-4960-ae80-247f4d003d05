import copy

from docvu_de_core.policy_classifier import *
from docvu_de_core.src import *
from docvu_de_core.utils import ResponseFormatter
from docvu_de_core import de_config_path
from docvu_de_core import client_path
from docvu_de_core import configure_logging
from docvu_de_core.modules import XMLToJSONConverter
from docvu_de_core.extraction_item import BaseTableData, BaseFieldData
from docvu_de_core.config import *

import os
import json


class API:
    def __init__(self, curr_path, ocr_dir="./ocr_output", mapper_config_path=None, **kwargs):
        logger = configure_logging(curr_path)
        self.classify_policy = ClassifyOld()
        self.parser = ParserClass(logger, **kwargs)
        self.formatter = ResponseFormatter()
        self.form_type_dict = {
            "9002231": os.path.join(de_config_path,'LenderPolicy_LongForm_de.json'),
            "9002233": os.path.join(de_config_path,'LenderPolicy_ShortForm_de.json'),
            "9002236": os.path.join(de_config_path,'OwnerPolicy_de.json'),
            "sub_keys": os.path.join(de_config_path,'sub_keys.json'),
            "identifier":os.path.join(de_config_path,'stewart_policy_de.json'),
            "9000001":os.path.join(de_config_path,'9000001-1003_classify.json'),
            "9000001_new":os.path.join(de_config_path,'9000001-1003_Loan_Application_new_de.json'),
            "9000181":os.path.join(de_config_path,'9000181-closing_disclosure_de.json'),
            "9000003":os.path.join(de_config_path,'9000003-underwriting_summary_de.json'),
            "9000780":os.path.join(de_config_path,'9000780-pmi_canc_disclosure_de.json'),
            "9000069":os.path.join(de_config_path,'9000069-appraisal_de.json'),
            "9000377":os.path.join(de_config_path,'9000377-flood_cert_de.json'),
            "9000671":os.path.join(de_config_path,'9000671-note_de.json'),
            "9000253":os.path.join(de_config_path,'9000253-deed_of_trust_de.json'),
            "9000089":os.path.join(de_config_path,'9000089_aus_de.json'),
            "9000593":os.path.join(de_config_path,'9000593-LP_de.json'),
            "9000468":os.path.join(de_config_path,'9000468-Home_Equity.json'),
            "9002218":os.path.join(de_config_path,'9002218-Tax_Escrow_Option_Election_Valon.json'),
            "9000001_old":os.path.join(de_config_path,'9000001_old-1003_de.json'),
            "9000001_npf":os.path.join(de_config_path,'9000001-1003_npf_de.json'),
            "9000017_452":os.path.join(de_config_path,'9000017-Ability_To_Repay_PostClose_classify.json'),
            "9000019_452":os.path.join(de_config_path,'9000019-ACH_Authorization_Form_classify.json'),
            "9001740_452":os.path.join(de_config_path,'9001740-Advance_Fee_Agreement_PostClose.json'),
            "9000033_452":os.path.join(de_config_path,'9000033-Address_Certification_PostClose.json'),
            "9000053_452":os.path.join(de_config_path,'9000053-Appraisal_Acknowledgement_PostClose.json'),
            "9000051_452":os.path.join(de_config_path,'9000051-Application_Disclosure_PostClose.json'),
            "9000304_452":os.path.join(de_config_path,'9000304-Fair_Lending_Notice_PostClose.json'),
            "9000869_452":os.path.join(de_config_path,'9000869-security_instrument_PostClose.json'),
            "9000001_452":os.path.join(de_config_path,'9000001-1003_new_PostClose.json'),
            "9000671_452":os.path.join(de_config_path,'9000671-note_classify_PostClose.json'),
            "9000181_452":os.path.join(de_config_path,'9000181-closing_disclosure_PostClose.json'),
            "9000031_452":os.path.join(de_config_path,'9000031-Addendum_to_Loan_Application_PostClose.json'),
            "9000026_452":os.path.join(de_config_path,'9000026-Acknowledgement_of_Receipt_of_the_Closing_Disclosure_PostClose.json'),
            "9001992_452":os.path.join(de_config_path,'9001992-Anti_Coercion_Disclosure_PostClose.json'),
            "9000116_452":os.path.join(de_config_path,'9000116-Borrower_Authorization_for_Counselling_PostClose.json'),
            "9002202_452":os.path.join(de_config_path,'9002202-California_All_Purpose_Acknowledgement_PostClose.json'),
            "9000045_452":os.path.join(de_config_path,'9000045-Error_Omissions_Compliance_Agreement_PostClose.json'),
            "9000558_452":os.path.join(de_config_path,'9000558-Loan_Amortization_Schedule_PostClose.json'),
            "9000004_452":os.path.join(de_config_path,'9000004-Form_1040_Tax_Returns_PostClose.json'),
            "9000936_452":os.path.join(de_config_path,'9000936-Collateral_Protection_Insurance_Disclosure_PostClose.json'),
            "9000743_452":os.path.join(de_config_path,'9000743-Owners_Affidavit_PostClose_classify.json'),
            "9000075_452":os.path.join(de_config_path,'9000075-Appraisal_Waiver_Receipt_Form_PostClose.json'),
            "9000759_452":os.path.join(de_config_path,'9000759-Planned_Unit_Development_Rider_PostClose.json'),
            "9000866_452":os.path.join(de_config_path,'9000866-Second_Home_Rider_PostClose.json'),
            "9000142_452":os.path.join(de_config_path,'9000142-Buydown_agreement_PostClose.json'),
            "9000938_452":os.path.join(de_config_path,'9000938-Texas_Home_Equity_Affidavit_And_Agreement_First_Lien_Disclosure_PostClose.json'),
            "9000036_452":os.path.join(de_config_path,'9000036-Adjustable_Rate_Mortgage_Disclosure_PostClose.json'),
            "9000298_452":os.path.join(de_config_path,'9000298-Exhibit_A_Legal_Description_PostClose.json'),
            "9000806_452":os.path.join(de_config_path,'9000806-Rate_Lock_Agreement_PostClose_Classify.json'),
            "9000709_452":os.path.join(de_config_path,'9000709-Notice_Of_Right_To_Cancel_Borrower_PostClose.json'),
            "8011370_452":os.path.join(de_config_path,'8011370-Notice_Of_Right_To_Cancel_Coborrower_PostClose.json'),
            "9000050_452":os.path.join(de_config_path,'9000050-Anti_steering_disclosure_PostClose.json'),
            "9000215_452":os.path.join(de_config_path,'9000215-Condominium_Rider_PostClose.json'),
            "9000297_452":os.path.join(de_config_path,'9000297-Evidence_of_joint_application_PostClose.json'),
            "9000762_452":os.path.join(de_config_path,'9000762-Power_of_Attorney_PostClose.json'),
            "9000041_452":os.path.join(de_config_path,'9000041-Affidavit_of_Occupancy_PostClose.json'),
            "9000115_452":os.path.join(de_config_path,'9000115-Borrower_Authorization_PostClose.json'),
            "9000882_452":os.path.join(de_config_path,'9000882-Signature_and_name_affidavit_Borrower_PostClose.json'),
            "8011371_452":os.path.join(de_config_path,'8011371-Signature_and_name_affidavit_Coborrower_PostClose.json'),
            "9000960_452":os.path.join(de_config_path,'9000960-Trust_Agreement_PostClose.json'),
            "9000501_452":os.path.join(de_config_path,'9000501-Indemnit_and_Affidavit_to_Debts_and_Liens_PostClose.json'),
            "9000010_452":os.path.join(de_config_path,'9000010-1-4_Family_Rider_PostClose.json'),
            "9000673_452":os.path.join(de_config_path,'9000673-Note_Allonge_PostClose.json'),
            "9000432_452":os.path.join(de_config_path,'9000432-GA_Foreclosure_Disclosure_PostClose.json'),
            "9000961_452":os.path.join(de_config_path,'9000961-Trust_Certification_PostClose.json'),
            "9000480_452":os.path.join(de_config_path,'9000480-HUD_92900A-1820A_PostClose.json'),
            "9000820_452":os.path.join(de_config_path,'9000820-Recorded_Subordination_Agreement_PostClose_classify.json'),
            "9000205_452":os.path.join(de_config_path,'9000205-Compliance_Agreement_PostClose.json'),
            "9002148_452":os.path.join(de_config_path,'9002148-Identification_Verification_Patriot_Act_PostClose.json'),
            "8011232_452": os.path.join(de_config_path,'8011232-Identification_Verification_Patriot_Act_PostClose.json'),
            "9000029_452":os.path.join(de_config_path,'9000029-Addendum_to_Closing_Disclosure_PostClose_Classify.json'),
            "9000263_452":os.path.join(de_config_path,'9000263-Disclosure_Notices_PostClose.json'),
            "9000283_452":os.path.join(de_config_path,'9000283-Equal_Credit_Opportunity_Act_Form_PostClose.json'),
            "9002216_452":os.path.join(de_config_path,'9002216-4506-C_PostClose.json'),
            "9000670_452":os.path.join(de_config_path,'9000670-No_HUD_Warranty_PostClose.json'),
            "9000932_452":os.path.join(de_config_path,'9000932-Taxpayer_Consent_PostClose.json'),
            "9001956_452":os.path.join(de_config_path,'9001956-Notice_to_Purchasers_PostClose.json'),
            "9000143_452":os.path.join(de_config_path,'9000143-CA_Per_Diem_Interest_Disclosure_PostClose.json'),
            "9000504_452":os.path.join(de_config_path,'9000504-Informed_Consumer_Choice_Disclosure_Notice_PostClose.json'),
            "9000293_452":os.path.join(de_config_path,'9000293-Escrow_Waiver_PostClose.json'),
            "9000494_452":os.path.join(de_config_path,'9000494-Impound_Authorization_PostClose.json'),
            "9000044_452":os.path.join(de_config_path,'9000044-Affiliated_Business_Disclosure_Statement_Postclose.json'),
            "9001996_452":os.path.join(de_config_path,'9001996-Title_Commitment_PostClose.json'),
            "9000370_452":os.path.join(de_config_path,'9000370-First_Lien_Letter_PostClose.json'),
            "9000222_452":os.path.join(de_config_path,'9000222-Correction_Agreement_Limited_POA_PostClose.json'),
            "9000856_452":os.path.join(de_config_path,'9000856-Right_To_Select_An_Attorney_PostClose.json'),
            "9002223_452":os.path.join(de_config_path,'9002223-Consumer_Explanation_Letter_PostClose.json'),
            "9000189_452":os.path.join(de_config_path,'9000189-Closing_Instructions_PostClose.json'),
            "9000323_452":os.path.join(de_config_path,'9000323-FHA_Appraisal_Certificate_PostClose.json'),
            "9000448_452":os.path.join(de_config_path,'9000448-Hazard_and_Flood_Authorization_PostClose.json'),
            "9000121_452":os.path.join(de_config_path,'9000121-Borrower_Debt_Certification_PostClose.json'),
            "9002153_452":os.path.join(de_config_path,'9002153-Affidavit_Of_Affixation_PostClose.json'),
            "9000371_452":os.path.join(de_config_path,'9000371-First_Payment_Letter_PostClose.json'),
            "9000488_452":os.path.join(de_config_path,'9000488-Designation_Of_Homestead_Affidavit_PostClose.json'),
            "9000879_452":os.path.join(de_config_path,'9000879-Settlement_Statement_PostClose.json'),
            "9001060_452":os.path.join(de_config_path,'9001060-Wire_Transfer_Disbursement_Detail_PostClose.json'),
            "9000506_452":os.path.join(de_config_path,'9000506-Initial_Escrow_Account_Disclosure_Statement_PostClose.json'),
            "9000020_452":os.path.join(de_config_path,'9000020-Acknowledgement_Of_Fair_Market_Value_PostClose.json'),
            "9000703_452":os.path.join(de_config_path,'9000703-Notice_Of_No_Oral_Agreement_PostClose.json'),
            "9002213_452":os.path.join(de_config_path,'9002213-Survey_affidavit_PostClose.json'),
            "9000719_452":os.path.join(de_config_path,'9000719-Notice_to_Homeowner_PostClose.json'),
            "9000634_452":os.path.join(de_config_path,'9000634-Mineral_Rights_Acknowledgement_and_Agreement_PostClose.json'),
            "9000301_452":os.path.join(de_config_path,'9000301-FACT_Act_Notice_To_Home_Loan_Applicant_PostClose.json'),
            "9001003_452":os.path.join(de_config_path,'9001003-VA_26-0592_Military_Checklist_PostClose.json'),
            "9000691_452":os.path.join(de_config_path,'9000691-Notice_of_Furnishing_Negative_Information_PostClose.json'),
            "9000663_452":os.path.join(de_config_path,'9000663-Net_Tangible_Benefit_PostClose.json'),
            "9000924_452":os.path.join(de_config_path,'9000924-Tax_Information_Sheet_PostClose.json'),
            "9000482_452":os.path.join(de_config_path,'9000482-HUD_Appraisal_Value_Disclosure_PostClose.json'),
            "9001053_452":os.path.join(de_config_path,'9001053-W9_IRS_PostClose.json'),
            "8011228_452":os.path.join(de_config_path,'8011228-W9_IRS_PostClose.json'),
            "9000554_452":os.path.join(de_config_path,'9000554-Line_of_Credit_Payoff_Affidavit_PostClose.json'),
            "8011144_452":os.path.join(de_config_path,'8011144-Price_Agreement_PostClose.json'),
            "9000228_452":os.path.join(de_config_path,'9000228-Cover_Sheet_PostClose_classify.json'),
            "8010382_452":os.path.join(de_config_path,'8010382-Disbursement_of_Proceeds_PostClose.json'),
            "9000916_452":os.path.join(de_config_path,'9000916-Tax_Authorization_PostClose.json'),
            "9000377_452":os.path.join(de_config_path,'9000377-Flood_Determination_Disclosure_PostClose.json'),
            "9000828_452":os.path.join(de_config_path,'9000828-Report_And_Certificate_Of_Loan_Disbursemt_PostClose_classify.json'),
            "9000461_452":os.path.join(de_config_path,'9000461-Hold_Harmless_Agreement_PostClose.json'),
            "9000481_452":os.path.join(de_config_path,'9000481-Importance_Notice_to_Homebuyers_PostClose.json'),
            "9000513_452":os.path.join(de_config_path,'9000513-Instruction_to_Escrow_PostClose.json'),
            "9002218_452":os.path.join(de_config_path,'9002218-Wisconsin_Property_Tax_Escrow_Option_PostClose.json'),
            "9001964_452":os.path.join(de_config_path,'9001964-SSA_Form_89_Verification_Report_PostClose.json'),
            "9000672_452":os.path.join(de_config_path,'9000672-Note_Addendum_PostClose.json'),
            "9000830_452":os.path.join(de_config_path,'9000830-Certificate_of_Eligibility_PostClose.json'),
            "9001002_452":os.path.join(de_config_path,'9001002-VA_26-0503_Federal_Collection_PostClose.json'),
            "9000671_401":'9000671-note_RoundPoint.json',
            "9000377_401":'9000377-Standard_Flood_Hazard_Determination_RoundPoint.json',
            "9000673_401":'9000673-Note_Allonge_RoundPoint.json',
            "9000712_401":'9000712-Notice_of_loan_servicing_transfer_disclosure_RoundPoint.json',
            "9000069_401":'9000069-Appraisal_RoundPoint.json',
            "9000109_401":'9000109-Billing_Statement_RoundPoint.json',
            "9000617_401":'9000617-Lost_Note_Affidavit_RoundPoint.json',
            "8008352_401":'8008352-Step_Rate_Loan_Servicing_RoundPoint.json',
            "9000440_401":'9000440-Servicing_Transfer_Statement_RoundPoint.json',
            "8008047_401":'8008047-Goodbye_Letter_RoundPoint.json',
            "9000506_401":'9000506-Initial_Escrow_Account_Disclosure_RoundPoint.json',
            "9000181_401":'9000181-Closing_Disclosure_TRID_RoundPoint.json',
            "8008033_401":'8008033-Note_Copy_RoundPoint.json',
            "8008097_401":'8008097-Escrow_Analysis_Annual_Notice_RoundPoint.json',
            "9000290_401":'9000290-Escrow_Analysis_RoundPoint.json',
            "8007736_401":'8007736-Payoff_Quote_RoundPoint.json',
            "9000019_401":'9000019-ACH_Form_RoundPoint.json',
            "9000752_401":'9000752-Payoff_Statement_RoundPoint.json',
            "8007263_401":'8007263-ACH_Verification_RoundPoint.json',
            "8008082_401":'8008082-Escrow_Cease_Letter_RoundPoint.json',
            "8007872_401":'8007872-Repayment_Plan_Letter_RoundPoint.json',
            "8008290_401":'8008290-Foreclosure_Invoices_RoundPoint.json',
            "8008213_401":'8008213-LM_Modification_Agreement-Fully_Executed_RoundPoint.json',
            "9000291_401":'9000291-Escrow_Letter_to_Borrower_RoundPoint.json',
            "8008080_401":'8008080-Account_Paid_In_Full-Letter_RoundPoint.json',
            "8008426_401":'8008426-Force_Placement_Notice-Flood_RoundPoint.json',
            "8008434_401":'8008434-Insurance_Document-Hazard_RoundPoint.json',
            "8008433_401":'8008433-LPI_Cancellation_Notice-Other_RoundPoint.json',
            "8008055_401":'8008055-Solicitation_Letter_Loss_Mit_RoundPoint.json',
            "9001057_401":'9001057-Welcome_Letter_RoundPoint.json',
            "8008430_401":'8008430-Insufficient_Policy_Notice-Other_RoundPoint.json',
            "8008343_401":'8008343-Loss_Mit_Complete_Application_Acknowledgement_Letter_RoundPoint.json',
            "8008028_401":'8008028-Rider_to_Note_RoundPoint.json',
            "8008029_401":'8008029-Deed_of_Trust_Rider_RoundPoint.json',
            "9000371_401":'9000371-First_Payment_Letter_RoundPoint.json',
            "9000253_401":'9000253-Deed_of_Trust_RoundPoint.json',
            "9000081_401":'9000081-Assignment_Of_Mortgage_RoundPoint.json',
            "8007854_401":'8007854-Deed_of_Trust-Recorded_RoundPoint.json',
            "8007831_401":'8007831-Servicer_Correspondence_RoundPoint.json',
            "9000392_401":'9000392-Forbearance_Agreement_RoundPoint.json',
            "8008042_401":'8008042-Rate_Change_Letter_RoundPoint.json',
            "9000581_401":'9000581-Loan_Modification_Agreement_RoundPoint.json',
            "8008083_401":'8008083-Short_Year_History_RoundPoint.json',
            "9000942_401":'9000942-Authorization_Form_3rd_Party_RoundPoint.json',
            "8007257_401":'8007257-Notice_of_Sale_or_Transfer_of_Mortgage_Loan_404_RoundPoint.json',
            "8007791_401":'8007791-Payoff_Request_RoundPoint.json',
            "9000036_401":'9000036-ARM_Servicing_RoundPoint.json',
            "8007641_401":'8007641-Late_Charge_Letter_RoundPoint.json',
            "8008248_401":'8008248-Reconveyance_Documentation_RoundPoint.json',
            "8007889_401":'8007889-SCRA_Notice_RoundPoint.json',
            "8008476_401":'8008476-Deed_of_Trust-re-recorded_RoundPoint.json',
            "8008478_401":'8008478-Welcome_Email_RoundPoint.json',
            "8008487_401":'8008487-URLA_RoundPoint.json',
            "8008073_401":'8008073-Deed_in_Lieu_Agreement_RoundPoint.json',
            "8008065_401":'8008065-Short_Sale_Agreement_RoundPoint.json',
            "8008120_401":'8008120-Notice_of_Sale_or_Transfer_of_Mortgage_Loan_RoundPoint.json',
            "8008481_401":'8008481-Deferral_Letter_RoundPoint.json',
            "9000959_401":'9000959_Transmittal_Summary_RoundPoint.json',
            "9000001_401":'9000001-1003_RoundPoint.json',
            "9002254_401":'9002254-Opt_Out_Notice_RoundPoint.json',
            "9000001_439":'9000001-1003_Phl.json',
            "9000069_439":'9000069-Appraisal_Phl.json',
            "9000377_439":'9000377-Flood_cert_Phl.json',
            "9000181_439":'9000181-Closing_Disclosure_Phl.json',
            "9000253_439":'9000253-Deed_of_trust_Phl.json',
            "9000671_439":'9000671_Note_Phl.json',
            "9001033_439":'9001033-Misc_Phl.json',
            "9000003_439":'9000003-Misc_Phl.json',
            "8011135_449_Appendix_A": '8011135-Appendix_A_MrCooper.json',
            "8011134_449": '8011134-Pooling_and_Servicing_Agreement_MrCooper.json',
            "8011135_449_MSA": '8011135-MSA_MrCooper.json',
            "8011135_449": '8011135-MSA_MrCooper_Classify.json',
            "8011139_449": '8011135-Indenture_MrCooper.json',
            "8011137_449": '8011137-Servicing_Agreement_MrCooper.json',
            "9000001_403":'9000001-1003_UnderWritter.json',
            "9000089_403":'9000089-AUS_DU_classify_UW.json',
            "9000618_403":'9000618-LP_UnderWritter.json',
            "9000377_403":'9000377-Flood_Hazard_Determination_Flood_Certificate_UnderWritter.json',
            "9000383_403":'9000383-Flood_Insurance_Policy_classify_UW.json',
            "9000181_403":'9000181-Closing_Disclosure_UnderWritter.json',
            "9000916_403":'9000916-Tax_Authorization_UnderWritter.json',
            "9000194_403":'9000194-Closing_Protection_Lette_UnderWritter.json',
            "9000246_403":'9000246-Credit_Supplement_UnderWritter.json',
            "9000364_403":'9000364-Final_Title_Policy_UnderWritter.json',
            "9000449_403":'9000449-Hazard_Insurance_Policy_classify_UW.json',
            "9000753_403":'9000753-Paystub_classify_UnderWritter.json',
            "9000802_403":'9000802-Purchase_and_Sale_agreement_UnderWritter.json',
            "9000806_403":'9000806-Rate_Lock_Agreement_UnderWritter.json',
            "9000918_403":'9000918-Tax_Certificate_UnderWritter.json',
            "9000069_403":'9000069-Appraisal_Report_UnderWritter.json',
            "9001955_403":'9001955-Credit_Report_UnderWritter.json',
            "9000001_443":'9000001-1003_DocVuDemo.json',
            "9000069_443":'9000069-appraisal_DocVuDemo.json',
            "9000089_443":'9000089-AUS_DU_classify_DocVuDemo.json',
            "9000115_443":'9000115-Borrower_Authorization_DocVuDemo.json',
            "9000181_443":'9000181-closing_disclosure_DocVuDemo.json',
            "9000293_443":'9000293-Escrow_Waiver_DocVuDemo.json',
            "9000301_443":'9000301-FACT_Act_Notice_To_Home_Loan_Applicant_DocVuDemo.json',
            "9000377_443":'9000377-Flood_Cert_DocVuDemo.json',
            "9000671_443":'9000671-note_DocVuDemo.json',
            "9000709_443":'9000709-Notice_Of_Right_To_Cancel_DocVuDemo.json',
            "9000869_443":'9000869-security_instrument_DocVuDemo.json',
            "9000882_443":'9000882-Signature_and_name_affidavit_DocVuDemo.json'
        }
        self.ocr_dir = ocr_dir
        # Load the JSON configuration for the document ID mapper
        print("Mapper Config Path:", mapper_config_path)
        if mapper_config_path is not None:
            with open(mapper_config_path, 'r') as config_file:
                self.mapper_config = json.load(config_file)
        else:
            self.mapper_config = None

    def get_correct_de_config(self, combined_json):
        form_type, search_start_page, num_pages = (
            self.classify_policy.process_form(combined_json, is_owner=False))
        return form_type, search_start_page, num_pages

    def _classify(self, pdf_data_path=None,
                  ocr_xml_json_path=None,
                  pdf_file_path=None,
                  classification_required=True,
                  return_class_id_and_name=False,
                  for_testing=False):
        if pdf_data_path is None and ocr_xml_json_path is not None and pdf_file_path is not None:
            combined_json_path, pdf_data_path = self._prepare_ocr_json(ocr_xml_json_path, pdf_file_path,
                                                                       for_testing=for_testing)

        combined_json_path = os.path.join(pdf_data_path, 'combined.json')
        form_type, search_start_page, num_pages = self.get_correct_de_config(combined_json_path)

        if form_type == "misc":
            return "misc", search_start_page

        if not classification_required:
            return form_type, search_start_page

        if os.path.isfile(combined_json_path):
            with open(combined_json_path) as temp_f:
                json_data = json.loads(temp_f.read())
        else:
            print("Combined JSON doesn't exist")
            return None, search_start_page
        page_numbers = sorted([int(it) for it in json_data.keys()])

        to_select_config_file = self.form_type_dict["identifier"]
        with open(to_select_config_file) as temp_f:
            identifier_config = json.loads(temp_f.read())

        raw_results, page_dimensions = self.parser.perform_de(identifier_config, json_data, pdf_data_path, search_start_page)
        policy_number = None
        policy_type_text = None
        modified_for_policy_type_text = False
        for item in raw_results:
            res_val = self.formatter.post_process_text(item)
            if item["field_name"] == "Policy_Type_Text":
                value = res_val
                if value is not None:
                    if type(value) is list:
                        value = value[0]
                    policy_type_text = value
            if item["field_name"] == "Full_Policy_Number":
                policy_number = res_val

        if policy_type_text is not None:
            if "short" in policy_type_text.lower():
                form_type = "short_form"
                modified_for_policy_type_text = True
            elif "owner" in policy_type_text.lower():
                form_type = "owner_form"
                modified_for_policy_type_text = True
            elif "junior" in policy_type_text.lower():
                form_type = "long_form"
                search_start_page -= 1
                modified_for_policy_type_text = True
            else:
                prev_form_type = form_type
                form_type = "long_form"

                if prev_form_type != form_type:
                    modified_for_policy_type_text = True

        ## give priority to owner form if policy_number is starting with digit
        if policy_number is not None \
                and ("O-" in policy_number or (len(policy_number) > 1
                                    and policy_number[0].isdigit())) \
                and form_type != "owner_form" and not modified_for_policy_type_text:
            form_type = "owner_form"
        if self.mapper_config is not None:
            form_id, form_name = self.mapper_config['DocumentName2IdMapper'][form_type]

        if return_class_id_and_name and self.mapper_config is not None:
            return form_id, form_name, form_type, search_start_page
        else:
            return form_type, search_start_page
    def _extract(self,
                 form_type,
                 search_start_page,
                 client_id = None,
                 pdf_data_path=None,
                 ocr_xml_json_path=None,
                 pdf_file_path=None,
                 return_indexing_info=True,
                 to_extract_field=None,
                 document_id=None,
                 document_type=None,
                 reference_no=None,
                 fetch_prev_to_extract_field=False,
                 separate_table_data = True,
                 for_testing=False):

        ## Apply client-specific page_boundaries
        if client_id:
            # Check if the client ID exists in the config, and update the default values if found
            client_boundaries = page_bound_configs.get(int(client_id))
            if client_boundaries:
                page_bound_configs.update(client_boundaries)

        if pdf_data_path is None and ocr_xml_json_path is not None and pdf_file_path is not None:
            combined_json_path, pdf_data_path = self._prepare_ocr_json(ocr_xml_json_path, pdf_file_path,
                                                                       for_testing=for_testing)

        combined_json_path = os.path.join(pdf_data_path, 'combined.json')
        if os.path.isfile(combined_json_path):
            with open(combined_json_path) as temp_f:
                json_data = json.loads(temp_f.read())
        else:
            print("Combined JSON doesn't exist")
            return None
        print("Selected Form {} -> is: {}, To Start Extract from Page No. {}".
              format(pdf_data_path, form_type, search_start_page))
        config_file = None
        use_client_specific_file_path = False
        # Load config file path based on form type
        
        if str(client_id) in ["401", "449", "443", "403", "439"]:
            print("INSIDE CLIENT ID IF LOOP")
            config_file = self.form_type_dict.get(document_id)
            
            if config_file:
                config_file_path = os.path.join(client_path, str(client_id), config_file)
            else:
                config_file_path = None
            use_client_specific_file_path = True
        else:
            config_file_path = self.form_type_dict.get(document_id)
        print("document_id!!!!!!!", document_id, config_file_path)

        if not config_file_path:
            return None
        with open(config_file_path, 'r') as f:
            config_file  = json.load(f)

        if isinstance(config_file, dict):
            classify_obj = Classify(config_file)
            classify_result = classify_obj.process_form(combined_json_path=combined_json_path)
            config_file_name = classify_result["document_type"]
            if use_client_specific_file_path:
                config_file_path = os.path.join(client_path, str(client_id), config_file_name)
            else:
                config_file_path = os.path.join(de_config_path, config_file_name)
            print("Updated config_file_path", config_file_path)
        extraction_fields = self.load_config_json(config_file_path)

        if to_extract_field:
            to_search = [i for i, val in enumerate(extraction_fields) if val["field_name"] == to_extract_field]
            if to_search:
                to_search = to_search[0]
            else:
                return None
        else:
            to_search = -1
        if to_search != -1:
            if fetch_prev_to_extract_field:
                extraction_fields = extraction_fields[0:to_search + 1]
            else:
                extraction_fields = extraction_fields[to_search:to_search + 1]

        page_numbers = sorted([int(it) for it in json_data.keys()])
        # json_data = self.parser.rearrange_page_json(json_data, page_numbers)
        raw_results, page_dimensions = self.parser.perform_de(extraction_fields, json_data, pdf_data_path, search_start_page, client_id)
        print("--------> Formatting Started for {}".format(pdf_data_path))

        results = self.formatter.get_formatted_json_output_new(raw_results, page_dimensions, separate_table_data = separate_table_data)
        print('results....', results)

        # print(file_path)
        file_name = pdf_data_path.split('/')[-1]
        de_results = results
        if for_testing:
            de_results = {'Form_type': form_type, 'Original_Image_Name': file_name,
                        'Original_Image_Length': len(page_numbers), 'Value': results}
            return de_results
        if not client_id or str(client_id) not in ["402", "405"]:
            return de_results
        else:
            if self.mapper_config:
                de_results = self.field_id_mapper_sls_process(document_id, reference_no, results)
            return de_results
            
        
    def field_id_mapper_sls_process(self, document_id, reference_no, results):
        
        exception_doc_id = 9002234
        endorsement_doc_id = 9002249
        indexed_results = {document_id: {'FieldData': [], 'TableData': []},
                        exception_doc_id: {'FieldData': [], 'TableData': []},
                        endorsement_doc_id: {'FieldData': [], 'TableData': []}}
        ## Process FieldData
        id_mapper_dit = self.mapper_config['TextFieldConfigurationIdMapper'][str(document_id)][
            'TextFieldConfigurationIdMapper']
        not_found_names = list(id_mapper_dit.keys())
        for idx, val in enumerate(results['FieldData']):
            if results['FieldData'][idx]['Name'] in id_mapper_dit:
                if results['FieldData'][idx]['Name'] in id_mapper_dit:
                    results['FieldData'][idx]['Id'] = str(id_mapper_dit[results['FieldData'][idx]['Name']])
                    if results['FieldData'][idx]['Name'] in not_found_names:
                        not_found_names.remove(results['FieldData'][idx]['Name'])

                # results['FieldData'][idx]['Id'] = id_mapper_dit[results['FieldData'][idx]['Name']]
                indexed_results[document_id]['FieldData'].append(results['FieldData'][idx])
                if results['FieldData'][idx]['Name'] in not_found_names:
                    not_found_names.remove(results['FieldData'][idx]['Name'])
            else:
                pass
        for idx, val in enumerate(results['TableData']):
            if results['TableData'][idx]['Name'] in id_mapper_dit:
                not_found_names.remove(results['TableData'][idx]['Name'])

                id = str(id_mapper_dit[results['TableData'][idx]['Name']])
                id_mapper = {results['TableData'][idx]['Name'] : id}
                results['TableData'][idx].update_field_ids(id_mapper, is_camel_case=True)
                indexed_results[document_id]['TableData'].append(results['TableData'][idx])
        for nfn in not_found_names:
            result = BaseFieldData(from_camel_case=True, force_fill=True, **{
                                                            "Id": id_mapper_dit[nfn],
                                                            "Name": nfn,
                                                            "Key": '',
                                                            "Value": '',
                                                            "PostProcessingValue": '',
                                                            "PageNumber": 0,
                                                            "LineNumber": 0,
                                                            "ConfidenceIndicator": 0.0,
                                                            "ColorIndicator": 0,
                                                            "FieldNameCoordinates": None,
                                                            "FieldValueCoordinates": None
                                                        })
            result.fill_defaults()
            result.drop_non_default_fields()
            result.to_camel_case()
            indexed_results[document_id]['FieldData'].append(result)

        ## Process TableData Exeption
        id_mapper_dit = self.mapper_config['TextFieldConfigurationIdMapper'][str(exception_doc_id)][
            'TextFieldConfigurationIdMapper']
        found_exception = False
        for idx, val in enumerate(results['TableData']):
            if results['TableData'][idx]['Name'] in id_mapper_dit:
                found_exception = True
                results['TableData'][idx].update_field_ids(id_mapper_dit, is_camel_case = True)
                indexed_results[exception_doc_id]['TableData'].append(results['TableData'][idx])
                break
        if not found_exception:
            result = BaseTableData(confidence_indicator=None,
                            color_indicator=None,
                            start_page=None,
                            end_page=None, rows=[])
            result.fill_defaults()
            result.to_camel_case()
            indexed_results[exception_doc_id]['TableData'].append(result)

        ## Process TableData Endorsement
        id_mapper_dit = self.mapper_config['TextFieldConfigurationIdMapper'][str(endorsement_doc_id)][
            'TextFieldConfigurationIdMapper']
        found_endorsement = False
        for idx, val in enumerate(results['TableData']):
            if results['TableData'][idx]['Name'] in id_mapper_dit:
                found_endorsement = True
                results['TableData'][idx].update_field_ids(id_mapper_dit, is_camel_case = True)
                indexed_results[endorsement_doc_id]['TableData'].append(results['TableData'][idx])
                break
        if not found_endorsement:
            result = BaseTableData(confidence_indicator=None,
                                color_indicator=None,
                                start_page=None,
                                end_page=None, rows=[])
            result.fill_defaults()
            result.to_camel_case()
            indexed_results[endorsement_doc_id]['TableData'].append(result)

        extracted_document_data = []
        for doc_id in [document_id, endorsement_doc_id, exception_doc_id]:
            extracted_document_data.append({
                'DocumentId': str(doc_id),
                'DocumentType': self.mapper_config['TextFieldConfigurationIdMapper'][str(doc_id)][
                    'DocumentName'],
                'FieldData': indexed_results[doc_id]['FieldData'],
                'TableData': indexed_results[doc_id]['TableData']
            })

        de_results = {
                    'ReferenceNo': reference_no,
                    'ExtractedDocumentData': extracted_document_data
        }

        return de_results


    def _prepare_ocr_json(self, ocr_xml_json_path, pdf_file_path, for_testing=False):
        converter = XMLToJSONConverter(input_kofax_json_path=ocr_xml_json_path, pdf_file_path=pdf_file_path,
                                       output_dir=self.ocr_dir, horizontal_threshold_line=80)
        combined_json_filename = os.path.join(converter.output_dir, f"combined.json")
        isExist = os.path.exists(combined_json_filename)
        if isExist and for_testing:
            return combined_json_filename, converter.output_dir
        combined_json_path, pdf_data_path = (
            converter.process_pages_and_generate_jsons(enable_vertical_separation=False, for_testing=for_testing))
        return combined_json_path, pdf_data_path

    def load_config_json(self, config_file):
        sub_keys_config_file = self.form_type_dict["sub_keys"]
        with open(config_file) as temp_f:
            extraction_fields = json.loads(temp_f.read())
        with open(sub_keys_config_file) as temp_f:
            sub_keys_fields = json.loads(temp_f.read())

        updated_extraction_fields = extraction_fields.copy()
        for index, ext_item in enumerate(updated_extraction_fields):
            if "sub_keys" in ext_item.keys():
                sub_keys_fields_temp = copy.deepcopy(sub_keys_fields)
                required_sub_key_fields = ext_item["sub_keys"]
                sub_keys = []
                for re_sub_key in required_sub_key_fields:
                    if re_sub_key in sub_keys_fields_temp.keys():
                        if sub_keys:
                            sub_keys += sub_keys_fields_temp[re_sub_key]
                        else:
                            sub_keys = sub_keys_fields_temp[re_sub_key]
                ext_item["sub_keys"] = sub_keys
            elif "sub_items" in ext_item.keys():
                sub_keys_fields_temp = copy.deepcopy(sub_keys_fields)
                for index, sub_item in enumerate(ext_item["sub_items"]):
                    if "sub_keys" in sub_item.keys():
                        required_sub_key_fields = ext_item["sub_items"][index]["sub_keys"]
                        sub_keys = []
                        for re_sub_key in required_sub_key_fields:
                            if re_sub_key in sub_keys_fields_temp.keys():
                                if sub_keys:
                                    sub_keys += sub_keys_fields_temp[re_sub_key]
                                else:
                                    sub_keys = sub_keys_fields_temp[re_sub_key]
                        ext_item["sub_items"][index]["sub_keys"] = sub_keys
        return updated_extraction_fields

    def clean(self, file_name=None):
        """
        Deletes contents of the OCR directory. If file_name is provided, deletes that specific file;
        otherwise, deletes all contents of the directory.

        :param file_name: Optional; name of a specific file to delete.
        """
        if file_name:
            # Build the full path to the file
            file_path = os.path.join(self.ocr_dir, file_name)
            # Check if the file exists and delete
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"Deleted file: {file_path}")
            else:
                print(f"No such file: {file_path}")
        else:
            # Check if the directory exists
            if os.path.exists(self.ocr_dir):
                # Delete all contents of the directory
                for filename in os.listdir(self.ocr_dir):
                    file_path = os.path.join(self.ocr_dir, filename)
                    try:
                        if os.path.isfile(file_path) or os.path.islink(file_path):
                            os.unlink(file_path)
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                    except Exception as e:
                        print(f'Failed to delete {file_path}. Reason: {e}')
                print(f"All contents of {self.ocr_dir} have been deleted.")
            else:
                print(f"No such directory: {self.ocr_dir}")