import importlib
import logging
from docvu_de_core.io import PostProcessOutputFormat

class PostProcess:
    """
    This class will be used to apply post-processing on the extracted content from the parser
    """

    def __init__(self, logger):
        self.logger = logger
        self.default_module = 'docvu_de_core.post_processor'
        self.custom_module = 'docvu_de_core.custom'

    @staticmethod
    def _to_camel_case(s):
        """Helper method to convert snake_case to CamelCase"""
        parts = s.split('_')
        return ''.join(x.capitalize() for x in parts)

    def __call__(self, extraction_item, elements, page_json_data, prev_page_json_data, image_path):
        output = PostProcessOutputFormat(value=None, success=False, line_number=None, bbox=None)
        if 'post_process' in extraction_item:
            post_process_list = extraction_item['post_process']
            if "string_operations_post_processor" in post_process_list:
                v = post_process_list['string_operations_post_processor']
                post_process_list.pop('string_operations_post_processor')
                post_process_list['string_operations_post_processor'] = v
            for pp_key, pp_val in post_process_list.items():
                if pp_key == "one_of":
                    continue
                class_name = self._to_camel_case(pp_key)
                try:
                    if not pp_val.get('custom', False):
                        module = importlib.import_module(self.default_module)
                    else:
                        module = importlib.import_module(self.custom_module)
                    pp_class = getattr(module, class_name)
                    pp_instance = pp_class(logger=self.logger, **pp_val)
                    if elements is not None:
                        output = pp_instance(elements=elements,
                                             page_json_data=page_json_data,
                                             prev_page_json_data=prev_page_json_data,
                                             image_path=image_path)
                    if output.success:
                        if post_process_list.get("one_of", False):
                            break
                        else:
                            try:
                                elements = output.elements
                            except:
                                elements = elements
                            continue
                except (ImportError, AttributeError) as e:
                    print(f"Post-processing class {class_name} not found in module. Error: {e}")
                    raise ValueError(f"Post-processing class {class_name} not found in module. Error: {e}")
        return output