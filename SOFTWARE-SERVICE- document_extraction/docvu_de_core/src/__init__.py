# from docvu_de_core.src.ocr_main import GetOcrResults
from docvu_de_core.src.parser import ParserClass
from docvu_de_core.src.search_key_fields import SearchKeyFields
from docvu_de_core.src.table_parser import TableParser
from docvu_de_core.utils.loggingconfig import configure_logging
from docvu_de_core.src.post_process import PostProcess
from docvu_de_core.src.output_format_processor import OutputFormatProcessor
from docvu_de_core.custom.ExceptionSearch import ExceptionSearch
from docvu_de_core.src.table_processor import TableProcessor
from docvu_de_core.custom.LegalDescriptionSearch import LegalDescriptionSearch
logger = configure_logging()