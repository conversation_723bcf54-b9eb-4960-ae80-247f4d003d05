import importlib
from docvu_de_core.io import OutputFormat

class OutputFormatProcessor:
    def __init__(self):
        self.default_module = 'docvu_de_core.output_format'
        self.custom_module = 'docvu_de_core.custom'

    @staticmethod
    def _to_camel_case(s):
        """Helper method to convert snake_case to CamelCase"""
        parts = s.split('_')
        return ''.join(x.capitalize() for x in parts)

    def __call__(self, extraction_items, extraction_item, de_start_page=0, client_id=None, **kwargs):
        if extraction_item is None:
            return OutputFormat(item=None, success=False, multi_item=False)
        processed_output = OutputFormat(item=extraction_item,
                                        success=False,
                                        multi_item=False)
        if 'output_format' in extraction_item:
            output_format_list = extraction_item['output_format']

            if "copy_extraction_items_output_format" in output_format_list:
                v = output_format_list['copy_extraction_items_output_format']
                output_format_list.pop('copy_extraction_items_output_format')
                output_format_list['copy_extraction_items_output_format'] = v
            if "string_operations_output_format" in output_format_list:
                v = output_format_list['string_operations_output_format']
                output_format_list.pop('string_operations_output_format')
                output_format_list['string_operations_output_format'] = v
            if "concat_extraction_items_output_format" in output_format_list:
                v = output_format_list['concat_extraction_items_output_format']
                output_format_list.pop('concat_extraction_items_output_format')
                output_format_list['concat_extraction_items_output_format'] = v
            if "ner_service_req_output_format" in output_format_list:
                v = output_format_list['ner_service_req_output_format']
                output_format_list.pop('ner_service_req_output_format')
                output_format_list['ner_service_req_output_format'] = v
            if "llm_operations_output_format" in output_format_list:
                v = output_format_list['llm_operations_output_format']
                output_format_list.pop('llm_operations_output_format')
                output_format_list['llm_operations_output_format'] = v
            for of_key, of_val in output_format_list.items():
                if of_key == "one_of":
                    continue
                class_name = self._to_camel_case(of_key)

                try:
                    if not of_val.get('custom', False):
                        module = importlib.import_module(self.default_module)
                    else:
                        module = importlib.import_module(self.custom_module)
                    of_class = getattr(module, class_name)
                    of_instance = of_class(**of_val)
                    processed_output = of_instance(
                                                processed_output=extraction_item,
                                                extraction_items=extraction_items,
                                                de_start_page=de_start_page,
                                                client_id=client_id,
                                                **kwargs
                                            )
                    if processed_output.success:
                        if output_format_list.get("one_of", False):
                            break
                        else:
                            extraction_item = processed_output.item
                            continue
                except (ImportError, AttributeError) as e:
                    print(f"Post-processing class {class_name} not found in module. Error: {e}")
                    raise ValueError(f"Post-processing class {class_name} not found in module. Error: {e}")

        return processed_output