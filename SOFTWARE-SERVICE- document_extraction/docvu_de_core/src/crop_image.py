import cv2
import numpy as np


class GetPaddingInfo:
    def __init__(self):
        pass

    @staticmethod
    def get_padding_in_vpos_hpos(image):
        if isinstance(image, str):
            image = cv2.imread(image)
        # Load image, grayscale, Gaussian blur, <PERSON><PERSON>'s threshold
        orig_height, orig_width, _ = image.shape
        original = image.copy()
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        blur = cv2.GaussianBlur(gray, (3, 3), 0)
        thresh = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]

        # Remove horizontal lines
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25,1))
        detected_lines = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, horizontal_kernel, iterations=1)
        contours = cv2.findContours(detected_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        contours = contours[0] if len(contours) == 2 else contours[1]
        for c in contours:
            cv2.drawContours(thresh, [c], -1, 0, -1)

        # Dilate to merge into a single contour
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2,30))
        dilate = cv2.dilate(thresh, vertical_kernel, iterations=3)

        # Find contours, sort for largest contour and extract ROI
        contours, _ = cv2.findContours(dilate, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[-2:]
        contours = sorted(contours, key=cv2.contourArea, reverse=True)[:-1]
        start_hpos = 0
        start_vpos = 0
        for c in contours:
            start_hpos, start_vpos, w, h = cv2.boundingRect(c)
            # cv2.rectangle(image, (page_start_x, page_start_y), (page_start_x + w, page_start_y + h), (36,255,12), 4)
            # cropped = original[page_start_y:page_start_y+h, page_start_x:page_start_x+w]
            break
        return [start_vpos, start_hpos]