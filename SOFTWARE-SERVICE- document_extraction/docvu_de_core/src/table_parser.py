
# Search for all the column names in the line field found or the next line
# store all the position value for the column names
# Get the line where we have the end_identifier
# Separate the data between the column names and the end identifier based on the column name position value
# Consider every line in that data as row data

# Code is written assuming the ocr is giving x,y starting from left top corner
from docvu_de_core.modules.TableExtractor import *
from docvu_de_core.utils.TextPreprocessor import *


class TableParser:
    def __init__(self):
        self.table_extractor = TableExtractor(vpos_tolerance=7,
                                              table_gap_threshold=77,
                                              string_spacing_threshold=350,
                                              min_area_threshold=20000,
                                              merge_closeby_rows=False)
        pass

    @staticmethod
    def get_data_in_roi(data, left_corner, right_corner):
        roi_data = []
        for word in data:
            cx = int(word["HPOS"]) + (int(word["WIDTH"])/2)
            cy = int(word["VPOS"]) + (int(word["HEIGHT"])/2)
            if left_corner[0] <= cx <= right_corner[0] and left_corner[1] <= cy <= right_corner[1]:
                roi_data.append(word)

        return roi_data

    @staticmethod
    # In case if the type is table check for all the columns in the same line and next line
    def check_table_key(data, tb_id, tl_id, field_info):
        current_line_list = []
        next_line_list = []

        # print(data, tb_id, tl_id)
        for text_string in data[tb_id]["TEXT_LINE"][tl_id]["STRING"]:
            current_line_list.append(text_string["text"])

        if tl_id + 1 < len(data[tb_id]["TEXT_LINE"]):
            for text_string in data[tb_id]["TEXT_LINE"][tl_id]["STRING"]:
                next_line_list.append(text_string["text"])
        else:
            if tb_id + 1 < len(data):
                for text_string in data[tb_id + 1]["TEXT_LINE"][0]["STRING"]:
                    next_line_list.append(text_string["text"])
            else:
                return False

        current_line = " ".join(current_line_list)
        next_line = " ".join(next_line_list)

        # print("current_line",current_line)
        # print("next_line",next_line)
        print(field_info["additional_info"]["column_names"])
        for col_name in field_info["additional_info"]["column_names"]:
            if col_name.lower() not in current_line.lower() and col_name.lower() not in next_line.lower():
                return False

        return True

    @staticmethod
    # convert list of words to row wise data. Used VPOS as a constant values for each row
    def get_row_wise_data(data, threshold=10):
        row_wise_data = []
        for item in data:
            if row_wise_data:
                added = False
                for idx, row_item in enumerate(row_wise_data.copy()):
                    if abs(row_item[0]-int(item["VPOS"])) < threshold:
                        row_wise_data[idx][1].append(item)
                        added = True
                        break
                if not added:
                    row_wise_data.append([int(item["VPOS"]),[item]])
            else:
                row_wise_data.append([int(item["VPOS"]),[item]])

        for i, row in enumerate(row_wise_data.copy()):
            if len(row[1]) == 1:
                row_wise_data[i] = row[1][0]
                continue
            new_row_text = {"HPOS":int(row[1][0]["HPOS"]), "VPOS":int(row[1][0]["VPOS"])}
            text = " ".join([itm["text"] for itm in row[1]])
            height = max([int(itm["HEIGHT"]) for itm in row[1]])
            width = (int(row[1][-1]["HPOS"])+int(row[1][-1]["WIDTH"])) - int(row[1][0]["HPOS"])
            new_row_text["text"] = text
            new_row_text["HEIGHT"] = height
            new_row_text["WIDTH"] = width
            row_wise_data[i] = new_row_text

        return row_wise_data

    def get_table_data(self, block_data, tb_id, found_field_data, field_info, word_level_data):
        # Considering some temp corners to get the data of the table
        temp_table_left_corner = [int(found_field_data["HPOS"]), int(found_field_data["VPOS"])]
        temp_table_right_corner = [999999, 999999]

        if field_info["end_identifier"]:
            for item in block_data:
                for block in item["TEXT_BLOCK"]:
                    for tl, text_line in enumerate(block["TEXT_LINE"]):
                        line_text = " ".join([ts["text"] for ts in text_line["STRING"]])
                        if (field_info["end_identifier"].lower() in line_text.lower()
                                and int(text_line["VPOS"]) >= temp_table_left_corner[1]):
                            temp_table_right_corner = [999999, int(text_line["VPOS"])]
                            break

        roi_data = self.get_data_in_roi(word_level_data, temp_table_left_corner, temp_table_right_corner)
        table_columns = field_info["additional_info"]["column_names"]

        # Calculating the starting position of each column
        # we will update table_columns value [text, (x,y), h]
        for col_idx, column in enumerate(table_columns.copy()):
            word_len = len(column.split())
            for i in range(len(roi_data)):
                temp_text = " ".join([ts["text"] for ts in roi_data[i:i+word_len]])
                if column.lower() in temp_text.lower():
                    if col_idx == len(table_columns)-1:
                        # Updating the right most x position by using last column data
                        temp_table_right_corner[0] = (int(roi_data[i+word_len-1]["HPOS"])
                                                      + int(roi_data[i+word_len]["WIDTH"]))
                    table_columns[col_idx] = [table_columns[col_idx],
                                              [int(roi_data[i]["HPOS"]),
                                                int(roi_data[i]["VPOS"])],
                                              int(roi_data[i]["HEIGHT"])]
                    break

        # Considering the table data left corner based on the max VPOS from column
        print(table_columns)
        corner_y = max([column[1][1]+column[2] for column in table_columns])
        table_data_left_corner = [table_columns[0][1][0], corner_y]

        table_data_by_column = {}
        for col_idx, column in enumerate(table_columns):
            left_corner = [column[1][0], corner_y]
            if col_idx == len(table_columns)-1:
                right_corner = temp_table_right_corner
            else:
                right_corner = [table_columns[col_idx+1][1][0],temp_table_right_corner[1]]
            # print(left_corner, right_corner)

            raw_data = self.get_data_in_roi(roi_data, left_corner, right_corner)
            # print(raw_data)
            # print("1"*50)
            row_wise_data = self.get_row_wise_data(raw_data)
            table_data_by_column[column[0]] = row_wise_data

        print(table_data_by_column)
        return table_data_by_column

    def extract_from_table_single(self, ext_item, page_json_data, page_no, all_ext_items):
        row_name = ext_item.get('choose_row_in_table_based_on')
        all_keys = [(id, ef['key']) for id, ef in enumerate(all_ext_items)]
        all_key_list = [k.lower().strip() for _, keys in all_keys for k in keys]
        row_ext_item = None
        for ei in all_ext_items:
            if row_name == ei['field_name']:
                row_ext_item = ei
                break
        tables = self.table_extractor.extract_tables_with_row_patterns(page_json_data)
        key_row_in_found_table = None
        table_idx = None

        if row_ext_item is not None:
            row_ext_item_result = self._extract_from_table_single(row_ext_item, tables, page_no, all_key_list)
            if row_ext_item_result:
                if row_ext_item_result[0]:
                    print(row_ext_item_result[0].get('key_row_in_found_table'))
                    table_idx, key_row_in_found_table = row_ext_item_result[0].get('key_row_in_found_table',
                                                                                   (None, None))

        return self._extract_from_table_single(ext_item, tables, page_no, all_key_list,
                                               table_idx=table_idx,
                                               key_row_in_found_table=key_row_in_found_table)


    def _extract_from_table_single(self, ext_item, tables, page_no, all_key_list,
                                   table_idx=None,
                                   key_row_in_found_table=None):
        fn = ext_item['field_name']
        ext_item = [ext_item]
        results = ext_item
        dont_extract_field_idxs = []

        found_result = False
        if tables:
            for t_idx, table in enumerate(tables):
                if table_idx is not None:
                    if t_idx != table_idx:
                        continue
                if found_result:
                    break
                # for i in range(0, 1, len(table)):
                for tr_idx in range(len(table["items"])):
                    if key_row_in_found_table is not None:
                        if tr_idx != key_row_in_found_table:
                            continue
                    if found_result:
                        break
                    current_row = table["items"][tr_idx]
                    next_row = table["items"][tr_idx + 1] if tr_idx < len(table["items"]) - 1 else None
                    for idx, th in enumerate(current_row):
                        if found_result:
                            break
                        th_element = th
                        th = th['text']
                        for jdx, extraction_field in enumerate(ext_item):
                            if not ext_item[jdx].get("search_in_tables", False):
                                continue
                            print('tr_idx0.1', fn, tr_idx)
                            if any(k.strip().lower() in th.strip().lower() for k in extraction_field['key']):
                                if ext_item[jdx].get('type') in ['date', 'amount']:
                                    same_row_value = self.extract_value_from_string(
                                        th, extraction_field['key']).strip()
                                    if any(same_row_value.startswith(c) for c in [':', '-', '.', ' ']):
                                        same_row_value = same_row_value[1:]
                                else:
                                    same_row_value = TextPreprocessor.remove_punctuation(self.extract_value_from_string(
                                        th, extraction_field['key']).strip(), tokenize=False)
                                print('same_row_value1 =', same_row_value)
                                same_row_element = th_element
                                same_row_next_col_value = current_row[idx + 1]['text'].strip() \
                                    if idx < len(current_row) - 1 else None
                                if same_row_next_col_value:
                                    if any([same_row_next_col_value.strip().startswith(ei) for ei in extraction_field.get('end_identifier', [])]):
                                        same_row_next_col_value = ''
                                same_row_next_col_element = current_row[idx + 1] \
                                    if idx < len(current_row) - 1 else None
                                print('tr_idx0.2', fn, tr_idx)
                                if next_row is not None:
                                    if (len(next_row[idx]['text'].split()) == 1 or
                                            ext_item[jdx].get("multi_line_value", False) or
                                                "WORDS" not in next_row[idx]):
                                        below_row_value = next_row[idx]['text']
                                        below_row_element = next_row[idx]
                                    else:
                                        words = next_row[idx]["WORDS"]
                                        if not words:
                                            below_row_value = next_row[idx]['text']
                                            below_row_element = next_row[idx]
                                        else:
                                            below_row_words = [words[0]]
                                            below_row_value = words[0]['text']
                                            below_row_element = words[0]
                                            for word in words[1:]:
                                                if ((below_row_words[-1]['HPOS'] + below_row_words[-1]['WIDTH'])
                                                    < word['HPOS']):
                                                    if abs(below_row_words[-1]['VPOS'] - word['VPOS']) < 8:
                                                        below_row_words.append(word)
                                                        below_row_value += word['text']
                                                        below_row_element = word['text']
                                else:
                                    below_row_value = None
                                    below_row_element = None
                                print('tr_idx0.3', fn, tr_idx)
                                # if len(same_row_value) < 2 and next_row is not None:
                                #    same_row_value = self.extract_value_from_string(next_row[idx]['text'],
                                #                                                    ext_item[jdx]['key']).strip()
                                #    same_row_element = next_row[idx]
                                print('tr_idx0.4', fn, tr_idx)
                                if len(same_row_value) < 2 and \
                                    next_row is not None and \
                                    len(next_row[idx][
                                            'text'].strip()) == 0 and same_row_next_col_value is not None and \
                                    len(same_row_next_col_value) < 2:
                                    continue
                                print('tr_idx0.5', fn, tr_idx)
                                same_row_value, same_row_element = (same_row_value, same_row_element) \
                                    if 'same_row_value' in ext_item[jdx].get('probable_place_in_tbale', []) or \
                                       len(ext_item[jdx].get('probable_place_in_tbale', [])) == 0 else (None, None)
                                same_row_next_col_value = same_row_next_col_value if 'same_row_next_col_value' in \
                                                                                     ext_item[jdx].get(
                                                                                         'probable_place_in_tbale',
                                                                                         []) or \
                                                                                     len(ext_item[jdx].get(
                                                                                         'probable_place_in_tbale',
                                                                                         [])) \
                                                                                     == 0 else None
                                same_row_next_col_element = same_row_next_col_element if 'same_row_next_col_value' in \
                                                                                         ext_item[jdx].get(
                                                                                             'probable_place_in_tbale',
                                                                                             []) or \
                                                                                         len(ext_item[jdx].get(
                                                                                             'probable_place_in_tbale',
                                                                                             [])) \
                                                                                         == 0 else None
                                below_row_value, below_row_element = (below_row_value, below_row_element) \
                                    if 'below_row_value' in ext_item[jdx].get('probable_place_in_tbale', []) or \
                                       len(ext_item[jdx].get('probable_place_in_tbale', [])) == 0 else (None, None)

                                value = ''
                                element = {}
                                print('tr_idx0.6', fn, tr_idx)
                                print('same_row_value =', same_row_value)
                                print('below_row_value =', below_row_value)
                                if (same_row_value is None or len(same_row_value) == 0) and \
                                    (same_row_next_col_value is None or len(same_row_next_col_value) == 0):
                                    value = below_row_value
                                    element = below_row_element
                                elif (below_row_value is None or len(below_row_value) == 0) and \
                                    (same_row_next_col_value is None or len(same_row_next_col_value) == 0):
                                    value = same_row_value
                                    element = same_row_element
                                elif th.endswith(':') and (same_row_next_col_value is None or len(same_row_next_col_value)) < 2:
                                    value = same_row_value
                                    element = same_row_element
                                elif below_row_value is None:
                                    if not len(same_row_value) < 2:
                                        value = same_row_value
                                        element = same_row_element
                                    else:
                                        value = same_row_next_col_value
                                        element = same_row_next_col_element
                                elif len(same_row_value.strip()) < 2:
                                    if same_row_next_col_value is not None and \
                                        any(k.lower() in same_row_next_col_value.lower().strip() for k in \
                                            all_key_list if k is not None):
                                        print(2)
                                        if any(k.lower() in below_row_value.lower().strip() for k in
                                               ext_item[jdx]['key']):
                                            print(3)
                                            value = self.extract_value_from_string(below_row_value,
                                                                                   ext_item[jdx]['key']).strip()
                                            tr_idx += 1
                                        else:
                                            print(4)
                                            value = below_row_value
                                            element = below_row_element
                                    elif len(table["items"][0]) == 2:
                                        print(5)
                                        value = same_row_next_col_value
                                        element = same_row_next_col_element
                                else:
                                    print(6, same_row_value)
                                    value = same_row_value
                                    element = same_row_element
                                if value is None or len(value.strip()) < 1:
                                    continue
                                if "additional_info" in ext_item[jdx].keys():
                                    if "starts_with" in ext_item[jdx]["additional_info"]:
                                        has_keywords = ext_item[jdx]["additional_info"]["starts_with"]
                                        if type(value) is str:
                                            value = value.strip()
                                            if len(value) > 0:
                                                if not (value[0] in has_keywords):
                                                    if not ext_item[jdx]["additional_info"].get('consider_only_starts_with', False):
                                                        if "found_val" in ext_item[jdx]["additional_info"]:
                                                            ext_item[jdx]["additional_info"]["found_val"].append(
                                                                value.strip())
                                                        else:
                                                            ext_item[jdx]["additional_info"]["found_val"] = []
                                                            ext_item[jdx]["additional_info"]["found_val"].append(
                                                                value.strip())
                                                        results[jdx] = ext_item[jdx]
                                                        continue
                                                    else:
                                                        value = None
                                key_info = current_row[idx]
                                dont_extract_field_idxs.append(jdx)
                                item = ext_item[jdx]
                                item['value'] = value
                                item["page_number"] = page_no
                                item["key_info"] = key_info
                                print('tr_idx1', tr_idx)
                                item['key_row_in_found_table'] = (t_idx, tr_idx)
                                results[jdx] = item
                                found_result = True
                                break

        return results

    @staticmethod
    def extract_value_from_string(string, keys):
        """
        Extracts a value from a string given a list of probable keys.
        Performs case-insensitive substring matching to find the key and then extracts the value following it.

        :param string: The string from which to extract the value.
        :param keys: A list of probable keys.
        :return: The extracted value or None if no key is found.
        """
        # Convert the input string to lower case for case-insensitive comparison
        lower_string = string.lower()
        for key in keys:
            # Perform case-insensitive search for each key
            key_pattern = re.compile(re.escape(key.lower()))
            match = key_pattern.search(lower_string)
            if match is not None:
                # Extract everything after the found key
                start_index = match.end()
                return string[start_index:].strip()

        # Return None if no key matches
        return ""
