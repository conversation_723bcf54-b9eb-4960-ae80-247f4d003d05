from docvu_de_core.config import *
from docvu_de_core.modules.ExtractOMRData import ExtractOMRData
from docvu_de_core.src.table_parser import *
from docvu_de_core.modules.SplitByCheckedBox import SplitByChecked<PERSON>ox
from docvu_de_core.io import OutputFormat, SearchKeyFieldsOutputFormat
from docvu_de_core.modules import SplitByBullets
import string
import re
from docvu_de_core.modules import MSTGraph
from docvu_de_core.utils import ElementUtils
from copy import deepcopy
from docvu_de_core.search.SearchKeyFieldHelper import Search<PERSON>eyFieldHelper
from docvu_de_core.search.direction_based_search.UpDirectionSearch import UpDirectionSearch
from docvu_de_core.search.direction_based_search.RightDirectionSearch import RightDirectionSearch
from docvu_de_core.search.direction_based_search.DownTillEndSearch import DownTillEndSearch
from docvu_de_core.search.direction_based_search.WholePageTextSearch import WholePageTextSearch
from docvu_de_core.search.direction_based_search.LeftDirectionSearch import LeftDirectionSearch
from docvu_de_core.search.direction_based_search.UpTillStartDirectionSearch import UpTillStartDirectionSearch
from docvu_de_core.search.direction_based_search.WholeValueDownSearch import WholeValueDownSearch
from docvu_de_core.search.direction_based_search.WholeValueDownInlineSearch import WholeValueDownInlineSearch
from docvu_de_core.search.direction_based_search.MultiPageSearch import MultiPageSearch
from docvu_de_core.search.direction_based_search.DownDirectionSearch import DownDirectionSearch
from docvu_de_core.search.direction_based_search.JustDownSearch import JustDownSearch
from docvu_de_core.custom.ExceptionSearch import ExceptionSearch
from docvu_de_core.src.table_processor import TableProcessor
from docvu_de_core.custom.LegalDescriptionSearch import LegalDescriptionSearch

class SearchKeyFields:
    def __init__(self, logger, debug=False):
        self.logger = logger
        self.debug = debug
        self.helper = SearchKeyFieldHelper(logger)
        self.updirectionsearch = UpDirectionSearch()
        self.rightdirectionsearch = RightDirectionSearch()
        self.downtillendsearch = DownTillEndSearch()
        self.wholepagetextsearch = WholePageTextSearch()
        self.leftdirectionsearch = LeftDirectionSearch()
        self.uptillstartdirectionsearch = UpTillStartDirectionSearch()
        self.wholevaluedownsearch = WholeValueDownSearch()
        self.wholevaluedowninlinesearch = WholeValueDownInlineSearch()
        self.multipagesearch = MultiPageSearch()
        self.downdirectionsearch = DownDirectionSearch()
        self.justdownsearch = JustDownSearch()
        self.table_utils = TableParser()
        self.exception_search = ExceptionSearch()
        self.omr_utils = ExtractOMRData()
        self.table_processor = TableProcessor(logger)
        self.legal_description_search = LegalDescriptionSearch()
        


    def get_value_in_same_block(self, block_data, tl_id, ts_id, found_field_data, field_info, field_key):
        possible_answer_list = []
        secondary_possible_answer_list = []
        consider_data = False
        found_value = False
        # Continue searching the data from the place where key was found
        for block in block_data:
            for tl, text_line in enumerate(block["TEXT_LINE"]):
                for ts, text_string in enumerate(text_line["STRING"]):
                    # for the value consider everything in the line.
                    if (found_field_data["text"].lower() in text_string[
                        "text"].lower()  # == found_field_data["text"].lower()
                        and tl == tl_id and ts == ts_id):
                        consider_data = True
                        continue
                    if consider_data:
                        if self.helper.process_utils.get_match(field_info["end_identifier"], text_string["text"]):
                            identifier_found_index = (
                                self.helper.process_utils.find_index_using_identifiers(
                                    field_info["end_identifier"], text_string["text"]))
                            # Check if there is any text before end_identifier
                            if identifier_found_index > 5:
                                if text_string:
                                    possible_answer_list.append(text_string)
                                    secondary_possible_answer_list.append(text_string)
                            found_value = True
                            break
                        if field_info["direction"] == "down":
                            if ("additional_info" in field_info.keys() and
                                "consider_complete_line" in field_info["additional_info"].keys()):
                                if (field_info["additional_info"]["consider_complete_line"] or
                                    len(possible_answer_list) >= field_info["additional_info"]["nth_line"]):
                                    if text_string:
                                        possible_answer_list.append(text_string)
                                    continue

                            if self.helper.dist_utils.on_same_vertical_line(found_field_data, text_string, 40):
                                if text_string:
                                    possible_answer_list.append(text_string)
                            if not field_info["multi_line_value"]:
                                if not self.helper.dist_utils.on_same_horizontal_line(found_field_data, text_string):
                                    if text_string:
                                        secondary_possible_answer_list.append(text_string)
                            elif field_info.get('additional_info', {}).get('consider_complete_line', False):
                                if not self.helper.dist_utils.on_same_horizontal_line(found_field_data, text_string):
                                    if text_string:
                                        secondary_possible_answer_list.append(text_string)

                        elif field_info["direction"] == "right" and not field_info["multi_line_value"]:
                            if self.helper.dist_utils.on_same_horizontal_line(found_field_data, text_string):
                                # ignore answer which has large space from the field
                                if self.helper.dist_utils.is_value_horizontally_near(found_field_data, text_string):
                                    if text_string:
                                        possible_answer_list.append(text_string)
                                    found_value = True
                        elif field_info["direction"] == "right" and field_info["multi_line_value"]:
                            # Check if the value is on the same line
                            if self.helper.dist_utils.on_same_horizontal_line(found_field_data, text_string):
                                if text_string:
                                    possible_answer_list.append(text_string)
                            # If we got some value in same line start searching for answers in down direction
                            elif possible_answer_list:
                                if self.helper.dist_utils.on_same_vertical_line(found_field_data, possible_answer_list[0]):
                                    if text_string:
                                        possible_answer_list.append(text_string)
                        else:
                            pass
                if found_value:
                    break
            if found_value:
                break
        if self.debug:
            print("possible_answer_list", possible_answer_list)
        if self.debug:
            print("secondary_possible_answer_list", secondary_possible_answer_list)
        if possible_answer_list or secondary_possible_answer_list:
            # if secondary_possible_answer_list:
            if field_info["direction"] == "down":
                # In case of multi line value enabled consider all possible answers
                if field_info["multi_line_value"]:
                    if ("additional_info" in field_info.keys() and
                        field_info.get('additional_info', {}).get('consider_complete_line', False)):
                        try:
                            return [pa for pa in possible_answer_list[field_info["additional_info"]["nth_line"] - 1:]
                                    if pa not in secondary_possible_answer_list] + secondary_possible_answer_list
                        except KeyError:
                            return [pa for pa in possible_answer_list if
                                    pa not in secondary_possible_answer_list] + secondary_possible_answer_list

                    # In case where if we hve nth_line also then truncate possible answers
                    try:
                        # return possible_answer_list[field_info["additional_info"]["nth_line"] - 1:]
                        elements = possible_answer_list[field_info["additional_info"]["nth_line"] - 1:]
                        bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(elements)
                        return SearchKeyFieldsOutputFormat(value=self.helper.process_utils.get_string_from_elements(elements),
                                                           search_next_page=False,
                                                           success=True,
                                                           elements=elements,
                                                           is_table=False,
                                                           line_number=line_number,
                                                           bbox=bbox,
                                                           field_key=field_key)
                    except KeyError:
                        pass
                    # return possible_answer_list
                    elements = possible_answer_list
                    bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(elements)
                    return SearchKeyFieldsOutputFormat(value=self.helper.process_utils.get_string_from_elements(elements),
                                                       search_next_page=False,
                                                       success=True,
                                                       elements=elements,
                                                       is_table=False,
                                                       line_number=line_number,
                                                       bbox=bbox,
                                                       field_key=field_key)
                # If we have to find only one line and that is the fist line from the key
                if (not field_info["multi_line_value"] and "additional_info" in field_info.keys() and
                    "nth_line" in field_info["additional_info"].keys() and
                    field_info["additional_info"]["nth_line"] == 1 and secondary_possible_answer_list):
                    # Sorting the all the value before to get the nearest value
                    sorted_answer_list = sorted(secondary_possible_answer_list,
                                                key=lambda x:
                                                self.helper.dist_utils.get_vertical_distance(found_field_data, x))
                    value = sorted_answer_list[0]
                    if field_info["end_identifier"]:
                        value_index = self.helper.process_utils.find_index_using_identifiers(
                            field_info["end_identifier"], value["text"])
                        if value_index != -1:
                            value["text"] = value["text"][:value_index]
                            # return value
                            return SearchKeyFieldsOutputFormat(value=value['text'],
                                                               search_next_page=False,
                                                               success=True,
                                                               elements=value,
                                                               is_table=False,
                                                               line_number=value['LINE_NUMBER'],
                                                               bbox=[value['HPOS'], value['VPOS'], value['END_HPOS'],
                                                                     value['END_VPOS']],
                                                               field_key=field_key)
                    # return value
                    return SearchKeyFieldsOutputFormat(value=value['text'],
                                                       search_next_page=False,
                                                       success=True,
                                                       elements=value,
                                                       is_table=False,
                                                       line_number=value['LINE_NUMBER'],
                                                       bbox=[value['HPOS'], value['VPOS'], value['END_HPOS'],
                                                             value['END_VPOS']],
                                                       field_key=field_key)
                # From the possible values consider nth index value (n-1 ideally)
                if ("additional_info" in field_info.keys() and "nth_line" in field_info["additional_info"].keys()
                    and len(possible_answer_list) >= field_info["additional_info"]["nth_line"]):
                    value = possible_answer_list[field_info["additional_info"]["nth_line"] - 1]
                    # If end_identifier exists truncate the text value until identifier
                    if field_info["end_identifier"]:
                        value_index = self.helper.process_utils.find_index_using_identifiers(
                            field_info["end_identifier"], value["text"])
                        if value_index != -1:
                            value["text"] = value["text"][:value_index]
                    # return value
                    return SearchKeyFieldsOutputFormat(value=value['text'],
                                                       search_next_page=False,
                                                       success=True,
                                                       elements=value,
                                                       is_table=False,
                                                       line_number=value['LINE_NUMBER'],
                                                       bbox=[value['HPOS'], value['VPOS'], value['END_HPOS'],
                                                             value['END_VPOS']],
                                                       field_key=field_key)
            if field_info["direction"] == "right":
                answers = []
                # Check the index of end identifier and crop the text from before the identifier and return
                for possible_answer in possible_answer_list:
                    end_identifier_index = (
                        self.helper.process_utils.find_index_using_identifiers(
                            field_info["end_identifier"], possible_answer["text"]))

                    if field_info["end_identifier"] and end_identifier_index != -1:
                        possible_answer["text"] = possible_answer["text"][:end_identifier_index]
                        answers.append(possible_answer)
                        break
                    else:
                        answers.append(possible_answer)
                # return answers
                elements = answers
                bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(elements)
                value_str = self.helper.process_utils.get_string_from_elements(elements)
                return SearchKeyFieldsOutputFormat(value=value_str,
                                                   search_next_page=False,
                                                   success=True,
                                                   elements=elements,
                                                   is_table=False,
                                                   line_number=line_number,
                                                   bbox=bbox,
                                                   field_key=field_key)
        # return None
        return SearchKeyFieldsOutputFormat(value=None,
                                           search_next_page=False,
                                           success=False,
                                           elements=None,
                                           is_table=False,
                                           line_number=None,
                                           bbox=None,
                                           field_key=field_key)

    def process_same_page_exceptions(self, field_info, whole_page_data, search_after, page, footer_starts=None):
        item = field_info.copy()
        consider_data = False
        possible_answer_list = []
        secondary_possible_answer_list = []
        temp_value = None
        found_val = False
        for ts_id, text_string in enumerate(whole_page_data):
            if (not consider_data and (field_info["start_identifier"][0] in text_string["text"])
                    and text_string["VPOS"] > search_after):
                consider_data = True
                if ("probable_place" in field_info.keys() and
                        field_info["probable_place"].lower() == 'individual'):
                    check_if_individual = (
                        self.helper.process_utils.check_if_key_found_is_individual(
                            [text_string["text"]], field_info["key"])[0])
                    print(f"Got Value of check_if_individual = {check_if_individual}")
                    if not check_if_individual:
                        consider_data = False
            if consider_data:
                if temp_value is None and "part" in text_string["text"].lower():
                    temp_value = text_string["text"]
                    found_val = True
                if found_val:
                    item["value"] = temp_value
                    item["page"] = page
                    found_val = False
                else:
                    if text_string:
                        # append the current text to secondary_possible_answer_list if it's before the footer
                        if footer_starts and footer_starts > text_string['VPOS']:
                            secondary_possible_answer_list.append(text_string)
            else:
                if text_string:
                    # append the current text to possible_answer_list if it's before the footer
                    if footer_starts and footer_starts > text_string['VPOS']:
                        possible_answer_list.append(text_string)

        if self.debug:
            print("possible_answer_list: ", possible_answer_list)
            print("secondary_possible_answer_list: ", secondary_possible_answer_list)
        return possible_answer_list, item, secondary_possible_answer_list


    def is_text_header(self, whole_page_data, found_tb_id, found_tl_id, found_ts_id):
        for i, item in enumerate(whole_page_data):
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    for ts_id, text_string in enumerate(text_line["STRING"]):
                        if found_tb_id == tb_id and found_tl_id == tl_id and found_ts_id == ts_id:
                            if self.helper.is_in_header(text_string):
                                return True
                            else:
                                return False

    def get_data_continue_in_next_page(self, whole_page_data, prev_page_json_data, field_info, field_key):
        possible_answer_list = []
        stop_searching = False
        consider_data = False
        found_end = False
        similarity_score_leg_desc = self.legal_description_search.process_pages(prev_page_json_data, whole_page_data)
        search_key_exp_prev = self.exception_search.get_prev_page_bullet_point(prev_page_json_data)
        search_key_exp_curr = self.exception_search.get_curr_page_bullet_point(whole_page_data)

        ### First try: Continue searching using search_key and search_after
        for i, item in enumerate(whole_page_data):
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    for ts_id, text_string in enumerate(text_line["STRING"]):
                        print("FIELD_INFO:::",field_info)
                        if "additional_info" in field_info:
                            if (not consider_data and "search_key" in field_info["additional_info"] and
                                    self.helper.process_utils.get_match(field_info["additional_info"]["search_key"],
                                                                        text_string["text"])
                                    and self.helper.is_in_header(text_string)):
                                consider_data = True
                                if "probable_place" in field_info and field_info[
                                    "probable_place"].lower() == "individual":
                                    key_found_individual = self.helper.process_utils.check_if_key_found_is_individual([text_string["text"]], field_info["key"])[0]
                                    if not key_found_individual:
                                        consider_data = False  # Only set consider_data to True if key is found individual
                            if (not consider_data and "search_after" in field_info["additional_info"] and
                                    self.helper.process_utils.get_match(field_info["additional_info"]["search_after"],
                                                                        text_string["text"])
                                    and self.helper.is_in_header(text_string)):
                                consider_data = True
                                continue
                        if consider_data:
                            if self.helper.process_utils.get_match(field_info["end_identifier"], text_string["text"]):
                                found_end = True
                            elif field_info.get("exclude_footer") and self.helper.is_in_footer(text_string):
                                found_end = True
                            else:
                                if text_string:
                                    possible_answer_list.append(text_string)
                        if found_end:
                            break
                    if found_end:
                        break
                if found_end:
                    break
            if found_end:
                break

        ### Second try: if no key found, fallback to other methods(bullet_point_match/sentence_transformer/un-structured)
        if not consider_data:
            # Continue searching the data from the place where key was found
            for i, item in enumerate(whole_page_data):
                for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                    for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                        for ts_id, text_string in enumerate(text_line["STRING"]):
                            print("FIELD_INFO:::",field_info)
                            if "additional_info" in field_info:

                                ### Continue without identifier un-structure format
                                if (not consider_data and "continue_to_next_page_without_identifier" in field_info[
                                    "additional_info"] and field_info["additional_info"].get(
                                    'continue_to_next_page_without_identifier')):
                                    if self.helper.is_on_page_boundary(text_string):
                                        continue
                                    consider_data = True

                            ### Sentence transformer match
                            if not consider_data and ("use_sentence_transformer" in field_info) and field_info.get(
                                    'use_sentence_transformer'):
                                if self.helper.is_on_page_boundary(text_string):
                                    continue
                                if similarity_score_leg_desc > 0.85:
                                    consider_data = True

                            ### Bullet point matching
                            if (not consider_data)  and ("use_bullet_point_match" in field_info) and field_info.get(
                                    'use_bullet_point_match'):
                                if self.helper.is_on_page_boundary(text_string):
                                    continue
                                if search_key_exp_prev is not None and search_key_exp_curr is not None:
                                    if (search_key_exp_prev + 1) == search_key_exp_curr:
                                        consider_data = True

                            if consider_data:
                                ## Checking for end_identifier is present in individual position
                                if "individual_end_identifier" in field_info.keys() and field_info["individual_end_identifier"]:
                                    found_end = self.helper.process_utils.is_identifier_at_start(field_info["end_identifier"], text_string["text"])
                                    ## Checking individual_end_identifier for hpos based position
                                    if found_end and ("hpos_aligned_end_identifier" in field_info.keys() and field_info["hpos_aligned_end_identifier"]):
                                        hpos_aligned_end_identifier = self.helper.process_utils.individual_hpos_aligned_identifier(text_string)
                                        found_end = hpos_aligned_end_identifier
                                ## Checking end_identifier is present in text string
                                else:
                                    found_end = self.helper.process_utils.get_match(field_info["end_identifier"],text_string["text"])
                                if field_info.get("terminating_keyword") and self.helper.process_utils.get_match(
                                        field_info["terminating_keyword"], text_string["text"]):
                                    f_index = self.helper.process_utils.find_index_using_identifiers(
                                        field_info["terminating_keyword"], text_string["text"])
                                    text_string["text"] = text_string["text"][:f_index]
                                if found_end:
                                    if field_info.get("additional_info", {}).get("stop_on_end_identifier"):
                                            stop_searching = True
                                    found_end = True
                                elif field_info.get("exclude_footer") and self.helper.is_in_footer(text_string):
                                    found_end = True
                                else:
                                    if text_string:
                                        possible_answer_list.append(text_string)
                            if found_end:
                                break
                        if found_end:
                            break
                    if found_end:
                        break
                if found_end:
                    break

        if self.debug:
            print("Possible Next Page Value: {}".format(possible_answer_list))
        if possible_answer_list:
            field_key = field_key or {}
            ##Making continue search in next page false if end_identifier found
            if stop_searching:
                field_key["to_continue_next_page"] = False
            elif (
                    "multi_page_value" in field_info
                    and field_info.get("multi_page_value")
                    and "search_key_in_all_pages" not in field_info
                    and field_info.get("type") != "checkbox"
            ):
                # Checking this condition to continue searching across multiple pages
                field_key["to_continue_next_page"] = True
        else:
            return OutputFormat(value=[],
                            search_next_page=False,
                            success=True,
                            elements=[],
                            is_table=True,
                            line_number=[],
                            bbox=[],
                            is_text_merged = False,
                            field_key = field_key)
        sorted_list = self.helper.process_utils.rearrange_based_on_vpos_hpos(possible_answer_list)
        new_list = []
        bboxes = []
        line_numbers = []
        count = 0
        is_text_merged = False
        for index, slist in enumerate(sorted_list):
            if index == 0:
                new_list.append(slist)
            else:
                curr_list_vpos = int(slist["VPOS"])
                prev_list_vpos = int(new_list[count]["VPOS"])
                if curr_list_vpos - prev_list_vpos <= 15:
                    new_list[count]["text"] += " " + slist["text"]
                    new_list[count]["HPOS"] = min(new_list[count]["HPOS"], slist["HPOS"])
                    new_list[count]["VPOS"] = min(new_list[count]["VPOS"], slist["VPOS"])
                    new_list[count]["END_HPOS"] = max(new_list[count]["END_HPOS"], slist["END_HPOS"])
                    new_list[count]["END_VPOS"] = max(new_list[count]["END_VPOS"], slist["END_VPOS"])
                    is_text_merged = True
                else:
                    start_vpos = float('-inf')
                    start_hpos = float('-inf')
                    end_vpos = float('inf')
                    end_hpos = float('inf')
                    line_no = float('inf')
                    for line in [new_list[-1]]:
                        if self.debug:
                            print('@@@@@@@@@@@')
                            print(line)
                        start_vpos = min(start_vpos, line['VPOS'])
                        start_hpos = min(start_hpos, line['HPOS'])
                        end_vpos = max(end_vpos, line['END_VPOS'])
                        end_hpos = max(end_hpos, line['END_HPOS'])
                        line_no = min(line_no, line['LINE_NUMBER'])
                    bboxes.append((start_vpos, start_hpos, end_vpos, end_hpos))
                    line_numbers.append(line_no)
                    new_list.append(slist)
                    count += 1
        return OutputFormat(value=self.helper.process_utils.get_string_from_elements(new_list),
                            search_next_page=False,
                            success=True,
                            elements=new_list,
                            is_table=True,
                            line_number=line_numbers,
                            bbox=bboxes,
                            field_key=field_key,
                            is_text_merged = is_text_merged)

    def get_key_continue_in_next_page(self, whole_page_data, field_info):
        possible_answer_list = []
        consider_data = False
        found_end = False
        # Continue searching the data from the place where key was found
        for i, item in enumerate(whole_page_data):
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    for ts_id, text_string in enumerate(text_line["STRING"]):
                        if (not consider_data and self.helper.process_utils.get_match(
                                field_info["additional_info"]["search_key"], text_string["text"])):
                            consider_data = True
                        if (not consider_data and self.helper.process_utils.get_match(
                                field_info["additional_info"]["search_after"], text_string["text"])):
                            consider_data = True
                            continue
                        if consider_data:
                            if self.helper.process_utils.get_match(field_info["end_identifier"], text_string["text"]):
                                found_end = True
                            else:
                                if text_string:
                                    possible_answer_list.append(text_string)
                        if found_end:
                            break
                    if found_end:
                        break
                if found_end:
                    break
            if found_end:
                break
        if self.debug:
            print("Possible Next Page Value: {}".format(possible_answer_list))
        if not possible_answer_list:
            return [], False
        sorted_list = self.helper.process_utils.rearrange_based_on_vpos_hpos(possible_answer_list)
        new_list = []
        count = 0
        is_text_merged = False
        for index, slist in enumerate(sorted_list):
            if index == 0:
                new_list.append(slist)
            else:
                curr_list_vpos = int(slist["VPOS"])
                prev_list_vpos = int(new_list[count]["VPOS"])
                if curr_list_vpos - prev_list_vpos <= 15:
                    new_list[count]["text"] += " " + slist["text"]
                    is_text_merged = True
                else:
                    new_list.append(slist)
                    count += 1
        return new_list, is_text_merged


    @staticmethod
    def get_has_keyword_text(items, field_value):
        split_values = field_value.split(',')
        for key in items["key"]:
            for fs1 in split_values:
                if key.lower() in fs1.lower():
                    fsplit = fs1.split(' ')[-2:]
                    return " ".join(fs for fs in fsplit)
        return field_value

    @staticmethod
    def get_contains_text(items, field_value):
        for key in items["key"]:
            match = re.search(key, field_value, flags=re.IGNORECASE)
            if match:
                return match.group(0)
        return None

    @staticmethod
    def extract_until_last_digit(s):
        # Check if the string contains any digits
        if not any(char.isdigit() for char in s):
            return s  # Return the original string if there are no digits
        # Find all segments of the string that consist of digits and non-digit characters
        parts = re.findall(r'\d+|[^\d]+', s)

        # Determine the index of the last digit sequence
        last_digit_idx = next((i for i, part in reversed(list(enumerate(parts))) if part.isdigit()), None)

        if last_digit_idx is not None:

            # Extract the string up to and including the last digit sequence
            result = ''.join(parts[:last_digit_idx+1])

            # Check if the rest of the string contains only letters or spaces
            if all(c.isalpha() or c.isspace() for c in s[len(result):]):
                return result
        return result  # return original string if no suitable pattern is found or condition is not met

    @staticmethod
    def add_loan_book_page_number(sub_items):
        loan_book_number_val = None
        loan_page_number_val = None

        for key, val in sub_items.items():
            if key == 'Loan_Book_Number':
                if val is not None and '/' in val:
                    numbers = val.split('/')
                    if len(numbers) == 2 and all(part.isdigit() for part in numbers):
                        loan_book_number_val = numbers[0]
                        loan_page_number_val = numbers[1]
                else:
                    loan_book_number_val = val
            if key == 'Loan_Page_Number':
                if val is not None and '/' in val:
                    numbers = val.split('/')
                    if len(numbers) == 2 and all(part.isdigit() for part in numbers):
                        loan_book_number_val = numbers[0]
                        loan_page_number_val = numbers[1]
                else:
                    loan_page_number_val = val

        if loan_book_number_val is not None and loan_page_number_val is not None:
            loan_book_page_number_val = f"{loan_book_number_val}/{loan_page_number_val}"
            sub_items['Loan_Book_Number'] = loan_book_number_val
            sub_items['Loan_Page_Number'] = loan_page_number_val
        elif loan_book_number_val is not None and loan_page_number_val is None:
            loan_book_page_number_val = loan_book_number_val
            sub_items['Loan_Book_Number'] = loan_book_number_val
        elif loan_page_number_val is not None and loan_book_number_val is None:
            loan_book_page_number_val = loan_page_number_val
            sub_items['Loan_Page_Number'] = loan_page_number_val
        else:
            loan_book_page_number_val = None

        sub_items['Loan_Book_Page_Number'] = loan_book_page_number_val

        return sub_items

    def get_sub_keys_values(self, answer_value, extraction_item, add_value=True, img_path=None):
        sub_items = {}
        original_answer = answer_value.copy() if answer_value else answer_value
        
        if type(answer_value) is list:
            answer_value = " ".join(v["text"] for v in answer_value)
            
            for items in extraction_item["sub_keys"]:
                if items['type'] in ["checkbox"] and img_path:
                    if 'additional_info' in items.keys() and 'field_options' in items[
                        'additional_info'].keys() and 'zone_coords' in items['additional_info'].keys():
                        if original_answer:
                            checked_field_dict = self.omr_utils.get_checkbox_value_from_image(img_path, original_answer, items)
                            
                            if checked_field_dict:
                                values_list = [item["value"] for item in checked_field_dict.values()]
                                bbox_list = [item["bbox"] for item in checked_field_dict.values()]

                                print("Checked Field Values:", values_list)
                                print("Checked Field Bounding Boxes:", bbox_list)

                                success = True

                                # Harsh - May 7 2025
                                # This change we have done for post close - closing disclosure document where we have field
                                # which needs to be extracted based on OMR checkbox ticked
                                if "get_checked_box_amount" in items.keys():
                                    all_required_cb_ticked = all(elem in values_list for elem in items["get_checked_box_amount"])
                                    if all_required_cb_ticked:
                                        sub_items[items["field_name"]] = answer_value
                                    else:
                                        sub_items[items["field_name"]] = None

                                else:
                                    sub_items[items["field_name"]] = values_list[0]

                                # Harsh - May 7 2025
                                #we have commented below line.
                                # sub_items[items["field_name"]] = values_list[0]
                                items["field_value_coordinates"] = bbox_list[0]  # Bounding Box Information
  
                else:
                    raw_key_str = items["key"]
                    key_str = [self.helper.process_utils.pre_process_text(k) for k in items["key"]]
                    text_line_string = answer_value
                    found_str = self.helper.process_utils.pre_process_text(text_line_string)
                    found_match = [k in found_str for k in key_str]

                    raw_key_found_similarity_ratio = 0.0
                    if "use_match" in items.keys() and items["use_match"].lower() == "fuzzy":
                        raw_key_found_similarity_ratio = self.helper.process_utils.similar(raw_key_str, text_line_string)

                    if any(found_match):
                        if self.debug:
                            print("Found Match: {}\nItem Keys = {}".format(found_match, items["key"]))
                        
                        keys_matched = [v for k, v in enumerate(items["key"]) if found_match[k]]

                        if self.debug:
                            print("Keys Matched: {}".format(keys_matched))

                        values = []
                        bbox_values = []  # Store bounding boxes

                        for key_to_search in keys_matched:
                            break_loop = False
                            indexes = [m.start() for m in
                                    re.finditer(re.escape(key_to_search.strip()), text_line_string, re.IGNORECASE)]
                            items["key"] = [key_to_search.lower().strip()]
                            if self.debug:
                                print(f"\nSearching for {key_to_search}\nAT Index={indexes}\nString={text_line_string}")
                            for ind in indexes:
                                continue_loop = False
                                text_line_part = text_line_string[ind:]
                                
                                if self.debug:
                                    print(f"Found Subkey Match: {items['field_name']} at Index {ind}, String: {text_line_part}")

                                temp_field_value, start_idx, end_idx = self.helper.process_utils.get_remaining_text(
                                    text_line_part, items, use_start_identifier=True
                                )

                                if self.debug:
                                    print("Here2: temp_field_value = ", temp_field_value)

                                if temp_field_value is not None:
                                    bbox = self.get_bounding_box(original_answer, temp_field_value)
                                    bbox_values.append(bbox)  # Store the bounding box

                                    if items["type"] == "date":
                                        date_val = self.helper.general_utils.get_dates_in_text(temp_field_value)
                                        values.append(date_val)
                                        if self.debug:
                                            print("Date Extracted:", date_val)
                                    
                                    elif items["type"] == "amount":
                                        if "$" not in temp_field_value:
                                            if len(indexes) == 1 and "$" in text_line_part:
                                                dollar_index = text_line_part.index("$")
                                                if dollar_index:
                                                    dollar_string = text_line_part[dollar_index:]
                                                    temp_field_value, _, _ = (
                                                        self.helper.process_utils.get_remaining_text(
                                                            dollar_string, items, use_start_identifier=True))
                                                    if temp_field_value is None:
                                                        continue_loop = True
                                                else:
                                                    continue_loop = True
                                            else:
                                                continue_loop = True
                                        if continue_loop:
                                            continue

                                        temp_field_value = temp_field_value.replace("$ ", "$").strip()
                                        amount_val = self.helper.general_utils.get_amount_in_text(temp_field_value)
                                        amount_val = "$" + amount_val.strip(string.punctuation)
                                        values.append(amount_val)
                                        if self.debug:
                                            print("Amount Extracted:", amount_val)

                                    elif items["type"] == "has_keyword":
                                        val = self.get_has_keyword_text(items, text_line_part)
                                        values.append(val)
                                        if self.debug:
                                            print("Has Keyword:", val)

                                    elif items["type"] == "contains":
                                        val = self.get_contains_text(items, text_line_part)
                                        values.append(val)
                                        if self.debug:
                                            print("Contains:", val)

                                    elif items["type"] == "county":
                                        val = self.helper.process_utils.get_county_value(text_line_string, items)
                                        values.append(val)
                                        if self.debug:
                                            print("County Extracted:", val)

                                    else:
                                        if "include_key" in items.keys():
                                            if items["include_key"]:
                                                temp_field_value = items["key"][0] + " " + temp_field_value
                                                temp_field_value = temp_field_value.strip()
                                        elif "include_start_identifier" in items.keys():
                                            if items["include_start_identifier"]:
                                                temp_field_value = items["start_identifier"][0] + " " + temp_field_value
                                                temp_field_value = temp_field_value.strip()
                                        values.append(temp_field_value)
                                        if self.debug:
                                            print("Extracted Value:", temp_field_value)
                                if break_loop:
                                    break
                            if break_loop:
                                break
                        if self.debug:
                            print("\n Field_name = {}--------Values = {}".format(items["field_name"], values))

                        values = [v for v in values if v]
                        bbox_values = [b for b in bbox_values if b]

                        sub_items[items["field_name"]] = values[0] if values else None
                        items["field_value_coordinates"] = bbox_values[0] if bbox_values else None

                        if items["field_name"] == 'Loan_Document_Number':
                            if values:
                                for value in values:
                                    if isinstance(value, str):
                                        if value[0].isdigit() or (
                                                value[0].isalpha() and len(value) >= 2 and value[1].isdigit()):
                                            sub_items[items["field_name"]] = self.extract_until_last_digit(value)
                                            break
                                else:
                                    sub_items[items["field_name"]] = self.extract_until_last_digit(values[-1])
                            else:
                                sub_items[items["field_name"]] = None
                            # Modified end
                        else:
                            sub_items[items["field_name"]] = values[0] if values else None
        
        if add_value:
            if ("additional_info" in extraction_item.keys() and "split_by_bullets" in
                extraction_item["additional_info"].keys() and
                extraction_item["additional_info"]["split_by_bullets"]):
                sub_items[extraction_item["field_name"]] = original_answer
            else:
                sub_items[extraction_item["field_name"]] = answer_value

        self.add_loan_book_page_number(sub_items)
        
        return sub_items

    def get_bounding_box(self, data, target_text):
        """
        Extracts the bounding box coordinates of a target text from OCR data.

        Parameters:
        - data (list): A list of dictionaries containing OCR-extracted text and word positions.
        - target_text (str): The text to locate within the OCR data.

        Returns:
        - dict: A dictionary containing the bounding box coordinates:
            - 'x' (int): x-coordinate of the top-left corner.
            - 'y' (int): y-coordinate of the top-left corner.
            - 'width' (int): Width of the bounding box.
            - 'height' (int): Height of the bounding box.
        - None: If the target text is not found in the OCR data.
        """
        # Split the target text into individual words
        words = target_text.split()
        # Iterate over the OCR data to find a match
        for item in data:
            # If the entire text matches, return its bounding box directly
            if item['text'] == target_text:
                return {
                    'x': item['HPOS'],    # Horizontal position (left)
                    'y': item['VPOS'],    # Vertical position (top)
                    'width': item['WIDTH'],  # Box width
                    'height': item['HEIGHT']  # Box height
                }
            # If exact match is not found, check for word-by-word matching
            word_positions = []
            for word in item.get('WORDS', []):  # Get word-level OCR data
                if word['text'] in words:  # Check if the word is part of target_text
                    word_positions.append(word)
            # If all words of the target text are found, compute the bounding box
            if len(word_positions) == len(words):
                hpos = word_positions[0]['HPOS']  # Leftmost position (first word)
                vpos = word_positions[0]['VPOS']  # Top position (first word)
                # Compute the rightmost boundary and width
                end_hpos = word_positions[-1]['HPOS'] + word_positions[-1]['WIDTH']
                width = end_hpos - hpos + 15  # Adding padding for better coverage
                # Compute the maximum height among the matched words
                height = max(w['HEIGHT'] for w in word_positions)
                return {
                    'x': hpos,
                    'y': vpos,
                    'width': width,
                    'height': height
                }
        # Return None if the target text is not found
        return None

    def check_alternate_locations(self, extraction_item, refined_page_json_data, prev_page_json_data,
                                  page_word_json_data, img_path, page_number, continue_search=False):
        return self.search_key_field(extraction_item, refined_page_json_data,
                                     prev_page_json_data, page_word_json_data, img_path, page_number, continue_search)

    def search_key_field(self, extraction_item, refined_page_json_data, prev_page_json_data, page_word_json_data,
                         img_path, page_number, continue_search, field_key=None):
        refined_page_json_data = deepcopy(refined_page_json_data)
        if continue_search and not \
                ("search_key_in_all_pages" in extraction_item.keys() and extraction_item["search_key_in_all_pages"]):
            if extraction_item["type"] in ["checkbox"]:
                if self.helper.cb is not None:
                    output = self.helper.cb.get_checkbox_value(
                        refined_page_json_data, extraction_item, img_path, continue_search)
                    if self.debug:
                        print("Answer:", output.value)
                    self.helper.cb = None
                    return SearchKeyFieldsOutputFormat(value=output.value,
                                        search_next_page=output.search_next_page,
                                        success=True,
                                        elements=output.elements,
                                        is_table=True,
                                        line_number=output.line_number,
                                        bbox=output.bbox,
                                        field_key=field_key)
                else:
                    return SearchKeyFieldsOutputFormat(value=None,
                                                       search_next_page=None,
                                                       success=True,
                                                       elements=None,
                                                       is_table=True,
                                                       line_number=None,
                                                       bbox=None,
                                                       field_key=None)

            output = self.get_data_continue_in_next_page(refined_page_json_data, prev_page_json_data, extraction_item, field_key)

            if ("additional_info" in extraction_item.keys() and "split_by_bullets" in extraction_item["additional_info"].keys()
                    and extraction_item["additional_info"]["split_by_bullets"]):
                # res_val = self.post_process_text(result_item)
                if self.debug:
                    print('*' * 20)
                    # print(res_val)
                    print(output.elements)
                    print('*' * 20)
                if isinstance(output.elements, dict):
                    for _, value in output.elements.items():
                        output.elements = value
                elif isinstance(output.elements, list) and len(output.elements) == 1 and \
                        isinstance(output.elements[0], dict): ## TODO
                    for _, value in output.elements[0].items():
                        output.elements = value
                output = SplitByBullets.split_by_bullets(output.elements)

            return SearchKeyFieldsOutputFormat(value=output.value,
                                    search_next_page=output.search_next_page,
                                    success=output.success,
                                    elements=output.elements,
                                    is_table=True,
                                    line_number=output.line_number,
                                    bbox=output.bbox,
                                    field_key=output.field_key)
        block_text_list = []
        up_block = []
        include_footer = False
        in_footer = False
        # Iterate through all the data until a match to the required field key is found
        for block_index, item in enumerate(refined_page_json_data):
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                up_block.append(item["TEXT_BLOCK"])
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    # sorted_block_vpos = (sorted(blocks["TEXT_LINE"], key=lambda d: int(d['VPOS'])))
                    # sorted_block_vpos = self.helper.process_utils.rearrange_based_on_vpos_hpos(sorted_block_vpos)
                    sorted_block_vpos = blocks["TEXT_LINE"]
                    same_line_str = []
                    same_line_element = []
                    for same_line in sorted_block_vpos:
                        if in_footer and ("additional_info" in extraction_item.keys()
                                          and "find_in_zone" in extraction_item["additional_info"].keys()
                                          and extraction_item["additional_info"]["find_in_zone"]):
                            left_zone, right_zone = self.helper.dist_utils.get_zones(same_line["STRING"])
                            # print("Left ZOne = {}\nRight Zone = {}".format(left_zone, right_zone))
                            string = " ".join(v["text"] for v in left_zone)
                            same_line_str.append(string)
                            same_line_element.extend(left_zone)
                        else:
                            string = " ".join(v["text"] for v in same_line["STRING"])
                            same_line_str.append(string)
                            same_line_element.extend(same_line)
                    text_line_string = "\n".join(v for v in same_line_str)
                    text_line_string_OLD = "\n ".join(k for k in [v['text'] for k in
                                                              sorted_block_vpos for v in k["STRING"]])
                    for ts_id, text_string in enumerate(text_line["STRING"]):
                        # print("text_string: ", text_string["text"])
                        if ("additional_info" in extraction_item.keys()
                                and "alternate_locations" in extraction_item["additional_info"].keys()
                                and extraction_item["additional_info"]["alternate_locations"] == "in_footer"):
                            include_footer = True

                        if self.helper.is_in_footer(text_string):
                            in_footer = True
                        if ("probable_type" in extraction_item.keys()
                            and extraction_item["probable_type"] == "Footer"):
                            if in_footer:
                                include_footer = True
                            else:
                                continue
                        if not include_footer and in_footer:
                            continue

                        block_text_list.append(text_string["text"])
                        key_str = [self.helper.process_utils.pre_process_text(k) for k in extraction_item["key"]]
                        found_str = self.helper.process_utils.pre_process_text(text_string["text"])

                        to_continue, up_block = self.helper.check_if_matching_header_config(
                            extraction_item, block_text_list, tb_id, tl_id, block_index, up_block,
                            refined_page_json_data)
                        if to_continue:
                            continue

                        if ("probable_place" in extraction_item.keys() and
                                extraction_item["probable_place"].lower() == 'individual'):
                            is_key_individual, key_end_idx = self.helper.process_utils.check_if_key_found_is_individual(
                                block_text_list, extraction_item["key"])
                            if self.debug:
                                print('is_key_individual =', is_key_individual)
                            if not is_key_individual and not in_footer:
                                continue

                        raw_key_found_similarity_ratio = 0.0
                        raw_key_str, raw_found_str = extraction_item["key"], text_string["text"]
                        if "use_match" in extraction_item.keys() and extraction_item["use_match"].lower() == "fuzzy":
                            raw_key_found_similarity_ratio = self.helper.process_utils.similar(raw_key_str, raw_found_str)
                            # print(raw_key_str, raw_found_str, raw_key_found_similarity_ratio)

                        if "use_match" in extraction_item.keys() and extraction_item["use_match"].lower() == "exact_match":
                            found_match = [k == found_str for k in key_str]
                        else:
                            found_match = [k in found_str for k in key_str]
                        field_key = None
                        field_value = None
                        ## Checks if any of the 'exclude_keys' are present in the found key and updates the match status accordingly.
                        if "exclude_keys" in extraction_item.keys():
                            exclude_key_str = [self.helper.process_utils.pre_process_text(k) for k in
                                               extraction_item["exclude_keys"]]
                            exclude_key_match = [k in found_str for k in exclude_key_str]
                            if any(exclude_key_match):
                                 found_match = [False]

                        if (any(found_match)) or raw_key_found_similarity_ratio > 0.9:
                            # and self.helper.process_utils.is_required_key(block_text_list, extraction_item)):
                            if self.debug:
                                print("Found Field Key on Page {} : {}".format(page_number, text_string))
                            if ("probable_type" in extraction_item.keys() and
                                    extraction_item["probable_type"].lower() == 'header'):
                                if not self.helper.is_in_header(text_string):
                                    field_key = text_string
                                    field_value = None
                                    return SearchKeyFieldsOutputFormat(value=field_value,
                                                                search_next_page=None,
                                                                success=True,
                                                                elements=None,
                                                                is_table=False,
                                                                line_number=None,
                                                                bbox=None,
                                                                field_key=field_key)

                            field_key = text_string
                            search_next_page = None
                            success = False
                            elements = None
                            is_table = False
                            line_number = None
                            bbox = None

                            if extraction_item["type"] in ["checkbox"]:
                                self.helper.cb = SplitByCheckedBox(self.logger)
                                output = self.helper.cb.get_checkbox_value(
                                    refined_page_json_data[block_index:], extraction_item, img_path)
                                field_value = output.value
                                search_next_page = output.search_next_page
                                success = output.success
                                elements = output.elements
                                is_table = output.is_table
                                line_number = output.line_number
                                bbox = output.bbox
                                if self.debug:
                                    print("Answer:", output.value)
                                if not output.search_next_page:
                                    self.helper.cb = None
                                if not output.value and not output.search_next_page:  # and not search_next_page
                                    self.helper.cb = None
                                    output = (
                                        self.multipagesearch.extract_from_multi_page(
                                            refined_page_json_data, tl_id, ts_id, text_string, extraction_item, text_string))
                                    field_value = output.value
                                    search_next_page = continue_search = output.search_next_page
                                    success = output.success
                                    elements = output.elements
                                    is_table = output.is_table
                                    ine_number = output.line_number
                                    is_text_merged = output.is_text_merged
                                if self.debug:
                                    print("Answer:", output.value)

                                field_key = text_string
                                field_value = field_value

                            elif extraction_item["type"] in ["number", "text", "date", "address", "amount"]:
                                if extraction_item["direction"] == "right":
                                    output = self.rightdirectionsearch.get_value_in_right_direction_new(
                                        extraction_item, text_line_string, text_string,
                                        item["TEXT_BLOCK"], tb_id, tl_id, ts_id)
                                    text_string = output.field_key
                                    search_next_page = output.search_next_page
                                    success = output.success
                                    elements = output.elements
                                    is_table = extraction_item.get("return_type") == "table"
                                    line_number = output.line_number
                                    bbox = output.bbox
                                    field_value = temp_field_value = output.value
                                    
                                    if self.debug:
                                        print('text_string, field_value =', text_string, field_value)
                                        print('######', field_value)
                                        print('search_next_page =', search_next_page)
                                    
                                    if field_value is None:
                                        if ("additional_info" in extraction_item.keys()
                                            and "alternate_locations" in extraction_item["additional_info"].keys()
                                            and extraction_item["additional_info"][
                                                "alternate_locations"] == "in_footer"):
                                            if self.debug:
                                                print("Continuing")
                                            continue

                                    field_key = text_string
                                    if self.debug:
                                        print('######1', field_value)

                                elif extraction_item["direction"] == "just_down":
                                    output = (
                                        self.justdownsearch.get_just_down_value(
                                            refined_page_json_data, tl_id, ts_id, text_string, extraction_item))

                                    text_string = output.field_key
                                    search_next_page = output.search_next_page
                                    success = output.success
                                    elements = output.elements
                                    is_table = extraction_item.get("return_type") == "table"
                                    line_number = output.line_number
                                    bbox = output.bbox
                                    answer_value = output.value

                                    if answer_value is None:
                                        continue
                                    field_key = text_string
                                    field_value = answer_value

                                elif extraction_item["direction"] == "down":
                                    output = (
                                        self.wholevaluedownsearch.get_whole_value_down(
                                            refined_page_json_data, tl_id, ts_id, text_string, extraction_item, field_key))

                                    text_string = output.field_key
                                    search_next_page = output.search_next_page
                                    success = output.success
                                    elements = output.elements
                                    is_table = extraction_item.get("return_type") == "table"
                                    line_number = output.line_number
                                    bbox = output.bbox
                                    field_value = answer_value = output.value
                                    is_text_merged = output.is_text_merged

                                    if not answer_value and text_string is not None:
                                        answer_value = self.downdirectionsearch.get_value_from_key_if_exists(extraction_item, text_string)
                                        if not answer_value:
                                            output = self.get_value_in_same_block(
                                                item["TEXT_BLOCK"], tl_id, ts_id, text_string, extraction_item, text_string)
                                            text_string = output.field_key
                                            search_next_page = output.search_next_page
                                            success = output.success
                                            elements = output.elements
                                            is_table = extraction_item.get("return_type", 'text') == "table"
                                            line_number = output.line_number
                                            bbox = output.bbox
                                            answer_value = output.value
                                    if self.debug:
                                        print("Answer:", answer_value)
                                        print('answer_value =')
                                        pprint(answer_value)
                                        print('elements =', elements)
                                        pprint(answer_value)

                                    if answer_value is None:
                                        continue
                                    field_key = text_string
                                    field_value = answer_value

                                elif extraction_item["direction"] == "down_inline":
                                    # Call get_whole_value_down_inline to retrieve filtered elements directly below the field key
                                    output = self.wholevaluedowninlinesearch.get_whole_value_down_inline(
                                            refined_page_json_data, tl_id, ts_id, text_string, extraction_item,
                                            field_key
                                    )
                                    text_string = output.field_key
                                    search_next_page = output.search_next_page
                                    success = output.success
                                    elements = output.elements
                                    is_table = extraction_item.get("return_type") == "table"
                                    line_number = output.line_number
                                    bbox = output.bbox
                                    temp_field_value = answer_value = output.value
                                    is_text_merged = output.is_text_merged
                                    if self.debug:
                                        print('down_block')
                                        print("Answer:", answer_value)
                                        print('answer_value =')
                                        pprint(answer_value)
                                        print('elements =', elements)
                                        pprint(answer_value)

                                    field_key = text_string
                                    field_value = answer_value

                                elif extraction_item["direction"] == "down_block":
                                    if self.debug:
                                        print(item["TEXT_BLOCK"])
                                    output = (
                                        self.wholevaluedownsearch.get_whole_value_down(
                                            refined_page_json_data, tl_id, ts_id, text_string, extraction_item, field_key))
                                    text_string = output.field_key
                                    search_next_page = output.search_next_page
                                    success = output.success
                                    elements = output.elements
                                    is_table = extraction_item.get("return_type") == "table"
                                    line_number = output.line_number
                                    bbox = output.bbox
                                    temp_field_value = answer_value = output.value
                                    is_text_merged = output.is_text_merged
                                    if self.debug:
                                        print('down_block')
                                        print("Answer:", answer_value)
                                        print('answer_value =')
                                        pprint(answer_value)
                                        print('elements =', elements)
                                        pprint(answer_value)

                                    field_key = text_string
                                    field_value = answer_value

                                elif extraction_item["direction"] == "down_multi_page":
                                    output = (
                                        self.multipagesearch.extract_from_multi_page(
                                            refined_page_json_data, tl_id, ts_id, text_string, extraction_item, field_key))
                                    field_value = answer_value = output.value
                                    search_next_page = continue_search = output.search_next_page
                                    success = output.success
                                    elements = output.elements
                                    is_table = output.is_table
                                    line_number = output.line_number
                                    is_text_merged = output.is_text_merged
                                    if self.debug:
                                        print("Answer:", answer_value)
                                    if answer_value:
                                        text_string["to_continue_next_page"] = continue_search

                                    field_key = text_string
                                    field_value = answer_value

                                elif extraction_item["direction"] == "up":
                                    if self.debug:
                                        print("TEXT_BLOCK: ", item["TEXT_BLOCK"])
                                        print("UP_BLOCK: ", up_block)
                                    output = self.updirectionsearch.get_value_in_up_direction(
                                        item["TEXT_BLOCK"], tl_id, ts_id, text_string, extraction_item, text_string)
                                    field_value = answer_value = output.value
                                    search_next_page = continue_search = output.search_next_page
                                    success = output.success
                                    elements = output.elements
                                    is_table = output.is_table
                                    line_number = output.line_number
                                    bbox = output.bbox
                                    if self.debug:
                                        print("Answer:", answer_value)

                                    field_key = text_string
                                    field_value = answer_value

                                elif extraction_item["direction"] == "up_till_start":
                                    if self.debug:
                                        print(item["TEXT_BLOCK"])
                                    if self.debug:
                                        print(up_block)
                                    output = self.uptillstartdirectionsearch.get_value_in_up_till_start_direction(
                                        up_block, tl_id, ts_id, text_string, extraction_item, text_string)
                                    field_value = answer_value = output.value
                                    search_next_page = continue_search = output.search_next_page
                                    success = output.success
                                    elements = output.elements
                                    is_table = output.is_table
                                    line_number = output.line_number
                                    bbox = output.bbox

                                    if self.debug:
                                        print("Answer:", answer_value)


                                    field_key = text_string
                                    field_value = answer_value

                                elif extraction_item["direction"] == "left":
                                    if self.debug:
                                        print(text_line_string)
                                    answer_value = self.leftdirectionsearch.get_value_in_left_direction(text_line_string, extraction_item)
                                    field_key = text_string
                                    field_value = answer_value

                                elif extraction_item["direction"] == "inline":
                                    field_key = text_string
                                    field_value = text_string["text"]

                                    search_next_page = False
                                    success = True
                                    elements = text_string
                                    is_table = False
                                    line_number = field_key['LINE_NUMBER']
                                    bbox = [field_key['HPOS'], field_key['VPOS'],
                                             field_key['END_HPOS'], field_key['END_VPOS']]

                                elif extraction_item["direction"] == "full_page":
                                    if self.debug:
                                        print('search_key_fields | full_page')
                                        print('field_key =', field_key)
                                        print('text_string =', text_string)
                                    output = (
                                        self.wholepagetextsearch.get_whole_page_text(
                                            refined_page_json_data, tl_id, ts_id, text_string, extraction_item,
                                            field_key))
                                    field_value = answer_value = output.value
                                    search_next_page = continue_search = output.search_next_page
                                    success = output.success
                                    elements = output.elements
                                    is_table = output.is_table
                                    line_number = output.line_number

                                    field_key = text_string
                                    field_value = answer_value


                                elif extraction_item["direction"] == "down_till_end":
                                    output = self.downtillendsearch.get_all_values_down_till_end(
                                        item["TEXT_BLOCK"], extraction_item, text_string)
                                    field_value = answer_value = output.value
                                    search_next_page = continue_search = output.search_next_page
                                    success = output.success
                                    elements = output.elements
                                    is_table = output.is_table
                                    line_number = output.line_number

                                    field_key = text_string
                                    field_value = answer_value


                            elif extraction_item["type"] in ["table", ]:
                                if not self.table_utils.check_table_key(
                                        item["TEXT_BLOCK"], tb_id, tl_id, extraction_item):
                                    continue
                                table_data = self.table_utils.get_table_data(
                                    refined_page_json_data, tb_id, text_string, extraction_item, page_word_json_data)
                                field_key = text_string
                                field_value = table_data

                            elif extraction_item["type"] in ["table_new"]:
                                if "table_processor" in extraction_item.keys():
                                    value = None
                                #     output = self.table_processor(ext_item, img_path, json_path)
                                    output = self.table_processor(extraction_item, img_path, refined_page_json_data)
                                    if output.success:
                                        item["value"] = output.value
                                        value = output.value
                                        field_value = output.value
                                        line_number=output.line_number
                                        bbox=output.bbox

                            else:
                                self.logger.warning("wrong type in search_key_field")
                                if self.debug:
                                    print("wrong type")
                            # return field_key, field_value
                            return SearchKeyFieldsOutputFormat(value=field_value if field_value else None,
                                                               search_next_page=search_next_page \
                                                                   if extraction_item.get("multi_page_value", False) else False,
                                                               success=success,
                                                               elements=elements,
                                                               is_table=extraction_item.get("return_type") == "table",
                                                               line_number=line_number,
                                                               bbox=bbox,
                                                               field_key=field_key)

        return SearchKeyFieldsOutputFormat(value=None,
                                           search_next_page=None,
                                           success=False,
                                           elements=None,
                                           is_table=None,
                                           line_number=None,
                                           bbox=None,
                                           field_key=None)


if __name__ == "__main__":
    print("Done")
