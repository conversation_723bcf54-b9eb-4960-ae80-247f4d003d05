import importlib
from docvu_de_core.io import OutputFormat

class SubItemProcessor:
    """
    This class will be used to apply post-processing on the extracted content from the parser
    """

    def __init__(self):
        self.default_module = 'docvu_de_core.modules'
        self.custom_module = 'docvu_de_core.custom'

    @staticmethod
    def _to_camel_case(s):
        """Helper method to convert snake_case to CamelCase"""
        parts = s.split('_')
        return ''.join(x.capitalize() for x in parts)

    def __call__(self, sub_item, extraction_item, elements):
        output = OutputFormat(sub_item=None,
                            success=False)
        if 'sub_item_processor' in sub_item:
            post_process_list = sub_item['sub_item_processor']
            for pp_key, pp_val in post_process_list.items():
                if pp_val:
                    class_name = self._to_camel_case(pp_key)
                    # try:
                    if not pp_val.get('custom', False):
                        module = importlib.import_module(self.default_module)
                    else:
                        module = importlib.import_module(self.custom_module)
                    pp_class = getattr(module, class_name)
                    pp_instance = pp_class(**pp_val)
                    output = pp_instance(sub_item, extraction_item, elements=elements)
                    if output.success:
                        break
                # except (ImportError, AttributeError) as e:
                    #    raise ValueError(f"Post-processing class {class_name} not found in module. Error: {e}")

        return output