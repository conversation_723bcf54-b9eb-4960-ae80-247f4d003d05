import re
from docvu_de_core.extraction_item.TypeMapper import TypeMapper


class BaseFieldData(dict):
    # Default required fields for all instances of the class
    default_required_fields = [
        "id", "name", "key", "value", "page_number", "line_number",
        "confidence_indicator", "color_indicator", "field_name_coordinates", "field_value_coordinates",
        "is_table", "post_processing_value"
    ]
    defaults = {
        "id": "0",
        "name": "",
        "key": "",
        "value": "",
        "post_processing_value": "",
        "page_number": 0,
        "line_number": 0,
        "confidence_indicator": 99.0,
        "color_indicator": 1,
        "field_name_coordinates": {'x': 0, 'y': 0, 'width': 0, 'height': 0},
        "field_value_coordinates": {'x': 0, 'y': 0, 'width': 0, 'height': 0}
    }
    default_types = {
        "id": str,
        "name": str,
        "key": str,
        "value": str,
        "post_processing_value": str,
        "page_number": int,
        "line_number": int,
        "confidence_indicator": float,
        "color_indicator": int,
        "field_name_coordinates": dict,
        "field_value_coordinates": dict,
    }

    def __init__(self, from_camel_case=False, force_fill=False, **kwargs):
        """
        Initializes a new instance of the BaseFieldData class with data provided via keyword arguments.
        Additional fields can be added dynamically.

        :param kwargs: A dict of keyword arguments containing the field data and metadata.
        """
        super().__init__()

        new_kwargs = {}
        if from_camel_case:
            for k, v in kwargs.items():
                new_kwargs[self._from_camel_case(k)] = v
            kwargs = new_kwargs

        if 'name' in kwargs and "field_name" not in kwargs:
            kwargs["field_name"] = kwargs["name"]
        elif 'field_name' in kwargs and "name" not in kwargs:
            kwargs["name"] = kwargs["field_name"]

        # Ensure that the first three required fields are provided
        for key in self.default_required_fields[:3]:
            if key not in kwargs:
                if not force_fill:
                    raise ValueError(f"Missing required field: {key} | {kwargs.keys()}")
                else:
                    kwargs[key] = None

        # Initialize all default fields to None
        for field in self.default_required_fields:
            self[field] = kwargs.get(field)

        # Include any additional fields provided
        for key, value in kwargs.items():
            self[key] = value

        self.is_table = False
        self['is_table'] = False
        self['post_processing_value'] = self['value']

        self.sub_keys = []
        if 'sub_keys' in self.keys():
            from docvu_de_core.utils import GeneralUtils
            for sub_item in self["sub_keys"]:
                self.sub_keys.append(GeneralUtils.get_base_format(sub_item))
            self["sub_keys"] = self.sub_keys

        self.update_type_processing()

    def update_type_processing(self):
        if 'type' not in self:
            return

        # Handle 'post_process'
        if self['type'] in TypeMapper.__post_processor_dict__:
            post_process_key = TypeMapper.__post_processor_dict__[self['type']][0]
            post_process_value = TypeMapper.__post_processor_dict__[self['type']][1]

            # If 'post_process' is not in the dictionary, initialize it
            if 'post_process' not in self:
                self['post_process'] = {}

            # Add or update the key-value pair at the end of 'post_process'
            # Temporarily store existing items
            temp_post_process = dict(self['post_process'])
            temp_post_process[post_process_key] = post_process_value
            # Recreate the dictionary with the new key-value pair at the end
            self['post_process'] = temp_post_process

        # Handle 'output_format'
        if self['type'] in TypeMapper.__output_format_dict__:
            output_format_key = TypeMapper.__output_format_dict__[self['type']][0]
            output_format_value = TypeMapper.__output_format_dict__[self['type']][1]

            # If 'output_format' is not in the dictionary, initialize it
            if 'output_format' not in self:
                self['output_format'] = {}

            # Add or update the key-value pair at the end of 'output_format'
            # Temporarily store existing items
            temp_output_format = dict(self['output_format'])
            temp_output_format[output_format_key] = output_format_value
            # Recreate the dictionary with the new key-value pair at the end
            self['output_format'] = temp_output_format

    def is_complete(self, required_fields="all"):
        """
        Checks if the field data instance has all required attributes populated.

        :param required_fields: A list, a string specifying which fields should be checked for completeness,
        or "all" for all fields.
        :return: True if all required attributes are populated, False otherwise.
        """
        if required_fields == "all":
            required_fields = self.default_required_fields

        if isinstance(required_fields, str):
            required_fields = [required_fields]

        return all(self.get(field) is not None for field in required_fields)

    def has_attribute(self, attr_name):
        """
        Checks if a given attribute exists in the TableData dictionary.

        :param attr_name: The name of the attribute to check for.
        :return: True if the attribute exists, False otherwise.
        """
        return attr_name in self

    def fill_defaults(self):
        """
        Populates default values for all required fields if they are None or not set.
        """
        for field in self.default_required_fields:
            if self.get(field) is None:
                if field in self.defaults.keys():
                    self[field] = self.defaults[field]
            else:
                if field in self.default_types.keys():
                    if not isinstance(self[field], self.default_types[field]):
                        if isinstance(self[field], list) and self.default_types[field] == str:
                            self[field] = " ".join(self[field])
                        else:
                            try:
                                self[field] = self.default_types[field](self[field])
                            except (ValueError, OverflowError):
                                # Handle cases where conversion is not possible
                                if self.default_types[field] == int:
                                    self[field] = int(self[field]) if not self[field] in [float('inf'),
                                                                                          float('-inf'), ''] else 0
                                elif self.default_types[field] == float:
                                    self[field] = float(self[field]) if not self[field] in [float('inf'),
                                                                                            float('-inf'), ''] else 0.0

        # Ensure confidence_indicator is properly scaled
        if "confidence_indicator" in self and isinstance(self["confidence_indicator"], (int, float)):
            self["confidence_indicator"] = self["confidence_indicator"] * 100 if self["confidence_indicator"] < 1 else \
            self["confidence_indicator"]

        if not self["post_processing_value"] or len(self["post_processing_value"]) == 0:
            self["post_processing_value"] = self["value"]

    def to_camel_case(self):
        """Converts all dictionary keys to camelCase in place."""
        new_dict = {}
        for key, value in self.items():
            new_key = self._to_camel_case(key)
            if isinstance(value, dict):
                # Recursively convert nested dictionaries
                new_value = {}
                for k, v in value.items():
                    new_value[self._to_camel_case(k)] = v
                # new_value = BaseFieldData(**value)
                # new_value.to_camel_case()
                new_dict[new_key] = new_value
            elif isinstance(value, list):
                # Handle lists of dictionaries
                new_dict[
                    new_key] = value  # [BaseFieldData(**item).to_camel_case() if isinstance(item, dict) else item
                # for item
                # in value]
            else:
                new_dict[new_key] = value
        self.clear()
        self.update(new_dict)

    @staticmethod
    def _to_camel_case(s):
        """Helper method to convert snake_case to camelCase"""
        parts = s.split('_')

        if len(parts) == 1:
            if parts[0][0].isupper():
                return s
        return ''.join(x.capitalize() for x in parts)

    @staticmethod
    def _from_camel_case(s):
        """Helper method to convert camelCase to snake_case."""
        # Add an underscore before any uppercase letters in the middle of the string followed by lowercase letters
        # and convert to lower
        s = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', s)
        # Handle cases where a capital letter is followed by another capital letter and eventually lowercase letters
        s = re.sub('([a-z0-9])([A-Z])', r'\1_\2', s)
        return s.lower()

    def update_post_processing_value_if_not_found(self):
        if not self['post_processing_value']:
            self['post_processing_value'] = self['value']

    def to_dict(self, include_extra_fields=True):
        """
        Converts the BaseFieldData instance into a dictionary format suitable for serialization.
        This function is optional since the object is already a dictionary.

        :param include_extra_fields: Boolean to indicate whether to include extra fields added beyond the default ones.
        :return: A dictionary representation of the BaseFieldData.
        """
        if include_extra_fields:
            return dict(self)
        else:
            return {key: self[key] for key in self.default_required_fields if key in self}

    def drop_non_default_fields(self):
        """
        Removes all fields not listed in the default_required_fields from this instance.
        """
        keys_to_remove = [key for key in self if key not in self.default_required_fields]
        for key in keys_to_remove:
            del self[key]

    def normalize_coordinates(self, image_height, image_width, target='both', is_camel_case=False):
        """
        Normalizes the 'name_coordinates' and 'value_coordinates' based on the image dimensions, unless they are
        already normalized.

        :param image_width: Width of the image to normalize coordinates against.
        :param image_height: Height of the image to normalize coordinates against.
        :param target: Which set of coordinates to normalize ('name', 'value', or 'both').
        """
        if is_camel_case:
            x = 'X'
            y = 'Y'
            width = 'Width'
            height = 'Height'
            name = 'Name'
            field_name_coordinates = 'FieldNameCoordinates'
            value = 'Value'
            field_value_coordinates = 'FieldValueCoordinates'
        else:
            x = 'x'
            y = 'y'
            width = 'width'
            height = 'height'
            name = 'name'
            field_name_coordinates = 'field_name_coordinates'
            value = 'value'
            field_value_coordinates = 'field_value_coordinates'

        def calculate_normalized(coords):
            if max(coords[x], coords[y], coords[width], coords[height]) <= 1.0:
                return coords  # Assume coordinates are already normalized if all components are <= 1.0
            return {
                x: coords[x] / image_width,
                y: coords[y] / image_height,
                width: coords[width] / image_width,
                height: coords[height] / image_height
            }

        if target in ['name', 'both'] and field_name_coordinates in self and self[field_name_coordinates]:
            self[field_name_coordinates] = calculate_normalized(self[field_name_coordinates])

        if target in ['value', 'both'] and field_value_coordinates in self and self[field_value_coordinates]:
            self[field_value_coordinates] = calculate_normalized(self[field_value_coordinates])

    def update_coordinates(self, bbox, type='x1y1x2y2', target='value', is_camel_case=False):
        """
        Updates the coordinates for the specified target ('value' or 'name').

        :param bbox: A tuple containing the bounding box coordinates.
                     The format depends on the 'type' parameter.
        :param type: The format of the bbox coordinates.
                     'x1y1x2y2' for (x1, y1, x2, y2) format.
                     'xywh' for (x, y, width, height) format.
        :param target: The target field to update ('value' or 'name').
                       'value' updates field_value_coordinates.
                       'name' updates field_name_coordinates.
        :param is_camel_case: Boolean flag to determine if the keys should be in camel case.
        """
        if bbox is None:
            raise ValueError("Bounding box (bbox) cannot be None")

        if type not in ['x1y1x2y2', 'xywh']:
            raise ValueError(f"Unsupported type: {type}")

        if target not in ['value', 'name']:
            raise ValueError(f"Unsupported target: {target}")

        x = 'X' if is_camel_case else 'x'
        y = 'Y' if is_camel_case else 'y'
        width = 'Width' if is_camel_case else 'width'
        height = 'Height' if is_camel_case else 'height'

        if type == 'x1y1x2y2':
            x1, y1, x2, y2 = bbox
            coords = {
                x: x1,
                y: y1,
                width: x2 - x1,
                height: y2 - y1
            }
        else:  # type == 'xywh'
            x_val, y_val, w, h = bbox
            coords = {
                x: x_val,
                y: y_val,
                width: w,
                height: h
            }

        if target == 'value':
            self['field_value_coordinates'] = coords
        else:
            self['field_name_coordinates'] = coords

    def update_value(self, other):
        """
        Updates the fields of the current BaseFieldData object with values from another BaseFieldData object.

        :param other: Another instance of BaseFieldData to update from.
        """
        if not isinstance(other, BaseFieldData):
            raise TypeError("Argument must be an instance of BaseFieldData")

        fields_to_update = [
            "value", "page_number", "line_number",
            "confidence_indicator", "color_indicator",
            "field_name_coordinates", "field_value_coordinates",
            "post_processing_value"
        ]

        for field in fields_to_update:
            if field in other:
                self[field] = other[field]

    def __str__(self):
        """
        Returns a pretty-printed string representation of the BaseFieldData instance.

        :return: A string representation of the BaseFieldData.
        """
        return ""


if __name__ == "__main__":
    # Example usage:
    fields = {
        "id": "001",
        "name": "Document Number",
        "key": "DocNum",
        "value": "12345",
        "page_number": 1,
        "line_number": 10,
        "confidence_indicator": 95,
        "color_indicator": 2,
        "name_coordinates": {"X": 0.1, "Y": 0.2, "Width": 0.1, "Height": 0.05},
        "value_coordinates": {"X": 0.15, "Y": 0.25, "Width": 0.15, "Height": 0.1},
        "additional_info": "Extra Data"
    }
    field_data = BaseFieldData(**fields)
    print(field_data)  # To view the pretty-printed output
