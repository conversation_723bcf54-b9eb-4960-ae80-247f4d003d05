import numpy as np
from numpy import array, linspace
from sklearn.neighbors import KernelDensity
from scipy.signal import argrelextrema
from sklearn.cluster import DBSCAN
from scipy.cluster.hierarchy import linkage, dendrogram, fcluster
from sklearn.mixture import GaussianMixture
from sklearn.cluster import MeanShift
from sklearn.cluster import AffinityPropagation
from collections import defaultdict


class CLusterPoints:
    """
    This class provides various methods to cluster points using different clustering techniques.
    """

    def __init__(self):
        pass

    @staticmethod
    def cluster_points_using_kde(array_1d):
        """
        Clusters points using Kernel Density Estimation (KDE).

        Args:
            array_1d (list or ndarray): 1D array of input points for clustering.
        """
        # Convert the input array to a 2D array (required for KDE)
        a = array(array_1d).reshape(-1, 1)
        
        # Fit the KDE model with Gaussian kernel and specified bandwidth
        kde = KernelDensity(kernel='gaussian', bandwidth=10).fit(a)
        
        # Generate a range of values for scoring
        s = linspace(0, 10000)
        
        # Evaluate the log-density model over the range
        e = kde.score_samples(s.reshape(-1, 1))
        
        # Identify local minima and maxima
        mi, ma = argrelextrema(e, np.less)[0], argrelextrema(e, np.greater)[0]
        
        # Print the minima and maxima points
        print("Minima:", s[mi])
        print("Maxima:", s[ma])
        
        # Group points based on minima values
        print('Grouping using KDE:')
        print(a[a < s[mi][0]], a[(a >= s[mi][0]) * (a < s[mi][1])], a[a >= s[mi][1]])

    @staticmethod
    def cluster_points_using_hierarchical(array_1d):
        """
        Clusters points using hierarchical clustering.

        Args:
            array_1d (list or ndarray): 1D array of input points for clustering.

        Returns:
            dict: Clusters in the form of a dictionary, where keys are cluster IDs, and values are points.
        """
        # Convert the input array to a 2D array
        a = array(array_1d).reshape(-1, 1)
        
        # Perform hierarchical clustering using the complete linkage method
        Z = linkage(a, method='complete')
        
        # Generate flat clusters based on a distance threshold
        clusters = fcluster(Z, t=3, criterion='distance')
        
        # Group the points into clusters
        cls = {}
        for index, c in enumerate(clusters):
            if c in cls:
                cls[c].append(array_1d[index])
            else:
                cls[c] = [array_1d[index]]
        
        print('Grouping using hierarchical:')
        
        # Sort clusters by the first point in each cluster for better organization
        new_cls = {k: v for k, v in sorted(cls.items(), key=lambda item: item[1][0])}
        
        # Reassign cluster IDs sequentially
        sorted_cls = {}
        k = 1
        for _, v in new_cls.items():
            sorted_cls[k] = v
            k += 1
        
        return sorted_cls

    @staticmethod
    def cluster_points_using_gmm(array_1d):
        """
        Clusters points using Gaussian Mixture Model (GMM).

        Args:
            array_1d (list or ndarray): 1D array of input points for clustering.
        """
        # Convert the input array to a 2D array
        a = array(array_1d).reshape(-1, 1)
        
        # Fit the Gaussian Mixture Model with 5 components
        gmm = GaussianMixture(n_components=5)
        gmm.fit(a)
        
        # Predict cluster labels for the points
        clusters = gmm.predict(a)
        
        print('Grouping using GMM:')
        
        # Group points by cluster labels
        cls = {}
        for index, c in enumerate(clusters):
            if c in cls:
                cls[c].append(array_1d[index])
            else:
                cls[c] = [array_1d[index]]
        
        print(cls)

    @staticmethod
    def cluster_points_using_affinity(array_1d):
        """
        Clusters points using Affinity Propagation.

        Args:
            array_1d (list or ndarray): 1D array of input points for clustering.
        """
        # Convert the input array to a 2D array
        a = array(array_1d).reshape(-1, 1)
        
        # Perform Affinity Propagation clustering
        af = AffinityPropagation().fit(a)
        
        # Predict cluster labels for the points
        clusters = af.predict(a)
        
        print('Grouping using Affinity Propagation:')
        
        # Group points by cluster labels
        cls = {}
        for index, c in enumerate(clusters):
            if c in cls:
                cls[c].append(array_1d[index])
            else:
                cls[c] = [array_1d[index]]
        
        print(cls)

    @staticmethod
    def cluster_points_using_dbscan(array_1d):
        """
        Clusters points using DBSCAN (Density-Based Spatial Clustering of Applications with Noise).

        Args:
            array_1d (list or ndarray): 1D array of input points for clustering.
        """
        # Convert the input array to a 2D array
        a = array(array_1d).reshape(-1, 1)
        
        # Perform DBSCAN clustering with specified parameters
        dbscan = DBSCAN(eps=0.7, min_samples=4)
        clusters = dbscan.fit_predict(a)
        
        print('Grouping using DBSCAN:')
        
        # Group points by cluster labels
        cls = {}
        for index, c in enumerate(clusters):
            if c in cls:
                cls[c].append(array_1d[index])
            else:
                cls[c] = [array_1d[index]]
        
        print(cls)

    @staticmethod
    def get_mode_from_arr(arr):
        """
        Finds the mode (most frequent value) in an array.

        Args:
            arr (list or ndarray): Input array.

        Returns:
            int or float: The mode of the array.
        """
        # Count occurrences of each value in the array
        value = dict()
        for i in range(len(arr)):
            if arr[i] in value.keys():
                value[arr[i]] += 1
            else:
                value[arr[i]] = 1
        
        # Find the element with the maximum count
        max_count = 0
        res = -1
        for i in value:
            if max_count < value[i]:
                res = i
                max_count = value[i]
        
        return res

    @staticmethod
    def cluster_rows_from_array(y_values, threshold=6):
        """
        Cluster a list of Y-values into rows based on proximity.

        Args:
            y_values (list or array): List of Y-values.
            threshold (float): Maximum allowed distance between points in a cluster.

        Returns:
            dict: Clustered rows {row_id: [y1, y2, ...]}.
        """
        # Step 1: Sort the values
        sorted_y = sorted(y_values)

        # Step 2: Cluster by proximity
        rows = defaultdict(list)
        row_id = 0
        prev_y = None

        for y in sorted_y:
            if prev_y is None:
                rows[row_id].append(y)
            else:
                if abs(y - prev_y) <= threshold:
                    rows[row_id].append(y)
                else:
                    row_id += 1
                    rows[row_id].append(y)
            prev_y = y

        return dict(rows)

if __name__ == '__main__':
    # Create an instance of the CLusterPoints class
    cl = CLusterPoints()
    
    # Sample input array
    a = [82, 437, 854, 1374, 437, 1534, 81, 437, 854, 1463, 82, 437, 854, 1496, 81, 437, 852, 1463, 82, 437, 853, 1463,
         82, 437, 853, 1463, 81, 437, 854, 1497, 82, 437, 854, 1518, 82, 853, 1518, 81, 854, 1534, 82, 437, 852, 1572]
    
    # Call various clustering methods
    # cl.cluster_points_using_kde(a)
    print("*" * 50)
    cl.cluster_points_using_hierarchical(a)
    print("*" * 50)
    # cl.cluster_points_using_gmm(a)
    print("*" * 50)
    # cl.cluster_points_using_affinity(a)
    print("*" * 50)
    # cl.cluster_points_using_dbscan(a)
    print("*" * 50)
