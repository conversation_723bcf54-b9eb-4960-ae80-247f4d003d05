
class TableUtils:
    def __init__(self):
        self.cord_tol = 10
        self.closed_contour_cell_association_tol = 5
        self.x_cord_tol = 10
        self.y_cord_tol = 40
        pass

    def get_primary_matching_table_coordinates(self, tables, start_cords,
                                               just_below=True, all_below=True, below_count=8):
        found_stats = None
        below_stats = [] #None
        for k, v in tables.items():
            for k1, v1 in v.items():
                x, y, w, h, a1 = v1[0]
                stx, sty = start_cords
                if x <= stx <= x + w and y <= sty <= y + h:
                    found_stats = (x, y, w, h)
            if found_stats is not None:
                break
        if found_stats:  # and below_stats is None:
            fx, fy, fw, fh = found_stats
            start_cord_x, start_cord_y = fx, fy
            end_cord_x, end_cord_y = fx + fw, fy + fh
            print("Found Stats: {}".format(found_stats))
            count = 0
            for k, v in tables.items():
                for k1, v1 in v.items():
                    x, y, w, h, a1 = v1[0]
                    st_x, st_y, ed_x, ed_y = x, y, x + w, y + h
                    if (x, y, w, h) != found_stats and y > fy:
                        #if (abs(fx - x) <= self.cord_tol or st_x < end_cord_x) and abs(y - fy) <= fh + 1:
                        if abs(fx - x) <= self.cord_tol and abs(y - fy) <= fh + self.cord_tol:
                            if (x, y, w, h) not in below_stats:
                                below_stats.append((x, y, w, h))
                        elif just_below and fx <= x and x + w <= fx + fw and abs(y - fy) <= fh + 1:
                            if (x, y, w, h) not in below_stats:
                                below_stats.append((x, y, w, h))
                        elif all_below:
                            if fx <= x and x + w <= fx + fw and count < below_count:
                                count += 1
                                if (x, y, w, h) not in below_stats:
                                    below_stats.append((x, y, w, h))
        return found_stats, below_stats

    def is_with_in_table_limit(self, text_string, table_limits):
        for table_count, table_cords in table_limits.items():
            x_min, y_min, x_max, y_max = table_limits[table_count]
            stx, sty = text_string["HPOS"], text_string["VPOS"]
            if (x_min - self.cord_tol <= stx <= x_max + self.cord_tol and
                    y_min - self.cord_tol <= sty <= y_max + self.cord_tol):
                return True, table_count
        return False, None

    def detect_table_row_cell(self, tables, table_id, text_string):
        """
            {0: {1: [(236, 1764, 298, 41, 11878.0), (534, 1764, 413, 41, 16478.0),
                    (947, 1764, 298, 41, 11878.0), (1245, 1764, 301, 41, 11998.0)],
                2: [(236, 1805, 298, 41, 11878.0), (534, 1805, 413, 41, 16478.0),
                    (947, 1805, 298, 41, 11878.0), (1245, 1805, 301, 41, 11998.0)],
                3: [(236, 1846, 298, 41, 11878.0), (534, 1846, 413, 41, 16478.0),
                    (947, 1846, 298, 41, 11878.0), (1245, 1846, 301, 41, 11998.0)],
                4: [(236, 1887, 298, 41, 11612.0), (534, 1887, 413, 40, 16066.0),
                    (947, 1887, 298, 40, 11581.0), (1245, 1887, 301, 40, 11698.0)],
                5: [(236, 1928, 298, 41, 11878.0), (534, 1928, 413, 41, 16478.0),
                    (947, 1928, 298, 41, 11878.0), (1245, 1928, 301, 41, 11998.0)],
                6: [(236, 1970, 297, 78, 22790.0), (534, 1970, 413, 78, 31722.0),
                (947, 1970, 298, 78, 22867.0), (1245, 1970, 301, 78, 23098.0)]}, 1: {}}
            """
        table_cords = tables[table_id]
        stx, sty = text_string["HPOS"], text_string["VPOS"]
        stx_end, sty_end = text_string["END_HPOS"], text_string["END_VPOS"]
        for row, values in table_cords.items():
            for col, val in enumerate(values):
                x_min, y_min, x_max, y_max = val[0], val[1], val[0] + val[2], val[1] + val[3]
                if (x_min - self.closed_contour_cell_association_tol <= stx and
                    stx_end <= x_max + self.closed_contour_cell_association_tol and
                        y_min - self.closed_contour_cell_association_tol <= sty and
                        sty_end <= y_max + self.closed_contour_cell_association_tol):
                    return row, col + 1
        return None, None

    @staticmethod
    def if_with_in_same_block(text_string, cords):
        x, y, w, h = cords
        stx, sty = text_string["HPOS"], text_string["VPOS"]
        if x <= stx <= x + w and y <= sty <= y + h:
            return True
        return False
    
    
    def get_bounding_box(original_answer, extracted_value):
        """
        Find the bounding box (x, y, width, height) of the extracted value in the original answer.
        """
        
        if isinstance(original_answer, list):
            for item in original_answer:
                if "text" in item and extracted_value in item["text"]:
                    return (int(item["HPOS"]), int(item["VPOS"]), int(item["END_HPOS"])+15, int(item["END_VPOS"])+10)
            
        if isinstance(original_answer, dict):
            for key, list_value in original_answer.items():
                for item in list_value:
                    if "text" in item and extracted_value in item["text"]:
                        return (int(item["HPOS"]), int(item["VPOS"]), int(item["END_HPOS"])+15, int(item["END_VPOS"])+10)
        
        return None  # If no bounding box is found


if __name__ == '__main__':
    pass
