class ExtractionUtils:
    def __init__(self):
        pass

    @staticmethod
    def pre_process_text(text):
        text = text.lower().replace(" ", "")
        text = "".join(c for c in text if (c.isalnum() or c in ["$", "\n"]))
        return text

    def get_word_level_start_cord(self, text_string, ext_item):
        key_str = [self.pre_process_text(k) for k in ext_item["key"]]
        len_key = len(ext_item["key"][0].split(' '))
        start_cord = []
        if len(text_string["WORDS"]) >= len_key:
            word = []
            hpos = []
            vpos = []
            for tw_id, words in enumerate(text_string["WORDS"]):
                if len(word) == len_key:
                    final_word = " ".join(word)
                    found_str = self.pre_process_text(final_word)
                    found_match = [k in found_str for k in key_str]
                    if any(found_match):
                        start_cord.append((hpos[0], vpos[0]))
                    word.pop(0)
                    hpos.pop(0)
                    vpos.pop(0)
                word.append(words["text"])
                hpos.append(words["HPOS"])
                vpos.append(words["VPOS"])
            if len(word) == len_key:
                final_word = " ".join(word)
                found_str = self.pre_process_text(final_word)
                found_match = [k in found_str for k in key_str]
                if any(found_match):
                    start_cord.append((hpos[0], vpos[0]))
        return start_cord

    @staticmethod
    def get_prev_next_image_line(line_list, text_stats):
        text_hpos_start, text_hpos_end, text_vpos_start, text_vpos_end = text_stats
        prev_line = None
        next_line = None
        for index, line in enumerate(line_list):
            x, y, w, h = line
            if text_vpos_start > y:
                if index > 0:
                    prev_line = index - 1
                else:
                    prev_line = next_line
            else:
                next_line = index
                break
        return prev_line, next_line

    @staticmethod
    def get_text_start_pos_between_lines(line_text_info):
        start_pos = {}
        max_col = 0
        for line_number, text_strings in line_text_info.items():
            print("At Line: {}, Value = {}".format(line_number, [v1["text"] for v1 in text_strings]))
            hpos = []
            vpos = []
            end_hpos = []
            for text in text_strings:
                hpos.append(text["HPOS"])
                vpos.append(text["VPOS"])
                end_hpos.append(text["END_HPOS"])
            sorted_hpos = [i for i in sorted(enumerate(hpos), key=lambda x: x[1])]
            sorted_vpos = [vpos[i[0]] for i in sorted_hpos]
            sorted_end_pos = [end_hpos[i[0]] for i in sorted_hpos]
            sorted_hpos = [i[1] for i in sorted_hpos]

            print("Derived = ", hpos, sorted_hpos, sorted_vpos, sorted_end_pos)

            sorted_pos = []
            ph, pv, phend = 0, 0, 0
            for id, (sh, sv, shend) in enumerate(zip(sorted_hpos, sorted_vpos, sorted_end_pos)):
                if id == 0:
                    sorted_pos.append((sh, sv, shend))
                    ph, pv, phend = sh, sv, shend
                else:
                    if 0 < abs(sh - ph) < 10:
                        if sv < pv:
                            sorted_pos[-1] = (sh, sv, shend)
                            sorted_pos.append((ph, pv, phend))
                    else:
                        sorted_pos.append((sh, sv, shend))
                        ph, pv, phend = sh, sv, shend
            print(f"Sorted = {sorted_pos}")

            blocks = {}
            bid = 0
            temp = []
            for index, val in enumerate(sorted_pos):
                if index == 0 or not temp:
                    temp.append(val)
                else:
                    if abs(val[0] - temp[-1][0]) < 10:
                        temp.append(val)
                        if len(temp) > 1:
                            min_hpos = min([i[0] for i in temp])
                            min_vpos = min([i[1] for i in temp])
                            max_endhpos = max([i[2] for i in temp])
                            temp = [(min_hpos, min_vpos, max_endhpos)]
                        blocks[bid] = temp
                        bid += 1
                        temp = []
                    else:
                        if len(temp) > 1:
                            min_hpos = min([i[0] for i in temp])
                            min_vpos = min([i[1] for i in temp])
                            max_endhpos = max([i[2] for i in temp])
                            temp = [(min_hpos, min_vpos, max_endhpos)]
                        blocks[bid] = temp
                        bid += 1
                        temp = [val]
            if temp:
                if len(temp) > 1:
                    min_hpos = min([i[0] for i in temp])
                    min_vpos = min([i[1] for i in temp])
                    max_endhpos = max([i[2] for i in temp])
                    temp = [(min_hpos, min_vpos, max_endhpos)]
                blocks[bid] = temp
                bid += 1
            print(f"Blocks = {blocks}")
            max_col = max(max_col, len(blocks))
            start_pos[line_number] = blocks
            print(line_number, start_pos[line_number])
            print("-" * 50)
        # print(start_hpos)
        return start_pos, max_col

    @staticmethod
    def extract_data_within_col_limits(col_limits, line_text_info):
        col_data = {}
        for index in range(len(col_limits) - 1):
            limit = col_limits[index]
            next_limits = col_limits[index + 1]
            for line_number, text_strings in line_text_info.items():
                line_data = []
                for text in text_strings:
                    if ((abs(limit[0] - text["HPOS"]) < 10 or abs(limit[1] - text["END_HPOS"]) < 50)
                            and text["END_HPOS"] < next_limits[0]):
                        line_data.append(text["text"])
                if not line_data:
                    line_data.append('')
                if line_number in col_data:
                    col_data[line_number].append(line_data)
                else:
                    col_data[line_number] = [line_data]
        last_col_limit = col_limits[-1]
        for line_number, text_strings in line_text_info.items():
            line_data = []
            for text in text_strings:
                if abs(last_col_limit[0] - text["HPOS"]) < 10 or abs(last_col_limit[1] - text["END_HPOS"]) < 50:
                    line_data.append(text["text"])
            if not line_data:
                line_data.append('')
            if line_number in col_data:
                col_data[line_number].append(line_data)
            else:
                col_data[line_number] = [line_data]
        print("COl Data = {}\n".format(col_data))
        return col_data

    @staticmethod
    def group_by_y_min(text_list, y_min_group_dict):
        grouped_results = {}

        for group_id, y_min_values in y_min_group_dict.items():
            grouped_results[group_id] = []
            for entry in text_list:
                x_min, x_max, y_min, y_max, text_string = entry
                if y_min in y_min_values:
                    grouped_results[group_id].append(entry)

        return grouped_results

    @staticmethod
    def find_nearest_element(values, target):
        #print(values, target)
        nearest_index = min(range(len(values)), key=lambda i: abs(values[i] - target))
        return nearest_index
    
    
if __name__ == '__main__':
    pass