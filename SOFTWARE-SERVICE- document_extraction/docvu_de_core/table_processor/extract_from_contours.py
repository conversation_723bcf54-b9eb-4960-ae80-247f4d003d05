#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
 *************************************************************************
 *
 *
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

 * NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
 * The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

 *************************************************************************
"""
from .image_table_recognition import TableDetection
from .table_utils import TableUtils
from .extraction_utils import ExtractionUtils
from .clustering import CLusterPoints
import pandas as pd
import numpy as np
from scipy.cluster.hierarchy import fcluster, linkage
import cv2
import re

class ExtractTableDataFromClosedContours:
    """
    A class to extract table data from closed contours in a document.
    """

    def __init__(self, results_path='./../results', save_results=True,
                 use_nested_table_config=False, debug=False, display=False):
        """
        Initializes the ExtractTableDataFromClosedContours class with specified parameters.

        Args:
            results_path (str): Path to save results (default: './../results').
            save_results (bool): Flag to save the results (default: True).
            use_nested_table_config (bool): Flag to enable nested table configuration (default: False).
            debug (bool): Flag for debug mode (default: False).
            display (bool): Flag to enable display of intermediate results (default: False).
        """
        # Initialize table detection utility with the given parameters
        self.table_det = TableDetection(
            results_path=results_path,
            save_results=save_results,
            use_nested_table_config=use_nested_table_config,
            debug=debug,
            display=display
        )

        # Utility for general table operations
        self.table_utils = TableUtils()

        # Utility for data extraction from detected tables
        self.extraction_utils = ExtractionUtils()

        # Utility for clustering points related to table structures
        self.cluster = CLusterPoints()

        # Horizontal position difference threshold for alignment calculations
        self.hpos_diff = 5

        pass  # Placeholder for potential future code

    
    def check_table_within_start_end_key(self, page_json_data, ext_item, table):
        """
        Checks if a table is located between specified start and end keys within a document.

        Args:
            page_json_data (dict): JSON data of the page containing the table and text blocks.
            ext_item (dict): Dictionary containing the start key and end identifier for the table.
            table (list): Table coordinates in the format [x, y, width, height].

        Returns:
            bool: True if the table lies within the start and end keys, False otherwise.
        """
        # Flags to control the search process
        search_start_key = True
        search_end_key = True
        got_start_key = False
        got_end_key = False

        # Vertical positions of the start and end keys
        start_key_y = 0
        end_key_y = 0

        # Iterate over all composed blocks in the page
        # for block_index, item in enumerate(page_json_data["COMPOSED_BLOCKS"]):
        for block_index, item in enumerate(page_json_data):
            # Iterate over text blocks in each composed block
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                # Iterate over text lines in each text block
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    # Iterate over strings in each text line
                    for ts_id, text_string in enumerate(text_line["STRING"]):
                        # Check for the start key
                        if search_start_key:
                            # Pre-process the key and text for matching
                            key_str = [self.extraction_utils.pre_process_text(k) for k in ext_item["key"]]
                            found_str = self.extraction_utils.pre_process_text(text_string["text"])
                            # Check if any key matches the text
                            found_match = [k in found_str for k in key_str]
                            if any(found_match):
                                # Update flags and store the vertical position of the start key
                                search_start_key = False
                                got_start_key = True
                                start_key_y = text_string["VPOS"]
                        else:
                            # Check for the end key if start key has been found
                            if search_end_key:
                                # Pre-process the end identifier and text for matching
                                key_str = [self.extraction_utils.pre_process_text(k) for k in ext_item["end_identifier"]]
                                found_str = self.extraction_utils.pre_process_text(text_string["text"])
                                # Check if any end identifier matches the text
                                found_match = [k in found_str for k in key_str]
                                if any(found_match):
                                    # Update flags and store the vertical position of the end key
                                    search_end_key = False
                                    got_end_key = True
                                    end_key_y = text_string["VPOS"]

                            # Break from inner loops if end key is found
                            if got_end_key:
                                break
                        if got_end_key:
                            break
                    if got_end_key:
                        break
                if got_end_key:
                    break
            if got_end_key:
                break

        # Validate if both start and end keys were found
        if got_start_key and got_end_key:
            # Extract table's vertical position and height
            table_y = table[1]
            table_height = table[3]

            # Check if the table lies completely between the start and end keys
            if start_key_y <= table_y and table_height <= end_key_y:
                return True

        # Return False if any condition is not met
        return False


    def extract_from_table(self, refined_page_json_data, image, ext_item, first_row_as_header=False):
        """
        Extracts data from tables detected in an image based on refined page JSON data.

        Args:
            refined_page_json_data (dict): Refined JSON data of the page, including text and layout.
            image (ndarray): Input image containing tables.
            ext_item (dict): Extraction configuration with start and end keys.
            first_row_as_header (bool): Whether to treat the first row of each table as the header.

        Returns:
            dict: A dictionary where keys are table IDs and values are pandas DataFrames containing table data.
        """
        # Detect tables in the image and get their bounding boxes
        tables = self.table_det.detect(image, thick_lines=True, merge_cells=True)
        old_table_limits = self.table_det.get_table_start_coordinates(tables)
        
        # # annotate old_table_limits on page
        # for tb_id, tb_bbox in old_table_limits.items():
        #     image = cv2.rectangle(image, (tb_bbox[0], tb_bbox[1]), (tb_bbox[2], tb_bbox[3]), (255, 0, 0), 2)
        #     image = cv2.putText(image, str(tb_id), (tb_bbox[0]-30, tb_bbox[1]), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2, cv2.LINE_AA)
        #     cv2.imwrite("table.jpg", image)

        table_limits = {}
        # Filter tables based on their presence within start and end keys
        if old_table_limits:
            for key, table_bbox in old_table_limits.items():
                if self.check_table_within_start_end_key(refined_page_json_data, ext_item, table_bbox):
                    table_limits[key] = table_bbox

        # Dictionary to hold text elements for each table
        table_id_values = {}
        use_word = False  # Flag to determine if text is processed at word level

        # Process each text element in the page JSON data
        # for block_index, item in enumerate(refined_page_json_data["COMPOSED_BLOCKS"]):
        for block_index, item in enumerate(refined_page_json_data):
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    for ts_id, text_string in enumerate(text_line["STRING"]):

                        if use_word:
                            # Process words within the text string
                            for tw_id, text_word in enumerate(text_string["WORDS"]):
                                is_in_table, table_id = self.table_utils.is_with_in_table_limit(text_word, table_limits)
                                # Compute the end positions for the text word
                                text_word["END_HPOS"] = text_word["HPOS"] + text_word["WIDTH"]
                                text_word["END_VPOS"] = text_word["VPOS"] + text_word["HEIGHT"]
                                if table_id is not None and is_in_table:
                                    # Add the word to the corresponding table's data
                                    if table_id in table_id_values.keys():
                                        table_id_values[table_id].append(text_word)
                                    else:
                                        table_id_values[table_id] = [text_word]
                        else:
                            # Process the entire text string
                            is_in_table, table_id = self.table_utils.is_with_in_table_limit(text_string, table_limits)
                            # Compute the end positions for the text string
                            text_string["END_HPOS"] = text_string["HPOS"] + text_string["WIDTH"]
                            text_string["END_VPOS"] = text_string["VPOS"] + text_string["HEIGHT"]
                            if table_id is not None and is_in_table:
                                # Add the string to the corresponding table's data
                                if table_id in table_id_values.keys():
                                    table_id_values[table_id].append(text_string)
                                else:
                                    table_id_values[table_id] = [text_string]

        # Sort the tables by their IDs for consistent processing
        mykeys = list(table_id_values.keys())
        mykeys.sort()
        sorted_table_id_values = {i: table_id_values[i] for i in mykeys}

        # Format text strings into row-column format for each table
        formatted_table_id_values = {}
        for table_id, text_strings in sorted_table_id_values.items():
            values = []
            for text_string in text_strings:
                # Detect the row and column of the text string within the table
                row, col = self.table_utils.detect_table_row_cell(tables, table_id, text_string)
                if row and col:
                    values.append((row, col, text_string["text"]))
            if values:
                formatted_table_id_values[table_id] = values

        # Convert extracted table data into pandas DataFrames
        table_df_dict = {}
        if formatted_table_id_values:
            for key, values in formatted_table_id_values.items():
                # Determine the table's maximum row and column counts
                max_row = max(v[0] for v in values)
                max_col = max(v[1] for v in values)
                # Create a blank 2D array to store table content
                arr = [['' for i in range(max_col)] for j in range(max_row)]
                for val in values:
                    # Populate the table array with extracted text
                    if arr[val[0] - 1][val[1] - 1]:
                        arr[val[0] - 1][val[1] - 1] += ' ' + val[2]
                    else:
                        arr[val[0] - 1][val[1] - 1] = val[2]

                # Determine column headers
                if first_row_as_header:
                    columns = arr[0]
                    arr.pop(0)  # Remove the header row from the data array
                else:
                    columns = [f'Column_{i + 1}' for i in range(len(arr[0]))]
                
                # Create a pandas DataFrame for the table
                df = pd.DataFrame(arr, columns=columns)
                table_df_dict[key] = df

        return table_df_dict, table_id_values


    def extract_from_block(self, json_data, image, ext_item,
                        nested_kernel_len=7, draw_virtual_line=False,
                        just_below=True, all_below=True, below_count=6):
        """
        Extracts data from text blocks in an image based on a key identifier and block relationships.

        Args:
            json_data (dict): JSON data containing composed blocks and text layout.
            image (ndarray): Input image from which tables and blocks are detected.
            ext_item (dict): Extraction configuration with key for identification.
            nested_kernel_len (int): Kernel size for nested table detection (default: 7).
            draw_virtual_line (bool): Whether to draw virtual lines for table alignment (default: False).
            just_below (bool): Flag to extract blocks just below the key block (default: True).
            all_below (bool): Flag to extract all blocks below the key block (default: True).
            below_count (int): Number of blocks to extract below the key block (default: 6).

        Returns:
            dict: A dictionary with data from the same block and below blocks:
                - "same_block": List of words within the primary block.
                - "below_block": Dictionary of words within blocks below, indexed by their position.
        """
        # Detect tables in the image
        tables = self.table_det.detect(
            image, 
            nested_kernel_len=nested_kernel_len,
            thick_lines=True, 
            draw_virtual_line=draw_virtual_line
        )
        
        # Output structure to hold results
        output = {"same_block": [], "below_block": {}}

        # Flags and variables to track the primary and below blocks
        search_primary_block = True
        primary_cords, below_cords = None, None

        # Iterate through composed blocks in the JSON data
        for block_index, item in enumerate(json_data["COMPOSED_BLOCKS"]):
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    for ts_id, text_string in enumerate(text_line["STRING"]):
                        # Pre-process key and text for matching
                        key_str = [self.extraction_utils.pre_process_text(k) for k in ext_item["key"]]
                        found_str = self.extraction_utils.pre_process_text(text_string["text"])
                        # Check if the text string matches any key
                        found_match = [k in found_str for k in key_str]
                        
                        if any(found_match) and search_primary_block:
                            # Found a match for the primary block
                            start_cord = self.extraction_utils.get_word_level_start_cord(text_string, ext_item)
                            
                            # Determine the primary and below block coordinates based on the detected tables
                            for cord in start_cord:
                                primary_cords, below_cords = self.table_utils.get_primary_matching_table_coordinates(
                                    tables, cord, just_below=just_below, all_below=all_below, below_count=below_count)
                                
                                if primary_cords:
                                    # Collect all words within the primary block
                                    for tw_id, text_word in enumerate(text_string["WORDS"]):
                                        if self.table_utils.if_with_in_same_block(text_word, primary_cords):
                                            output["same_block"].append(text_word)
                                    search_primary_block = False  # Stop searching for the primary block
                                break  # Break after finding the primary block

                        # Process words for the primary block and below blocks
                        if primary_cords:
                            for tw_id, text_word in enumerate(text_string["WORDS"]):
                                # Add words to the same block if within primary coordinates
                                if self.table_utils.if_with_in_same_block(text_word, primary_cords):
                                    output["same_block"].append(text_word)
                                # Add words to below blocks if within below coordinates
                                if below_cords:
                                    for index, cords in enumerate(below_cords):
                                        x, y, w, h = cords
                                        stx, sty = text_word["HPOS"], text_word["VPOS"]
                                        if x <= stx <= x + w and y <= sty <= y + h:
                                            if index in output["below_block"]:
                                                output["below_block"][index].append(text_word)
                                            else:
                                                output["below_block"][index] = [text_word]

        return output


    def extract_from_open_table_with_horizontal_lines(self, json_data, image, ext_item):
        """
        Extracts table data from an image with open tables delineated by horizontal lines.

        Args:
            json_data (dict): JSON data containing composed blocks and text layout.
            image (ndarray): Input image for table and line detection.
            ext_item (dict): Extraction configuration with keys and end identifiers.

        Returns:
            pd.DataFrame or None: Extracted table as a DataFrame, or None if no table is found.
        """
        # Detect horizontal lines in the image and retrieve line statistics
        line_stats, image = self.table_det.detect_lines(image)

        # Flags to track the search for start and end keys
        search_start_key = True
        search_end_key = True
        got_end_key = False

        # Dictionary to store text information between horizontal lines
        line_text_info = {}

        # Iterate through composed blocks in the JSON data
        # for block_index, item in enumerate(json_data["COMPOSED_BLOCKS"]):
        for block_index, item in enumerate(json_data):
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    for ts_id, text_string in enumerate(text_line["STRING"]):
                        
                        # Check for the start key
                        if search_start_key:
                            key_str = [self.extraction_utils.pre_process_text(k) for k in ext_item["key"]]
                            found_str = self.extraction_utils.pre_process_text(text_string["text"])
                            found_match = [k in found_str for k in key_str]
                            if any(found_match):
                                # Start key found, stop searching for it
                                search_start_key = False
                                continue
                        
                        else:
                            # Check for the end key
                            if search_end_key:
                                key_str = [self.extraction_utils.pre_process_text(k) for k in ext_item["end_identifier"]]
                                found_str = self.extraction_utils.pre_process_text(text_string["text"])
                                found_match = [k in found_str for k in key_str]
                                if any(found_match):
                                    # End key found, stop searching for it
                                    search_end_key = False
                                    got_end_key = True
                                    break
                                
                                # Get text statistics and find the previous and next line indices
                                text_stats = (text_string["HPOS"], text_string["END_HPOS"],
                                            text_string["VPOS"], text_string["END_VPOS"])
                                prev_line_index, next_line_index = (
                                    self.extraction_utils.get_prev_next_image_line(line_stats, text_stats))
                                
                                # Add text to the corresponding line index
                                if prev_line_index in line_text_info:
                                    line_text_info[prev_line_index].append(text_string)
                                else:
                                    line_text_info[prev_line_index] = [text_string]
                            
                            # Exit if the end key is found
                            if got_end_key:
                                break
                    if got_end_key:
                        break
                if got_end_key:
                    break
            if got_end_key:
                break

        # If text between horizontal lines is found, process it
        if line_text_info:
            # Determine column start positions and the maximum number of columns
            start_pos, max_col = self.extraction_utils.get_text_start_pos_between_lines(line_text_info)

            # Identify column limits based on start positions
            col_limits = []
            for k1, cords in start_pos.items():
                if len(cords) == max_col:
                    for k, val in cords.items():
                        col_limits.append([val[0][0], val[0][2]])
                    break

            # Extract text data within the determined column limits
            col_data = self.extraction_utils.extract_data_within_col_limits(col_limits, line_text_info)

            # Format the extracted column data
            final_col_data = []
            for k, value in col_data.items():
                data = []
                for c_data in value:
                    if len(c_data) > 1:
                        data.append(" ".join(c_data))  # Concatenate multiple strings in a cell
                    else:
                        data.append(c_data[0])
                final_col_data.append(data)

            # Create a DataFrame with column headers
            columns = [f'Column_{i + 1}' for i in range(max_col)]
            df = pd.DataFrame(final_col_data, columns=columns)
            return df, line_text_info

        # Return None if no table data is extracted
        return None, line_text_info


    def extract_from_open_table_without_lines_v1(self, json_data, image, ext_item):
        """
        Extracts tabular data from an image where horizontal lines are absent.

        Args:
            json_data (dict): Contains composed blocks and text layout information.
            image (ndarray): The input image for extraction.
            ext_item (dict): Configuration with keys like "key", "end_identifier", and optional parameters.

        Returns:
            pd.DataFrame or None: A DataFrame with the extracted table data, or None if no table is found.
        """
        # Flags to manage the search for the start and end keys
        search_start_key = True
        search_end_key = True
        got_end_key = False

        # Variables to store extracted text and positional boundaries
        line_text_info = []
        start_hpos = 0
        end_hpos = 0
        end_pos_search = False
        end_hpos_key = []

        # If specified, enable checking for elements near the start HPOS
        use_on_after_hpos = ext_item.get("consider_on_after_hpos", False)

        # Check if HPOS-based end search is enabled
        if "hpos_end_identifier" in ext_item.keys():
            end_pos_search = ext_item["hpos_end_identifier"]
            end_hpos_key = ext_item["hpos_end_key"]
            if not end_hpos_key:
                end_pos_search = False

        # Iterate over the composed blocks in the JSON data
        # for block_index, item in enumerate(json_data["COMPOSED_BLOCKS"]):
        for block_index, item in enumerate(json_data):
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    for ts_id, text_string in enumerate(text_line["STRING"]):
                        
                        # Search for the start key
                        if search_start_key:
                            key_str = [self.extraction_utils.pre_process_text(k) for k in ext_item["key"]]
                            found_str = self.extraction_utils.pre_process_text(text_string["text"])
                            found_match = [k in found_str for k in key_str]
                            if any(found_match):
                                search_start_key = False
                                if start_hpos == 0:
                                    start_hpos = text_string["HPOS"]
                                # Optionally retain the start key in the extracted text
                                if ext_item.get("keep_start_key", False):
                                    line_text_info.append(text_string)
                                continue
                        
                        # Search for the end key
                        else:
                            if search_end_key:
                                key_str = [self.extraction_utils.pre_process_text(k) for k in ext_item["end_identifier"]]
                                found_str = self.extraction_utils.pre_process_text(text_string["text"])
                                found_match = [k in found_str for k in key_str]
                                if any(found_match):
                                    search_end_key = False
                                    got_end_key = True
                                    break

                                # Handle HPOS-based end criteria if enabled
                                if end_pos_search:
                                    key_str = [self.extraction_utils.pre_process_text(k) for k in end_hpos_key]
                                    found_str = self.extraction_utils.pre_process_text(text_string["text"])
                                    found_match = [k in found_str for k in key_str]
                                    if any(found_match):
                                        end_hpos = text_string["HPOS"]
                                if end_hpos != 0:
                                    if (abs(text_string["HPOS"] - end_hpos) <= self.hpos_diff or
                                            text_string["HPOS"] >= end_hpos):
                                        continue
                                # Collect text data based on positional boundaries
                                if use_on_after_hpos:
                                    if start_hpos != 0:
                                        if (abs(text_string["HPOS"] - start_hpos) <= self.hpos_diff or
                                                text_string["HPOS"] >= start_hpos):
                                            line_text_info.append(text_string)
                                else:
                                    line_text_info.append(text_string)
                            if got_end_key:
                                break
                    if got_end_key:
                        break
                if got_end_key:
                    break
            if got_end_key:
                break

        # Process the collected text to identify rows and columns
        if line_text_info:
            hpos = []
            end_hpos = []
            vpos = []
            end_vpos = []
            text_stats = []

            for text_string in line_text_info:
                # Ensure all necessary keys are present in the text string
                if "END_HPOS" not in text_string.keys():
                    text_string["END_HPOS"] = text_string["HPOS"] + text_string["WIDTH"]
                if "END_VPOS" not in text_string.keys():
                    text_string["END_VPOS"] = text_string["VPOS"] + text_string["HEIGHT"]

                # Record text statistics
                text_stat = (text_string["HPOS"], text_string["END_HPOS"],
                            text_string["VPOS"], text_string["END_VPOS"], text_string["text"])
                hpos.append(text_string["HPOS"])
                end_hpos.append(text_string["END_HPOS"])
                vpos.append(text_string["VPOS"])
                end_vpos.append(text_string["END_VPOS"])
                text_stats.append(text_stat)

            # Cluster VPOS values to group text into rows
            use_vpos_clustering = True
            if use_vpos_clustering and len(vpos)>1:
                new_cls = self.cluster.cluster_points_using_hierarchical(vpos)
                num_rows = new_cls.keys()
                cols = [len(v) for k, v in new_cls.items()]
                num_cols = self.cluster.get_mode_from_arr(cols)
                secondary_num_cols = max(cols)
                
                if secondary_num_cols != num_cols:
                    num_cols = secondary_num_cols

                # Group text into rows and align to columns
                row_grouped_values = self.extraction_utils.group_by_y_min(text_stats, new_cls)
               
                df = self.create_dataframe(row_grouped_values)
                return df, line_text_info

        # Return None if no valid table is extracted
        return None, line_text_info
    

    def extract_from_open_table_without_lines_v2(self, json_data, image, ext_item):
        """
        Extracts tabular data from an image where horizontal lines are absent.

        Args:
            json_data (dict): Contains composed blocks and text layout information.
            image (ndarray): The input image for extraction.
            ext_item (dict): Configuration with keys like "key", "end_identifier", and optional parameters.

        Returns:
            pd.DataFrame or None: A DataFrame with the extracted table data, or None if no table is found.
        """
        # Flags to manage the search for the start and end keys
        search_start_key = True
        search_end_key = True
        got_end_key = False

        # Variables to store extracted text and positional boundaries
        line_text_info = []
        start_hpos = 0
        end_hpos = 0
        end_pos_search = False
        end_hpos_key = []
        # table_start_string = "Lender"
        # table_header_list = ["Lender", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"]
        if "table_header_list" in ext_item:
            table_header_list = ext_item["table_header_list"]
        else:
            table_header_list = None

        # If specified, enable checking for elements near the start HPOS
        use_on_after_hpos = ext_item.get("consider_on_after_hpos", False)

        # Check if HPOS-based end search is enabled
        if "hpos_end_identifier" in ext_item.keys():
            end_pos_search = ext_item["hpos_end_identifier"]
            end_hpos_key = ext_item["hpos_end_key"]
            if not end_hpos_key:
                end_pos_search = False

        # Iterate over the composed blocks in the JSON data
        # for block_index, item in enumerate(json_data["COMPOSED_BLOCKS"]):
        for block_index, item in enumerate(json_data):
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    for ts_id, text_string in enumerate(text_line["STRING"]):

                        # Search for the start key
                        if search_start_key:
                            key_str = [self.extraction_utils.pre_process_text(k) for k in ext_item["key"]]
                            found_str = self.extraction_utils.pre_process_text(text_string["text"])
                            found_match = [k in found_str for k in key_str]

                            if any(found_match):
                                search_start_key = False
                                if start_hpos == 0:
                                    start_hpos = text_string["HPOS"]
                                # Optionally retain the start key in the extracted text
                                if ext_item.get("keep_start_key", False):
                                    line_text_info.append(text_string)
                                continue
                        
                        # Search for the end key
                        else:
                            if search_end_key:
                                key_str = [self.extraction_utils.pre_process_text(k) for k in ext_item["end_identifier"]]
                                found_str = self.extraction_utils.pre_process_text(text_string["text"])
                                found_match = [k in found_str for k in key_str]
                                if any(found_match):
                                    search_end_key = False
                                    got_end_key = True
                                    break

                                # Handle HPOS-based end criteria if enabled
                                if end_pos_search:
                                    key_str = [self.extraction_utils.pre_process_text(k) for k in end_hpos_key]
                                    found_str = self.extraction_utils.pre_process_text(text_string["text"])
                                    found_match = [k in found_str for k in key_str]
                                    if any(found_match):
                                        end_hpos = text_string["HPOS"]
                                if end_hpos != 0:
                                    if (abs(text_string["HPOS"] - end_hpos) <= self.hpos_diff or
                                            text_string["HPOS"] >= end_hpos):
                                        continue
                                # Collect text data based on positional boundaries
                                if use_on_after_hpos:
                                    if start_hpos != 0:
                                        if (abs(text_string["HPOS"] - start_hpos) <= self.hpos_diff or
                                                text_string["HPOS"] >= start_hpos):
                                            line_text_info.append(text_string)
                                else:
                                    line_text_info.append(text_string)
                            if got_end_key:
                                break
                    if got_end_key:
                        break
                if got_end_key:
                    break
            if got_end_key:
                break

        # Process the collected text to identify rows and columns
        if line_text_info:
            hpos = []
            end_hpos = []
            vpos = []
            end_vpos = []
            text_stats = []
            
            for text_string in line_text_info:
                if "WORDS" in text_string:
                    for word in text_string["WORDS"]:

                        # Ensure all necessary keys are present in the text string
                        if "END_HPOS" not in word.keys():
                            word["END_HPOS"] = word["HPOS"] + word["WIDTH"]
                        if "END_VPOS" not in word.keys():
                            word["END_VPOS"] = word["VPOS"] + word["HEIGHT"]

                        # Record text statistics
                        text_stat = (word["HPOS"], word["END_HPOS"],
                                    word["VPOS"], word["END_VPOS"], word["text"])
                        hpos.append(word["HPOS"])
                        end_hpos.append(word["END_HPOS"])
                        vpos.append(word["VPOS"])
                        end_vpos.append(word["END_VPOS"])
                        text_stats.append(text_stat)
            
            use_vpos_clustering = True
            if use_vpos_clustering and len(vpos)>1:
                new_cls = self.cluster.cluster_rows_from_array(vpos)
                # new_cls = self.cluster.cluster_points_using_hierarchical(vpos)
                num_rows = new_cls.keys()
                cols = [len(v) for k, v in new_cls.items()]
                num_cols = self.cluster.get_mode_from_arr(cols)
                secondary_num_cols = max(cols)
                
                if secondary_num_cols != num_cols:
                    num_cols = secondary_num_cols

                # Group text into rows and align to columns
                row_grouped_values = self.extraction_utils.group_by_y_min(text_stats, new_cls)
                # print(row_grouped_values)

                if "table_structure" in ext_item and ext_item["table_structure"] == "vertical":
                    text_by_key = {
                        key: [item[4] for item in value]
                        for key, value in row_grouped_values.items()
                    }
                    
                    row_key = self.get_matching_row(text_by_key, table_header_list)
                    if row_key is not None and row_key >= 0:
                        # print(row_grouped_values[row_key])

                        # Sort by first element
                        sorted_row_data = sorted(row_grouped_values[row_key], key=lambda x: x[0])
                        # header_positions = self.match_and_merge_complex_headers(row_grouped_values[row_key], table_header_list)
                        header_positions = self.match_and_merge_complex_headers(sorted_row_data, table_header_list)

                        # print(header_positions)
                        # raw_df_dict = self.extract_info_from_headers(header_positions, row_grouped_values, horizontal_margin=30)                
                        
                        if header_positions:
                            updated_header_positions = self.increase_x_position(header_positions)
                            # print(updated_header_positions)
                            raw_df_dict = self.extract_info_from_headers(updated_header_positions, row_grouped_values, horizontal_margin=10)


                            sorted_x_position_df = self.sort_text_using_x_position(raw_df_dict)
                            text_df_dict = self.pick_text_from_df(sorted_x_position_df)
                            
                            # Convert to DataFrame
                            df = pd.DataFrame.from_dict(text_df_dict, orient='index')
                            df = df[sorted(df.columns)]

                            return df, line_text_info

                else:
                    # final_grouped_val = {}
                    # col_limits = []
                    
                    # for group_id, entries in row_grouped_values.items():
                    #     if len(entries) == num_cols:
                    #         col_limits = [v[0] for v in entries]
                    #         break

                    # for group_id, entries in row_grouped_values.items():
                    #     col_ids = [''] * num_cols
                    #     if len(entries) == num_cols:
                    #         col_ids = [v[-1] for v in entries]
                    #     else:
                    #         for entry in entries:
                    #             id = self.extraction_utils.find_nearest_element(col_limits, entry[0])
                    #             col_ids[id] = entry[-1]
                    #     final_grouped_val[group_id] = col_ids

                    # # Convert the final grouped values into a DataFrame
                    # columns = [f'Column_{i + 1}' for i in range(num_cols)]
                    # final_col_data = [v for k, v in final_grouped_val.items()]
                    # df = pd.DataFrame(final_col_data, columns=columns)
                    # return df

                    df = self.create_dataframe(row_grouped_values)
                    return df, line_text_info

        # Return None if no valid table is extracted
        return None, line_text_info    


    def get_matching_row(self, data, target_roles):
        """
        Finds the key in `data` whose word tokens have the highest overlap with the given `target_roles`.

        Args:
            data (dict): Dictionary where keys map to a list or set of words.
            target_roles (list): List of role strings to match against.

        Returns:
            The key with the maximum token overlap. Returns None if no matches are found.
        """

        # Create a set of all unique tokens from the target roles
        target_tokens = set()
        for role in target_roles:
            target_tokens.update(role.split())

        # Initialize variables to track the best match
        max_match_count = 0
        best_key = None

        # Iterate over each item in the data to find the best match
        for key, words in data.items():
            # Find intersection of tokens
            matched_tokens = target_tokens.intersection(words)
            match_count = len(matched_tokens)

            # Update best match if this has more matches
            if match_count > max_match_count:
                max_match_count = match_count
                best_key = key

        # Return the key with the highest overlap, or None if no match found
        return best_key


    def clean_header_text(self, header):
        # Remove parenthesis and split into words
        return re.sub(r'\([^)]*\)', '', header).strip().split()
        # return header.strip().split()


    def merge_multiple_bboxes(self, bbox_list):
        """
        Merge multiple bounding boxes into a single bounding box
        that fully contains all the given boxes.

        Args:
            bbox_list (list of tuples): Each tuple should be (x0, x1, y0, y1, label).

        Returns:
            tuple: A single merged bounding box (x0, x1, y0, y1, label),
                where the coordinates define the outer bounds that
                encompass all input boxes. The label is taken from the first box.
        """

        # Find the minimum x0 and y0 (top-left corner)
        x0 = min(b[0] for b in bbox_list)
        y0 = min(b[2] for b in bbox_list)

        # Find the maximum x1 and y1 (bottom-right corner)
        x1 = max(b[1] for b in bbox_list)
        y1 = max(b[3] for b in bbox_list)

        # Use the label from the first bounding box
        label = bbox_list[0][4]

        return (x0, x1, y0, y1, label)


    def match_and_merge_complex_headers(self, bboxes, headers):
        """
        Match sequences of bounding boxes to complex multi-word headers
        and merge their bounding boxes into a single bounding box for each header.

        Args:
            bboxes (list of tuples): Each tuple is (x0, x1, y0, y1, label).
            headers (list of str): List of complex headers, possibly containing multiple words.

        Returns:
            dict: A mapping from header index to merged bounding box coordinates (x0, x1, y0, y1).
        """

        result = {}  # Store matched and merged bounding boxes
        i = 0  # Current index in the list of bounding boxes

        for header_idx, header in enumerate(headers):
            # Clean the header text and split into tokens
            tokens = self.clean_header_text(header)

            matched = []  # Store bounding boxes matching this header
            token_idx = 0  # Pointer for tokens in the header
            j = i  # Separate pointer for traversing bboxes without advancing `i` prematurely

            # Try to match each token in order with sequential bboxes
            while j < len(bboxes) and token_idx < len(tokens):
                if bboxes[j][4] == tokens[token_idx]:
                    # If the bbox label matches the current token, store it
                    matched.append(bboxes[j])
                    token_idx += 1  # Move to the next token
                    j += 1  # Move to the next bbox
                else:
                    # Skip this bbox and check the next
                    j += 1

            if token_idx == len(tokens):
                # All tokens matched successfully
                merged_bbox = self.merge_multiple_bboxes(matched)
                result[header_idx] = merged_bbox[:4]  # Keep only coordinates, ignore label
                i = j  # Advance `i` to continue matching from here

        return result


    def extract_info_from_headers(self, headers, ocr_dict, horizontal_margin):
        result_text = {}

        for row_key, row_text_li in ocr_dict.items():
            updated_row = {}

            updated_row[-1] = []
            header_max_key = max(key for key in headers.keys())
            updated_row[header_max_key + 1] = []

            for x1, x2, y1, y2, text in row_text_li:
                
                header_flag = False
                for header_key, (x1_h, x2_h, y1_h, y2_h) in headers.items():
                    if header_key not in updated_row:
                        updated_row[header_key] = []

                    # Check if the word is below the header and horizontally aligned
                    horizontally_aligned = (x1 >= x1_h - horizontal_margin) and (x1 <= x2_h + horizontal_margin)
                    
                    if horizontally_aligned: # and vertically_below:
                        if header_key in updated_row:
                            updated_row[header_key].append((x1, x2, y1, y2, text))
                            header_flag = True

                if not header_flag:
                    # Find the minimum of the first elements in the tuples
                    min_first_element = min(value[0] for value in headers.values())
                    max_second_element = max(value[1] for value in headers.values())

                    if x1 < min_first_element:
                        # updated_row["before_header"].append((x1, x2, y1, y2, text))
                        updated_row[-1].append((x1, x2, y1, y2, text))

                    elif x1 > max_second_element:
                        # updated_row["after_header"].append((x1, x2, y1, y2, text))
                        updated_row[header_max_key + 1].append((x1, x2, y1, y2, text))

            result_text[row_key] = updated_row
        
        return result_text

    def increase_x_position(self, data):
        # Convert to a list of keys for ordered iteration
        keys = list(data.keys())

        # Check the value at index 1 for the last key
        if data[keys[-1]][1] < 1500:
            # Get the old tuple
            old_tuple = data[keys[-1]]
            
            # Make a new tuple with updated second element
            new_tuple = (old_tuple[0], 1700, old_tuple[2], old_tuple[3])
            
            # Update the dictionary
            data[keys[-1]] = new_tuple

        # Iterate from the second item onward
        for i in range(1, len(keys)):
            prev_key = keys[i - 1]
            curr_key = keys[i]

            prev_x1, prev_x2, prev_y1, prev_y2 = data[prev_key]
            curr_x1, curr_x2, curr_y1, curr_y2 = data[curr_key]

            diff = curr_x1 - prev_x2

            if diff > 100:
                # Add 100 to the previous x2
                # new_prev_x2 = prev_x2 + 100
                new_prev_x2 = prev_x2 + (diff*0.75)
                # Update the tuple in the dict
                data[prev_key] = (prev_x1, new_prev_x2, prev_y1, prev_y2)

        return data


    def sort_text_using_x_position(self, df):
        """
        Sorts lists of text elements within a nested dictionary `df`
        based on their x-coordinate (assumed to be the first element in each tuple).

        Args:
            df (dict): Nested dictionary structured as {row_idx: {header_idx: [tuples]}}.

        Returns:
            dict: The same dictionary with lists sorted left-to-right by x-position.
        """
        for row_idx, row_value in df.items():
            for header_idx, header_text in row_value.items():
                if header_text:
                    # Sort each list of tuples by the x-coordinate (index 0)
                    sorted_data = sorted(header_text, key=lambda x: x[0])
                    row_value[header_idx] = sorted_data  # Update with sorted list

        return df


    def pick_text_from_df(self, df):
        """
        Joins sorted text elements in the nested dictionary `df` into single strings,
        replacing each list with the concatenated text.

        Args:
            df (dict): Nested dictionary structured as {row_idx: {header_idx: [tuples]}}.

        Returns:
            dict: The same dictionary with lists replaced by concatenated strings.
        """
        for row_idx, row_value in df.items():
            for header_idx, header_text in row_value.items():
                if header_text:
                    # Extract the text from each tuple and join with spaces
                    joined_text = " ".join(item[-1] for item in header_text)
                    row_value[header_idx] = joined_text  # Replace list with string
                else:
                    # If no text elements, set as empty string
                    row_value[header_idx] = ""

        return df


    def create_dataframe(self, data, threshold=200):
        rows = []
        
        for key, values in data.items():
            for value in values:
                x, y, x2, y2, text = value
                rows.append([key, x, y, x2, y2, text])
        
        df = pd.DataFrame(rows, columns=["row_key", "x", "y", "x2", "y2", "text"])
        # print(df)
        
        # Perform hierarchical clustering on x-coordinates
        clusters = fcluster(linkage(df["x"].values.reshape(-1, 1), method='ward'), threshold, criterion='distance')
        df["cluster"] = clusters
        # print(df)

        # Sort by row_key, then by y, then by x
        df = df.sort_values(by=["row_key", "y", "x"]).reset_index(drop=True)
        
        # Combine text within the same row_key and cluster
        df = df.groupby(["row_key", "cluster"])['text'].apply(' '.join).reset_index()
        
        # Pivot the table to group similar x values into the same column, keeping row_key as index
        pivot_df = df.pivot(index="row_key", columns="cluster", values="text").fillna('')
        
        return pivot_df

    
    def get_column_index(self, df, col):
        col_index = None
        
        if isinstance(col, int):
            return col
        
        # Ensure col is always treated as a list for consistency
        if not isinstance(col, list):
            col = [col]
        
        # Apply inline transformation to check if any column name appears in the cell value
        cleaned_df = df.applymap(lambda cell: any(str(col_value) in str(cell) for col_value in col))
        
        # Find the first matching column index
        matching_cols = list(np.where(cleaned_df)[1])
        
        if matching_cols:
            col_index = matching_cols[0]
        
        return col_index


    def get_field_value(self, df, field_config):
        """
        Extracts the value of a specific field from a DataFrame based on the given configuration.

        Args:
            df (pd.DataFrame): The DataFrame containing the data.
            field_config (dict): Configuration for extracting the field value. 
                                Contains keys like "field_string", "field_string_col", 
                                "probable_field_value_col", "probable_field_value_next_row",
                                and "probable_place_in_table".

        Returns:
            str or None: The extracted field value, or None if the value cannot be determined.
        """
         
        pattern_field = False
        return_as_list = False

        # Extract relevant configuration parameters
        field_name = field_config["field_string"]
        # field_string_col_index = field_config["field_string_col"]
        field_string_col = field_config["field_string_col"]
        probable_value_col = field_config["probable_field_value_col"]
        probable_value_next_row = field_config["probable_field_value_next_row"]
        probable_place = field_config["probable_place_in_table"]
        
        if "value_return_as_table" in field_config:
            return_as_list = field_config["value_return_as_table"]

        if "pattern_field" in field_config:
            pattern_field = field_config["pattern_field"]

        field_string_col_index = None
        probable_value_col_index = None
    
        field_string_col_index = self.get_column_index(df, field_string_col)
        probable_value_col_index = self.get_column_index(df, probable_value_col)
        
        print("Index of column where Field String is present: " + str(field_string_col_index))
        print("Index of column where Field Value is present: " + str(probable_value_col_index))

        result = None

        if field_string_col_index is not None and probable_value_col_index is not None:
            if probable_value_col_index >= 0:
                
                # Case 1: Field value is in the same row as the field string
                if probable_place == "same_row":
                    # if isinstance(field_name, list):
                    #     pattern = '|'.join(map(re.escape, field_name))  # Escape special characters
                    # else:
                        # pattern = re.escape(field_name)  # Escape in case of special characters in the string
                    pattern = field_name

                    if pattern_field:
                        result = df.loc[
                            df.iloc[:, field_string_col_index].str.match(pattern, na=False),
                            df.columns[probable_value_col_index]
                        ]
                    else:
                        if isinstance(field_name, list):
                           result = df.loc[
                                df.iloc[:, field_string_col_index].isin(pattern),
                                df.columns[probable_value_col_index]
                            ]
                        else:                            
                            result = df.loc[
                                df.iloc[:, field_string_col_index] == pattern,
                                df.columns[probable_value_col_index]
                            ]
                    
                # Case 2: Field value is in the rows below the field string
                elif probable_place == "below_row":
                    matching_indices=None
                    if isinstance(field_name, list):
                        TEMP_match=[]
                        
                        for idx in field_name:
                            temp_matching_indices = df[df.iloc[:, field_string_col_index].str.contains(idx, na=False)].index
                            TEMP_match.append(temp_matching_indices)
                        
                        if TEMP_match:
                            # matching_indices=max(TEMP_match)
                            matching_indices = max(TEMP_match, key=lambda idx: idx.max() if not idx.empty else float('-inf'))
                    else:
                        matching_indices = df[df.iloc[:, field_string_col_index].str.contains(field_name, na=False)].index
                    
                    if not matching_indices.empty:
                        if isinstance(probable_value_next_row, str):
                            matching_end_indices = df[df.iloc[:, field_string_col_index].str.contains(probable_value_next_row, na=False)].index
                            
                            if not matching_end_indices.empty:
                                start_row = matching_indices[0] + 1
                                end_row = matching_end_indices[0]
                                
                                # combined_values = [" ".join(df.iloc[start_row:end_row, probable_value_col_index].astype(str))]
                                # result = pd.Series(combined_values)
                                values_list = df.iloc[start_row:end_row, probable_value_col_index].astype(str).tolist()
                                result = pd.Series([values_list if return_as_list else " ".join(values_list)])
                        
                        elif isinstance(probable_value_next_row, list):
                            TEMP_match=[]
                        
                            for idx in probable_value_next_row:
                                temp_matching_indices = df[df.iloc[:, probable_value_col_index].str.contains(idx, na=False)].index
                                TEMP_match.append(temp_matching_indices)
                            
                            if TEMP_match:
                                matching_end_indices = max(TEMP_match, key=lambda idx: idx.max() if not idx.empty else float('-inf'))

                                if not matching_end_indices.empty:
                                    start_row = matching_indices[0] + 1
                                    end_row = matching_end_indices[0]
                                    
                                    # combined_values = [" ".join(df.iloc[start_row:end_row, probable_value_col_index].astype(str))]
                                    # result = pd.Series(combined_values)
                                    values_list = df.iloc[start_row:end_row, probable_value_col_index].astype(str).tolist()
                                    result = pd.Series([values_list if return_as_list else " ".join(values_list)])

                        else:
                            start_row = matching_indices[0]
                            end_row = start_row + probable_value_next_row + 1
                            
                            if probable_value_next_row > 0:
                                # combined_values = [" ".join(df.iloc[start_row:end_row, probable_value_col_index].astype(str))]
                                # result = pd.Series(combined_values)
                                values_list = df.iloc[start_row:end_row, probable_value_col_index].astype(str).tolist()
                                result = pd.Series([values_list if return_as_list else " ".join(values_list)])

                            elif probable_value_next_row == "False":
                                start_row = start_row + 1
                                end_row = df.index[-1]
                                
                                combined_values = df.iloc[start_row:end_row + 1, probable_value_col_index].astype(str).tolist()

                                # if start_row == end_row:
                                #     # combined_values = [" ".join(df.iloc[start_row:end_row + 1, probable_value_col_index].astype(str))]
                                #     combined_values = df.iloc[start_row:end_row + 1, probable_value_col_index].astype(str).tolist()
                                    
                                # else:
                                #     # combined_values = [" ".join(df.iloc[start_row:end_row, probable_value_col_index].astype(str))]
                                #     combined_values = df.iloc[start_row:end_row, probable_value_col_index].astype(str).tolist()

                                # result = pd.Series(combined_values)
                                result = pd.Series([combined_values if return_as_list else " ".join(combined_values)])

                            else:
                                if matching_indices[0] < (df.index[-1]-1):
                                    # result = df.iloc[matching_indices + 1, probable_value_col_index].reset_index(drop=True)
                                    result = df.iloc[matching_indices, probable_value_col_index].reset_index(drop=True)
                                else:
                                    result = df.iloc[matching_indices, probable_value_col_index].reset_index(drop=True)

                                if not return_as_list:
                                    result = pd.Series([" ".join(result.astype(str).tolist())])
                            
                elif probable_place == "below_next_row":
                    matching_indices = None

                    if isinstance(field_name, list):
                        TEMP_match = []
                        
                        for idx in field_name:
                            temp_matching_indices = df[df.iloc[:, field_string_col_index].str.contains(idx, na=False)].index
                            TEMP_match.append(temp_matching_indices)
                        
                        if TEMP_match:
                            # matching_indices=max(TEMP_match)
                            matching_indices = max(TEMP_match, key=lambda idx: idx.max() if not idx.empty else float('-inf'))
                    else:
                        matching_indices = df[df.iloc[:, field_string_col_index].str.contains(field_name, na=False)].index

                    if not matching_indices.empty:
                        if isinstance(probable_value_next_row, str):
                            matching_end_indices = df[df.iloc[:, field_string_col_index].str.contains(probable_value_next_row, na=False)].index
                            
                            if not matching_end_indices.empty:
                                start_row = matching_indices[0] + 1
                                end_row = matching_end_indices[0]
                                
                                # combined_values = [" ".join(df.iloc[start_row:end_row, probable_value_col_index].astype(str))]
                                # result = pd.Series(combined_values)
                                values_list = df.iloc[start_row:end_row, probable_value_col_index].astype(str).tolist()
                                result = pd.Series([values_list if return_as_list else " ".join(values_list)])

                        
                        elif isinstance(probable_value_next_row, list):  
                            start_row = matching_indices[0] + 1

                            TEMP_match = []
                            for idx in probable_value_next_row:
                                temp_matching_indices = df[df.iloc[:, field_string_col_index].str.contains(idx, na=False)].index
                                TEMP_match.append(temp_matching_indices)

                            if TEMP_match:
                                end_matching_indices = max(TEMP_match, key=lambda idx: idx.max() if not idx.empty else float('-inf'))
                                end_row = end_matching_indices[0]

                            if start_row and end_row:
                                # combined_values = [" ".join(df.iloc[start_row:end_row, probable_value_col_index].astype(str))]
                                # result = pd.Series(combined_values)
                                values_list = df.iloc[start_row:end_row, probable_value_col_index].astype(str).tolist()
                                result = pd.Series([values_list if return_as_list else " ".join(values_list)])

                        else:
                            start_row = matching_indices[0] + 1
                            end_row = start_row + probable_value_next_row

                            if start_row and end_row:
                                values_list = df.iloc[start_row:end_row, probable_value_col_index].astype(str).tolist()
                                result = pd.Series([values_list if return_as_list else " ".join(values_list)])

                return result.iloc[0] if result is not None and not result.empty else None
        
        return result


    def make_headers_first_row(self, df):
        """
        Converts the current headers of the DataFrame into the first row of the DataFrame.

        Args:
            df (pd.DataFrame): The DataFrame to modify.

        Returns:
            pd.DataFrame: The updated DataFrame with new column headers.
        """
        # Add the current column headers as the first row
        df.loc[-1] = df.columns  # Insert headers as a new row
        df.index = df.index + 1  # Adjust the indices to account for the new row
        df.sort_index(inplace=True)  # Reorder the rows by index

        # Rename the columns to generic names (e.g., Column1, Column2, ...)
        df.columns = [f"Column{i+1}" for i in range(len(df.columns))]

        return df


if __name__ == '__main__':
    # Entry point for the script; currently does nothing
    pass
