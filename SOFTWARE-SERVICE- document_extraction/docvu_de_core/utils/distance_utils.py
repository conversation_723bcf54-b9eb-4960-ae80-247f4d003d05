import math
from sklearn.cluster import KMeans
import numpy as np


class DistanceUtils:
    def __init__(self):
        pass

    @staticmethod
    def on_same_vertical_line(item1, item2, threshold=20):
        return abs(item1["HPOS"] - item2["HPOS"]) <= threshold

    @staticmethod
    def on_same_horizontal_line(item1, item2, threshold=20):
        return abs(int(item1["VPOS"]) - int(item2["VPOS"])) <= threshold

    def is_value_horizontally_near(self, item1, item2, threshold=40):
        return self.on_same_horizontal_line(item1, item2, threshold)

    @staticmethod
    def is_center_on_vertical_line(item1, item2, threshold=20):
        return abs((item1["HPOS"]+(item1['WIDTH']/2)) - (item2["HPOS"]+(item2['WIDTH']/2))) <= threshold

    @staticmethod
    def get_zones(text_block, tol=10):
        start_pos = sorted([block["HPOS"] for block in text_block])
        diff = [(j-i) > tol for i, j in zip(start_pos[:-1], start_pos[1:])]
        if any(diff):
            kmeans = KMeans(init="random", n_clusters=2, n_init=5, max_iter=50, random_state=42)
            hpos = []
            for block in text_block:
                hpos.append(block["HPOS"]) # block["END_HPOS"]])
            hpos = np.reshape(hpos,(-1,1))
            kmeans.fit(hpos)
            labels = kmeans.labels_[:len(hpos)]
            #print(labels)
            zone_1 = []
            zone_2 = []
            for lab, val in zip(labels, text_block):
                if lab == 0:
                    zone_1.append(val)
                else:
                    zone_2.append(val)
            left = zone_1
            right = zone_2
            if zone_1 and zone_2:
                if zone_1[0]["HPOS"] > zone_2[0]["HPOS"]:
                    left = zone_2
                    right = zone_1
            return left, right
        else:
            return text_block, text_block

    @staticmethod
    def is_in_left_zone(item1, item2, threshold=20):
        return abs((item1["HPOS"] + (item1['WIDTH'] / 2)) - (item2["HPOS"] + (item2['WIDTH'] / 2))) <= threshold

    @staticmethod
    def get_centroid_dist(item1, item2):
        return (item1["HPOS"] + (item1['WIDTH'] / 2)) - (item2["HPOS"] + (item2['WIDTH'] / 2))

    @staticmethod
    def get_distance(item1, item2):
        distance = math.sqrt(((item1["HPOS"] - item2["HPOS"]) ** 2) + ((item1["VPOS"] - item2["VPOS"]) ** 2))
        return distance

    @staticmethod
    def get_horizontal_distance(item1, item2):
        return abs(item1["HPOS"] - item2["HPOS"])

    @staticmethod 
    def get_vertical_distance(item1, item2):
        return abs(item1["VPOS"] - item2["VPOS"])
    
    def is_value_just_down(self, item1, item2, threshold=40):
        if self.on_same_horizontal_line(item1, item2, threshold) and self.on_same_vertical_line(item1, item2):
            return True
        return False
    

    def is_value_down_inline(self, item1, item2, lower_bound=0.3, upper_bound=3.0, threshold=300):
        """
        Check if item2 is directly below item1, considering vertical alignment
        and overlap within a percentage of the width of item2.

        :param item1: The reference element (field key).
        :param item2: The element being checked.
        :param lower_bound: Lower bound percentage of item2's width for overlap.
        :param upper_bound: Upper bound percentage of item2's width for overlap.
        :param threshold: Additional threshold for horizontal alignment.
        :return: True if item2 is directly below item1, False otherwise.
        """
        # Calculate the horizontal boundaries of item1 and item2
        item1_left = item1["HPOS"]
        item1_right = item1["HPOS"] + item1["WIDTH"]
        item2_left = item2["HPOS"]
        item2_right = item2["HPOS"] + item2["WIDTH"]

        # Calculate the overlap length
        #overlap_length = max(0, min(item1_right, item2_right) - max(item1_left, item2_left))
        overlap_length = max(0, abs(max(item1_right, item2_right) - min(item1_left, item2_left)))

        # Calculate the required overlap range as a percentage of item2's width
        required_overlap_min = lower_bound * min(item1["WIDTH"], item2["WIDTH"])
        required_overlap_max = upper_bound * max(item1['WIDTH'], item2["WIDTH"])

        # Check if the overlap falls within the required range
        overlap_condition = required_overlap_min <= overlap_length <= required_overlap_max
        threshold_condition1 = item2['VPOS'] > item1['VPOS'] + item1['HEIGHT']
        threshold_condition2 = abs(item1["HPOS"] - item2["HPOS"]) <= threshold
        # Directly below implies vertical alignment, overlap within range, and horizontal proximity
        is_directly_below = (
                overlap_condition and
                threshold_condition1
                 and  # Ensures item2 is below item1
                threshold_condition2 # Check for proximity
        )

        return is_directly_below


if __name__ == '__main__':
    du = DistanceUtils()
    sample = [{'text': 'American Land Title Association',
    'HPOS': 100, 'VPOS': 107, 'WIDTH': 331, 'HEIGHT': 16, 'END_HPOS': 431, 'END_VPOS': 124},
    {'text': 'Oregon Title Insurance Rating Organization (OTIRO)',
    'HPOS': 1050, 'VPOS': 107, 'WIDTH': 543, 'HEIGHT': 16, 'END_HPOS': 1593, 'END_VPOS': 123},
    {'text': 'ALTA Loan Policy (6-17-2006) Revised 12/6/2010',
    'HPOS': 100, 'VPOS': 133, 'WIDTH': 493, 'HEIGHT': 16, 'END_HPOS': 593, 'END_VPOS': 150},
    {'text': 'OTIRO No. PL-05',
    'HPOS': 1422, 'VPOS': 133, 'WIDTH': 166, 'HEIGHT': 16, 'END_HPOS': 1588, 'END_VPOS': 149},
    {'text': 'File No.: 25834',
    'HPOS': 1440, 'VPOS': 159, 'WIDTH': 148, 'HEIGHT': 16, 'END_HPOS': 1588, 'END_VPOS': 175}]
    labels = du.get_zones(sample)
    print('Labels: ', labels)