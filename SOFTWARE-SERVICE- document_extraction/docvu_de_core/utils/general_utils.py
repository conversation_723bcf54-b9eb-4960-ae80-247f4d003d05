import csv
import re
import json
import os
from docvu_de_core.modules.DateFinder import Date<PERSON>inder
from docvu_de_core.extraction_item import BaseFieldData, BaseTableData, TableRow


class GeneralUtils:
    def __init__(self, return_date_format="%m/%d/%Y", debug=False):
        self.date_finder = DateFinder(return_format=return_date_format)
        self.min_zip_code_len = '5'
        self.debug = False

    @staticmethod
    def get_bbox(bbox, idx=None):
        if idx is not None:
            return {"x": bbox[idx][0],
                    "y": bbox[idx][1],
                    "width": abs(bbox[idx][0] - bbox[idx][2]),
                    "height": abs(bbox[idx][1] - bbox[idx][3])}
        else:
            return {"x": bbox[0],
                    "y": bbox[1],
                    "width": abs(bbox[0] - bbox[2]),
                    "height": abs(bbox[1] - bbox[3])}


    @staticmethod
    def get_base_format(_sub_field, default="text", force=False):
        return_type = _sub_field.get('return_type', False) if not force else False
        if return_type:
            if return_type == 'table':
                # Initialize an empty TableData object with the given metadata
                # Rows can be added later as they become available
                sub_field = BaseTableData(**_sub_field)

            elif return_type == 'text':
                # Initialize BaseFieldData with available information
                # Additional details can be added later as they become available
                sub_field = BaseFieldData(**_sub_field)

                # 'start_page' may not always be directly applicable, this is just an example
                if 'start_page' in _sub_field:
                    sub_field["page_number"] = _sub_field["start_page"]
            else:
                # Handle unknown return_type
                if default == "table":
                    sub_field = BaseTableData(**_sub_field)
                else:
                    sub_field = BaseFieldData(**_sub_field)
                    if 'start_page' in _sub_field:
                        sub_field["page_number"] = _sub_field["start_page"]
                # print(f"Warning: Unrecognized return_type '{return_type}'. Item will not be processed.")
        else:
            # Handle case when return_type is not specified
            # print("No return_type specified. Item will not be processed.")
            if default == "table":
                sub_field = BaseTableData(**_sub_field)
            else:
                sub_field = BaseFieldData(**_sub_field)
                if 'start_page' in _sub_field:
                    sub_field["page_number"] = _sub_field["start_page"]
        return sub_field

    def get_mapped_key_values(self, sub_field, sub_item_dict, item, page, default="table"):
        result = []
        for sub_keys in sub_field["sub_keys"]:
            _sub_key = sub_keys.copy()
            sub_key = self.get_base_format(_sub_key, default=default)
            found = False
            for key1, value1 in sub_item_dict.items():
                if sub_key["field_name"] == key1:
                    sub_key["value"] = value1
                    sub_key["key_info"] = item["key_info"]
                    sub_key["page_number"] = page
                    found = True
            if not found:
                sub_key["value"] = None
                sub_key["key_info"] = item["key_info"]
                sub_key["page_number"] = page
            result.append(sub_key)
        return result

    def get_updated_row(self, search_key_field_output, page_no, sub_field):
        line_number = search_key_field_output.line_number
        bbox = search_key_field_output.bbox
        sub_field["confidence_indicator"] = 0.95
        sub_field["color_indicator"] = 1
        sub_field["start_page"] = min(page_no)
        sub_field["end_page"] = max(page_no)
        if search_key_field_output.value:
            for idx, v in enumerate(search_key_field_output.value):
                columns = [
                    BaseFieldData(id=sub_field['field_id'],
                                  field_name=sub_field['field_name'], key=sub_field['key'],
                                  value=v,
                                  post_processing_value=v,
                                  page_number=sub_field["page_number"],
                                  line_number=line_number[idx], confidence_indicator=95,
                                  color_indicator=1,
                                  field_name_coordinates={"x": 0, "y": 0, "width": 0, "height": 0},
                                  field_value_coordinates=self.get_bbox(bbox, idx))
                ]
                row = TableRow(row_number=sub_field.n_rows(),
                               page_number=sub_field["page_number"],
                               line_number=0,
                               columns=columns)
                sub_field.add_row(row)
        return sub_field

    @staticmethod
    def write_results_to_csv(results, result_path):
        keys = results[0].keys()

        with open(f"{result_path}/result.csv", 'w', newline='') as output_file:
            dict_writer = csv.DictWriter(output_file, keys)
            dict_writer.writeheader()
            dict_writer.writerows(results)

    def get_regex(self):
        min_zip_code_len = self.min_zip_code_len
        re1 = '\d{' + min_zip_code_len + ',}' + '\-?\d{' + min_zip_code_len + ',}'
        regex_in1 = r'{}'.format(re1)

        re2 = '\d{' + min_zip_code_len + ',}'
        regex_in2 = r'{}'.format(re2)
        return regex_in1, regex_in2

    def get_regex_with_state_code(self):
        min_zip_code_len = self.min_zip_code_len
        re1 = '\\b[A-Z]{2,}\\b ' + '\d{' + min_zip_code_len + ',}' + '\-?\d{' + min_zip_code_len + ',}'
        regex_in1 = r'{}'.format(re1)

        re2 = '\\b[A-Z]{2,}\\b ' + '\d{' + min_zip_code_len + ',}'
        regex_in2 = r'{}'.format(re2)
        return regex_in1, regex_in2

    def check_if_alphanumeric(self, field_str, use_state_with_zip=True):
        s1 = re.sub('[^0-9a-zA-Z]+', '', field_str)
        if s1.isdigit():
            # print(f"For Text = {s1}, Got all digits")
            return False
        if use_state_with_zip:
            regex_in1, regex_in2 = self.get_regex_with_state_code()
            got = re.findall(regex_in1, field_str)
            # print(f"For Text = {s}, Got match 1 = {got}")
            if got:
                return True
            else:
                got = re.findall(regex_in2, field_str)
                # print(f"For Text = {s}, Got match 2 = {got}")
                if got:
                    return True
                else:
                    return False
        return True

    def split_address_field_into_multiple_addresses(self, field_value, regex_in, zip_codes):
        address = []
        indices = [(m.start(0), m.end(0)) for m in re.finditer(regex_in, field_value)]
        # print(f"indices = {indices}")
        last = []
        final_indices = []
        to_attach_with_prev = None
        continued = False
        final_zip_codes = []
        for index, (start, end) in enumerate(indices):
            temp1 = None
            if index == 0:
                temp = field_value[0:end]
            else:
                temp = field_value[last[index - 1]:end]
            last.append(end)
            if continued:
                continued = False
                if to_attach_with_prev.strip()[-1] != ',':
                    to_attach_with_prev += ','
                temp1 = to_attach_with_prev + temp

            # print("Temp = {}".format(temp))
            if not self.check_if_alphanumeric(temp):
                to_attach_with_prev = temp
                continued = True
                continue
            m = re.search(regex_in, temp)
            # print("======M = {}".format(m))

            if temp1 is not None:
                # print("Final Temp = {}".format(temp1))
                address.append(temp1)
                a_len = len(to_attach_with_prev)
                final_indices.append((a_len + m.start(), a_len + m.end()))
                final_zip_codes.append(zip_codes[index])
            else:
                address.append(temp)
                final_zip_codes.append(zip_codes[index])
                final_indices.append((m.start(), m.end()))
        # print(address)
        if not address:
            last = []
            final_indices = []
            to_attach_with_prev = None
            continued = False
            final_zip_codes = []
            for index, (start, end) in enumerate(indices):
                temp1 = None
                if index == 0:
                    temp = field_value[0:end]
                else:
                    temp = field_value[last[index - 1]:end]
                last.append(end)
                if continued:
                    continued = False
                    if to_attach_with_prev.strip()[-1] != ',':
                        to_attach_with_prev += ','
                    temp1 = to_attach_with_prev + temp

                # print("Temp = {}".format(temp))
                if not self.check_if_alphanumeric(temp, use_state_with_zip=False):
                    to_attach_with_prev = temp
                    continued = True
                    continue
                m = re.search(regex_in, temp)
                # print("======M = {}".format(m))

                if temp1 is not None:
                    # print("Final Temp = {}".format(temp1))
                    address.append(temp1)
                    a_len = len(to_attach_with_prev)
                    final_indices.append((a_len + m.start(), a_len + m.end()))
                    final_zip_codes.append(zip_codes[index])
                else:
                    address.append(temp)
                    final_zip_codes.append(zip_codes[index])
                    final_indices.append((m.start(), m.end()))
        return address, final_indices, final_zip_codes

    @staticmethod
    def remove_special_characters(text):
        special_characters = "!@#$%^&*()-+?_=,<>/"
        new_text = ""
        for c in text:
            if c not in special_characters:
                new_text += c
        return new_text

    def process_each_address(self, addresses, indices, zip_codes):
        results = {}
        for index, (addr, (start, end), zcode) in enumerate(zip(addresses, indices, zip_codes)):
            print("Processing Address = {}".format(addr))
            ind = start
            val = addr[0:ind]
            # print(f"Without Zip Code Raw = {val}")
            v1 = [n.split('.') for n in val.split(',')]
            v = [x.strip() for xs in v1 for x in xs if len(x.strip()) > 0]

            if v[-1][-1] == ',':
                v[-1] = v[-1][0:-1]
            if v[0][0] in [',', '&']:
                v[0] = v[0][1:]
            if v[-1].strip().lower() in ["zip code", "zipcode", "zip_code"]:
                v.remove(v[-1])
            # print(f"Without Zip Code Final = {v}")
            if len(v) < 2:
                return None

            if len(v) == 2:
                vf = v[-1].split(' ')
                v.remove(v[-1])
                v += vf

            state_add = v[-1].strip()
            if "state" in state_add.lower():
                a = re.search(r'\bstate\b', state_add.lower())
                if a is None:
                    state_add = self.remove_special_characters(state_add)
                    a = re.search(r'\bstate\b', state_add.lower())
                ind = a.end()
                state_add = state_add[ind::]
            city_add = v[-2].strip()
            if "city" in city_add.lower():
                a = re.search(r'\bcity\b', city_add.lower())
                ind = a.end()
                if a.start() == 0:
                    city_add = city_add[ind::]
            line_add = ",".join(v[0:-2])
            full_address = line_add + ", " + city_add + ", " + state_add + " " + zcode
            temp = {"full_address": full_address, "line_add": line_add,
                    "city_add": city_add, "state_add": state_add, "zip_add": zcode}
            results[index + 1] = temp

        return results

    def extract_address_components(self, field_value):
        regex_in1, regex_in2 = self.get_regex()
        temp_zip_code = ' 00000'
        result = None
        zip_add = re.findall(regex_in1, field_value)
        if self.debug:
            print("Found ZIP_ADD in Address String = {}".format(zip_add))
        if not zip_add:
            zip_add = re.findall(regex_in2, field_value)
            if self.debug:
                print("Found ZIP_ADD in Address String = {}".format(zip_add))
            if zip_add:
                addresses, indices, zip_add = (
                    self.split_address_field_into_multiple_addresses(field_value, regex_in2, zip_add))
                result = self.process_each_address(addresses, indices, zip_add)
            else:
                field_value += temp_zip_code
                zip_add = re.findall(regex_in2, field_value)
                if zip_add:
                    addresses, indices, zip_add = (
                        self.split_address_field_into_multiple_addresses(field_value, regex_in2, zip_add))
                    result = self.process_each_address(addresses, indices, zip_add)
        else:
            addresses, indices, zip_add = (
                self.split_address_field_into_multiple_addresses(field_value, regex_in1, zip_add))
            result = self.process_each_address(addresses, indices, zip_add)
            if not result:
                zip_add = re.findall(regex_in2, field_value)
                if self.debug:
                    print("Found ZIP_ADD in Address String = {}".format(zip_add))
                if zip_add:
                    addresses, indices, zip_add = (
                        self.split_address_field_into_multiple_addresses(field_value, regex_in2, zip_add))
                    result = self.process_each_address(addresses, indices, zip_add)
                else:
                    field_value += temp_zip_code
                    zip_add = re.findall(regex_in2, field_value)
                    if zip_add:
                        addresses, indices, zip_add = (
                            self.split_address_field_into_multiple_addresses(field_value, regex_in2, zip_add))
                        result = self.process_each_address(addresses, indices, zip_add)
        return result

    @staticmethod
    def load_word_json_by_page(page_num, folder_path):
        combined_word_json_path = f"{folder_path}/combined_words.json"
        if os.path.isfile(combined_word_json_path):
            with open(combined_word_json_path) as temp_f:
                json_data = json.loads(temp_f.read())
                return json_data[str(page_num)]
        else:
            return None


    def get_dates_in_text(self, text, return_position_info=False, return_first=True):
        return self.date_finder.get_dates_in_text(text, return_position_info, return_first)


    @staticmethod
    def get_amount_in_text(text):
        text = text.replace("$ ", "$")
        split_text = text.split(' ')
        for v in split_text:
            if "$" in v:
                return v
        return text

    def validate_value_type(self, value, value_type):
        number_type = r'^[0-9\W_]+$'
        if value_type == "number":
            if bool(re.match(number_type, value.replace(" ", ""))):
                return True
        elif value_type == "date":
            dates = list(self.get_dates_in_text(value))
            if len(dates) < 1:
                return False
            return True
        elif value_type == "text":
            return True

        return False

    # Function to check if a date is valid
    def ends_with_date_or_year(self, value):
        # Simple regex for date formats (you might need to adjust this based on your date formats)
        # date_regex = r"\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b"
        date_regex = r"(?:\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b|\b\d{4}\b)$"
        return bool(re.search(date_regex, value))

    def has_year(self, value):
        # Regex to check if a date or year occurs anywhere in the string
        date_regex = r"(?:\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b|\b\d{4}\b)"
        return bool(re.search(date_regex, value))




