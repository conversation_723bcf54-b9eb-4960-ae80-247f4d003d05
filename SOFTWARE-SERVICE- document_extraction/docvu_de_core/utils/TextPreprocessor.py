import re
import nltk
from nltk.corpus import stopwords, wordnet
from nltk.tokenize import word_tokenize
from nltk.stem import <PERSON><PERSON><PERSON><PERSON>, WordNetLemmatizer
import string

class TextPreprocessor:
    """
    A class for preprocessing text data, specifically designed for cleaning up OCR
    text outputs. It removes non-ASCII characters, tokenizes the text, filters out
    stop words, removes punctuation, and performs stemming and lemmatization,
    optionally retaining numeric tokens.

    Attributes:
        stop_words (set): A set of English stop words loaded from NLTK.
    """

    # Initialize stopwords as a class attribute
    stop_words = set()
    is_stop_words_initialised = False

    @classmethod
    def initialize_stop_words(cls):
        """
        Ensures that stopwords are downloaded and initializes them as a class attribute.
        """
        if not cls.is_stop_words_initialised:
            try:
                cls.stop_words = set(stopwords.words('english'))
            except LookupError:
                nltk.download('stopwords')
                cls.stop_words = set(stopwords.words('english'))
            finally:
                cls.is_stop_words_initialised = True

    @staticmethod
    def remove_non_ascii(text):
        """
        Removes non-ASCII characters from the text.
        """
        return re.sub(r'[^\x00-\x7F]+', ' ', text)

    @staticmethod
    def tokenize_text(text):
        """
        Tokenizes the text into individual words.
        """
        return word_tokenize(text.lower())

    @staticmethod
    def remove_punctuation(words, tokenize = True):
        """
        Removes punctuation and special characters from a list of words.
        """
        TextPreprocessor.initialize_stop_words()
        table = str.maketrans('', '', string.punctuation)
        return [word.translate(table) for word in words] if tokenize else ''.join([word.translate(table) for word in words])

    @staticmethod
    def filter_stop_words_and_non_alpha(words, keep_numeric=False):
        """
        Filters out stop words from the list of words and optionally retains numeric tokens.
        """
        return [word for word in words if word not in TextPreprocessor.stop_words and (word.isalpha() or (keep_numeric and word.isdigit()))]

    @staticmethod
    def stem_words(words):
        """
        Applies stemming to a list of words, reducing them to their root form.
        """
        stemmer = PorterStemmer()
        return [stemmer.stem(word) for word in words]

    @staticmethod
    def lemmatize_words(words):
        """
        Applies lemmatization to a list of words, reducing them to their base or dictionary form.
        """
        lemmatizer = WordNetLemmatizer()
        return [lemmatizer.lemmatize(word) for word in words]

    @classmethod
    def preprocess(cls, text, return_string=False, bag_of_words=True, keep_numeric=False, use_stemming=False, use_lemmatization=False):
        """
        Preprocesses the input text by applying a series of preprocessing steps including
        removal of non-ASCII characters, tokenization, stop words filtering, punctuation removal,
        and optionally stemming or lemmatization.
        """
        text = cls.remove_non_ascii(text)
        words = cls.tokenize_text(text)
        words = cls.remove_punctuation(words)
        filtered_words = cls.filter_stop_words_and_non_alpha(words, keep_numeric=keep_numeric)
        
        if use_stemming:
            filtered_words = cls.stem_words(filtered_words)
        elif use_lemmatization:
            filtered_words = cls.lemmatize_words(filtered_words)
        
        if return_string:
            return ' '.join(list(set(filtered_words))) if bag_of_words else ' '.join(filtered_words)
        else:
            return set(filtered_words) if bag_of_words else filtered_words
      
    @staticmethod  
    def strip_and_remove_starting_substring(string, substrings):
        """
        Strips the string, checks if it starts with any of the substrings,
        and removes the matching substring if found.

        :param string: The target string to process.
        :param substrings: A list of substrings to check against the target string.
        :return: A tuple where the first element is a boolean indicating whether a match was found,
                and the second element is the modified string with the substring removed if a match was found.
        """
        stripped_string = string.strip()
        for sub in substrings:
            if stripped_string.startswith(sub):
                # Remove the matching substring and return
                return True, stripped_string[len(sub):]
        # Return the original string (stripped) if no match is found
        return False, stripped_string
