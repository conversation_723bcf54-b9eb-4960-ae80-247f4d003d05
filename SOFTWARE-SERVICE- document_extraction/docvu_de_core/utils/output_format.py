from docvu_de_core.utils.process_utils import *

# from docvu_de_core.io import BaseTableData, BaseFieldData

def save_to_json(data, file_path):
    """
    Saves a list or dictionary to a JSON file.

    :param data: The list or dictionary to save.
    :param file_path: The path of the file where the data will be saved.
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"An error occurred while writing to the file: {e}")


class ResponseFormatter:
    def __init__(self):
        self.dist_utils = DistanceUtils()
        self.general_utils = GeneralUtils()
        self.process_utils = ProcessUtils()
        self.multiple_value_for_dict_list = ['Exception_Header', 'Exception_Text', 'Exception_Text_Sequence_Number']


    @staticmethod
    def convert_bbox_to_ratio(value, img_shape):
        """
            img_shape (height, width, channels)
        """
        ratio = {"height": 0, "width": 0, "x": 0, "y": 0}

        if value is None:
            return ratio

        try:
            if type(value) == dict:
                x, y, w, h = int(value["HPOS"]), int(value["VPOS"]), int(value["WIDTH"]), int(value["HEIGHT"])
                ratio["height"] = h / img_shape[0]
                ratio["width"] = w / img_shape[1]
                ratio["x"] = x / img_shape[1]
                ratio["y"] = y / img_shape[0]
            elif type(value) == list:
                # Get all 4 extreme corner points using all the bounding boxes
                x1, y1, x2, y2 = 9999999, 9999999, 0, 0
                for item in value:
                    if item == dict:
                        if x1 > int(item["HPOS"]):
                            x1 = int(item["HPOS"])
                        if y1 > int(item["VPOS"]):
                            y1 = int(item["VPOS"])
                        if x2 < (int(item["HPOS"]) + int(item["WIDTH"])):
                            x2 = int(item["HPOS"]) + int(item["WIDTH"])
                        if y2 < (int(item["VPOS"]) + int(item["HEIGHT"])):
                            y2 = int(item["VPOS"]) + int(item["HEIGHT"])

                    ratio["height"] = abs(y1 - y2) / img_shape[0]
                    ratio["width"] = abs(x1 - x2) / img_shape[1]
                    ratio["x"] = x1 / img_shape[1]
                    ratio["y"] = y2 / img_shape[0]

            return ratio
        except Exception as ex:
            print("Error in converting bbox to ratio : ", str(ex))
            return ratio


    def post_process_results(self, value, field_info):
        # Merge the list of text into single text
        # Convert the text into date format so that we dont have any extra text
        if field_info["type"] == 'address':
            if type(value) == list:
                value = " ".join(value)
            value = self.general_utils.extract_address_components(value)

        # # Convert the text into date format so that we dont have any extra text
        elif field_info["type"] == "date":
            if isinstance(value, list):
                value = value[0]
            dates = self.general_utils.get_dates_in_text(value)
            if len(dates) > 0:
                value = dates[0][0].strftime('%m-%d-%Y')
                return value

        elif field_info["type"] == "number":
            value = ''.join([c for c in value if not c.isalpha()])
            return value
            # Remove the unnecessary characters from the text
        if type(value) == str:
            value = ''.join([c for c in value if c not in parser_config["POST_PROCESS_REMOVE_CHARS"]])
        if type(value) == list and field_info["type"] != "table" and field_info.get('additional_info', {}).get(
                'format_type', '') != "list":
            value = " ".join(value)
            value = ''.join([c for c in value if c not in parser_config["POST_PROCESS_REMOVE_CHARS"]])
        elif field_info.get('additional_info', {}).get('format_type', False):
            value = value
        elif type(value) == list and field_info["type"] != "table":
            value = " ".join(value)
            value = ''.join([c for c in value if c not in parser_config["POST_PROCESS_REMOVE_CHARS"]])

        if type(value) == str:
            value = value.strip()

        return value

    def remove_duplicate_field_ids(self, results):
        results_dict = {}
        for item in results:
            if item["field_id"] not in results_dict:
                results_dict[item["field_id"]] = []

            results_dict[item["field_id"]].append(item)

        for key, values in results_dict.items():
            if len(values) > 1:
                # Consider only one item which has field value
                for item in values:
                    temp_value = self.post_process_results(self.get_only_value(item), item)
                    if item["value"] is not None and self.general_utils.validate_value_type(temp_value, item["type"]):
                        results_dict[key] = item
                        break
                if type(results_dict[key]) == list:
                    # If we are unable to remove duplicate then consider the fist item in the list
                    results_dict[key] = results_dict[key][0]
        # Convert the list of list into single flat list
        flatList = []
        for item in results_dict.values():
            if type(item) == list:
                flatList.extend(item)
            else:
                flatList.append(item)

        return flatList

    # Define a single function that encapsulates the entire process
    def extract_lines_from_texts(self, texts):
        # Sort texts by VPOS and then by HPOS
        if texts is None:
            return ""
        texts_sorted = sorted(texts, key=lambda x: (x['VPOS'], x['HPOS']))

        lines = []
        current_line = []
        current_vpos = texts_sorted[0]['VPOS']

        for text in texts_sorted:
            if self.dist_utils.on_same_horizontal_line(text, {'VPOS': current_vpos}) or not current_line:
                current_line.append(text)
            else:
                # Sort the current line by HPOS before adding to lines
                current_line.sort(key=lambda x: x['HPOS'])
                line_text = " ".join(
                    str['text'].strip() for item in current_line for txt in item["TEXT_LINE"] for str in txt["STRING"])
                lines.append(line_text)
                current_line = [text]
            current_vpos = text['VPOS']

        # Don't forget to add the last line
        if current_line:
            current_line.sort(key=lambda x: x['HPOS'])
            line_text = " ".join(
                str['text'].strip() for item in current_line for txt in item["TEXT_LINE"] for str in txt["STRING"])

            lines.append(line_text)

        return lines

    @staticmethod
    def get_only_value(data):
        value = ""
        # Extract only text from the value
        if data["type"] == "table":
            value = data["value"]
        elif data["type"] == "checkbox":
            value = ResponseFormatter.extract_checkbox_value(data['value'], parser_config['FOUND_OMR_TAG'],
                                                             checked_id=parser_config['CHECKED_OMR_ID'])
        elif data.get('additional_info', {}).get('split_by_bullets', False):
            value = ResponseFormatter.extract_lines_from_texts(data['value'])
        elif data.get('additional_info', {}).get('format_type', '') == 'list':
            value = ResponseFormatter.extract_lines_from_texts(data['value'])
        elif type(data["value"]) == dict:
            value = data["value"]["text"]
        elif type(data["value"]) == list:
            value = []
            for itm in data["value"]:
                if type(itm) != str and len(itm) > 1:
                    if len(itm["text"]) > 1:
                        value.append(itm["text"])
            # value = [itm["text"] for itm in data["value"] if (len(itm["text"]) and type(itm) != str)]
        elif type(data["value"]) == str:
            value = data["value"]

        return value

    @staticmethod
    def post_process_text(result):
        res_val = result["value"]
        if res_val is None or (isinstance(res_val, str) and len(res_val.strip()) == 0):
            result["key_info"] = None
            if "additional_info" in result.keys():
                if "found_val" in result["additional_info"] and result["additional_info"]["found_val"]:
                    found_val = result["additional_info"]["found_val"]
                    # Get the maximum count
                    max_count = max(found_val.count(x) for x in set(found_val))
                    # If max count is greater than 1, get the most frequent value
                    if max_count > 1:
                        final_val = max(set(found_val), key=found_val.count)
                    else:
                        final_val = found_val[0]
                    res_val = final_val
        if type(res_val) is list:
            res_list = []
            for v in res_val:
                if v is None or len(v) == 0:
                    continue
                if type(v) is dict:
                    res_list.append(v["text"].strip())
                elif type(v) is list:
                    for u in v:
                        if type(u) is dict:
                            res_list.append(u["text"].strip())
                        else:
                            res_list.append(u.strip())
                else:
                    res_list.append(v.strip())
            res_val = res_list
        elif type(res_val) is dict:
            if "text" in res_val.keys():
                res_val = res_val["text"].strip()
            else:
                for k, value in res_val.items():
                    if type(value) is list:
                        res_list = []
                        for v in value:
                            if type(v) is dict:
                                res_list.append(v["text"].strip())
                            else:
                                res_list.append(v.strip())
                        res_val = res_list

        elif res_val is not None:
            res_val = res_val.strip()

            if res_val != '' and res_val[-1] in ['.', ',']:
                res_val = res_val[:-1]
        return res_val

    def get_value(self, result, page_dimensions):
        if result["value"] is None or not result["value"]:
            result["field_value_coordinates"] = (0, 0, 0, 0)

        if "field_value_coordinates" in result and \
            isinstance(result.get("field_value_coordinates"), (tuple, list)) and \
            result["field_value_coordinates"]:

                result["field_value_coordinates"] = {
                    'x': result["field_value_coordinates"][0],
                    'y': result["field_value_coordinates"][1],
                    'width': result["field_value_coordinates"][2] - result["field_value_coordinates"][0],
                    'height': result["field_value_coordinates"][3] - result["field_value_coordinates"][1]
                }

        if not hasattr(result, "is_table") or not result.is_table:
            res_val = self.post_process_text(result)
            
            if res_val is None:
                result["key_info"] = None
                if "additional_info" in result.keys():
                    if "found_val" in result["additional_info"]:
                        found_val = result["additional_info"]["found_val"]
                        if found_val:
                            final_val = max(set(found_val), key=found_val.count)
                            res_val = final_val

            if 'key_info' in result and result['key_info'] is not None:
                result['field_name_coordinates'] = self.convert_bbox_to_ratio(result["key_info"],
                                                                              page_dimensions[result["page_number"]])
            result.fill_defaults()
            result.drop_non_default_fields()
            result["value"] = res_val
            return result
        else:
            result.fill_defaults()
            result.drop_non_default_fields()
            return result

    @staticmethod
    def rename_field(results, old_field_name, new_field_name):
        new_results = []
        for item in results:
            if item.get("name") == old_field_name:
                item["name"] = new_field_name
                new_results.append(item)
            else:
                new_results.append(item)
        return new_results


    def remove_duplicate_items(self, ext_output):
        results_dict = {}
        results_dict['table'] = []
        for item in ext_output:
            if 'name' not in item.keys():
                results_dict['table'].append(item)
                continue
            if item["name"] not in results_dict:
                results_dict[item["name"]] = []

            results_dict[item["name"]].append(item)

        for key, values in results_dict.items():
            if key == 'table':
                continue
            if key in self.multiple_value_for_dict_list:
                continue
            if len(values) > 1:
                selected_item = None
                # Consider only one item which has field value
                for item in values:
                    if item['name'] == 'Legal_Subdivision':
                        pass
                    if item["value"] is not None and item['value'] != '':
                        selected_item = item
                        break

                if selected_item is None:
                    # If no item with non-empty value was found, default to the first item
                    selected_item = values[0]

                results_dict[key] = selected_item

                if type(results_dict[key]) == list:
                    # If we are unable to remove duplicate then consider the fist item in the list
                    results_dict[key] = results_dict[key][0]
        # Convert the list of list into single flat list
        flatList = []
        for item in results_dict.values():
            if key == 'table':
                continue
            if item and type(item) is list:
                if 'name' in item[0].keys() and 'value' in item[0].keys():
                    print("Field_Name: {}\nValue: {}".format(item[0]["name"], item[0]["value"]))
            elif item:
                if 'name' in item.keys() and 'value' in item.keys():
                    print("Field_Name: {}\nValue: {}".format(item["name"], item["value"]))
            if type(item) == list:
                flatList.extend(item)
            else:
                flatList.append(item)

        return flatList

    def separate_field_data_and_table_data(self, results, page_dimensions):
        separate_results = {'FieldData': [], 'TableData': []}
        for idx, result in enumerate(results):
            if not result.get('is_table') or (hasattr(result, 'is_table') and not result.is_table):
                result.fill_defaults()
                result.drop_non_default_fields()
                page_number = result.get('page_number', 0)
                page_number = 1 if page_number == 0 or page_number >= len(page_dimensions) else page_number
                image_height, image_width, _ = page_dimensions[page_number]
                result.normalize_coordinates(image_height, image_width, target='value', is_camel_case=False)
                result.to_camel_case()
                separate_results['FieldData'].append(result)
            else:
                result.fill_defaults()
                result.normalize_coordinates(page_dimensions)
                result.drop_non_default_fields()
                result.to_camel_case()
                separate_results['TableData'].append(result)

        return separate_results

    def get_formatted_json_output_new(self, results, page_dimensions, separate_table_data=True):
        extracted_output = []
        updated_results = results  # self.remove_duplicate_field_ids(results)
        for result_item in updated_results:
            extracted_result = self.get_value(result_item, page_dimensions)
            extracted_output.append(extracted_result)
            if "sub_keys" in result_item.keys():
                for sub_item in result_item["sub_keys"]:
                    if "value" in sub_item.keys():
                        extracted_output.append(self.get_value(sub_item, page_dimensions))

        extracted_output = self.rename_field(extracted_output, 'Exception_Text_Sequence_Number',
                                             'Exception_Sequence_Number')
        final_output = self.remove_duplicate_items(extracted_output)


        if separate_table_data:
            final_output = self.separate_field_data_and_table_data(final_output, page_dimensions)

        return final_output

