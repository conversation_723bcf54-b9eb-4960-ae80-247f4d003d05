import logging
from logging.handlers import TimedRotatingFileHandler
import os
from docvu_de_core.config import azure_storage_connection_string, model_blob_container_name, blob_name_vm
from docvu_de_core._version import __version__
yolo_model_path = os.path.join(os.path.dirname(__file__), 'models/pretrained/yolo_v8_for_checkbox.pt')
de_config_path = os.path.join(os.path.dirname(__file__), 'de_config')
client_path = os.path.join(os.path.dirname(__file__), 'de_config/clients')
llm_prompt_config_path = os.path.join(os.path.dirname(__file__), 'de_config/llm_prompts_config/clients')

PROJECT_NAME = "Data_Extraction"


def configure_logging(current_file_path):
    # current_file_path = Path(__file__)
    os.makedirs(os.path.join(current_file_path, 'de_logs/'), exist_ok=True)
    log_file_path = os.path.join(current_file_path, "de_logs/de_logs.log")
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    handler = TimedRotatingFileHandler(log_file_path, when="midnight", interval=1, encoding="utf8")
    handler_suffix = "%Y-%m-%d"
    handler.setFormatter(formatter)
    logger = logging.getLogger(PROJECT_NAME)
    logger.setLevel(logging.DEBUG)
    logger.addHandler(handler)
    logger.info(f'{PROJECT_NAME} startup')
    return logger