import xml.etree.ElementTree as ET
import json
import random
import cv2

# from config import kofax_config

class KofaxXML2JSON:
    def __init__(self, xml_path):
        self.debug = True
        self.xml_path = xml_path

    @staticmethod
    def get_score():
        return round(random.uniform(90.0,100.0), 2)

    @staticmethod
    # def is_blocks_nearby(block1, block2, threshold=kofax_config["space_threshold"]):
    def is_blocks_nearby(block1, block2, threshold=30):
        vpos_block1 = int(block1.get('VPOS'))
        vpos_block2 = int(block2.get('VPOS'))
        hpos_block1 = int(block1.get('HPOS'))
        hpos_block2 = int(block2.get('HPOS'))
        height_block1 = int(block1.get('HEIGHT'))
        height_block2 = int(block2.get('HEIGHT'))
        width_block1 = int(block1.get('WIDTH'))
        width_block2 = int(block2.get('WIDTH'))

        # two blocks are side by side
        if abs(hpos_block1 + width_block1 - hpos_block2) <= threshold and abs(vpos_block1 - vpos_block2) <= threshold:
            return True

        # Two blocks are one below other
        # if abs(vpos_block1 + height_block1 - vpos_block2) <= threshold and abs(hpos_block1 - hpos_block2) <= threshold:
        #     return True
        if abs(vpos_block1 + height_block1 - vpos_block2) <= threshold:
            return True
        
        # Two blocks are one over other horizontally
        if (hpos_block1 <= hpos_block2 <= (hpos_block1 + width_block1)) and abs(vpos_block1 - vpos_block2) <= threshold:
            return True
        
        if (hpos_block2 <= hpos_block1 <= (hpos_block2 + width_block2)) and abs(vpos_block1 - vpos_block2) <= threshold:
            return True
        
        # Two blocks are one over other vertically
        if (vpos_block1 <= vpos_block2 <= (vpos_block1 + height_block1)) and abs(hpos_block1 - hpos_block2) <= threshold:
            return True
        
        if (vpos_block2 <= vpos_block1 <= (vpos_block2 + height_block2)) and abs(hpos_block1 - hpos_block2) <= threshold:
            return True
        
        # When one box is inside another box
        if (vpos_block1 <= vpos_block2 <= (vpos_block1 + height_block1)) and (hpos_block1 <= hpos_block2 <= (hpos_block1 + width_block1)):
            return True
        
        if (vpos_block2 <= vpos_block1 <= (vpos_block2 + height_block2)) and (hpos_block2 <= hpos_block1 <= (hpos_block2 + width_block2)):
            return True
        
        return False

    def xml2json(self, item):
        width = abs(int(item.get('StartingX')) - int(item.get('EndingX')))
        new_item = {
            "HPOS": int(item.get('StartingX')), "VPOS": int(item.get('StartingY')),
            "WIDTH": width, "HEIGHT": int(item.get('Height')), "text": item.text,
            "SCORE": float(self.get_score())
            }
        return new_item
    
    def get_line_bbox(self, line_items):
        if len(line_items) == 1:
            temp = self.xml2json(line_items[0])
            bbox = (temp["HPOS"], temp["VPOS"], temp["WIDTH"], temp["HEIGHT"])
        elif len(line_items) >= 2:
            # Use first and last item to calculate total bbox
            first_item = self.xml2json(line_items[0])
            last_item = self.xml2json(line_items[-1])
            width = abs((last_item["HPOS"] + last_item["WIDTH"]) - first_item["HPOS"])
            height = max([first_item["HEIGHT"], last_item["HEIGHT"]])
            bbox = (first_item["HPOS"], first_item["VPOS"], width, height)
        else:
            print(line_items, len(line_items))
            bbox = (0,0,0,0)

        return bbox

    @staticmethod
    def merge_item_in_list(item, item_list):
        # assuming the x-axis is from left to right and y-axis is from top to bottom
        new_x = min([item["HPOS"], item_list["HPOS"]])
        new_y = min([item["VPOS"], item_list["VPOS"]])
        new_w = max([(item["HPOS"]+item["WIDTH"]), (item_list["HPOS"]+item_list["WIDTH"])]) - new_x
        new_h = max([(item["VPOS"]+item["HEIGHT"]), (item_list["VPOS"]+item_list["HEIGHT"])]) - new_y
        return new_x, new_y, new_w, new_h

    def get_text_blocks(self, all_lines):
        text_lines = []
        # Converting all xml line words to text lines
        for line in all_lines:
            line_items = []
            for string in line:
                line_items.append(self.xml2json(string))
            line_bbox = self.get_line_bbox(line)
            text_line_item = {"STRING":line_items, "HPOS": line_bbox[0], "VPOS": line_bbox[1],
                              "WIDTH": line_bbox[2], "HEIGHT": line_bbox[3]}
            text_lines.append(text_line_item)

        text_blocks = []
        # merge textlines which are nearby to make textblocks
        for text_line_item in text_lines:
            if len(text_blocks) == 0:
                new_item = {"TEXT_LINE": [text_line_item], "HPOS": text_line_item["HPOS"], "VPOS": text_line_item["VPOS"],
                            "WIDTH": text_line_item["WIDTH"], "HEIGHT": text_line_item["HEIGHT"]}
                text_blocks.append(new_item)
            else:
                added = False
                for tb_id, text_block in enumerate(text_blocks):
                    if self.is_blocks_nearby(text_block, text_line_item):
                        new_bbox = self.merge_item_in_list(text_line_item, text_block)
                        new_item = {"TEXT_LINE": text_block["TEXT_LINE"] + [text_line_item], "HPOS": new_bbox[0],
                                    "VPOS": new_bbox[1], "WIDTH": new_bbox[2], "HEIGHT": new_bbox[3]}
                        text_blocks[tb_id] = new_item
                        added = True
                        break
                if not added:
                    new_item = {"TEXT_LINE": [text_line_item], "HPOS": text_line_item["HPOS"], "VPOS": text_line_item["VPOS"],
                            "WIDTH": text_line_item["WIDTH"], "HEIGHT": text_line_item["HEIGHT"]}
                    text_blocks.append(new_item)
        return text_blocks

    def get_cblock(self, text_blocks):
        cblocks = []
        
        # Merge text blocks nearby to make cblocks
        for t_block in text_blocks:
            if len(cblocks) == 0:
                new_item = {"TEXT_BLOCK": [t_block], "HPOS": t_block["HPOS"], "VPOS": t_block["VPOS"],
                            "WIDTH": t_block["WIDTH"], "HEIGHT": t_block["HEIGHT"]}
                cblocks.append(new_item)
            else:
                added = False
                for tb_id, text_block in enumerate(cblocks):
                    if self.is_blocks_nearby(text_block, t_block):
                        new_bbox = self.merge_item_in_list(t_block, text_block)
                        new_item = {"TEXT_BLOCK": text_block["TEXT_BLOCK"] + [t_block], "HPOS": new_bbox[0],
                                    "VPOS": new_bbox[1], "WIDTH": new_bbox[2], "HEIGHT": new_bbox[3]}
                        cblocks[tb_id] = new_item
                        added = True
                        break
                if not added:
                    new_item = {"TEXT_BLOCK": [t_block], "HPOS": t_block["HPOS"], "VPOS": t_block["VPOS"],
                            "WIDTH": t_block["WIDTH"], "HEIGHT": t_block["HEIGHT"]}
                    cblocks.append(new_item)
        return cblocks

    def convert_xml_to_word_level_json(self, json_path):
        tree = ET.parse(xml_path)
        root = tree.getroot()

        all_words = root.findall(".//Word")

        modified_list = []
        for item in all_words:
            modified_list.append(self.xml2json(item))

        with open(json_path, "w+") as temp_f:
            temp_f.write(json.dumps(modified_list))

    def convert_xml_to_json(self, json_path):
        # Load the XML data
        tree = ET.parse(xml_path)
        root = tree.getroot()

        all_lines = root.findall(".//Words")

        text_blocks = self.get_text_blocks(all_lines)
        c_blocks = self.get_cblock(text_blocks)

        # import cv2
        # img_path = "/home/<USER>/secondry_drive/pavan/SOFTWARE-SERVICE-%20document_extraction/data/1003_XML and PDF/461084028_1003 Loan Application (c)_31328/0.jpg"
        # img = cv2.imread(img_path)
        # img_h, img_w, _ = img.shape
        # # print(img_w, img_h)
        # r_w = img_w/612
        # r_h = img_h/792
        
        # for block in text_blocks:
        #     x = int(block["HPOS"]*r_w)
        #     y = int(block["VPOS"]*r_h)
        #     w = int(block["WIDTH"] * r_w)
        #     h = int(block["HEIGHT"] * r_h)
        #     # print(x,y,w,h)
        #     img = cv2.rectangle(img, (x,y), (x+w, y+h), (255, 0, 0), 1)
        # cv2.imwrite("test.jpg", img)
        
        with open(json_path, "w+") as temp_f:
            temp_f.write(json.dumps(c_blocks))
            
    def get_text_string(self, all_lines):
        text_lines = []
        # Converting all xml line words to text lines
        for line in all_lines:
            line_items = []
            for string in line:
                line_items.append(self.xml2json(string))
            line_bbox = self.get_line_bbox(line)
            text_line_item = {"STRING":line_items, "HPOS": line_bbox[0], "VPOS": line_bbox[1],
                              "WIDTH": line_bbox[2], "HEIGHT": line_bbox[3]}
            text_lines.append(text_line_item)
        
        sorted_text_lines = sorted(text_lines, key=lambda x: x['VPOS'])

        # Step 2: Extract and concatenate text
        lines = []
        for item in sorted_text_lines:
            line_text = ' '.join([string['text'] for string in item['STRING']])
            lines.append(line_text)

        # Combine all lines into a single string
        full_text = ' [SEP] '.join(lines)
        
        return full_text
            
    def convert_xml_to_string(self):
        # Load the XML data
        tree = ET.parse(xml_path)
        root = tree.getroot()

        all_lines = root.findall(".//Words")
        full_text = self.get_text_string(all_lines)
        print(full_text)
        
        return full_text
    
    def true_page_to_string(self, txt_file_path):
        processed_lines = []

        with open(txt_file_path, 'r') as file:
            for line in file:
                # Strip leading and trailing whitespace
                stripped_line = line.strip()
                # Replace tabs with [TAB]
                processed_line = stripped_line.replace('\t', '[TAB]')
                processed_lines.append(processed_line)

        # Join the processed lines using [SEP]
        final_text = '[SEP]'.join(processed_lines)
        
        return final_text


if __name__ == "__main__":
    xml_path = "/home/<USER>/secondry_drive/pavan/SOFTWARE-SERVICE-%20document_extraction/data/1003_XML and PDF/1003_1.xml"
    # json_path = "/home/<USER>/secondry_drive/pavan/SOFTWARE-SERVICE-%20document_extraction/data/1003_XML and PDF/1003_1.json"
    # xml_path = "/home/<USER>/secondry_drive/clone_namrata/dummy/output.xml"
    json_path = "/home/<USER>/secondry_drive/clone_namrata/dummy/output.json"
    kofax_obj = KofaxXML2JSON(xml_path)
    # kofax_obj.convert_xml_to_json(json_path)
    kofax_obj.convert_xml_to_string()
    
    
    
    