import os
import shutil
from pdf2image import convert_from_path
import cv2
import numpy as np
from docvu_de_core.legacy.pytesseract_json import PyTesseractXML2JSON
import json
import pytesseract
# pytesseract.pytesseract.tesseract_cmd = r'/opt/local/bin/tesseract'


class GetOcrResults:
    def __init__(self, output_folder):
        all_lang = pytesseract.get_languages(config='')
        self.ocr_xml_dir = output_folder
        self.xml_to_json = PyTesseractXML2JSON()
        pass

    @staticmethod
    def create_dir(dir_path, flush=False):
        if flush:
            if os.path.exists(dir_path):
                shutil.rmtree(dir_path)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            return dir_path, False
        return dir_path, True

    @staticmethod
    def get_ocr_text_in_xml(image_path):
        xml = pytesseract.image_to_alto_xml(image_path)
        return xml

    @staticmethod
    def get_images_from_pdf(pdf_path):
        images = convert_from_path(pdf_path)
        return images

    @staticmethod
    def enhance_image(img_path):
        img = cv2.imread(img_path)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        img = cv2.GaussianBlur(img, ksize=(1, 1), sigmaX=0)
        kernel = np.ones((1, 1), np.uint8)
        img = cv2.erode(img, kernel)
        img = cv2.dilate(img, kernel)
        cv2.imwrite(img_path, img)

    def get_result_path(self, pdf_file_path, flush_dir=False):
        raw_pdf_name = pdf_file_path.split('/')[-1].split('.')[0]
        pdf_name = '_'.join(v for v in raw_pdf_name.split(' '))
        result_path = os.path.join(self.ocr_xml_dir, pdf_name)
        result_path, already_exist = self.create_dir(result_path, flush=flush_dir)
        return result_path, already_exist

    def get_ocr_text_and_save_page_xml(self, pdf_file_path, pre_process=False, flush_dir=False, dont_process=False):
        result_path, already_exist = self.get_result_path(pdf_file_path, flush_dir=flush_dir)
        combined_json_path = os.path.join(result_path, 'combined.json')
        combined_word_json_path = os.path.join(result_path, 'combined_words.json')
        if already_exist or dont_process:
            return combined_json_path, result_path

        images = self.get_images_from_pdf(pdf_file_path)
        print("Processing {} Images".format(len(images)))
        combined_json = {}
        combined_word_json = {}
        for index, img in enumerate(images):
            img_name = os.path.join(result_path, f'{index + 1}.jpg')
            images[index].save(img_name, 'JPEG')
            if pre_process:
                self.enhance_image(img_name)
            self.enhance_image(img_name)
            xml = self.get_ocr_text_in_xml(img_name)

            xml_file_name = f'{index + 1}.xml'
            xml_path = os.path.join(result_path, xml_file_name)
            with open(xml_path, 'w+b') as f:
                f.write(xml)

            json_file_name = f'{index + 1}.json'
            json_path = os.path.join(result_path, json_file_name)
            self.xml_to_json.convert_xml_to_json(xml_path, json_path)

            with open(json_path) as fo:
                combined_json[index + 1] = json.loads(fo.read())

            json_file_name = f'{index + 1}_words.json'
            json_path = os.path.join(result_path, json_file_name)
            self.xml_to_json.convert_xml_to_word_level_json(xml_path, json_path)

            with open(json_path) as fo:
                combined_word_json[index + 1] = json.loads(fo.read())

        with open(combined_json_path, "w+") as fo:
            fo.write(json.dumps(combined_json))

        with open(combined_word_json_path, "w+") as fo:
            fo.write(json.dumps(combined_word_json))

        return combined_json_path, result_path


if __name__ == '__main__':
    xml_save_dir = '../ocr_xml'
    ocrop = GetOcrResults(xml_save_dir)
    input_pdf_file_path = '../input_samples/STEWARTECM_PROD000052544596.pdf'
    ocrop.get_ocr_text_and_save_page_xml(input_pdf_file_path, pre_process=False, flush_dir=True)
    print('Done')
