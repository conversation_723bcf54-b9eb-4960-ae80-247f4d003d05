import re
from docvu_de_core.io import OutputFormat

class NumberParserOutputFormat:
    def __init__(self, **kwargs):
        self.extract_multiple = kwargs.get('extract_multiple', True)

        self.patterns = {
            # IDs
            "ssn": r"\b\d{3}-\d{2}-\d{4}\b",
            "pan": r"\b[A-Z]{5}\d{4}[A-Z]\b",
            "passport": r"\b[A-Z]\d{7}\b",
            "driver_license": r"\bDL-\d{13}\b",
            "loan_or_account_number": r"\b\d{10,16}\b",
            "credit_card_number": r"\b\d{4}[\s\-]?\d{4}[\s\-]?\d{4}[\s\-]?\d{4}\b",

            # Invoice, document, serial numbers
            "invoice_number": r"\bINV-\d{4}-\d{3,5}\b",
            "policy_number": r"\bPL-\d{9}\b",
            "serial_number": r"\bSN-\d{6,15}\b",
            "document_id": r"\bDOC-\d{4}-\d{3,5}\b",

            # Dates
            "date_ddmmyyyy": r"\b\d{1,2}/\d{1,2}/\d{4}\b",
            "date_textual": r"\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\.?\s+\d{1,2},\s+\d{4}\b",
            "date_iso": r"\b\d{4}-\d{2}-\d{2}\b",
            "datetime_iso": r"\b\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z\b",

            # Time
            "time_12hr": r"\b\d{1,2}:\d{2}\s?(AM|PM|am|pm)\b",
            "time_24hr": r"\b\d{2}:\d{2}\b",

            # Currency
            "usd_amount": r"\$\d{1,3}(?:,\d{3})*(?:\.\d{2})?",
            "inr_amount": r"₹\d{1,3}(?:,\d{3})*(?:\.\d{2})?",
            "eur_amount": r"€\d{1,3}(?:,\d{3})*(?:\.\d{2})?",

            # Percent
            # "percent_or_rate": r"\b\d{1,3}\s?\.\s?\d{1,5}\s?%?\b",
            # "percent_or_rate": r"\b\d{1,3}(?:\s?\.\s?\d{1,5})?(?=\s?%)",  
            "percent_or_rate": r"\b\d{1,3}(?:\s?\.\s?\d{1,5})?\s?%?\b",


            # Phone numbers
            "phone_us": r"\+1[-.\s]?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}",
            "phone_india": r"\b0\d{2,4}-\d{6,8}\b",
            "phone_uk": r"\+44[-.\s]?\d{2}[-.\s]?\d{4}[-.\s]?\d{4}",

            # Zip/postal codes
            "us_zip": r"\b\d{5}(?:-\d{4})?\b",
            "uk_postcode": r"\b[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}\b",

            # Fractions & ratings
            "fraction": r"\b\d+/\d+\b",
            "rating": r"\b\d+(?:\.\d+)?/\d+(?:\.\d+)?\b",

            # Year
            "year": r"\b(?:19|20)\d{2}\b",

            # Address line (basic numeric street and unit numbers)
            "street_number": r"\b\d+\s+[A-Za-z]+\s+(St|Street|Ave|Avenue|Rd|Road|Blvd|Lane|Ln)\b",
            "apt_number": r"\bApt\s+\d+\b",
        }

    def extract(self, text):
        results = {key: [] for key in self.patterns}

        for key, pattern in self.patterns.items():
            if self.extract_multiple:
                matches = re.findall(pattern, text)
            else:
                match = re.search(pattern, text)
                matches = [match.group()] if match else []

            results[key] = matches if self.extract_multiple else (matches[0] if matches else "")

        return results

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        print(processed_output["value"])
        success = False
        if processed_output["value"]:
            text = processed_output["value"].strip()
            extracted_data = self.extract(text)

            # Find the first non-empty extracted value
            final_value = ""
            for key, value in extracted_data.items():
                if isinstance(value, list) and value:
                    final_value = value[0]
                    break
                elif isinstance(value, str) and value:
                    final_value = value
                    break

            if final_value:
                processed_output['value'] = final_value
                processed_output['post_processing_value'] = final_value
                success = True
            else:
                processed_output['value'] = ""
                processed_output['post_processing_value'] = ""

        return OutputFormat(item=processed_output, success=success, multi_item=self.extract_multiple)

    
# Example usage
if __name__ == "__main__":
    input_text = """
    123-45-6789
    ABCDE1234F / 1234 5678 9123
    ********
    DL-0420110149646
    1401368863
    9876543210123456
    INV-2023-00123
    PL-785467321
    12/04/2023
    April 12, 2023
    2023-04-12
    10:30 AM, 22:45
    2023-04-12T10:30:00Z
    $123.45
    ₹10,000.00
    €89.99
    15%, 7.5%, 2.350%
    +1-234-567-8900
    022-12345678
    +44-20-7123-4567
    123 Main St
    Apt 405
    90210
    SW1A 1AA
    SN-1234567890
    DOC-2023-456
    92/100
    4.5/5
    2024, 1999
    """

    extractor = NumberParserOutputFormat(extract_multiple=True)
    result = extractor({'value': input_text})

    print("Extracted Values:\n")
    for key, values in result.item['value'].items():
        print(f"{key}: {values}")