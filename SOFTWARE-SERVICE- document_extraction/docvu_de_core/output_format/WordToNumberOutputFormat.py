import re
from docvu_de_core.io import OutputFormat
from word2number import w2n  # Make sure to install: pip install word2number

class WordToNumberOutputFormat:
    def __init__(self, **kwargs):
        # Regex pattern to find spelled-out number words (common numbers)
        self.number_word_pattern = re.compile(
            r'\b(?:zero|one|two|three|four|five|six|seven|eight|nine|ten|'
            r'eleven|twelve|thirteen|fourteen|fifteen|sixteen|seventeen|'
            r'eighteen|nineteen|twenty|thirty|forty|fifty|sixty|seventy|'
            r'eighty|ninety|hundred|thousand)\b', 
            flags=re.IGNORECASE
        )

    def _replace_number_word(self, match):
        word = match.group(0).lower()
        try:
            # Convert word to number string
            return str(w2n.word_to_num(word))
        except ValueError:
            # If conversion fails, return original word
            return word

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        """
        Converts all spelled-out number words in 'value' of processed_output to digits.
        Returns processed output with converted text.
        """
        success = False
        text = processed_output.get('value', '')

        if not text:
            return OutputFormat(item=processed_output, success=False, multi_item=False)

        # Replace all number words with digits
        converted_text = self.number_word_pattern.sub(self._replace_number_word, text)

        if converted_text != text:
            success = True

        processed_output['value'] = converted_text
        processed_output['post_processing_value'] = converted_text

        return OutputFormat(item=processed_output, success=success, multi_item=False)


# === Example usage ===
if __name__ == "__main__":
    input_text = {
        'value': "If the Note Holder has not received payment by FIFTEEN calendar days, a charge applies."
    }

    converter = WordToNumberOutputFormat()
    result = converter(input_text)
    print(f"Converted Text: {result.item['value']}")
