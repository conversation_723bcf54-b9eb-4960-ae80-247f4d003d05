import re
from docvu_de_core.io import OutputFormat

class Phoneparseroutputformat:
    def __init__(self, **kwargs):
        # Define regex pattern to match phone numbers in standard formats
        self.phone_number_pattern = r"\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}"  
        self.return_numeric_only = kwargs.get('return_numeric_only', True)  # Default to numeric-only output
        self.extract_multiple = kwargs.get('extract_multiple', False)  # Option to extract multiple phone numbers

    def parse_phone_numbers(self, text):
        """
        Extract phone numbers from the given text.
        """
        if not text:
            return []

        if self.extract_multiple:
            matches = re.findall(self.phone_number_pattern, text)
        else:
            match = re.search(self.phone_number_pattern, text)
            matches = [match.group()] if match else []

        if self.return_numeric_only:
            # Remove all non-numeric characters from phone numbers
            matches = [re.sub(r"\D", "", number) for number in matches]

        return matches

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        """
        Execute the phone number parsing operations on the given input.
        :param processed_output: Dictionary containing 'value' which is the input text to be processed.
        :param extraction_items: Additional fields for potential extraction if needed.
        :return: Processed output with the extracted phone number(s).
        """
        # Extract the text to be processed
        text = processed_output.get('value', '')

        if not text:  # Ensure text is not empty or None
            text = ''

        # Strip any leading/trailing spaces for more accurate matching
        text = text.strip()

        # Extract phone number(s)
        phone_numbers = self.parse_phone_numbers(text)

        success = bool(phone_numbers)

        # Update processed_output with the results
        processed_output['value'] = phone_numbers if self.extract_multiple else (phone_numbers[0] if phone_numbers else '')
        processed_output['post_processing_value'] = processed_output['value']

        return OutputFormat(item=processed_output, success=success, multi_item=self.extract_multiple)


# Example usage
if __name__ == "__main__":
    input_text = """
    Contact Numbers:
     huhhhdsd*************bwdjkgbvj
    """

    # Example with default settings (numeric only, single match)
    phone_parser = Phoneparseroutputformat(return_numeric_only=True, extract_multiple=False)
    result = phone_parser({'value': input_text})
    print(f"Single Parsed Phone Number: {result.item['value']}")  # Expect '8442725626'

    # Example with multiple matches and formatted output
    phone_parser_multiple = Phoneparseroutputformat(return_numeric_only=False, extract_multiple=True)
    result_multiple = phone_parser_multiple({'value': input_text})
    print(f"Multiple Parsed Phone Numbers: {result_multiple.item['value']}")  # Expect ['(*************', '(*************', '************']
