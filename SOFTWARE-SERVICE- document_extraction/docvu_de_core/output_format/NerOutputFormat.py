#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""


from docvu_de_core.io import OutputFormat
from docvu_de_core.models.ModelManager import ModelManager


class NerOutputFormat:
    def __init__(self, text_field_name=None, label=None):
        self.text_field_name = text_field_name
        self.label = label

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        if not ModelManager.ner_model_loaded:
            print("Model is not loaded. Skipping operation.")
            return OutputFormat(item=processed_output, success=False, multi_item=False)
        else:
            print("NER Model Loaded.")
            
        context = ''
        if self.text_field_name:
            for ei in extraction_items:
                if ei['name'] == self.text_field_name:
                    context = ei['post_processing_value']
                    if isinstance(context, list):
                        if len(context) > 0:
                            if isinstance(context[0], str):
                                context = ' '.join(context)       
                    break
        if not context:
            return OutputFormat(item=processed_output, success=False, multi_item=False)
        
        entities = ModelManager.ner_model.predict_entities(context, self.label)
        print("entities>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", entities)
        # If no entities are found, return the processed output as unsuccessful
        if not entities:
            return OutputFormat(item=processed_output, success=False, multi_item=False)

        # Depending on the label, select the appropriate entity
        generated_text = None
        if self.label:
            for entity in entities:
                if entity["label"] == self.label[0]:
                    generated_text = entity["text"]
                    break
        if not generated_text:
            return OutputFormat(item=processed_output, success=False, multi_item=False)

        processed_output['post_processing_value'] = generated_text
        return OutputFormat(item=processed_output, success=True, multi_item=False)



if __name__ == "__main__":
    # Example elements to process
    elements = [
        {"text": "Lot 3, in Block 1, of FINAL PLAT OF COUNTRY MEADOWS EAST,", "bbox": [0, 0, 100, 10]},
        {"text": "an addition in Harris County, Texas, according to the Map or", "bbox": [0, 10, 100, 20]},
        {"text": "Plat thereof recorded in/under Film Code No. 392120 of the Map", "bbox": [0, 20, 100, 30]},
        {"text": "Records of Harris County, Texas.", "bbox": [0, 30, 100, 40]}
    ]
