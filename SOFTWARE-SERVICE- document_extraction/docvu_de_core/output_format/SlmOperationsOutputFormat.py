#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""


from docvu_de_core.io import OutputFormat
from docvu_de_core.models.ModelManager import ModelManager

class SlmOperationsOutputFormat:
    def __init__(self, text_field_name=None, question=None):
        self.text_field_name = text_field_name
        self.question = question

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        if not self.question:
            return OutputFormat(item=processed_output, success=False, multi_item=False)
        
        if not ModelManager.slm_model_loaded:
            print("Model is not loaded. Skipping operation.")
            return OutputFormat(item=processed_output, success=False, multi_item=False)
        
        else:
            print("SLM Model Loaded.")
            
        context = ''
        if self.text_field_name:
            for ei in extraction_items:
                if ei['name'] == self.text_field_name:
                    context = ei['post_processing_value']
                    if isinstance(context, list):
                        if len(context) > 0:
                            if isinstance(context[0], str):
                                context = ' '.join(context)       
                    break
        # print("Context>>>>>>>>>", context)
        if not context:
            return OutputFormat(item=processed_output, success=False, multi_item=False)
        
        full_input = f"{context}\n{self.question}"
        generated_output = ModelManager.slm_generate_text(full_input)
        generated_text = generated_output[0]['generated_text']
        if not generated_text:
            return OutputFormat(item=processed_output, success=False, multi_item=False)
        
        if ":" in generated_text:
            start_index = generated_text.index(":") + 1  
        elif "=" in generated_text:
            start_index = generated_text.index("=") + 1  
        else:
            start_index = 0

        index_r = generated_text.find('<')
        
        if index_r != -1:
            generated_text = generated_text[start_index:index_r].strip()
        else:
            generated_text = generated_text[start_index:].strip()

        processed_output['post_processing_value'] = generated_text
        
        return OutputFormat(item=processed_output, success=True, multi_item=False)



if __name__ == "__main__":
    # Example elements to process
    elements = [
        {"text": "Lot 3, in Block 1, of FINAL PLAT OF COUNTRY MEADOWS EAST,", "bbox": [0, 0, 100, 10]},
        {"text": "an addition in Harris County, Texas, according to the Map or", "bbox": [0, 10, 100, 20]},
        {"text": "Plat thereof recorded in/under Film Code No. 392120 of the Map", "bbox": [0, 20, 100, 30]},
        {"text": "Records of Harris County, Texas.", "bbox": [0, 30, 100, 40]}
    ]
    questions = "Extract the Names of land_subdivision, land_county and state?"

    slm_instance = SlmOperationsOutputFormat()
    result = slm_instance(elements=elements, questions=questions)

    print("Bounding Box:", result.bbox)
    print("Line Number:", result.line_number)
    print("Value:", result.value)
    print("Success:", result.success)
    print("Elements:", result.elements)
    
    
    