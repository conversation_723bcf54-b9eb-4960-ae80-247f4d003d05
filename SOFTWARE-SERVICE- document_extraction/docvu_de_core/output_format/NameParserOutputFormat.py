from sympy import false
from docvu_de_core.io import OutputFormat
import re

class NameParserOutputFormat:
    def __init__(self, from_field=None, **kwargs):
        """
        Initialize NameParserOutputFormat with various name processing options.

        Supported arguments:
        - from_field: Specify the field to extract the name from.
        - get_first_name: <PERSON><PERSON><PERSON> to extract the first name.
        - get_middle_name: <PERSON><PERSON><PERSON> to extract the middle name.
        - get_last_name: <PERSON><PERSON><PERSON> to extract the last name.
        - get_borrower_name: <PERSON><PERSON>an to extract the borrower's full name.
        - get_co_borrower_name: <PERSON><PERSON><PERSON> to extract the co-borrower's full name.
        - get_addendum_co_borrower_name: <PERSON><PERSON><PERSON> to extract the addendum co-borrower's name.
        """
        self.from_field = from_field
        self.get_borrower_name = kwargs.get('get_borrower_name', False)
        self.get_co_borrower_name = kwargs.get('get_co_borrower_name', False)
        self.get_addendum_co_borrower_name = kwargs.get('get_addendum_co_borrower_name', False)
        self.get_first_name = kwargs.get('get_first_name', False)
        self.get_middle_name = kwargs.get('get_middle_name', False)
        self.get_last_name = kwargs.get('get_last_name', False)
        self.get_note_coborrower_name = kwargs.get('get_note_coborrower_name', False)
        self.get_note_borrower_name =  kwargs.get('get_note_borrower_name', False)
        self.get_disburs_coborrower_name = kwargs.get('get_disburs_coborrower_name', False)
        self.get_identification_Name_Matcher = kwargs.get('get_identification_Name_Matcher', False)

    def parse_names(self, text):
        print("Input text:", text)
        
        if not text:
            return []
        
        # Remove address (assumed to start with a number)
        match = re.search(r'\b\d{2,}', text)
        if match:
            text = text[:match.start()].strip()
        print("Text after address removal:", text)

        if "," in text:
            comma_separated_names = [name.strip() for name in text.split(',')]
            return comma_separated_names
        
        # Extract names followed by '-Borrower'
        borrower_matches = re.findall(r"\b([A-Z][\w\.\' ]+)\s*-Borrower", text)
        
        # Extract names separated by 'and'
        and_split_names = re.split(r'\s+and\s+', text)
        
        
        # Extract names when two names are present without separators
        name_pattern = re.findall(r'\b[A-Z][a-z]+\s+[A-Z]?[a-z]*\s*[A-Z][a-z]+\b', text)

        if len(name_pattern) == 2:
            borrower_matches.append(name_pattern[0])
            borrower_matches.append(name_pattern[1])
        
        # Combine all extracted names
        all_names = borrower_matches + [name.strip() for name in and_split_names if name.strip()]
        
        # Remove duplicates while maintaining order
        seen = set()
        unique_names = []
        for name in all_names:
            if name not in seen:
                unique_names.append(name.strip())
                seen.add(name)
        
        print("Extracted unique names:", unique_names)
        return unique_names


    def parse_borrower_name(self, text):
        """Extract the primary borrower's name."""
        names = self.parse_names(text)
        return names[0] if names else ''

    def parse_co_borrower_name(self, text):
        """Extract the co-borrower's name."""
        names = self.parse_names(text)
        return names[1] if len(names) > 1 else ''

    def parse_first_name(self, text):
        """Extract and return the first name."""
        if not text:
            return ''
        name_parts = text.split()
        return name_parts[0] if name_parts else ''

    def parse_middle_name(self, text):
        """Extract and return the middle name(s)."""
        if not text:
            return ''
        name_parts = text.split()
        if len(name_parts) <= 2:
            return ''  # No middle name if there are 2 or fewer parts
        return ' '.join(name_parts[1:-1])  # Join all parts between first and last

    def parse_last_name(self, text):
        """Extract and return the last name."""
        if not text:
            return ''
        name_parts = text.split()
        return name_parts[-1] if name_parts else ''

    def parse_addendum_co_borrower_name(self, text):
        """Extract the addendum co-borrower's name."""
        return self.parse_names(text)
    
    def parse_cd_coborrower_name(self, text):
        "Extract coborrower name of closing disclosure"
        coborrower_name = None

        if "Date" in text:
            coborrower_name = text.split("Date")[1]
        else:
            if len(text.split()) == 4:
                coborrower_name = text.split()[-2:]
            else:
                coborrower_name = text
        return coborrower_name

    def parse_note_coborrower_name(self, text):
        # Helper function to remove the primary borrower's name from the extracted name section
        def remove_borrower_name(text):
            final_name = None
            
            # Clean punctuation and split into words
            text = text.replace(",", "").replace("'", "")
            text_words = text.split()
            print(text_words)
            
            temp_name_list = []
            name_list = []
            
            # Loop through words to find where the borrower's name might end
            for i in range(0, len(text_words) - 1):  
                if text_words[i] == text_words[-1]:
                    # If current word is the same as the last word, take everything after it
                    name_list = text_words[i+1:]
                    break
                
                elif text_words[i].lower() in text_words[-1].lower():
                    # If word is part of the borrower's name
                    if len(text_words[i]) <= 2:
                        # Possibly initials, keep temporarily
                        temp_name_list.append(text_words[i])
                    else:
                        # Found full part of borrower's name, break and extract rest
                        name_list = text_words[i+1:]
                        break
                else:
                    # Keep collecting until pattern is found
                    temp_name_list.append(text_words[i])
            
            if name_list:
                final_name = " ".join(name_list)
            return final_name

        coborrower_name = text

        # Special case: when "sign original only" appears in the text
        if "sign original only" in text.lower():
            # Find all capitalized names that appear after "(Seal)"
            matches = re.findall(r'\(Seal\)\s+([A-Z]+(?:\s+[A-Z]+)*)', text)

            # Extract the second name after the second (Seal), assuming it's the co-borrower
            if len(matches) >= 2:
                coborrower_name = matches[1]
                return coborrower_name

        # Case: "-Borrower -Borrower" appears, indicating multiple borrowers
        if "-Borrower -Borrower" in text:
            # Match everything between the last (Seal) and the "-Borrower" tag
            match = re.search(r'\(Seal\)(?:.*?\(Seal\))*\s*(.*?)\s*-Borrower', text)

            if match:
                name_part = match.group(1)
                split_names = name_part.split()

                # Remove filler words like "member" or "of"
                filtered_names_list = [item for item in split_names if item.lower() not in ["member", "of"]]

                # Clean up borrower's name and isolate co-borrower's name
                temp_coborrower_name = " ".join(filtered_names_list)
                coborrower_name = remove_borrower_name(temp_coborrower_name)
                return coborrower_name

        if "(Seal)" in text:
            # Check if (Seal) appears more than once
            seals = re.findall(r'\(Seal\)', text)
            if len(seals) <= 1:
                return None

            patterns = [
                # Pattern: borrower Name - borrower
                r'\bborrower\b\s+([a-zA-Z.\-\'\s]+?)\s*-\s*borrower\b',

                # Pattern: Name borrower (e.g., Eva'd Jackson-Conner borrower)
                r'([a-zA-Z\'\-\.]+\s+[a-zA-Z\'\-\.]+)\s+\bborrower\b',

                # Pattern: after second (Seal) to the end of string
                r'\(Seal\).*?\(Seal\)\s+(.*)',
            ]

            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
                if match:
                    name = match.group(1).strip()

                    # Optional: truncate name at next stop word or unlikely characters
                    name = re.split(r'\s{2,}|\n|[^a-zA-Z.\'\-\s]', name)[0].strip()

                    return name

            return text

        # General case: extract all name-like patterns ending in "-Borrower"
        matches = re.findall(
            r'([A-Z][a-z]+(?:\s+[A-Z]\.?\s*[A-Z][a-z]+|\s+[A-Z][a-z]+){1,})\s*-Borrower', text, flags=re.IGNORECASE
        )

        # Assume the last match is the co-borrower name
        if matches:
            coborrower_name = matches[-1]

        return coborrower_name

    def parse_get_note_borrower_name(self,text):
        match = re.search(r'\(Seal\)\s*(.*?)\s*\(Seal\)', text)

        if match:
            result = match.group(1)
            return result
        else:
            return text

    def disburs_coborrower_name(self, text):
        # Remove all date-like patterns from the text (e.g., "day of March, 2023", "March 12, 2023", etc.)
        date_patterns = [
            r"\bday of [A-Za-z]+, \d{4}\.?",  # day of March, 2023
            r"\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{1,2}, \d{4}",  # March 12, 2023
            r"\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}",  # 03/12/2023 or 3-12-2023
            r"\b\d{4}[/-]\d{1,2}[/-]\d{1,2}"  # 2023-03-12
        ]
        for pattern in date_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        cleaned_text = text.strip()

        tokens = cleaned_text.split()

        # Count all capitalized tokens
        last_name_counts = {}
        for token in tokens:
            if token[0].isupper():
                last_name_counts[token] = last_name_counts.get(token, 0) + 1

        repeated_last_names = [name for name, count in last_name_counts.items() if count > 1]

        if repeated_last_names:
            # Find the first index where repeated name occurs
            for i, token in enumerate(tokens):
                if token in repeated_last_names:
                    result = " ".join(tokens[i + 1:])
                    return result.strip()

        # Fallback: remove first two tokens
        if len(tokens) > 2:
            result = " ".join(tokens[2:])
            return result.strip()

        # Final fallback: Not enough tokens, returning empty.
        return ""

    def identification_Name_Matcher(self, text):
        # Predefined list of names to search for
        name_list = [
            "Notary", "Notary Public", "signing agent", "Syng Agreed",
            "ATTORM", "Esg", "Atty"
        ]
        # Precompile regex for case-insensitive search
        patterns = {
            name: re.compile(r'\b' + re.escape(name) + r'\b', re.IGNORECASE)
            for name in name_list
        }
        if not text:
            return ""

        matches = [
            name for name, pattern in patterns.items()
            if pattern.search(text)
        ]

        if matches:
            # Return the longest match (most specific)
            return max(matches, key=len)
        return ""


    def __call__(self, processed_output, extraction_items=None, **kwargs):
        """
        Execute the configured name parsing operations on the given input.

        :param processed_output: Dictionary containing 'value' which is the input name string.
        :param extraction_items: Additional fields for potential extraction if needed.
        :return: Processed output with the extracted name.
        """
        # Determine which text to use based on from_field or default to processed_output['value']
        text = processed_output.get('value', '')

        if self.from_field and extraction_items:
            for item in extraction_items:
                if item['name'] == self.from_field:
                    text = item['value']
                    break

        if text is None:
            text = ''

        output_value = ''
        success = False

        # Determine which parsing operation to use based on initialization parameters
        if self.get_borrower_name:
            output_value = self.parse_borrower_name(text)
        elif self.get_co_borrower_name:
            output_value = self.parse_co_borrower_name(text)
        elif self.get_addendum_co_borrower_name:
            output_value = self.parse_addendum_co_borrower_name(text)
        elif self.get_first_name:
            output_value = self.parse_first_name(text)
        elif self.get_middle_name:
            output_value = self.parse_middle_name(text)
        elif self.get_last_name:
            output_value = self.parse_last_name(text)
        elif self.get_note_coborrower_name:
            output_value = self.parse_note_coborrower_name(text)
        elif self.get_note_borrower_name:
            output_value = self.parse_get_note_borrower_name(text)
        elif self.get_disburs_coborrower_name:
            output_value = self.disburs_coborrower_name(text)
        elif self.get_identification_Name_Matcher :
            output_value = self.identification_Name_Matcher(text)
        else:
            raise ValueError("No valid parsing operation specified. Please set one option to True.")

        success = bool(output_value)

        processed_output['value'] = output_value
        processed_output['post_processing_value'] = output_value

        return OutputFormat(item=processed_output, success=success, multi_item=false)


if __name__ == "__main__":

    #  Test Case 1:
    #  this Example usage for Addendum Co-Borrower Result : Lois Jane Walker
    input_value = "Lois Jane Walker 706 South Cir Lawrenceburg, NT 38464 "


    name_parser_addendum_coborrower = NameParserOutputFormat(get_addendum_co_borrower_name=True)
    addendum_coborrower_result = name_parser_addendum_coborrower({'value': input_value})
    print("Field_Name: addendum_co_borrower_name")
    print("Value:", addendum_coborrower_result.item['value'])

    
    # # Test Case 2: Names appended with "and"
    # input_value = "Milta Rush and Jake Rush 123 Main St"

    # # Example usage for Borrower
    # name_parser_borrower = NameParserOutputFormat(get_borrower_name=True)
    # borrower_result = name_parser_borrower({'value': input_value})
    # print("Field_Name: note_borrower_name")
    # print("Value:", borrower_result.item['value'])

    # # Example usage for Co-Borrower
    # name_parser_coborrower = NameParserOutputFormat(get_co_borrower_name=True)
    # coborrower_result = name_parser_coborrower({'value': input_value})
    # print("Field_Name: note_co_borrower_name")
    # print("Value:", coborrower_result.item['value'])

    
    # # Test Case 3: Names separated by a comma
    # input_value_2 = "AVERY ARTHUR TARASOV, OSTINE"

    # # Example usage for Borrower
    # name_parser_borrower = NameParserOutputFormat(get_borrower_name=True)
    # borrower_name = name_parser_borrower({'value': input_value_2})
    # print("Field_Name: note_borrower_name")
    # print("Value:", borrower_name.item['value'])

    # name_parser_borrower = NameParserOutputFormat(get_co_borrower_name=True)
    # coborrower_name = name_parser_borrower({'value': input_value_2})
    # print("Field_Name: note_borrower_name")
    # print("Value:", coborrower_name.item['value'])

    # input_value = "(Seal) (Seal) Caleb Hawley, Member of Caleb Hawley -Borrower -Borrower RETROSTAYS LLC"
    # name_parser_borrower = NameParserOutputFormat(get_note_coborrower_name=True)
    # coborrower_name = name_parser_borrower({'value': input_value})
    # print("Field_Name: note_borrower_name")
    # print("Value:", coborrower_name.item['value'])

    # input_value = "The document was signed by a Notary Public on 5th July."
    #
    # name_parser_addendum_coborrower = NameParserOutputFormat(get_identification_Name_Matcher=True)
    # addendum_coborrower_result = name_parser_addendum_coborrower({'value': input_value})
    # print("Field_Name: addendum_co_borrower_name")
    # print("Value:", addendum_coborrower_result.item['value'])