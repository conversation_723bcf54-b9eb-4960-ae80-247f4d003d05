#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""

from docvu_de_core.model_api_request.ner_service_req import ModelServiceResponse
from docvu_de_core.io import OutputFormat
from docvu_de_core.config import server_url, end_point


class NerServiceReqOutputFormat:
    def __init__(self, text_field_name=None, label=None):
        self.text_field_name = text_field_name
        self.label = label

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        context = ''
        if self.text_field_name:
            for ei in extraction_items:
                if ei['name'] == self.text_field_name:
                    context = ei['post_processing_value']
                    if isinstance(context, list):
                        if len(context) > 0:
                            if isinstance(context[0], str):
                                context = ' '.join(context)
                    break
        if not context:
            return OutputFormat(item=processed_output, success=False, multi_item=False)

        try:
            model_infer = ModelServiceResponse(server_url, end_point)
            response = model_infer.send_request_to_ner(context, self.label)

            if not response or response.status_code != 200:
                print(f"Error: Received invalid response (status code: {response.status_code if response else 'None'})")
                return OutputFormat(item=processed_output, success=False, multi_item=False)

            response_json = response.json()
            generated_text = response_json.get("Entities", "")

            if not generated_text:
                return OutputFormat(item=processed_output, success=False, multi_item=False)

            processed_output['post_processing_value'] = generated_text
            return OutputFormat(item=processed_output, success=True, multi_item=False)

        except Exception as e:
            print(f"Error occurred while processing NER request: {e}")
            return OutputFormat(item=processed_output, success=False, multi_item=False)
