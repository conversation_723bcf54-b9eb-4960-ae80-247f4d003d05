from docvu_de_core.io import OutputFormat

class OmrCheckboxOutputFormat:
    def __init__(self, **kwargs):
        """
        Initialize the OMR checkbox parser with configurable options.
        """
        self.default_value_if_unchecked = kwargs.get('default_value_if_unchecked', 'NO')
        self.checked_value = kwargs.get('checked_value', 'YES')
        self.valid_options = kwargs.get('valid_options', ['YES'])
        self.extract_multiple = kwargs.get('extract_multiple', False)

    def parse_checkbox(self, text):
        """
        Determine checkbox value based on extracted text.
        Return 'YES' if any valid option is matched, else 'NO'.
        """
        if not text:
            return self.default_value_if_unchecked

        text = text.strip().upper()

        if any(opt.upper() in text for opt in self.valid_options):
            return self.checked_value
        return self.default_value_if_unchecked

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        """
        Apply the OMR logic to the processed output.
        """
        text = processed_output.get('value', '')
        text = text.strip() if text else ''

        value = self.parse_checkbox(text)
        success = value == self.checked_value

        processed_output['value'] = value
        processed_output['post_processing_value'] = value

        return OutputFormat(item=processed_output, success=success, multi_item=self.extract_multiple)


# Example usage
if __name__ == "__main__":
    input_data = {'value': 'YES'}
    omr_parser = OmrCheckboxOutputFormat(
        valid_options=['YES'], checked_value='YES', default_value_if_unchecked='NO'
    )
    result = omr_parser(input_data)
    print(f"OMR Checkbox Result: {result.item['value']}")  # Expect 'YES'
