import re
from docvu_de_core.io import OutputFormat


class IdNumberExtractorOutputFormat:
    def __init__(self, **kwargs):
        pass

    def extract_first_nine_digits(self, text):
        if not text:
            return ""

        # Remove everything before the first digit
        text = re.sub(r'^[^\d]*', '', text)

        # Extract only digits from here onward
        digits = re.findall(r'\d', text)

        # Join and return the first 9 digits if available
        return ''.join(digits[:9]) if len(digits) >= 9 else ''.join(digits)

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        text = processed_output.get("value", "") or ""
        text = text.strip()

        extracted = self.extract_first_nine_digits(text)
        success = bool(extracted)

        processed_output["value"] = extracted
        processed_output["post_processing_value"] = extracted

        return OutputFormat(item=processed_output, success=success, multi_item=False)



if __name__ == "__main__":
    samples = [
        "B62011270-62904-9-19704-9-284-7-20Yes",
        "/NJG028259065089428/16/19947/8/20248/16/2027No",
        "6258212-19-195812-19-2027",
        "/In4550-12-22153/14/742/20/243114130No",
        "Issuance523730R36254510/13/195210/13/202710/10/2019Date(s)Yes"
    ]

    extractor = IdNumberExtractorOutputFormat()
    for sample in samples:
        result = extractor({'value': sample})
        print(result.item['value'])