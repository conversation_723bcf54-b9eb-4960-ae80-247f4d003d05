from docvu_de_core.io import OutputFormat
from docvu_de_core.modules.DateFinder import DateFinder
import re
from datetime import datetime


class DateRemoverOutputFormat:
    def __init__(self, date_format="%m/%d/%Y", **kwargs):
        self.date_format = date_format
        self.dr = DateFinder()  # No need to pass return_format here as it's for removal, not formatting

    def is_valid_date(self, date_str):
        try:
            # Try to parse the date string using the provided date format
            datetime.strptime(date_str, self.date_format)
            return True
        except ValueError:
            # If a ValueError is raised, the format is incorrect
            return False

    def remove_dates(self, text):
        # Use the DateRemove class to remove dates from the text
        text = self.dr.remove_dates_in_text(text)

        # Clean up any extra spaces left after date removal
        text = re.sub(' +', ' ', text).strip()

        return text

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        """
        Execute the configured text processing operations to remove dates.

        :param processed_output: Dictionary containing 'value' which is the input string.
        :return: OutputFormat with the processed text and related information.
        """
        text = processed_output.get('post_processing_value', '')
        original_text = text
        if original_text:
            # Remove dates from the text
            text = self.remove_dates(text)

        processed_output['post_processing_value'] = text

        # Determine success based on whether any dates were removed
        success = original_text != text

        return OutputFormat(item=processed_output, success=success, multi_item=False)


# Example usage:
if __name__ == "__main__":
    input_text = "This document was signed on 03/15/2023 and submitted on 04/20/2024."

    date_remove_operations = DateRemoveOutputFormat(date_format="%m/%d/%Y")
    result = date_remove_operations({'value': input_text})

    print("Result:")
    print(result.item['value'])
