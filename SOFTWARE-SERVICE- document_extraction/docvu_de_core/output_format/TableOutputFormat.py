#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""


import re
from docvu_de_core.io import OutputFormat


class TableOutputFormat:
    def __init__(self, **kwargs):
        """
        Initialize TableOutputFormat with various filtering options.

        Supported arguments:
        - remove_bullet: Boolean to remove values starting with a bullet (number followed by a dot).
        - remove_all_caps: Boolean to remove values that are all uppercase.
        - remove_all_lower: Boolean to remove values that are all lowercase.
        - remove_mostly_caps: Boolean to remove values that are mostly uppercase.
        - remove_mostly_lower: Boolean to remove values that are mostly lowercase.
        - custom_patterns: List of custom regex patterns to filter out.
        - remove_duplicate_global: Boolean to remove duplicate values globally across all rows.
        - remove_duplicate_intra_row: Boolean to remove duplicate values within each row.
        - remove_duplicate_inter_row: Boolean to remove rows that are exact duplicates of other rows.
        """
        self.remove_bullet = kwargs.get('remove_bullet', False)
        self.remove_all_caps = kwargs.get('remove_all_caps', False)
        self.remove_all_lower = kwargs.get('remove_all_lower', False)
        self.remove_mostly_caps = kwargs.get('remove_mostly_caps', False)
        self.remove_mostly_lower = kwargs.get('remove_mostly_lower', False)
        self.custom_patterns = kwargs.get('custom_patterns', [])
        self.remove_duplicate_global = kwargs.get('remove_duplicate_global', False)
        self.remove_duplicate_intra_row = kwargs.get('remove_duplicate_intra_row', False)
        self.remove_duplicate_inter_row = kwargs.get('remove_duplicate_inter_row', False)

    def _starts_with_bullet(self, text):
        """Check if the text starts with a bullet (e.g., a number followed by a dot)."""
        return bool(re.match(r'^\d+\.', text.strip()))

    def _is_all_caps(self, text):
        """Check if the text is all uppercase."""
        return text.isupper()

    def _is_all_lower(self, text):
        """Check if the text is all lowercase."""
        return text.islower()

    def _is_mostly_caps(self, text):
        """Check if most of the characters in the text are uppercase."""
        uppercase_count = sum(1 for c in text if c.isupper())
        return uppercase_count > len(text) / 2

    def _is_mostly_lower(self, text):
        """Check if most of the characters in the text are lowercase."""
        lowercase_count = sum(1 for c in text if c.islower())
        return lowercase_count > len(text) / 2

    def _matches_custom_patterns(self, text):
        """Check if the text matches any custom patterns provided."""
        for pattern in self.custom_patterns:
            if re.search(pattern, text):
                return True
        return False

    def _remove_duplicate_global(self, rows):
        """Remove duplicate values globally across all rows."""
        seen_values = set()
        for row in rows:
            filtered_columns = []
            for column in row.get('columns', []):
                value = column['value']
                if value not in seen_values:
                    seen_values.add(value)
                    filtered_columns.append(column)
            row['columns'] = filtered_columns
        return rows

    def _remove_duplicate_intra_row(self, rows):
        """Remove duplicate values within each row."""
        for row in rows:
            seen_values = set()
            filtered_columns = []
            for column in row.get('columns', []):
                value = column['value']
                if value not in seen_values:
                    seen_values.add(value)
                    filtered_columns.append(column)
            row['columns'] = filtered_columns
        return rows

    def _remove_duplicate_inter_row(self, rows):
        """Remove rows that are exact duplicates of other rows."""
        unique_rows = []
        seen_rows = set()

        for row in rows:
            row_tuple = tuple(column['value'] for column in row.get('columns', []))
            if row_tuple not in seen_rows:
                seen_rows.add(row_tuple)
                unique_rows.append(row)

        return unique_rows

    def __call__(self, processed_output, **kwargs):
        """
        Filter the endorsement data based on various rules defined during initialization.

        :param processed_output: Dictionary containing endorsement rows under 'rows'.
        :return: OutputFormat with the filtered data and success flag.
        """
        filtered_rows = []

        if processed_output.get('rows', []) is not None:
            rows = processed_output.get('rows', [])

            for row in rows:
                filtered_columns = []

                for column in row.get('columns', []):
                    value = column['post_processing_value']

                    # Apply filtering rules
                    if self.remove_bullet and self._starts_with_bullet(value):
                        continue
                    if self.remove_all_caps and self._is_all_caps(value):
                        continue
                    if self.remove_all_lower and self._is_all_lower(value):
                        continue
                    if self.remove_mostly_caps and self._is_mostly_caps(value):
                        continue
                    if self.remove_mostly_lower and self._is_mostly_lower(value):
                        continue
                    if self.custom_patterns and self._matches_custom_patterns(value):
                        continue

                    filtered_columns.append(column)

                # Only add non-empty rows
                if filtered_columns:
                    row['columns'] = filtered_columns
                    filtered_rows.append(row)

            # Apply duplicate removal options
            if self.remove_duplicate_global:
                filtered_rows = self._remove_duplicate_global(filtered_rows)
            if self.remove_duplicate_intra_row:
                filtered_rows = self._remove_duplicate_intra_row(filtered_rows)
            if self.remove_duplicate_inter_row:
                filtered_rows = self._remove_duplicate_inter_row(filtered_rows)

            if filtered_rows:
                processed_output['rows'] = filtered_rows

            processed_output.remove_empty_rows()

            success = any(row['columns'] for row in processed_output.get('rows', []))
            return OutputFormat(item=processed_output, success=success, multi_item=False)

        else:
            # Handle the case where processed_output is None
            print("Error: processed_output is None")
            return OutputFormat(item=None, success=False, multi_item=False)


# Example usage:
if __name__ == "__main__":
    data = {
        "rows": [
            {
                "row_number": 0,
                "columns": [
                    {"value": "T-19.1 RESTRICTIONS, ENCROACHMENTS, MINERALS ENDORSEMENT- OWNERS POLICY"},
                    {"value": "5. This endorsement does not insure against loss or damage"},
                    {"value": "t-19.2 minerals and surface damage endorsement"},
                    {"value": "T-23 ACCESS ENDORSEMENT"},
                    {"value": "this is an all lower case text"},
                    {"value": "THIS IS AN ALL UPPER CASE TEXT"},
                    {"value": "This Is A Mixed Case Text"},
                    {"value": "T-23 ACCESS ENDORSEMENT"}  # Duplicate for testing
                ]
            },
            {
                "row_number": 1,
                "columns": [
                    {"value": "THIS IS AN ALL UPPER CASE TEXT"},
                    {"value": "THIS IS AN ALL UPPER CASE TEXT"}   # Duplicate within the row
                ]
            },
            {
                "row_number": 2,
                "columns": [
                    {"value": "T-19.1 RESTRICTIONS, ENCROACHMENTS, MINERALS ENDORSEMENT- OWNERS POLICY"},
                    {"value": "5. This endorsement does not insure against loss or damage"},
                    {"value": "t-19.2 minerals and surface damage endorsement"}
                ]
            }
        ]
    }

    # Initialize with filtering rules
    table_filter = TableOutputFormat(
        remove_bullet=True,
        remove_all_caps=True,
        remove_all_lower=True,
        remove_mostly_caps=True,
        remove_mostly_lower=True,
        remove_duplicate_global=False,  # Example: Set to True to test global duplicates
        remove_duplicate_intra_row=True,  # Remove duplicates within each row
        remove_duplicate_inter_row=True,  # Remove rows that are exact duplicates of others
        custom_patterns=[r'EXAMPLE_CUSTOM_PATTERN']
    )

    # Apply filtering
    filtered_data = table_filter(processed_output=data)

    # Print the filtered result
    print("Filtered Data:", filtered_data.item)