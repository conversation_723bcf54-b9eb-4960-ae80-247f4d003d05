from docvu_de_core.io import OutputFormat

class OmrFlaggerOutputFormat:
    def __init__(self, **kwargs):
        """
        Initialize the OMR checkbox parser with configurable options.
        """
        self.default_value_if_unchecked = kwargs.get('default_value_if_unchecked', 'NO')
        self.checked_value = kwargs.get('checked_value', 'YES')
        self.extract_multiple = kwargs.get('extract_multiple', False)

    def parse_checkbox(self, text):
        """
        Return checked_value if any text is present (i.e., checkbox is checked),
        otherwise return default_value_if_unchecked.
        """
        if text and text.strip():
            return self.checked_value
        return self.default_value_if_unchecked

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        """
        Apply the OMR logic to the processed output.
        """
        text = processed_output.get('value', '')
        text = text.strip() if text else ''

        value = self.parse_checkbox(text)
        success = value == self.checked_value

        processed_output['value'] = value
        processed_output['post_processing_value'] = value

        return OutputFormat(item=processed_output, success=success, multi_item=self.extract_multiple)



if __name__ == "__main__":
    input_data_checked = {'value': 'some detected text'}
    input_data_unchecked = {'value': ''}

    omr_parser = OmrFlaggerOutputFormat(
        checked_value='YES',
        default_value_if_unchecked='NO'
    )

    result_checked = omr_parser(input_data_checked)
    result_unchecked = omr_parser(input_data_unchecked)
