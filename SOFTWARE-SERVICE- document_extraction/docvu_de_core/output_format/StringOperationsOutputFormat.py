#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""


from docvu_de_core.io import OutputFormat
from docvu_de_core.utils.process_utils import ProcessUtils
from datetime import datetime
import re
import string


class StringOperationsOutputFormat:
    def __init__(self, **kwargs):
        """
        Initialize StringOperationOutputFormat with various text processing options.

        Supported arguments:
        - remove_from_beginning: List of substrings or regex patterns to remove from the beginning of the text.
        - remove_from_beginning_ignore_case: Boolean to ignore case when removing from the beginning (default: True).
        - remove_from_end: List of substrings or regex patterns to remove from the end of the text.
        - remove_from_end_ignore_case: Boolean to ignore case when removing from the end (default: True).
        - contains: List of substrings or regex patterns to check if the text contains.
        - contains_ignore_case: Boolean to ignore case when checking if the text contains patterns (default: True).
        - remove_special_chars_from_beginning: Boolean to remove special characters from the beginning.
        - remove_special_chars_from_end: Boolean to remove special characters from the end.
        - remove_special_chars_from_beginning_exceptions: List of characters to exclude when removing special characters from the beginning.
        - remove_special_chars_from_end_exceptions: List of characters to exclude when removing special characters from the end.
        - remove_spaces: Boolean to remove spaces from the text.
        - remove_substrings: Boolean to remove "," from the text.
        - starts_with: List of substrings or regex patterns to check if the text starts with.
        - starts_with_ignore_case: Boolean to ignore case when checking if the text starts with patterns (default: True).
        - ends_with: List of substrings or regex patterns to check if the text ends with.
        - ends_with_ignore_case: Boolean to ignore case when checking if the text ends with patterns (default: True).
        - remove_till_from_end: List of substrings or regex patterns to remove text till from the end.
        - remove_till_from_end_ignore_case: Boolean to ignore case when removing till from the end (default: True).
        - remove_till_from_beginning: List of substrings or regex patterns to remove text till from the beginning.
        - remove_till_from_beginning_ignore_case: Boolean to ignore case when removing till from the beginning (default: True).
        - remove_till_return_default_if_not_found: Boolean to return default text if remove_till is not found.
        - remove_non_digit: Boolean to remove all non-digit characters from the text.
        - replace_from_beginning: List of tuples where the first element is the pattern to match and the second element is the replacement string.
        - replace_if_match: List of tuples where the first element is the pattern to match and the second element is the replacement string.
        - remove_alpha_from_beginning: Boolean to remove all alphabetic characters from the beginning.
        - remove_alpha_from_end: Boolean to remove all alphabetic characters from the end.
        - remove_all: Boolean to remove all content from the text.
        - set_default_if_empty: to set some value if it is empty.
        - clean_address: to clean address.
        - replace_within_string: List of tuples where the first element is the pattern to match and the second element is the replacement string(to handle ocr issues).
        - update_year_to_next: function to modify the year from date to next year of current year.
        - replace_with_custom_value: function to replace the existing value with custom value.
        - prepend_custom_value: function to add the any custom text at the beginning.
        :param kwargs: Dictionary of processing options.
        """
        self.remove_from_beginning = kwargs.get('remove_from_beginning', [])
        self.remove_from_end = kwargs.get('remove_from_end', [])
        self.contains = kwargs.get('contains', [])

        self.remove_special_chars_from_beginning = kwargs.get('remove_special_chars_from_beginning', False)
        self.remove_special_chars_from_end = kwargs.get('remove_special_chars_from_end', False)
        self.remove_special_chars_from_beginning_exceptions = kwargs.get(
            'remove_special_chars_from_beginning_exceptions', [])
        self.remove_special_chars_from_end_exceptions = kwargs.get('remove_special_chars_from_end_exceptions', [])
        self.remove_spaces = kwargs.get('remove_spaces', False)
        self.remove_substrings = kwargs.get('remove_substrings', False)
        self.remove_non_digit = kwargs.get('remove_non_digit', False)

        self.starts_with = kwargs.get('starts_with', [])
        self.ends_with = kwargs.get('ends_with', [])
        self.remove_till_from_end = kwargs.get('remove_till_from_end', [])
        self.remove_till_from_beginning = kwargs.get('remove_till_from_beginning', [])
        self.remove_till_return_default_if_not_found = kwargs.get('remove_till_return_default_if_not_found', False)

        self.replace_from_beginning = kwargs.get('replace_from_beginning', [])
        self.replace_if_match = kwargs.get('replace_if_match', [])

        self.remove_alpha_from_beginning = kwargs.get('remove_alpha_from_beginning', False)
        self.remove_alpha_from_end = kwargs.get('remove_alpha_from_end', False)
        self.remove_all = kwargs.get('remove_all', False)

        self.extract_community_number = kwargs.get('extract_community_number', False)
        self.extract_panel_number = kwargs.get('extract_panel_number', False)
        self.extract_panel_suffix = kwargs.get('extract_panel_suffix', False)

        self.set_default_if_empty = kwargs.get('set_default_if_empty', [])
        self.clean_address_patterns = kwargs.get('clean_address', [])
        self.replace_within_string = kwargs.get("replace_within_string", [])
        self.remove_characters = kwargs.get("remove_characters", [])

        self.remove_keywords = kwargs.get('remove_keywords', [])
        self.update_year_to_next = kwargs.get('update_year_to_next', False)
        self.remove_integer_from_text = kwargs.get('remove_integer_from_text', False)
        self.note_get_name = kwargs.get('note_get_name', False)
        self.replace_with_custom_value = kwargs.get('replace_with_custom_value', False)
        self.prepend_custom_value = kwargs.get('prepend_custom_value', False)


        self.ignore_case_flags = {
            'remove_from_beginning': kwargs.get('remove_from_beginning_ignore_case', True),
            'remove_from_end': kwargs.get('remove_from_end_ignore_case', True),
            'contains': kwargs.get('contains_ignore_case', True),
            'starts_with': kwargs.get('starts_with_ignore_case', True),
            'ends_with': kwargs.get('ends_with_ignore_case', True),
            'remove_till_from_end': kwargs.get('remove_till_from_end_ignore_case', True),
            'remove_till_from_beginning': kwargs.get('remove_till_from_beginning_ignore_case', True),
            'replace_if_match': kwargs.get('replace_if_match_ignore_case', True),
            'set_default_if_empty': kwargs.get('set_default_if_empty', True),
            'clean_address': kwargs.get('clean_address_ignore_case', True),
            'replace_within_string': kwargs.get('replace_within_string', True),
            'update_year_to_next': kwargs.get('update_year_to_next', True)
        }

        self.process_utils = ProcessUtils()
        self.operation_order = [key for key in kwargs.keys() if key in [
            'replace_with_custom_value', 'prepend_custom_value', 'remove_from_beginning', 'remove_from_end', 'contains', 'remove_special_chars_from_beginning',
            'remove_special_chars_from_end', 'remove_spaces', 'starts_with', 'ends_with',
            'remove_till_from_end', 'remove_till_from_beginning', 'remove_non_digit', 'remove_substrings',
            'replace_from_beginning',
            'replace_if_match', 'remove_alpha_from_beginning', 'remove_alpha_from_end', 'remove_all',
            'extract_community_number', 'extract_panel_number', 'extract_panel_suffix', 'set_default_if_empty',
            'clean_address'
            'remove_till_from_end', 'remove_till_from_beginning', 'remove_non_digit', 'remove_substrings', 'replace_from_beginning',
            'replace_if_match', 'remove_alpha_from_beginning', 'remove_alpha_from_end', 'remove_all', 'set_default_if_empty', 'clean_address', 'replace_within_string', 'remove_characters', 'remove_keywords',
            'update_year_to_next', 'remove_integer_from_text', 'note_get_name'
        ]]

    def remove_from_beginning_func(self, text):
        for pattern in self.remove_from_beginning:
            flags = re.IGNORECASE if self.ignore_case_flags['remove_from_beginning'] else 0
            if re.match(pattern, text, flags):
                return re.sub(f'^{pattern}', '', text, flags).lstrip()
        return text

    def remove_from_end_func(self, text):
        for pattern in self.remove_from_end:
            flags = re.IGNORECASE if self.ignore_case_flags['remove_from_end'] else 0
            if re.search(f'{pattern}$', text, flags):
                return re.sub(f'{pattern}$', '', text, flags).rstrip()
        return text

    def contains_func(self, text):
        for pattern in self.contains:
            flags = re.IGNORECASE if self.ignore_case_flags['contains'] else 0
            match = re.search(pattern, text, flags)
            if match:
                return match.group(0)
        return text

    def remove_special_chars_from_beginning_func(self, text):
        except_list = self.remove_special_chars_from_beginning_exceptions
        if any(text.startswith(char) for char in except_list):
            # If the text starts with any of the exception characters, do not remove anything
            return text

        except_pattern = ''.join(re.escape(char) for char in except_list)
        # Create a pattern that matches special characters at the beginning that are not in except_pattern
        pattern = f'^[^\w{except_pattern}]+'

        return re.sub(pattern, '', text)

    def remove_special_chars_from_end_func(self, text):
        except_list = self.remove_special_chars_from_end_exceptions
        if any(text.endswith(char) for char in except_list):
            # If the text ends with any of the exception characters, do not remove anything
            return text

        except_pattern = ''.join(re.escape(char) for char in except_list)
        # Create a pattern that matches special characters that are not in except_pattern
        pattern = f'[^\w{except_pattern}]+$'

        return re.sub(pattern, '', text)

    def remove_spaces_func(self, text):
        return text.replace(' ', '')

    def remove_substrings_func(self, text):
        return text.replace(',', '')

    def starts_with_func(self, text):
        for pattern in self.starts_with:
            flags = re.IGNORECASE if self.ignore_case_flags['starts_with'] else 0
            if re.match(f'^{pattern}', text, flags):
                return True
        return False

    def ends_with_func(self, text):
        for pattern in self.ends_with:
            flags = re.IGNORECASE if self.ignore_case_flags['ends_with'] else 0
            if re.search(f'{pattern}$', text, flags):
                return True
        return False

    def remove_till_from_end_func(self, text):
        for pattern in self.remove_till_from_end:
            flags = re.IGNORECASE if self.ignore_case_flags['remove_till_from_end'] else 0
            match = re.search(pattern, text, flags)
            if match:
                return text[:match.start()]
        return text

    def remove_till_from_beginning_func(self, text):
        for pattern in self.remove_till_from_beginning:
            flags = re.IGNORECASE if self.ignore_case_flags['remove_till_from_beginning'] else 0
            match = re.search(pattern, text, flags)
            if match:
                return text[match.end():]
        return text

    def remove_non_digit_func(self, text):
        return ''.join(filter(str.isdigit, text))

    def replace_from_beginning_func(self, text):
        for pattern, replacement in self.replace_from_beginning:
            flags = re.IGNORECASE if self.ignore_case_flags.get('replace_from_beginning', True) else 0
            if re.match(pattern, text, flags):
                return re.sub(f'^{pattern}', replacement, text, flags)
        return text

    def replace_if_match_func(self, text):
        for pattern, replacement in self.replace_if_match:
            flags = re.IGNORECASE if self.ignore_case_flags['replace_if_match'] else 0
            if re.fullmatch(pattern, text, flags):
                return replacement
        return text

    def remove_alpha_from_beginning_func(self, text):
        return re.sub(r'^[A-Za-z]+', '', text)

    def remove_alpha_from_end_func(self, text):
        return re.sub(r'[A-Za-z]+$', '', text)

    def remove_all_func(self, text):
        return ''

    def extract_community_number_func(self, text):
        """
        Extract the first 6 characters from the provided text.

        :param text: The text value that will be processed.
        :return: A string with the first 6 characters of the text or None if text is too short.
        """

        if isinstance(text, str):
            # Remove spaces from the text
            text = text.replace(" ", "")

            # Ensure that the length of the text is sufficient to extract variable_1
            if len(text) >= 11:
                variable_1 = text[:6]
                return variable_1
            else:
                print(f"Text is too short for variable_1. Returning original: {text}")  # Debugging: if text is too short
        else:
            print(f"Invalid input: Text is not a string. Returning original: {text}")
            return str(text)  # Return the original input as a string if it's not a string

    def extract_panel_number_func(self, text):
        """
        Extract the last 5 characters from a map number string.
        Handles both raw map numbers and embedded formats like 'MapNumber:12099C0554F'.

        :param text: The text value to process.
        :return: A 5-character panel number string, or the original input if extraction fails.
        """
        if not isinstance(text, str):
            print(f"[extract_panel_number_func] Invalid input: not a string. Returning original.")
            return str(text)

        # Remove all whitespaces
        text = text.replace(" ", "")

        # Case 1: Input contains 'MapNumber:'
        if "MapNumber:" in text:
            map_number = text.split("MapNumber:")[-1]
            if len(map_number) >= 5:
                return map_number[-5:]
            else:
                print(f"[extract_panel_number_func] MapNumber present but too short: '{map_number}'")
                return map_number

        # Case 2: Raw map number format like '12099C0554F'
        elif len(text) >= 11:
            return text[-5:]

        # Fallback: Input too short for extraction
        else:
            print(f"[extract_panel_number_func] Text too short to extract panel number: '{text}'")
            return text

    def extract_panel_suffix_func(self, text):
        """
        Extract the last character from the provided text.

        :param text: The text value that will be processed.
        :return: The last character of the text or None if text is too short.
        """

        if isinstance(text, str):
            # Remove spaces from the text
            text = text.replace(" ", "")

            # Ensure that the text is long enough to have a last character
            if len(text) >= 11:
                last_char = text[-1]
                return last_char
            else:
                print(f"Text is too short for last_char. Returning original: {text}")  # Debugging: if text is too short
        else:
            print(f"Invalid input: Text is not a string. Returning original: {text}")
            return str(text)  # Return the original input as a string if it's not a string

    def set_default_if_empty_func(self, text):
        """
        Check if text is None or empty. Replace it with a default value from configuration.
        """
        # Retrieve default text from the configuration
        default_text = self.set_default_if_empty

        # Check if text is None or empty
        if text is None or text.strip() == "":
            return default_text
        return text

    def clean_address_func(self, text):
        for pattern in self.clean_address_patterns:
            flags = re.IGNORECASE if self.ignore_case_flags.get('clean_address', False) else 0
            if re.search(pattern, text, flags):
                text = re.sub(pattern, '', text, flags)
        return text

    def replace_within_string_func(self, text):
        for pattern, replacement in self.replace_within_string:  # Can rename for better context
            flags = re.IGNORECASE if self.ignore_case_flags.get('replace_within_string', True) else 0
            if re.search(pattern, text, flags):  # Check for a match anywhere in the text
                return re.sub(pattern, replacement, text, flags)  # Replace all occurrences of the pattern
        return text

    def remove_characters_func(self, text):
        """
        Remove specific characters from anywhere in the text.
        Example: remove_characters = ["$", "#"]
        """
        for ch in self.remove_characters:
            text = text.replace(ch, "")
        return text


    def remove_keywords_func(self, text):
        """
        Remove specific keywords or phrases from anywhere in the text.
        Example: remove_keywords = ["Unit #", "City", "State", "ZIP"]
        """
        for kw in self.remove_keywords:
            text = text.replace(kw, "")
        # Remove extra spaces
        text = " ".join(text.split())
        return text

    def update_year_to_next_func(self, text):
        """
        Parses a date string in 'dd/mm/YYYY' format, increments the year by 1,
        and returns the updated date string in the same format.
        If parsing fails, returns the original text.
        """
        current_year = datetime.now().year
        next_year = current_year + 1
        try:
            date = datetime.strptime(text, "%m/%d/%Y")
            return date.replace(year=next_year).strftime("%m/%d/%Y")
        except ValueError:
            return text

    def remove_integer_from_text_func(self, text):
        cleaned_text = re.sub(r'\b\d+\b', '', text)
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()

        return cleaned_text

    def note_get_name_func(self, text):
        words = text.split()
        if len(words) > 3:
            words = words[1:]
        return ' '.join(words)

    def replace_with_custom_value_func(self, text):
        """
        Replaces the input text with a predefined custom value if the text is non-empty
        and not just whitespace.
        """
        custom_value = self.replace_with_custom_value
        if text and text.strip():
            return custom_value
        return text

    def prepend_custom_value_func(self, text):
        """
        Prepends a predefined custom value to the input text if the text is non-empty
        and not just whitespace.
        """
        prepend_value = self.prepend_custom_value  # e.g., "PSA - "
        if text:
            return f"{prepend_value}{text}"
        return text

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        """
        Execute the configured text processing operations on the given string.

        :param processed_output: Dictionary containing 'value' which is the input string.
        :return: OutputFormat with the processed text and related information.
        """
        # text = processed_output['post_processing_value']

        if processed_output.get('type') == 'table_new' and processed_output.get('return_type') == 'table':
            value = processed_output.get('value')

            if value is None:
                value = ''
            elif isinstance(value, list):
                value = " ".join(value)
            
            processed_output['value'] = value

            if processed_output.get('rows', []):
                for row in processed_output.get('rows', []):
                    for col in row.get('columns', []):
                        text = col.get('post_processing_value')

                        if text is None:
                            text = ''
                        elif isinstance(text, list):
                            text = " ".join(text)

                        original_text = text
                        success = False

                        for operation in self.operation_order:
                            func = getattr(self, f"{operation}_func", None)
                            if func:
                                text = func(text)
                                if text != original_text:
                                    success = True
                                original_text = text

                        if success:
                            col['value'] = text
                            col['post_processing_value'] = text

            return OutputFormat(item=processed_output, success=True, multi_item=False)

        else:
            text = processed_output.get('post_processing_value')

            if text is None:
                text = ''
            elif isinstance(text, list):
                text = " ".join(text)

            original_text = text
            success = False

            for operation in self.operation_order:
                func = getattr(self, f"{operation}_func", None)
                if func:
                    text = func(text)
                    if text != original_text:
                        success = True
                    original_text = text

            if success:
                processed_output['value'] = text
                processed_output['post_processing_value'] = text

            return OutputFormat(item=processed_output, success=True, multi_item=False)
