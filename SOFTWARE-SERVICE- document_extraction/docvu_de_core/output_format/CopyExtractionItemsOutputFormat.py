#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""
"""
Supports only BaseFieldData and BaseTableData with 1 col.
"""
from docvu_de_core.io import OutputFormat
from copy import deepcopy

class CopyExtractionItemsOutputFormat:
    def __init__(self,
                 swap_with=None,
                 found_str=[],
                 col_idx=-1,
                 is_empty=False,
                 **kwargs):
        # found_str = ["exhibit \"a\"", "exhibit a"]
        for idx, fs in enumerate(found_str):
            found_str[idx] = fs.lower()

        self.swap_with = swap_with
        self.found_str = found_str
        self.col_idx = 0 if col_idx == -1 else col_idx
        self.is_empty = is_empty

    def is_found_str(self, value):
        search_list = [fs in value.lower() for fs in self.found_str]
        return any(search_list)

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        legal_description_text_item = None
        legal_description_exhibit_item = None
        success = False
        current_item_name = processed_output['name']
        swap = False

        # Identify the legal description text and exhibit items
        for item in extraction_items:
            if item.get('name') == current_item_name:
                legal_description_text_item = item
            elif item.get('name') == self.swap_with:
                legal_description_exhibit_item = item

        # Check if we have both items
        if legal_description_text_item and legal_description_exhibit_item:
            # Check the values for the presence of the exhibit reference
            if self.found_str:
                values = processed_output['post_processing_value'] if processed_output else ''
                if type(values) is list:
                    values = " ".join(v for v in values)
                if values is None and not self.is_empty:
                    return OutputFormat(item=processed_output,
                                 success=success,
                                 multi_item=False)
                # if ("exhibit \"a\"".lower() in values.lower()) or ("exhibit a".lower() in values.lower()):
                if values and self.is_found_str(values):
                    swap = True
            if self.is_empty:
                values = processed_output['post_processing_value'] if processed_output else ''
                if type(values) is list:
                    if len(values) > 0 and type(values[0]) is list:
                        values = [" ".join(v for v in value) for value in values]
                    values = " ".join(v for v in values)
                if not values:
                    swap = True
            if swap:
                # Copy the contents from exhibit to legal description text
                processed_output['post_processing_value'] = deepcopy(legal_description_exhibit_item['post_processing_value'])
                if processed_output.is_table and legal_description_exhibit_item.is_table:
                    processed_output['rows'] = deepcopy(legal_description_exhibit_item['rows'])
                    processed_output.update_name(current_item_name, col_idx=self.col_idx)
                else:
                    processed_output.update_value(legal_description_exhibit_item)
                    processed_output['post_processing_value'] = deepcopy(legal_description_exhibit_item['post_processing_value'])
                success = True


        return OutputFormat(item=processed_output,
                            success=success,
                            multi_item=False)
