from openai import AzureOpenAI, OpenAIError
from docvu_de_core.utils.process_utils import ProcessUtils
from docvu_de_core.io import OutputFormat
from docvu_de_core.config import llm_config
from docvu_de_core.utils import logger
import os, json
from docvu_de_core import llm_prompt_config_path

# Disable parallelism to avoid HuggingFace fork warning
os.environ["TOKENIZERS_PARALLELISM"] = "false"
class LlmOperationsOutputFormat:
    def __init__(self, **kwargs):
        self.process_utils = ProcessUtils()
        self.endpoint = llm_config.get('endpoint_url')
        self.deployment = llm_config.get('deployment_name')
        self.api_key = llm_config.get('azure_openai_api_key')
        self.api_version = llm_config.get('api_version')
        self.temperature = llm_config.get('temperature')
        self.top_p = llm_config.get('top_p')
        self.max_tokens = llm_config.get('max_tokens')
        self.text_field_name = kwargs.get("text_field_name", None)
        try:
            self.client = AzureOpenAI(
                azure_endpoint=self.endpoint,
                api_key=self.api_key,
                api_version=self.api_version
            )
            logger.info("AzureOpenAI client initialized successfully.")
        except Exception as e:
            logger.exception(f"Failed to initialize AzureOpenAI client {e}.")
            raise e

    def __call__(self, processed_output, extraction_items=None, client_id=None, **kwargs):

        context = ''
        try:
            base_config_path = llm_prompt_config_path
            client_path = os.path.join(base_config_path, str(client_id), "llm_prompts.json")
            with open(client_path, "r") as f:
                llm_prompt_config = json.load(f)

            if self.text_field_name:
                for ei in extraction_items:
                    if ei['name'] == self.text_field_name:
                        context = ei['post_processing_value']
                        if isinstance(context, list):
                            if len(context) > 0:
                                if isinstance(context[0], str):
                                    context = ' '.join(context)
                        break
            else:
                context = processed_output['post_processing_value']
                if isinstance(context, list) and context:
                    context = context[0]
            if not context:
                return OutputFormat(item=processed_output, success=False, multi_item=False)

            document_id = str(processed_output['document_id'])
            field_id = str(processed_output['field_id'])

            if document_id not in llm_prompt_config:
                logger.error(f"Document ID '{document_id}' not found in llm_prompt_config.")
                return OutputFormat(item=processed_output, success=False, multi_item=False)

            if field_id not in llm_prompt_config[document_id]:
                logger.error(f"Field ID '{field_id}' not found under Document ID '{document_id}'.")
                return OutputFormat(item=processed_output, success=False, multi_item=False)

            prompt = llm_prompt_config[document_id][field_id]["prompt"]
            if not prompt:
                logger.error(f"'prompt' key is missing under Document ID '{document_id}' and Field ID '{field_id}'.")
                return OutputFormat(item=processed_output, success=False, multi_item=False)

            chat_prompt = [
                {
                    "role": "system",
                    "content": [{"type": "text",
                                 "text": "You are a legal analyst. Carefully review legal agreements and provide precise answers to questions based on the content."}]
                },
                {
                    "role": "user",
                    "content": [{"type": "text", "text": f"Document:\n{context}\n\nQuestion:\n{prompt}"}]
                }
            ]
            completion = self.client.chat.completions.create(
                model=self.deployment,
                messages=chat_prompt,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                top_p=self.top_p,
                frequency_penalty=0,
                presence_penalty=0,
                stop=None,
                stream=False
            )
            generated_output = completion.choices[0].message.content
            if not generated_output:
                return OutputFormat(item=processed_output, success=False, multi_item=False)

            processed_output['post_processing_value'] = generated_output

            return OutputFormat(item=processed_output, success=True, multi_item=False)

        except OpenAIError as e:
            logger.exception(f"OpenAI API error occurred.{e}")
            return OutputFormat(item=processed_output, success=False, multi_item=False)
        except Exception as e:
            logger.exception(f"Unexpected error in LlmOutputFormat.{e}")
            return OutputFormat(item=processed_output, success=False, multi_item=False)


# Example usage (adjust this based on your actual usage)
if __name__ == "__main__":
    # Mock processed output and config for testing
    test_processed_output = {
        'post_processing_value': ["This is a sample legal contract between Company A and Company B."]
    }

    extraction_items = [
        {'name': 'Legal_Text', 'post_processing_value': ["This is a sample legal contract..."]}
    ]

    llm_output = LlmOperationsOutputFormat(
        question="Who are the parties involved in the contract?",
        text_field_name="Legal_Text"
    )

    response = llm_output(test_processed_output, extraction_items)
    print("OutputFormat JSON:", response.to_json())
