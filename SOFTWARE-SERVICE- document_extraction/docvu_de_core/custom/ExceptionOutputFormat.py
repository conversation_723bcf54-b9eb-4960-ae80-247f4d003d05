import re
from docvu_de_core.io import OutputFormat
from docvu_de_core.utils import GeneralUtils
from docvu_de_core.extraction_item import TableRow
from docvu_de_core.modules import SplitByBullets

class ExceptionOutputFormat:
    def __init__(self, **kwargs):
        self.gu = GeneralUtils()

    def update_blank_exception(self, processed_output):
        if processed_output['rows'] is None:
            return processed_output
        if len(processed_output['rows']) == 1:
            is_part_1 = False
            is_part_2 =False
            can_update_val = False
            exception_text_idx = None
            for idx, col in enumerate(processed_output['rows'][0]['columns']):
                if col['name'] == 'Exception_Header':
                    if col['value'].strip().lower() in ['schedule b part I'.lower(),
                                                        'schedule b part 1'.lower(),
                                                        'schedule b'.lower()]:
                        is_part_1 = True
                    elif col['value'].strip().lower() in ['schedule b part II'.lower(),
                                                        'schedule b part I1'.lower(),
                                                        'schedule b part 11'.lower(),
                                                        'schedule b part 1I'.lower()]:
                        is_part_2 = True
                elif col['name'] == 'Exception_Text':
                    if is_part_1:
                        if not((col['value'].strip().lower().startswith('1')) or (col['value'].strip().lower() == 'none')):
                            processed_output['rows'][0]['columns'][idx]['post_processing_value'] = 'BLANK'
                    elif is_part_2:
                        eh = self.gu.get_base_format(processed_output)
                        ety = self.gu.get_base_format(processed_output['sub_items'][0], default='text', force=True)
                        et = self.gu.get_base_format(processed_output['sub_items'][0]['sub_items'][0], default='text', force=True)
                        eh['post_processing_value'] = 'SCHEDULE B'
                        et['post_processing_value'] = 'BLANK'
                        ety['post_processing_value'] = ''
                        processed_output['rows'].insert(0, TableRow(columns=[eh, et, ety]))
        else:
            part_2_found = False
            part_1_found = False
            is_single_part_1 = True
            is_single_part_2 = True
            for idx, row in enumerate(processed_output['rows']):
                for jdx, col in enumerate(row['columns']):
                    if col['name'] == 'Exception_Header':
                        if col['value'].strip().lower() in ['schedule b part II'.lower(),
                                                            'schedule b part I1'.lower(),
                                                            'schedule b part 11'.lower(),
                                                            'schedule b part 1I'.lower()]:
                            if part_2_found and is_single_part_2:
                                is_single_part_2 = False
                            part_2_found = True
                        if col['value'].strip().lower() in ['schedule b part I'.lower(),
                                                            'schedule b part 1'.lower(),
                                                            'schedule b'.lower()]:
                            if part_1_found and is_single_part_1:
                                is_single_part_1 = False
                            part_1_found = True

            if part_2_found and not part_1_found:
                eh = self.gu.get_base_format(processed_output)
                ety = self.gu.get_base_format(processed_output['sub_items'][0], default='text', force=True)
                et = self.gu.get_base_format(processed_output['sub_items'][0]['sub_items'][0], default='text', force=True)
                eh['post_processing_value'] = 'SCHEDULE B'
                et['post_processing_value'] = 'BLANK'
                ety['post_processing_value'] = ''
                processed_output['rows'].insert(0, TableRow(columns=[eh, et, ety]))

        return processed_output

    def update_exception_type(self, processed_output):
        if processed_output['rows'] is None:
            return processed_output
        found_et = False
        previous_et = None

        ## process schedule b part 1
        for row_idx, row in enumerate(processed_output['rows']):
            for col_idx, col in enumerate(row['columns']):
                if col['name'] == 'Exception_Header':
                    if "part II".lower() in col['value'].lower() or \
                            "part I1".lower() in col['value'].lower() or \
                            "part 1I".lower() in col['value'].lower() or \
                            "part 11".lower() in col['value'].lower():
                        break
                if col['name'] == 'Exception_Type':
                    if col['value']:
                        if col['value'].rstrip(':').lower() == "exceptions":
                            col['value'] = None
                            col['post_processing_value'] = None
                        elif col['value'].rstrip(':').lower() == "any subject property specific exceptions":
                            col['value'] = "Specific Exceptions"
                            col['post_processing_value'] = "Specific Exceptions"
                        else:
                            # Remove the trailing colon if it exists
                            col['value'] = col['value'].rstrip(':')
                            col['post_processing_value'] = col['post_processing_value'].rstrip(':')
                        found_et = True
                        previous_et = col
                    elif found_et and previous_et is not None:
                        processed_output['rows'][row_idx]['columns'][col_idx] = previous_et

        ## process schedule b part II
        for row_idx, row in enumerate(processed_output['rows']):
            for col_idx, col in enumerate(row['columns']):
                if col['name'] == 'Exception_Header':
                    if not ("part II".lower() in col['value'].lower() or \
                            "part I1".lower() in col['value'].lower() or \
                            "part 1I".lower() in col['value'].lower() or \
                            "part 11".lower() in col['value'].lower()):
                        break
                if col['name'] == 'Exception_Type':
                    if col['value']:
                        if col['value'].rstrip(':').lower() == "exceptions":
                            col['value'] = None
                            col['post_processing_value'] = None
                        elif col['value'].rstrip(':').lower() == "any subject property specific exceptions":
                            col['value'] = "Specific Exceptions"
                            col['post_processing_value'] = "Specific Exceptions"
                        else:
                            # Remove the trailing colon if it exists
                            col['value'] = col['value'].rstrip(':')
                            col['post_processing_value'] = col['post_processing_value'].rstrip(':')
                        found_et = True
                        previous_et = col
                    elif found_et and previous_et is not None:
                        processed_output['rows'][row_idx]['columns'][col_idx] = previous_et

        return processed_output


    def merge_exceptions_if_next_point_found(self, processed_output):
        prev_point = 0
        prev_row = -1
        prev_header = ''
        prev_type = ''
        for row_idx, row in enumerate(processed_output['rows']):
            for col_idx, col in enumerate(row['columns']):
                if col['name'] == "Exception_Text":
                    is_bulltet, point = SplitByBullets.is_bullet_point(col['value'])
                    if prev_point == 0 and is_bulltet:
                        prev_point = point
                        prev_row = row_idx
                        for _, col2 in enumerate(row['columns']):
                            if col2['name'] == "Exception_Header":
                                prev_header = col3['value']
                            if col3['name'] == "Exception_Type":
                                prev_type = col3['value']
                        continue
                    if point - prev_point == 1:
                        curr_header = ''
                        curr_type = ''
                        for _, col3 in enumerate(processed_output['rows'][prev_row]):
                            if col3['name'] == "Exception_Header":
                                curr_header = col3['value']
                            if col3['name'] == "Exception_Type":
                                curr_type = col3['value']
                        if (prev_header == curr_header) and (prev_type == curr_type):
                            ## merge it here
                            pass
                        prev_point = 0
                        prev_row = -1

    def update_exception_header(self, processed_output):
        """
        Updates the 'Exception_Header' column in the processed output to ensure it is valid.

        This method adjusts the exception header for cases where multi-page exceptions
        do not contain a "Schedule B" header. Specifically, it sets the value to `None`
        if the header is not relevant, such as when it takes the first text string as the header.

        Parameters:
            processed_output (dict): A dictionary containing the processed output data.

        Returns:
            dict: The modified `processed_output` dictionary with updated 'Exception_Header' values.
        """
        if processed_output['rows'] is None:
            return processed_output
        for row_idx, row in enumerate(processed_output['rows']):
            for col_idx, col in enumerate(row['columns']):
                if col['name'] == 'Exception_Header' and col['value']:
                    if "schedule" not in col['value'].strip().lower():
                        col['value'] = 'SCHEDULE B'
                        col['post_processing_value'] = 'SCHEDULE B'

        return processed_output
    def __call__(self,  processed_output, extraction_items=None, **kwargs):
        processed_output = self.update_blank_exception(processed_output)
        processed_output = self.update_exception_type(processed_output)
        processed_output = self.update_exception_header(processed_output)

        return OutputFormat(item=processed_output,
                            success=True,
                            multi_item=False)

