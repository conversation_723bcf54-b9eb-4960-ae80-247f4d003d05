#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""

from wordllama import Word<PERSON>lama
from docvu_de_core.search import SearchKey<PERSON>ieldHelper
from docvu_de_core.utils import logger

class LegalDescriptionSearch:
    def __init__(self, debug=False):
        super().__init__()
        self.helper = SearchKeyFieldHelper(logger)  # Assuming 'logger' is defined elsewhere
        self.debug = debug

        # Load the WordLlama model
        self.word_llama = WordLlama.load()
        print("word_llama loaded successfully")

    def _extract_data_from_page(self, data):
        possible_answer_list = []

        # Flatten the data and extract valid text strings
        for item in data:
            for blocks in item.get("TEXT_BLOCK", []):
                for text_line in blocks.get("TEXT_LINE", []):
                    for text_string in text_line.get("STRING", []):
                        if text_string:
                            possible_answer_list.append(text_string)

        # Re-arrange based on vertical and horizontal positions
        sorted_list = self.helper.process_utils.rearrange_based_on_vpos_hpos(possible_answer_list)
        return self.helper.process_utils.get_string_from_elements(sorted_list)  # Combine the text

    def get_curr_page_text(self, whole_page_data):
        # Extract the text from the current page
        return self._extract_data_from_page(whole_page_data)

    def get_prev_page_text(self, prev_page_json_data):
        # Extract the text from the previous page
        return self._extract_data_from_page(prev_page_json_data)

    def calculate_similarity(self, prev_page_text, curr_page_text):
        # Check for empty or None text inputs
        if not curr_page_text or not prev_page_text:
            logger.error("One or both input texts are empty.")
            return None

        # Calculate similarity using WordLlama
        similarity_score = self.word_llama.similarity(prev_page_text, curr_page_text)
        return similarity_score

    def process_pages(self, prev_page_data, curr_page_data):
        # Extract text from current and previous pages
        curr_page_text = self.get_curr_page_text(curr_page_data)
        prev_page_text = self.get_prev_page_text(prev_page_data)

        # Calculate similarity
        similarity_score = self.calculate_similarity(prev_page_text, curr_page_text)
        print("Similarity Score:", similarity_score)

        if self.debug:
            logger.info(f"Current Page Text: {curr_page_text}")
            logger.info(f"Previous Page Text: {prev_page_text}")
            logger.info(f"Similarity Score: {similarity_score}")

        return similarity_score
