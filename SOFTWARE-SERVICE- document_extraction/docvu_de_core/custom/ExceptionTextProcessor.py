import re
from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.modules import SplitByRegEx, SplitByBullets, CCA
import re


class ExceptionTextProcessor:
    def remove_special_chars(self, string):
        # Remove special characters from the beginning and end
        cleaned_string = re.sub(r'^[\W_]+|[\W_]+$', '', string)
        return cleaned_string

    def __call__(self, **kwargs):
        elements = kwargs.get('elements')
        regex_list = kwargs.get('regex')
        image_path = kwargs.get('image_path')

        cca = None
        if image_path is not None:
            cca = CCA(image_path)
            cca = False

        # Split the text by the regex
        regex_splitter = SplitByRegEx(regex_list, consider_contents_before_found=True, keyword_zone='left')
        regex_split_output = regex_splitter.split_text(elements, get_elements=True, get_keyword_info=True)
        regex_split_output_bbox = []
        formatted_bbox = []
        formatted_line_number = []
        formatted_value = []

        if regex_split_output.success:
            # Split the parts by bullet points
            bullet_points_split = []
            bullet_points_outputs = []

            # to_remove_idx = []
            for regex_idx, part in enumerate(regex_split_output.elements):
                if not part:
                    # to_remove_idx.append(regex_idx)
                    regex_split_output.elements.pop(regex_idx)
                    regex_split_output.bbox.pop(regex_idx)
                    regex_split_output.value.pop(regex_idx)
                    regex_split_output.keyword.pop(regex_idx)
                    regex_split_output.line_number.pop(regex_idx)
                    continue
                pre_processed_value = self.remove_special_chars(regex_split_output.value[regex_idx])
                if pre_processed_value.strip().lower() == regex_split_output.keyword[regex_idx].strip().lower():
                    regex_split_output.elements.pop(regex_idx)
                    regex_split_output.bbox.pop(regex_idx)
                    regex_split_output.value.pop(regex_idx)
                    regex_split_output.keyword.pop(regex_idx)
                    regex_split_output.line_number.pop(regex_idx)
                    continue

            for regex_idx, part in enumerate(regex_split_output.elements):
                cleaned_text = self.remove_special_chars(part[0]['text']).lower()
                keyword_text = regex_split_output.keyword[regex_idx].strip().lower()

                if cleaned_text in ['taxes', 'exceptions', 'any subject property specific exceptions'] and cleaned_text in keyword_text:
                    regex_split_output.elements[regex_idx].pop(0)
                    continue
            for regex_idx, part in enumerate(regex_split_output.elements):
                if not part:
                    continue

                keyword = regex_split_output.keyword[regex_idx]

                bullet_split_output = SplitByBullets.split_by_bullets(part)

                if bullet_split_output.value is not None:
                    pre_processed_value = self.remove_special_chars(bullet_split_output.value[0])
                    if pre_processed_value.strip().lower() == keyword.strip().lower():
                        bullet_split_output.elements.pop(0)
                        bullet_split_output.bbox.pop(0)
                        bullet_split_output.value.pop(0)
                        bullet_split_output.line_number.pop(0)

                    if bullet_split_output.value:
                        ## detect striken through bullets
                        for idx, bpo in enumerate(bullet_split_output.value):
                            if cca:
                                if cca.is_strikethrough(bullet_split_output.bbox[idx]):
                                    _, point_number = SplitByBullets.is_bullet_point(bullet_split_output.value[idx])
                                    bullet_split_output.value[idx] = f"{point_number}. DELETED."

                        bullet_points_outputs.append((bullet_split_output, regex_idx))
                        try:
                            regex_split_output_bbox.append(regex_split_output.bbox[regex_idx])
                        except:
                            regex_split_output_bbox.append([0, 0, 0, 0])

            # Format the output
            formatted_bbox_temp = []
            for item in bullet_points_outputs:
                try:
                    formatted_bbox_temp.append((item[0].bbox, regex_split_output_bbox[item[1]]))
                except:
                    formatted_bbox_temp.append((item[0].bbox, [0, 0, 0, 0]))
            # formatted_bbox_temp = [(item[0].bbox, regex_split_output.bbox[item[1]]) for item in bullet_points_outputs]
            formatted_bbox = []
            for fbt in formatted_bbox_temp:
                bullet_bbox, keyword_bbox = fbt
                for i in range(len(bullet_bbox)):
                    bullet_bbox[i] = [bullet_bbox[i], keyword_bbox]
                formatted_bbox += bullet_bbox

            formatted_line_number_temp = [(item[0].line_number, regex_split_output.line_number[item[1]]) for item in
                                          bullet_points_outputs]
            formatted_line_number = []
            for flnt in formatted_line_number_temp:
                bullet_line_number, keyword_line_number = flnt
                for i in range(len(bullet_line_number)):
                    bullet_line_number[i] = [bullet_line_number[i], keyword_line_number]
                formatted_line_number += bullet_line_number

            formatted_value_temp = [(item[0].value, regex_split_output.keyword[item[1]]) for item in
                                    bullet_points_outputs]
            formatted_value = []
            for fvt in formatted_value_temp:
                bullet_value, keyword_value = fvt
                for i in range(len(bullet_value)):
                    bullet_value[i] = [bullet_value[i], keyword_value]
                formatted_value += bullet_value
        else:
            bullet_split_output = SplitByBullets.split_by_bullets(elements)

            ## detect striken through bullets
            if bullet_split_output.value is not None:
                for idx, bpo in enumerate(bullet_split_output.value):
                    if cca:
                        if cca.is_strikethrough(bullet_split_output.bbox[idx]):
                            _, point_number = SplitByBullets.is_bullet_point(bullet_split_output.value[idx])
                            bullet_split_output.value[idx] = f"{point_number}. DELETED."

                formatted_bbox = bullet_split_output.bbox
                formatted_line_number = bullet_split_output.line_number
                formatted_value = bullet_split_output.value

        # Format the output
        output_format = PostProcessOutputFormat(
                bbox=formatted_bbox,
                line_number=formatted_line_number,
                value=formatted_value,
                success=regex_split_output.success,
                is_table=True
        )

        return output_format


## TODO
# 2. change SplitByBullets to take start bullet point as input
# 3. Add entire Exception processing here
# 4. Return everything above the first keyword as well to not to miss general exceptions

# Example usage
if __name__ == "__main__":
    # Assume elements and regex_list are provided as needed
    elements = [
        {'text': 'SCHEDULE B', 'HPOS': 742, 'VPOS': 195, 'WIDTH': 193, 'HEIGHT': 24, 'END_HPOS': 935, 'END_VPOS': 219,
         'WORDS': [
             {'text': 'SCHEDULE', 'HPOS': 742.0, 'VPOS': 195.33333333333331, 'WIDTH': 162.66666666666663, 'HEIGHT': 24.0
              },
             {'text': 'B', 'HPOS': 935.9999999999999, 'VPOS': 196.0, 'WIDTH': 0.0, 'HEIGHT': 24.0
              }
         ], 'LINE_NUMBER': 2
         },
        {'text': 'PART I', 'HPOS': 798, 'VPOS': 234, 'WIDTH': 98, 'HEIGHT': 24, 'END_HPOS': 896, 'END_VPOS': 258,
         'WORDS': [
             {'text': 'PART', 'HPOS': 798.6666666666666, 'VPOS': 234.66666666666666, 'WIDTH': 66.66666666666674,
              'HEIGHT': 24.0
              },
             {'text': 'I', 'HPOS': 896.0000000000001, 'VPOS': 234.66666666666666, 'WIDTH': 0.0, 'HEIGHT': 24.0
              }
         ], 'LINE_NUMBER': 3
         },
        {'text': 'File No.: 20180337', 'HPOS': 102, 'VPOS': 272, 'WIDTH': 232, 'HEIGHT': 21, 'END_HPOS': 334,
         'END_VPOS': 293, 'WORDS': [
            {'text': 'File', 'HPOS': 102.0, 'VPOS': 272.0, 'WIDTH': 30.666666666666657, 'HEIGHT': 21.333333333333332
             },
            {'text': 'No.:', 'HPOS': 157.33333333333331, 'VPOS': 272.0, 'WIDTH': 45.33333333333334,
             'HEIGHT': 21.333333333333332
             },
            {'text': '20180337', 'HPOS': 225.33333333333331, 'VPOS': 271.3333333333333, 'WIDTH': 108.66666666666669,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 4
         },
        {'text': 'Policy No.: M-0000-75767547 3', 'HPOS': 1200, 'VPOS': 272, 'WIDTH': 385, 'HEIGHT': 21,
         'END_HPOS': 1585, 'END_VPOS': 293, 'WORDS': [
            {'text': 'Policy', 'HPOS': 1200.6666666666667, 'VPOS': 272.0, 'WIDTH': 63.99999999999977,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'No.:', 'HPOS': 1290.0, 'VPOS': 272.0, 'WIDTH': 45.333333333333485, 'HEIGHT': 21.333333333333332
             },
            {'text': 'M-0000-75767547', 'HPOS': 1358.6666666666667, 'VPOS': 272.0, 'WIDTH': 211.33333333333326,
             'HEIGHT': 21.333333333333332
             },
            {'text': '3', 'HPOS': 1585.3333333333333, 'VPOS': 271.3333333333333, 'WIDTH': 0.0,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 4
         },
        {
            'text': "This policy does not insure against loss or damage (and the Company will not pay costs, "
                    "attorneys' fees or expenses) that",
            'HPOS': 100, 'VPOS': 368, 'WIDTH': 1492, 'HEIGHT': 21, 'END_HPOS': 1592, 'END_VPOS': 389, 'WORDS': [
            {'text': 'This', 'HPOS': 100.66666666666667, 'VPOS': 368.0, 'WIDTH': 38.66666666666664,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'policy', 'HPOS': 162.0, 'VPOS': 373.3333333333333, 'WIDTH': 55.333333333333314,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'does', 'HPOS': 240.0, 'VPOS': 368.0, 'WIDTH': 46.666666666666686, 'HEIGHT': 21.333333333333332
             },
            {'text': 'not', 'HPOS': 309.3333333333333, 'VPOS': 373.3333333333333, 'WIDTH': 29.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'insure', 'HPOS': 355.3333333333333, 'VPOS': 368.0, 'WIDTH': 60.00000000000006,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'against', 'HPOS': 438.6666666666667, 'VPOS': 373.3333333333333, 'WIDTH': 81.33333333333331,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'loss', 'HPOS': 536.6666666666666, 'VPOS': 368.0, 'WIDTH': 35.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'or', 'HPOS': 593.3333333333333, 'VPOS': 373.3333333333333, 'WIDTH': 16.666666666666742,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'damage', 'HPOS': 626.6666666666667, 'VPOS': 368.0, 'WIDTH': 84.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': '(and', 'HPOS': 736.0, 'VPOS': 367.3333333333333, 'WIDTH': 39.33333333333326,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'the', 'HPOS': 798.0, 'VPOS': 368.0, 'WIDTH': 24.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'Company', 'HPOS': 846.0, 'VPOS': 367.3333333333333, 'WIDTH': 103.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'will', 'HPOS': 971.3333333333334, 'VPOS': 373.3333333333333, 'WIDTH': 34.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'not', 'HPOS': 1019.3333333333334, 'VPOS': 373.3333333333333, 'WIDTH': 29.333333333333144,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'pay', 'HPOS': 1066.0, 'VPOS': 373.3333333333333, 'WIDTH': 29.333333333333258,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'costs,', 'HPOS': 1118.6666666666667, 'VPOS': 373.3333333333333, 'WIDTH': 66.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': "attorneys'", 'HPOS': 1199.3333333333333, 'VPOS': 373.3333333333333, 'WIDTH': 113.33333333333348,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'fees', 'HPOS': 1324.0, 'VPOS': 367.3333333333333, 'WIDTH': 39.333333333333485,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'or', 'HPOS': 1385.3333333333335, 'VPOS': 373.3333333333333, 'WIDTH': 16.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'expenses)', 'HPOS': 1418.0, 'VPOS': 373.3333333333333, 'WIDTH': 119.33333333333326,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'that', 'HPOS': 1554.0, 'VPOS': 368.0, 'WIDTH': 38.0, 'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 5
            },
        {'text': 'arise by reason of:', 'HPOS': 100, 'VPOS': 404, 'WIDTH': 222, 'HEIGHT': 21, 'END_HPOS': 322,
         'END_VPOS': 425, 'WORDS': [
            {'text': 'arise', 'HPOS': 100.66666666666667, 'VPOS': 404.6666666666667, 'WIDTH': 44.66666666666667,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'by', 'HPOS': 168.66666666666666, 'VPOS': 399.3333333333333, 'WIDTH': 14.666666666666686,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'reason', 'HPOS': 206.0, 'VPOS': 404.6666666666667, 'WIDTH': 70.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'of:', 'HPOS': 298.0, 'VPOS': 404.6666666666667, 'WIDTH': 24.66666666666663,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 6
         },
        {'text': 'Standard Exceptions:', 'HPOS': 100, 'VPOS': 462, 'WIDTH': 258, 'HEIGHT': 21, 'END_HPOS': 358,
         'END_VPOS': 483, 'WORDS': [
            {'text': 'Standard', 'HPOS': 100.66666666666667, 'VPOS': 462.6666666666667, 'WIDTH': 96.66666666666664,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Exceptions:', 'HPOS': 222.0, 'VPOS': 463.3333333333333, 'WIDTH': 136.0,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 7
         },
        {'text': '1. a. Rights or claims of parties in possession not shown by the public records.', 'HPOS': 157,
         'VPOS': 528, 'WIDTH': 971, 'HEIGHT': 21, 'END_HPOS': 1128, 'END_VPOS': 549, 'WORDS': [
            {'text': '1.', 'HPOS': 157.33333333333331, 'VPOS': 528.6666666666666, 'WIDTH': 14.666666666666686,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'a.', 'HPOS': 194.0, 'VPOS': 532.6666666666667, 'WIDTH': 17.333333333333343,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Rights', 'HPOS': 233.99999999999997, 'VPOS': 527.3333333333333, 'WIDTH': 64.00000000000003,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'or', 'HPOS': 319.3333333333333, 'VPOS': 532.6666666666667, 'WIDTH': 16.000000000000057,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'claims', 'HPOS': 352.0, 'VPOS': 532.6666666666667, 'WIDTH': 64.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'of', 'HPOS': 438.0, 'VPOS': 532.6666666666667, 'WIDTH': 14.666666666666686,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'parties', 'HPOS': 470.00000000000006, 'VPOS': 532.6666666666667, 'WIDTH': 68.66666666666657,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'in', 'HPOS': 560.6666666666667, 'VPOS': 527.3333333333333, 'WIDTH': 5.999999999999886,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'possession', 'HPOS': 590.0, 'VPOS': 532.6666666666667, 'WIDTH': 123.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'not', 'HPOS': 736.6666666666667, 'VPOS': 532.6666666666667, 'WIDTH': 29.333333333333258,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'shown', 'HPOS': 782.0, 'VPOS': 532.6666666666667, 'WIDTH': 66.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'by', 'HPOS': 871.3333333333334, 'VPOS': 527.3333333333333, 'WIDTH': 13.999999999999886,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'the', 'HPOS': 906.6666666666666, 'VPOS': 527.3333333333333, 'WIDTH': 24.000000000000114,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'public', 'HPOS': 954.0, 'VPOS': 532.6666666666667, 'WIDTH': 58.66666666666674,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'records.', 'HPOS': 1034.6666666666667, 'VPOS': 532.6666666666667, 'WIDTH': 93.33333333333326,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 8
         },
        {'text': 'b. Easements, or claims of easements, not shown by the public records.', 'HPOS': 194, 'VPOS': 591,
         'WIDTH': 886, 'HEIGHT': 21, 'END_HPOS': 1080, 'END_VPOS': 612, 'WORDS': [
            {'text': 'b.', 'HPOS': 194.66666666666669, 'VPOS': 591.3333333333334, 'WIDTH': 16.666666666666657,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Easements,', 'HPOS': 233.99999999999997, 'VPOS': 591.3333333333334, 'WIDTH': 139.33333333333334,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'or', 'HPOS': 387.3333333333333, 'VPOS': 596.6666666666666, 'WIDTH': 16.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'claims', 'HPOS': 419.33333333333337, 'VPOS': 596.6666666666666, 'WIDTH': 64.66666666666669,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'of', 'HPOS': 506.00000000000006, 'VPOS': 596.6666666666666, 'WIDTH': 15.3333333333332,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'easements,', 'HPOS': 537.3333333333334, 'VPOS': 596.6666666666666, 'WIDTH': 136.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'not', 'HPOS': 688.6666666666666, 'VPOS': 596.6666666666666, 'WIDTH': 30.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'shown', 'HPOS': 734.6666666666666, 'VPOS': 596.6666666666666, 'WIDTH': 65.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'by', 'HPOS': 823.3333333333334, 'VPOS': 591.3333333333334, 'WIDTH': 14.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'the', 'HPOS': 859.3333333333334, 'VPOS': 591.3333333333334, 'WIDTH': 24.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'public', 'HPOS': 906.6666666666666, 'VPOS': 596.6666666666666, 'WIDTH': 58.000000000000114,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'records.', 'HPOS': 986.6666666666667, 'VPOS': 596.6666666666666, 'WIDTH': 93.33333333333326,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 9
         },
        {'text': 'c. Any encroachment, encumbrance, violation, or adverse circumstance that would be disclosed by an',
         'HPOS': 194, 'VPOS': 660, 'WIDTH': 1243, 'HEIGHT': 21, 'END_HPOS': 1437, 'END_VPOS': 681, 'WORDS': [
            {'text': 'c.', 'HPOS': 194.0, 'VPOS': 660.0, 'WIDTH': 15.333333333333343, 'HEIGHT': 21.333333333333332
             },
            {'text': 'Any', 'HPOS': 228.66666666666669, 'VPOS': 654.6666666666666, 'WIDTH': 34.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'encroachment,', 'HPOS': 285.3333333333333, 'VPOS': 660.0, 'WIDTH': 177.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'encumbrance,', 'HPOS': 476.6666666666667, 'VPOS': 660.0, 'WIDTH': 169.99999999999994,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'violation,', 'HPOS': 660.0, 'VPOS': 660.0, 'WIDTH': 103.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'or', 'HPOS': 777.3333333333334, 'VPOS': 660.0, 'WIDTH': 16.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'adverse', 'HPOS': 810.6666666666666, 'VPOS': 660.0, 'WIDTH': 82.66666666666674,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'circumstance', 'HPOS': 916.6666666666666, 'VPOS': 660.0, 'WIDTH': 147.9999999999999,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'that', 'HPOS': 1087.3333333333335, 'VPOS': 654.6666666666666, 'WIDTH': 38.666666666666515,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'would', 'HPOS': 1142.0, 'VPOS': 660.0, 'WIDTH': 57.33333333333326, 'HEIGHT': 21.333333333333332
             },
            {'text': 'be', 'HPOS': 1222.6666666666667, 'VPOS': 654.6666666666666, 'WIDTH': 15.333333333333258,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'disclosed', 'HPOS': 1260.6666666666667, 'VPOS': 654.6666666666666, 'WIDTH': 100.66666666666652,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'by', 'HPOS': 1385.3333333333335, 'VPOS': 654.6666666666666, 'WIDTH': 13.999999999999773,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'an', 'HPOS': 1421.3333333333333, 'VPOS': 660.0, 'WIDTH': 16.0, 'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 10
         },
        {'text': 'accurate and complete land survey of the Land.', 'HPOS': 233, 'VPOS': 692, 'WIDTH': 578, 'HEIGHT': 21,
         'END_HPOS': 811, 'END_VPOS': 713, 'WORDS': [
            {'text': 'accurate', 'HPOS': 233.33333333333334, 'VPOS': 692.0, 'WIDTH': 90.66666666666666,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'and', 'HPOS': 346.6666666666667, 'VPOS': 692.0, 'WIDTH': 30.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'complete', 'HPOS': 401.3333333333333, 'VPOS': 692.0, 'WIDTH': 96.66666666666674,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'land', 'HPOS': 522.0, 'VPOS': 686.6666666666666, 'WIDTH': 36.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'survey', 'HPOS': 582.0, 'VPOS': 692.0, 'WIDTH': 66.66666666666674, 'HEIGHT': 21.333333333333332
             },
            {'text': 'of', 'HPOS': 671.3333333333334, 'VPOS': 692.0, 'WIDTH': 14.666666666666629,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'the', 'HPOS': 701.3333333333333, 'VPOS': 686.6666666666666, 'WIDTH': 24.000000000000114,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Land.', 'HPOS': 750.0, 'VPOS': 686.6666666666666, 'WIDTH': 61.33333333333326,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 11
         },
        {
            'text': 'd. Any lien, or right to a lien, for services, labor or material heretofore or hereafter '
                    'furnished, imposed',
            'HPOS': 194, 'VPOS': 750, 'WIDTH': 1229, 'HEIGHT': 21, 'END_HPOS': 1423, 'END_VPOS': 771, 'WORDS': [
            {'text': 'd.', 'HPOS': 194.0, 'VPOS': 750.6666666666667, 'WIDTH': 17.333333333333343,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Any', 'HPOS': 229.99999999999997, 'VPOS': 750.6666666666667, 'WIDTH': 34.666666666666714,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'lien,', 'HPOS': 287.3333333333333, 'VPOS': 750.6666666666667, 'WIDTH': 44.00000000000006,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'or', 'HPOS': 345.3333333333333, 'VPOS': 756.0, 'WIDTH': 16.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'right', 'HPOS': 378.6666666666667, 'VPOS': 756.0, 'WIDTH': 44.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'to', 'HPOS': 438.6666666666667, 'VPOS': 750.6666666666667, 'WIDTH': 8.666666666666629,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'a', 'HPOS': 470.00000000000006, 'VPOS': 756.0, 'WIDTH': 0.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'lien,', 'HPOS': 494.66666666666663, 'VPOS': 750.6666666666667, 'WIDTH': 44.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'for', 'HPOS': 552.0, 'VPOS': 750.0, 'WIDTH': 24.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'services,', 'HPOS': 592.6666666666666, 'VPOS': 756.0, 'WIDTH': 103.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'labor', 'HPOS': 710.6666666666666, 'VPOS': 750.6666666666667, 'WIDTH': 52.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'or', 'HPOS': 779.3333333333334, 'VPOS': 756.0, 'WIDTH': 16.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'material', 'HPOS': 812.6666666666666, 'VPOS': 756.0, 'WIDTH': 92.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'heretofore', 'HPOS': 918.6666666666667, 'VPOS': 750.6666666666667, 'WIDTH': 110.66666666666652,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'or', 'HPOS': 1052.6666666666667, 'VPOS': 756.0, 'WIDTH': 16.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'hereafter', 'HPOS': 1085.3333333333333, 'VPOS': 750.6666666666667, 'WIDTH': 102.66666666666674,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'furnished,', 'HPOS': 1203.3333333333335, 'VPOS': 750.0, 'WIDTH': 116.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'imposed', 'HPOS': 1334.6666666666667, 'VPOS': 750.6666666666667, 'WIDTH': 88.66666666666674,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 12
            },
        {'text': 'by law and not shown by the public records.', 'HPOS': 233, 'VPOS': 782, 'WIDTH': 532, 'HEIGHT': 21,
         'END_HPOS': 765, 'END_VPOS': 803, 'WORDS': [
            {'text': 'by', 'HPOS': 233.99999999999997, 'VPOS': 782.6666666666666, 'WIDTH': 14.000000000000028,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'law', 'HPOS': 270.6666666666667, 'VPOS': 782.6666666666666, 'WIDTH': 20.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'and', 'HPOS': 319.3333333333333, 'VPOS': 788.0, 'WIDTH': 30.666666666666686,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'not', 'HPOS': 374.0, 'VPOS': 788.0, 'WIDTH': 30.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'shown', 'HPOS': 420.0, 'VPOS': 788.0, 'WIDTH': 65.33333333333331, 'HEIGHT': 21.333333333333332
             },
            {'text': 'by', 'HPOS': 508.0, 'VPOS': 782.6666666666666, 'WIDTH': 14.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'the', 'HPOS': 544.0, 'VPOS': 782.6666666666666, 'WIDTH': 24.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'public', 'HPOS': 592.0, 'VPOS': 788.0, 'WIDTH': 57.33333333333337, 'HEIGHT': 21.333333333333332
             },
            {'text': 'records.', 'HPOS': 672.0, 'VPOS': 788.0, 'WIDTH': 93.33333333333337, 'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 13
         },
        {'text': 'e. Taxes or special assessments which are not shown as existing liens by the public records.',
         'HPOS': 194, 'VPOS': 852, 'WIDTH': 1137, 'HEIGHT': 21, 'END_HPOS': 1331, 'END_VPOS': 873, 'WORDS': [
            {'text': 'e.', 'HPOS': 194.0, 'VPOS': 852.0000000000001, 'WIDTH': 17.333333333333343,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Taxes', 'HPOS': 232.66666666666669, 'VPOS': 846.6666666666666, 'WIDTH': 58.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'or', 'HPOS': 312.6666666666667, 'VPOS': 852.0000000000001, 'WIDTH': 16.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'special', 'HPOS': 345.3333333333333, 'VPOS': 852.0000000000001, 'WIDTH': 80.66666666666674,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'assessments', 'HPOS': 439.3333333333333, 'VPOS': 852.0000000000001, 'WIDTH': 148.00000000000006,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'which', 'HPOS': 608.6666666666666, 'VPOS': 852.0000000000001, 'WIDTH': 56.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'are', 'HPOS': 687.3333333333333, 'VPOS': 852.0000000000001, 'WIDTH': 25.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'not', 'HPOS': 736.6666666666667, 'VPOS': 852.0000000000001, 'WIDTH': 29.333333333333258,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'shown', 'HPOS': 782.0, 'VPOS': 852.0000000000001, 'WIDTH': 65.33333333333326,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'as', 'HPOS': 869.3333333333333, 'VPOS': 852.0000000000001, 'WIDTH': 16.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'existing', 'HPOS': 907.3333333333333, 'VPOS': 852.0000000000001, 'WIDTH': 78.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'liens', 'HPOS': 1009.3333333333334, 'VPOS': 846.6666666666666, 'WIDTH': 42.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'by', 'HPOS': 1074.6666666666667, 'VPOS': 846.6666666666666, 'WIDTH': 14.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'the', 'HPOS': 1110.0, 'VPOS': 846.6666666666666, 'WIDTH': 24.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'public', 'HPOS': 1157.3333333333333, 'VPOS': 852.0000000000001, 'WIDTH': 58.66666666666674,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'records.', 'HPOS': 1238.0, 'VPOS': 852.0000000000001, 'WIDTH': 93.33333333333326,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 14
         },
        {'text': 'NOTE: Items 1a through e above are hereby deleted.', 'HPOS': 195, 'VPOS': 910, 'WIDTH': 655,
         'HEIGHT': 21, 'END_HPOS': 850, 'END_VPOS': 931, 'WORDS': [
            {'text': 'NOTE:', 'HPOS': 195.33333333333331, 'VPOS': 910.6666666666666, 'WIDTH': 77.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Items', 'HPOS': 295.33333333333337, 'VPOS': 910.6666666666666, 'WIDTH': 53.333333333333314,
             'HEIGHT': 21.333333333333332
             },
            {'text': '1a', 'HPOS': 372.0, 'VPOS': 910.6666666666666, 'WIDTH': 13.333333333333314,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'through', 'HPOS': 408.0, 'VPOS': 910.6666666666666, 'WIDTH': 80.66666666666669,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'e', 'HPOS': 510.6666666666667, 'VPOS': 915.3333333333334, 'WIDTH': 0.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'above', 'HPOS': 534.0, 'VPOS': 916.0, 'WIDTH': 60.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'are', 'HPOS': 616.6666666666666, 'VPOS': 916.0, 'WIDTH': 25.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'hereby', 'HPOS': 666.0, 'VPOS': 910.6666666666666, 'WIDTH': 69.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'deleted.', 'HPOS': 757.3333333333334, 'VPOS': 910.6666666666666, 'WIDTH': 92.66666666666663,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 15
         },
        {'text': 'Special Exceptions:', 'HPOS': 100, 'VPOS': 973, 'WIDTH': 236, 'HEIGHT': 21, 'END_HPOS': 336,
         'END_VPOS': 994, 'WORDS': [
            {'text': 'Special', 'HPOS': 100.66666666666667, 'VPOS': 973.3333333333333, 'WIDTH': 85.33333333333333,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Exceptions:', 'HPOS': 200.66666666666666, 'VPOS': 974.0000000000001, 'WIDTH': 135.99999999999997,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 16
         },
        {'text': '2. Taxes for the 2nd 1/2 2017 and subsequent years', 'HPOS': 155, 'VPOS': 1039, 'WIDTH': 635,
         'HEIGHT': 21, 'END_HPOS': 790, 'END_VPOS': 1060, 'WORDS': [
            {'text': '2.', 'HPOS': 155.33333333333334, 'VPOS': 1039.3333333333333, 'WIDTH': 16.666666666666657,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Taxes', 'HPOS': 194.0, 'VPOS': 1038.6666666666667, 'WIDTH': 58.66666666666666,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'for', 'HPOS': 274.0, 'VPOS': 1038.0, 'WIDTH': 24.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'the', 'HPOS': 314.0, 'VPOS': 1038.6666666666667, 'WIDTH': 23.333333333333314,
             'HEIGHT': 21.333333333333332
             },
            {'text': '2nd', 'HPOS': 361.3333333333333, 'VPOS': 1038.6666666666667, 'WIDTH': 30.666666666666686,
             'HEIGHT': 21.333333333333332
             },
            {'text': '1/2', 'HPOS': 417.3333333333333, 'VPOS': 1038.6666666666667, 'WIDTH': 20.666666666666686,
             'HEIGHT': 21.333333333333332
             },
            {'text': '2017', 'HPOS': 461.3333333333333, 'VPOS': 1038.6666666666667, 'WIDTH': 46.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'and', 'HPOS': 530.6666666666666, 'VPOS': 1044.0, 'WIDTH': 31.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'subsequent', 'HPOS': 584.6666666666666, 'VPOS': 1044.0, 'WIDTH': 135.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'years', 'HPOS': 735.3333333333334, 'VPOS': 1044.0, 'WIDTH': 55.33333333333326,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 17
         },
        {'text': 'none now deliquent', 'HPOS': 194, 'VPOS': 1075, 'WIDTH': 232, 'HEIGHT': 21, 'END_HPOS': 426,
         'END_VPOS': 1096, 'WORDS': [
            {'text': 'none', 'HPOS': 194.66666666666669, 'VPOS': 1075.3333333333333, 'WIDTH': 45.99999999999997,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'now', 'HPOS': 264.0, 'VPOS': 1075.3333333333333, 'WIDTH': 29.333333333333314,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'deliquent', 'HPOS': 322.66666666666663, 'VPOS': 1070.0, 'WIDTH': 104.00000000000006,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 18
         },
        {
            'text': '3. Taxes for the year 2018 and all subsequent years, and all special assessments not included in '
                    'general taxes.',
            'HPOS': 155, 'VPOS': 1194, 'WIDTH': 1370, 'HEIGHT': 21, 'END_HPOS': 1525, 'END_VPOS': 1215, 'WORDS': [
            {'text': '3.', 'HPOS': 155.33333333333334, 'VPOS': 1194.6666666666667, 'WIDTH': 16.666666666666657,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Taxes', 'HPOS': 194.0, 'VPOS': 1194.0, 'WIDTH': 58.66666666666666, 'HEIGHT': 21.333333333333332
             },
            {'text': 'for', 'HPOS': 274.0, 'VPOS': 1193.3333333333333, 'WIDTH': 24.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'the', 'HPOS': 314.0, 'VPOS': 1194.0, 'WIDTH': 23.333333333333314, 'HEIGHT': 21.333333333333332
             },
            {'text': 'year', 'HPOS': 360.0, 'VPOS': 1199.3333333333333, 'WIDTH': 46.666666666666686,
             'HEIGHT': 21.333333333333332
             },
            {'text': '2018', 'HPOS': 422.6666666666667, 'VPOS': 1194.0, 'WIDTH': 46.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'and', 'HPOS': 492.0, 'VPOS': 1199.3333333333333, 'WIDTH': 30.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'all', 'HPOS': 546.0, 'VPOS': 1199.3333333333333, 'WIDTH': 22.666666666666742,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'subsequent', 'HPOS': 582.0, 'VPOS': 1199.3333333333333, 'WIDTH': 135.33333333333326,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'years,', 'HPOS': 732.6666666666666, 'VPOS': 1199.3333333333333, 'WIDTH': 70.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'and', 'HPOS': 816.6666666666667, 'VPOS': 1199.3333333333333, 'WIDTH': 30.666666666666515,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'all', 'HPOS': 869.9999999999999, 'VPOS': 1199.3333333333333, 'WIDTH': 23.333333333333485,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'special', 'HPOS': 906.0, 'VPOS': 1199.3333333333333, 'WIDTH': 80.66666666666674,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'assessments', 'HPOS': 1000.0, 'VPOS': 1199.3333333333333, 'WIDTH': 148.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'not', 'HPOS': 1170.6666666666665, 'VPOS': 1199.3333333333333, 'WIDTH': 30.000000000000227,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'included', 'HPOS': 1217.3333333333333, 'VPOS': 1194.0, 'WIDTH': 86.66666666666674,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'in', 'HPOS': 1328.0, 'VPOS': 1194.0, 'WIDTH': 6.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'general', 'HPOS': 1356.6666666666667, 'VPOS': 1199.3333333333333, 'WIDTH': 87.33333333333326,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'taxes.', 'HPOS': 1456.6666666666665, 'VPOS': 1194.0, 'WIDTH': 68.66666666666674,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 19
            },
        {'text': '4. Future installments levied by City of Merriam.', 'HPOS': 154, 'VPOS': 1258, 'WIDTH': 589,
         'HEIGHT': 21, 'END_HPOS': 743, 'END_VPOS': 1279, 'WORDS': [
            {'text': '4.', 'HPOS': 154.66666666666666, 'VPOS': 1258.6666666666665, 'WIDTH': 17.333333333333343,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Future', 'HPOS': 195.33333333333331, 'VPOS': 1257.3333333333333, 'WIDTH': 63.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'installments', 'HPOS': 282.66666666666663, 'VPOS': 1257.3333333333333,
             'WIDTH': 132.00000000000006, 'HEIGHT': 21.333333333333332
             },
            {'text': 'levied', 'HPOS': 437.33333333333337, 'VPOS': 1257.3333333333333, 'WIDTH': 56.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'by', 'HPOS': 518.0, 'VPOS': 1257.3333333333333, 'WIDTH': 14.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'City', 'HPOS': 554.6666666666667, 'VPOS': 1256.6666666666667, 'WIDTH': 32.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'of', 'HPOS': 609.3333333333334, 'VPOS': 1262.6666666666667, 'WIDTH': 14.666666666666629,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Merriam.', 'HPOS': 642.0, 'VPOS': 1257.3333333333333, 'WIDTH': 101.33333333333337,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 20
         },
        {
            'text': '5. Building setback lines, utility easements, easements, rights-of-way or servitudes appearing '
                    'in the public records.',
            'HPOS': 155, 'VPOS': 1323, 'WIDTH': 1409, 'HEIGHT': 21, 'END_HPOS': 1564, 'END_VPOS': 1344, 'WORDS': [
            {'text': '5.', 'HPOS': 155.33333333333334, 'VPOS': 1323.3333333333333, 'WIDTH': 16.666666666666657,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Building', 'HPOS': 195.33333333333331, 'VPOS': 1321.3333333333333, 'WIDTH': 82.00000000000006,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'setback', 'HPOS': 300.66666666666663, 'VPOS': 1326.6666666666667, 'WIDTH': 82.66666666666669,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'lines,', 'HPOS': 404.6666666666667, 'VPOS': 1321.3333333333333, 'WIDTH': 58.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'utility', 'HPOS': 477.3333333333333, 'VPOS': 1326.6666666666667, 'WIDTH': 48.666666666666686,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'easements,', 'HPOS': 548.0, 'VPOS': 1326.6666666666667, 'WIDTH': 137.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'easements,', 'HPOS': 699.3333333333334, 'VPOS': 1326.6666666666667, 'WIDTH': 137.33333333333326,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'rights-of-way', 'HPOS': 851.3333333333333, 'VPOS': 1326.6666666666667,
             'WIDTH': 143.33333333333337, 'HEIGHT': 21.333333333333332
             },
            {'text': 'or', 'HPOS': 1017.3333333333333, 'VPOS': 1326.6666666666667, 'WIDTH': 16.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'servitudes', 'HPOS': 1049.3333333333335, 'VPOS': 1326.6666666666667, 'WIDTH': 113.33333333333326,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'appearing', 'HPOS': 1184.0, 'VPOS': 1326.6666666666667, 'WIDTH': 108.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'in', 'HPOS': 1316.0, 'VPOS': 1321.3333333333333, 'WIDTH': 6.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'the', 'HPOS': 1344.0, 'VPOS': 1321.3333333333333, 'WIDTH': 23.333333333333258,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'public', 'HPOS': 1391.3333333333335, 'VPOS': 1326.6666666666667, 'WIDTH': 58.666666666666515,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'records.', 'HPOS': 1472.0, 'VPOS': 1326.6666666666667, 'WIDTH': 92.66666666666674,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 21
            },
        {
            'text': '6. Terms, provisions, and assessments, if any, under any instruments creating covenants, '
                    'conditions, restrictions, or',
            'HPOS': 155, 'VPOS': 1386, 'WIDTH': 1437, 'HEIGHT': 21, 'END_HPOS': 1592, 'END_VPOS': 1407, 'WORDS': [
            {'text': '6.', 'HPOS': 155.33333333333334, 'VPOS': 1386.6666666666667, 'WIDTH': 16.666666666666657,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Terms,', 'HPOS': 194.0, 'VPOS': 1385.3333333333335, 'WIDTH': 77.33333333333331,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'provisions,', 'HPOS': 287.3333333333333, 'VPOS': 1390.6666666666665, 'WIDTH': 125.33333333333331,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'and', 'HPOS': 428.66666666666663, 'VPOS': 1390.6666666666665, 'WIDTH': 31.333333333333314,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'assessments,', 'HPOS': 484.00000000000006, 'VPOS': 1390.6666666666665,
             'WIDTH': 163.99999999999994, 'HEIGHT': 21.333333333333332
             },
            {'text': 'if', 'HPOS': 664.0, 'VPOS': 1385.3333333333335, 'WIDTH': 4.666666666666629,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'any,', 'HPOS': 686.0, 'VPOS': 1390.6666666666665, 'WIDTH': 44.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'under', 'HPOS': 746.6666666666666, 'VPOS': 1390.6666666666665, 'WIDTH': 61.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'any', 'HPOS': 826.0, 'VPOS': 1390.6666666666665, 'WIDTH': 30.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'instruments', 'HPOS': 880.6666666666667, 'VPOS': 1385.3333333333335, 'WIDTH': 128.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'creating', 'HPOS': 1032.6666666666665, 'VPOS': 1390.6666666666665, 'WIDTH': 83.33333333333348,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'covenants,', 'HPOS': 1140.6666666666667, 'VPOS': 1390.6666666666665, 'WIDTH': 127.99999999999977,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'conditions,', 'HPOS': 1284.6666666666667, 'VPOS': 1390.6666666666665, 'WIDTH': 126.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'restrictions,', 'HPOS': 1426.6666666666667, 'VPOS': 1390.6666666666665, 'WIDTH': 134.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'or', 'HPOS': 1576.0, 'VPOS': 1390.6666666666665, 'WIDTH': 16.0, 'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 22
            },
        {'text': 'homes association appearing in the public records.', 'HPOS': 194, 'VPOS': 1417, 'WIDTH': 619,
         'HEIGHT': 21, 'END_HPOS': 813, 'END_VPOS': 1438, 'WORDS': [
            {'text': 'homes', 'HPOS': 194.66666666666669, 'VPOS': 1417.3333333333333, 'WIDTH': 68.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'association', 'HPOS': 285.3333333333333, 'VPOS': 1422.6666666666667, 'WIDTH': 124.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'appearing', 'HPOS': 431.99999999999994, 'VPOS': 1422.6666666666667, 'WIDTH': 108.00000000000006,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'in', 'HPOS': 564.0, 'VPOS': 1417.3333333333333, 'WIDTH': 6.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'the', 'HPOS': 592.0, 'VPOS': 1417.3333333333333, 'WIDTH': 23.333333333333258,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'public', 'HPOS': 639.3333333333334, 'VPOS': 1422.6666666666667, 'WIDTH': 58.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'records.', 'HPOS': 720.0, 'VPOS': 1422.6666666666667, 'WIDTH': 93.33333333333337,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 23
         },
        {
            'text': 'NOTE: This document omits from any exception any covenant, condition or restrictions based on '
                    'race, color,',
            'HPOS': 195, 'VPOS': 1480, 'WIDTH': 1399, 'HEIGHT': 20, 'END_HPOS': 1594, 'END_VPOS': 1500, 'WORDS': [
            {'text': 'NOTE:', 'HPOS': 195.33333333333331, 'VPOS': 1480.6666666666667, 'WIDTH': 77.33333333333337,
             'HEIGHT': 20.666666666666668
             },
            {'text': 'This', 'HPOS': 302.6666666666667, 'VPOS': 1480.6666666666667, 'WIDTH': 38.66666666666663,
             'HEIGHT': 20.666666666666668
             },
            {'text': 'document', 'HPOS': 366.6666666666667, 'VPOS': 1480.6666666666667, 'WIDTH': 113.99999999999994,
             'HEIGHT': 20.666666666666668
             },
            {'text': 'omits', 'HPOS': 500.66666666666663, 'VPOS': 1486.0, 'WIDTH': 52.66666666666674,
             'HEIGHT': 20.666666666666668
             },
            {'text': 'from', 'HPOS': 578.6666666666666, 'VPOS': 1480.0, 'WIDTH': 33.33333333333337,
             'HEIGHT': 20.666666666666668
             },
            {'text': 'any', 'HPOS': 646.6666666666666, 'VPOS': 1486.0, 'WIDTH': 30.666666666666742,
             'HEIGHT': 20.666666666666668
             },
            {'text': 'exception', 'HPOS': 703.3333333333333, 'VPOS': 1486.0, 'WIDTH': 104.00000000000011,
             'HEIGHT': 20.666666666666668
             },
            {'text': 'any', 'HPOS': 834.0, 'VPOS': 1486.0, 'WIDTH': 30.666666666666742, 'HEIGHT': 20.666666666666668
             },
            {'text': 'covenant,', 'HPOS': 891.3333333333333, 'VPOS': 1486.0, 'WIDTH': 113.33333333333337,
             'HEIGHT': 20.666666666666668
             },
            {'text': 'condition', 'HPOS': 1023.3333333333333, 'VPOS': 1486.0, 'WIDTH': 96.0,
             'HEIGHT': 20.666666666666668
             },
            {'text': 'or', 'HPOS': 1146.6666666666667, 'VPOS': 1486.0, 'WIDTH': 16.0, 'HEIGHT': 20.666666666666668
             },
            {'text': 'restrictions', 'HPOS': 1184.0, 'VPOS': 1486.0, 'WIDTH': 119.33333333333348,
             'HEIGHT': 20.666666666666668
             },
            {'text': 'based', 'HPOS': 1330.0, 'VPOS': 1480.6666666666667, 'WIDTH': 59.33333333333326,
             'HEIGHT': 20.666666666666668
             },
            {'text': 'on', 'HPOS': 1417.3333333333333, 'VPOS': 1486.0, 'WIDTH': 16.0, 'HEIGHT': 20.666666666666668
             },
            {'text': 'race,', 'HPOS': 1460.6666666666667, 'VPOS': 1486.0, 'WIDTH': 55.33333333333326,
             'HEIGHT': 20.666666666666668
             },
            {'text': 'color,', 'HPOS': 1534.0, 'VPOS': 1486.0, 'WIDTH': 60.0, 'HEIGHT': 20.666666666666668
             }
        ], 'LINE_NUMBER': 24
            },
        {
            'text': 'religion, sex, handicap, familial status or national origin as provided in 42 U.S.C. 3604, '
                    'unless and only to the',
            'HPOS': 194, 'VPOS': 1517, 'WIDTH': 1391, 'HEIGHT': 21, 'END_HPOS': 1585, 'END_VPOS': 1538, 'WORDS': [
            {'text': 'religion,', 'HPOS': 194.66666666666669, 'VPOS': 1518.0, 'WIDTH': 90.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'sex,', 'HPOS': 302.6666666666667, 'VPOS': 1518.0, 'WIDTH': 44.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'handicap,', 'HPOS': 364.6666666666667, 'VPOS': 1512.6666666666665, 'WIDTH': 113.33333333333331,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'familial', 'HPOS': 494.66666666666663, 'VPOS': 1512.0, 'WIDTH': 81.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'status', 'HPOS': 592.6666666666666, 'VPOS': 1518.0, 'WIDTH': 60.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'or', 'HPOS': 677.3333333333334, 'VPOS': 1518.0, 'WIDTH': 16.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'national', 'HPOS': 714.0, 'VPOS': 1518.0, 'WIDTH': 90.66666666666663, 'HEIGHT': 21.333333333333332
             },
            {'text': 'origin', 'HPOS': 821.3333333333334, 'VPOS': 1518.0, 'WIDTH': 52.66666666666674,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'as', 'HPOS': 900.0, 'VPOS': 1518.0, 'WIDTH': 15.333333333333371, 'HEIGHT': 21.333333333333332
             },
            {'text': 'provided', 'HPOS': 940.6666666666666, 'VPOS': 1518.0, 'WIDTH': 90.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'in', 'HPOS': 1058.0, 'VPOS': 1512.6666666666665, 'WIDTH': 6.0, 'HEIGHT': 21.333333333333332
             },
            {'text': '42', 'HPOS': 1089.3333333333333, 'VPOS': 1512.6666666666665, 'WIDTH': 16.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'U.S.C.', 'HPOS': 1133.3333333333333, 'VPOS': 1512.6666666666665, 'WIDTH': 73.33333333333348,
             'HEIGHT': 21.333333333333332
             },
            {'text': '3604,', 'HPOS': 1224.6666666666665, 'VPOS': 1512.0, 'WIDTH': 78.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'unless', 'HPOS': 1320.6666666666667, 'VPOS': 1518.0, 'WIDTH': 65.33333333333326,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'and', 'HPOS': 1411.3333333333333, 'VPOS': 1518.0, 'WIDTH': 28.666666666666742,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'only', 'HPOS': 1466.6666666666667, 'VPOS': 1518.0, 'WIDTH': 36.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'to', 'HPOS': 1527.3333333333333, 'VPOS': 1512.6666666666665, 'WIDTH': 8.666666666666742,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'the', 'HPOS': 1561.3333333333335, 'VPOS': 1512.6666666666665, 'WIDTH': 23.999999999999773,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 25
            },
        {
            'text': 'extent that the covenant (a) is not in violation of state or federal law, (b) is exempt under 42 '
                    'U.S.C. 3607, or (c)',
            'HPOS': 194, 'VPOS': 1550, 'WIDTH': 1398, 'HEIGHT': 21, 'END_HPOS': 1592, 'END_VPOS': 1571, 'WORDS': [
            {'text': 'extent', 'HPOS': 194.0, 'VPOS': 1550.0, 'WIDTH': 67.33333333333331, 'HEIGHT': 21.333333333333332
             },
            {'text': 'that', 'HPOS': 278.66666666666663, 'VPOS': 1544.6666666666665, 'WIDTH': 38.66666666666674,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'the', 'HPOS': 334.6666666666667, 'VPOS': 1544.6666666666665, 'WIDTH': 23.999999999999943,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'covenant', 'HPOS': 383.3333333333333, 'VPOS': 1550.0, 'WIDTH': 104.00000000000006,
             'HEIGHT': 21.333333333333332
             },
            {'text': '(a)', 'HPOS': 506.00000000000006, 'VPOS': 1544.0, 'WIDTH': 24.666666666666572,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'is', 'HPOS': 549.3333333333333, 'VPOS': 1544.6666666666665, 'WIDTH': 6.000000000000114,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'not', 'HPOS': 578.6666666666666, 'VPOS': 1550.0, 'WIDTH': 30.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'in', 'HPOS': 627.3333333333334, 'VPOS': 1544.6666666666665, 'WIDTH': 6.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'violation', 'HPOS': 656.6666666666667, 'VPOS': 1550.0, 'WIDTH': 87.99999999999989,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'of', 'HPOS': 768.6666666666666, 'VPOS': 1550.0, 'WIDTH': 14.666666666666629,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'state', 'HPOS': 801.3333333333334, 'VPOS': 1550.0, 'WIDTH': 44.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'or', 'HPOS': 871.3333333333334, 'VPOS': 1550.0, 'WIDTH': 16.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'federal', 'HPOS': 904.6666666666666, 'VPOS': 1544.0, 'WIDTH': 79.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'law,', 'HPOS': 1000.0, 'VPOS': 1544.6666666666665, 'WIDTH': 41.33333333333326,
             'HEIGHT': 21.333333333333332
             },
            {'text': '(b)', 'HPOS': 1057.3333333333333, 'VPOS': 1544.0, 'WIDTH': 24.666666666666742,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'is', 'HPOS': 1101.3333333333333, 'VPOS': 1544.6666666666665, 'WIDTH': 5.333333333333485,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'exempt', 'HPOS': 1130.0, 'VPOS': 1550.0, 'WIDTH': 82.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'under', 'HPOS': 1231.3333333333335, 'VPOS': 1550.0, 'WIDTH': 61.33333333333326,
             'HEIGHT': 21.333333333333332
             },
            {'text': '42', 'HPOS': 1310.0, 'VPOS': 1544.6666666666665, 'WIDTH': 16.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'U.S.C.', 'HPOS': 1352.6666666666665, 'VPOS': 1544.6666666666665, 'WIDTH': 71.33333333333348,
             'HEIGHT': 21.333333333333332
             },
            {'text': '3607,', 'HPOS': 1440.0, 'VPOS': 1544.0, 'WIDTH': 78.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'or', 'HPOS': 1534.6666666666665, 'VPOS': 1550.0, 'WIDTH': 16.0, 'HEIGHT': 21.333333333333332
             },
            {'text': '(c)', 'HPOS': 1569.3333333333333, 'VPOS': 1544.0, 'WIDTH': 22.666666666666742,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 26
            },
        {'text': 'relates to a handicap, but does not discriminate against handicapped people.', 'HPOS': 194,
         'VPOS': 1582, 'WIDTH': 938, 'HEIGHT': 21, 'END_HPOS': 1132, 'END_VPOS': 1603, 'WORDS': [
            {'text': 'relates', 'HPOS': 194.66666666666669, 'VPOS': 1582.0, 'WIDTH': 68.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'to', 'HPOS': 284.6666666666667, 'VPOS': 1576.6666666666667, 'WIDTH': 8.666666666666629,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'a', 'HPOS': 316.6666666666667, 'VPOS': 1582.0, 'WIDTH': 0.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'handicap,', 'HPOS': 340.0, 'VPOS': 1576.6666666666667, 'WIDTH': 113.99999999999994,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'but', 'HPOS': 468.6666666666667, 'VPOS': 1576.6666666666667, 'WIDTH': 29.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'does', 'HPOS': 514.0, 'VPOS': 1576.6666666666667, 'WIDTH': 46.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'not', 'HPOS': 582.0, 'VPOS': 1582.0, 'WIDTH': 30.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'discriminate', 'HPOS': 628.0, 'VPOS': 1576.6666666666667, 'WIDTH': 132.66666666666663,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'against', 'HPOS': 784.0, 'VPOS': 1582.0, 'WIDTH': 81.33333333333337, 'HEIGHT': 21.333333333333332
             },
            {'text': 'handicapped', 'HPOS': 882.0, 'VPOS': 1576.6666666666667, 'WIDTH': 142.66666666666674,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'people.', 'HPOS': 1048.6666666666665, 'VPOS': 1582.0, 'WIDTH': 84.00000000000023,
             'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 27
         },
        {
            'text': '7. Minerals of whatsoever kind, subsurface and surface substances, including but not limited to '
                    'coal, lignite, oil, gas,',
            'HPOS': 155, 'VPOS': 1642, 'WIDTH': 1439, 'HEIGHT': 21, 'END_HPOS': 1594, 'END_VPOS': 1663, 'WORDS': [
            {'text': '7.', 'HPOS': 155.33333333333334, 'VPOS': 1642.6666666666667, 'WIDTH': 16.666666666666657,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'Minerals', 'HPOS': 195.33333333333331, 'VPOS': 1640.6666666666667, 'WIDTH': 90.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'of', 'HPOS': 308.0, 'VPOS': 1646.0, 'WIDTH': 14.666666666666629, 'HEIGHT': 21.333333333333332
             },
            {'text': 'whatsoever', 'HPOS': 338.6666666666667, 'VPOS': 1646.0, 'WIDTH': 134.66666666666669,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'kind,', 'HPOS': 490.6666666666667, 'VPOS': 1640.6666666666667, 'WIDTH': 51.99999999999994,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'subsurface', 'HPOS': 557.3333333333333, 'VPOS': 1646.0, 'WIDTH': 106.66666666666674,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'and', 'HPOS': 702.0, 'VPOS': 1646.0, 'WIDTH': 31.33333333333337, 'HEIGHT': 21.333333333333332
             },
            {'text': 'surface', 'HPOS': 756.6666666666666, 'VPOS': 1646.0, 'WIDTH': 62.0, 'HEIGHT': 21.333333333333332
             },
            {'text': 'substances,', 'HPOS': 856.6666666666666, 'VPOS': 1646.0, 'WIDTH': 141.33333333333337,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'including', 'HPOS': 1014.0, 'VPOS': 1640.6666666666667, 'WIDTH': 93.33333333333326,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'but', 'HPOS': 1132.6666666666667, 'VPOS': 1640.6666666666667, 'WIDTH': 29.333333333333258,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'not', 'HPOS': 1180.0, 'VPOS': 1646.0, 'WIDTH': 29.333333333333485, 'HEIGHT': 21.333333333333332
             },
            {'text': 'limited', 'HPOS': 1226.6666666666667, 'VPOS': 1640.6666666666667, 'WIDTH': 63.99999999999977,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'to', 'HPOS': 1314.6666666666667, 'VPOS': 1640.6666666666667, 'WIDTH': 8.0,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'coal,', 'HPOS': 1346.6666666666665, 'VPOS': 1646.0, 'WIDTH': 52.00000000000023,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'lignite,', 'HPOS': 1415.3333333333333, 'VPOS': 1640.6666666666667, 'WIDTH': 73.33333333333348,
             'HEIGHT': 21.333333333333332
             },
            {'text': 'oil,', 'HPOS': 1504.0, 'VPOS': 1646.0, 'WIDTH': 28.666666666666742, 'HEIGHT': 21.333333333333332
             },
            {'text': 'gas,', 'HPOS': 1548.0, 'VPOS': 1646.0, 'WIDTH': 46.0, 'HEIGHT': 21.333333333333332
             }
        ], 'LINE_NUMBER': 28
            },
        {
            'text': 'uranium, clay, rock, sand and gravel in, on, under and that may be produced from the Land, '
                    'together with all',
            'HPOS': 194, 'VPOS': 1678, 'WIDTH': 1400, 'HEIGHT': 15, 'END_HPOS': 1594, 'END_VPOS': 1693, 'WORDS': [
            {'text': 'uranium,', 'HPOS': 194.66666666666669, 'VPOS': 1678.0, 'WIDTH': 100.66666666666669,
             'HEIGHT': 15.333333333333334
             },
            {'text': 'clay,', 'HPOS': 314.0, 'VPOS': 1678.0, 'WIDTH': 49.333333333333314, 'HEIGHT': 15.333333333333334
             },
            {'text': 'rock,', 'HPOS': 382.0, 'VPOS': 1678.0, 'WIDTH': 53.33333333333337, 'HEIGHT': 15.333333333333334
             },
            {'text': 'sand', 'HPOS': 453.3333333333333, 'VPOS': 1678.0, 'WIDTH': 44.66666666666674,
             'HEIGHT': 15.333333333333334
             },
            {'text': 'and', 'HPOS': 526.0, 'VPOS': 1678.0, 'WIDTH': 30.66666666666663, 'HEIGHT': 15.333333333333334
             },
            {'text': 'gravel', 'HPOS': 584.0, 'VPOS': 1678.0, 'WIDTH': 70.0, 'HEIGHT': 15.333333333333334
             },
            {'text': 'in,', 'HPOS': 672.0, 'VPOS': 1672.6666666666665, 'WIDTH': 22.66666666666663,
             'HEIGHT': 15.333333333333334
             },
            {'text': 'on,', 'HPOS': 712.6666666666666, 'VPOS': 1678.0, 'WIDTH': 32.66666666666663,
             'HEIGHT': 15.333333333333334
             },
            {'text': 'under', 'HPOS': 764.0, 'VPOS': 1678.0, 'WIDTH': 62.0, 'HEIGHT': 15.333333333333334
             },
            {'text': 'and', 'HPOS': 846.6666666666666, 'VPOS': 1678.0, 'WIDTH': 30.666666666666742,
             'HEIGHT': 15.333333333333334
             },
            {'text': 'that', 'HPOS': 904.0, 'VPOS': 1672.6666666666665, 'WIDTH': 38.66666666666674,
             'HEIGHT': 15.333333333333334
             },
            {'text': 'may', 'HPOS': 963.3333333333333, 'VPOS': 1678.0, 'WIDTH': 37.33333333333337,
             'HEIGHT': 15.333333333333334
             },
            {'text': 'be', 'HPOS': 1028.0, 'VPOS': 1672.6666666666665, 'WIDTH': 14.666666666666515,
             'HEIGHT': 15.333333333333334
             },
            {'text': 'produced', 'HPOS': 1070.6666666666665, 'VPOS': 1678.0, 'WIDTH': 99.33333333333348,
             'HEIGHT': 15.333333333333334
             },
            {'text': 'from', 'HPOS': 1197.3333333333335, 'VPOS': 1672.0, 'WIDTH': 34.0, 'HEIGHT': 15.333333333333334
             },
            {'text': 'the', 'HPOS': 1264.6666666666665, 'VPOS': 1672.6666666666665, 'WIDTH': 24.000000000000227,
             'HEIGHT': 15.333333333333334
             },
            {'text': 'Land,', 'HPOS': 1317.3333333333333, 'VPOS': 1672.6666666666665, 'WIDTH': 62.00000000000023,
             'HEIGHT': 15.333333333333334
             },
            {'text': 'together', 'HPOS': 1396.6666666666665, 'VPOS': 1672.6666666666665, 'WIDTH': 94.0,
             'HEIGHT': 15.333333333333334
             },
            {'text': 'with', 'HPOS': 1510.6666666666667, 'VPOS': 1678.0, 'WIDTH': 35.33333333333326,
             'HEIGHT': 15.333333333333334
             },
            {'text': 'all', 'HPOS': 1572.6666666666665, 'VPOS': 1678.0, 'WIDTH': 22.0, 'HEIGHT': 15.333333333333334
             }
        ], 'LINE_NUMBER': 29
            }
    ]
    regex_list = [
        r"(?i)\bAny\s+subject\s+property\s+specific\s+exceptions:?\b",
        r"(?i)\bStandard\s+Exceptions[:?]?\b",
        r"(?i)\bSpecial\s+Exceptions:?\b",
        r"\bTaxes:",
        r"\bExceptions:",
    ]
    post_processor = ExceptionTextProcessor()
    output = post_processor(elements=elements, regex=regex_list)
    print(output)
