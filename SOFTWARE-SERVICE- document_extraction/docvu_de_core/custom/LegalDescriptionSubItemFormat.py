from docvu_de_core.io import OutputFormat

class LegalDescriptionSubItemFormat:
    def __init__(self, **kwargs):
        pass

    def __call__(self, sub_item, parent_item, elements=None, **kwargs):
        try:
            value = parent_item['rows'][0]['columns'][0]['value'].strip()
            value = value[:-3].strip() if value.endswith("and") else value
            bbox = parent_item['rows'][0]['columns'][0]['field_value_coordinates']
            page_number = parent_item['rows'][0]['columns'][0]['page_number']
            line_number = parent_item['rows'][0]['columns'][0]['line_number']

            if len(parent_item['rows']) > 1:
                row2_value = parent_item['rows'][1]['columns'][0]['value'].strip()
                row2_value = row2_value[:-3].strip() if row2_value.endswith("and") else row2_value

                if row2_value.lower().startswith("For Company Reference Purposes Only".lower()):
                    value += " " + row2_value

        except:
            value = ''
            bbox = {}
            page_number = 0
            line_number = 0

        sub_item['value'] = value
        sub_item['bbox'] = bbox
        sub_item['page_number'] = page_number
        sub_item['line_number'] = line_number

        return OutputFormat(sub_item=sub_item,
                            success=True)