#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""

from docvu_de_core.io import OutputFormat


class ScheduleAOutputFormat:
    def __init__(self, **kwargs):
        pass

    def __call__(self, processed_output, extraction_items=None, de_start_page=0, **kwargs):
        """
        Processes the given output based on the page number and probable page.

        :param processed_output: Dictionary containing 'value' and 'page_number'.
        :param extraction_items: Not used in this implementation.

        :return: OutputFormat with processed text and related information.
        """
        success = False
        exception_start_page = None
        curr_page_number = processed_output.get('page_number', 0)

        # Find exception_start_page from extraction_items
        for ei in extraction_items:
            if ei['name'] == 'Exception_Header':
                exception_start_page = ei.get_page_numbers()
                if not exception_start_page:
                    exception_start_page = None
                break

        # Ensure exception_start_page is provided
        if exception_start_page is None:
            return OutputFormat(item=processed_output, success=success, multi_item=False)

        # Use min(exception_start_page) if it's a list and compare it with de_start_page
        if isinstance(exception_start_page, list) and min(exception_start_page) < de_start_page:
            exception_start_page = []  # Empty the list if condition is met

        # Check if the current page number is within the exception_start_page range
        if isinstance(exception_start_page, list) and len(exception_start_page) > 0:
            if curr_page_number >= min(exception_start_page):  # Compare using the minimum value
                # Set the value to None if the page number exceeds the probable page
                processed_output['value'] = ''
                processed_output['page_number'] = ''
                processed_output['post_processing_value'] = ''
                processed_output.update_coordinates([0, 0, 0, 0])
                success = True
            else:
                success = False
        else:
            success = False

        return OutputFormat(item=processed_output, success=success, multi_item=False)


# Example usage:
if __name__ == "__main__":
    processed_output = {
        'value': "Loan information",
        'page_number': 5
    }

    probable_page = 3

    loan_recording_info = ScheduleAOutputFormat()
    result = loan_recording_info(processed_output, probable_page=probable_page)

    print("Result:")
    print(result.item['value'])  # Should print None since page_number > probable_page