import copy
from docvu_de_core.extraction_item import TableRow, BaseTableData, BaseFieldData
from docvu_de_core.utils import GeneralUtils
from docvu_de_core.modules import SplitByBullets

class ExceptionTableProcessor:
    def __init__(self, **kwargs):
        self.gu = GeneralUtils()

    def remove_up_to_substring_case_insensitive(self, main_string, substring):
        # Convert both strings to lowercase for case-insensitive comparison
        lower_main_string = main_string.lower()
        lower_substring = substring.lower()

        # Find the start index of the substring in the lowercase main string
        start_index = lower_main_string.find(lower_substring)

        # If the substring is not found, return the original string
        if start_index == -1:
            return main_string

        # Calculate the index up to which the string should be removed
        remove_index = start_index

        # Return the sliced string from the original main string
        return main_string[remove_index:]


    def __call__(self, type_item, text_item, header_item, output=None, exception_table=None,
                 is_first_epage=True, *kwargs):
        if exception_table is None:
            exception_table = BaseTableData(**header_item)
        header_item['value'] = self.remove_up_to_substring_case_insensitive(header_item['value'], "schedule")

        if output is not None:
            if output.value is not None:
                is_first_point = True
                for idx, v in enumerate(output.value):
                    if isinstance(v, list):
                        type_value = v[1]
                        text_value = v[0]
                    else:
                        type_value = ''
                        text_value = v
                    header_value = header_item['value']

                    if len(output.bbox[idx]) == 2:
                        type_bbox = self.gu.get_bbox(output.bbox[idx][1])
                        text_bbox = self.gu.get_bbox(output.bbox[idx][0])
                    else:
                        type_bbox = self.gu.get_bbox([0, 0, 0, 0])
                        text_bbox = self.gu.get_bbox(output.bbox, idx)
                    header_bbox = header_item['field_value_coordinates']

                    if isinstance(output.line_number[idx], list):
                        type_line_number = output.line_number[idx][1]
                        text_line_number = output.line_number[idx][0]
                    else:
                        type_line_number = 0
                        text_line_number = output.line_number[idx]
                    header_line_number = header_item['line_number']

                    type_page_number = header_item['page_number']
                    text_page_number = header_item['page_number']
                    header_page_number = header_item['page_number']

                    type_row_item = BaseFieldData(**type_item)
                    type_row_item['value'] = type_value
                    type_row_item['post_processing_value'] = type_value
                    type_row_item['field_value_coordinates'] = type_bbox
                    type_row_item['page_number'] = type_page_number
                    type_row_item['line_number'] = type_line_number

                    text_row_item = BaseFieldData(**text_item)
                    text_row_item['value'] = text_value
                    text_row_item['post_processing_value'] = text_value
                    text_row_item['field_value_coordinates'] = text_bbox
                    text_row_item['page_number'] = text_page_number
                    text_row_item['line_number'] = text_line_number

                    header_row_item = BaseFieldData(**header_item)
                    header_row_item['value'] = header_value
                    header_row_item['post_processing_value'] = header_value
                    header_row_item['field_value_coordinates'] = header_bbox
                    header_row_item['page_number'] = header_page_number
                    header_row_item['line_number'] = header_line_number

                    if (not is_first_epage) and is_first_point:
                        merged = False
                        if exception_table['rows'] and len(exception_table['rows']) > 1:
                            last_etype = ''
                            last_etext = ''
                            last_eheader = ''

                            for exception_col in exception_table['rows'][-1]['columns']:
                                if exception_col['name'] == "Exception_Type":
                                    last_etype = exception_col['value']
                                if exception_col['name'] == "Exception_Text":
                                    last_etext = exception_col['value']
                                if exception_col['name'] == "Exception_Header":
                                    last_eheader = exception_col['value']

                            if ((type_value.lower().strip() == last_etype.lower().strip()) \
                                    or ((type_value.lower().strip() == '') and (last_etype.lower().strip() != ''))) \
                                and header_value.lower().strip() == last_eheader.lower().strip():
                                if not SplitByBullets.is_bullet_point(text_value)[0]:
                                    text_row_item['value'] = last_etext + text_value
                                    text_row_item['post_processing_value'] = last_etext + text_value
                                    columns = [header_row_item, text_row_item, type_row_item]
                                    row = TableRow(row_number=idx, page_number=header_page_number,
                                                   line_number=text_line_number, columns=columns)
                                    exception_table['rows'][-1] = row
                                    merged = True
                        if SplitByBullets.is_bullet_point(text_value)[0]:
                            is_first_point = False
                        if merged:
                            continue

                    columns = [header_row_item, text_row_item, type_row_item]
                    row = TableRow(row_number=idx, page_number=header_page_number, line_number=text_line_number, columns=columns)
                    exception_table.add_row(row)

                    if is_first_point:
                        is_first_point = False


        return exception_table