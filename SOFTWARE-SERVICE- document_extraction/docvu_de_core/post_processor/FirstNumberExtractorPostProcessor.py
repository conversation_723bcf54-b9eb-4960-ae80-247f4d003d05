from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.utils.process_utils import ProcessUtils
from docvu_de_core.utils.element_utils import ElementUtils
from copy import deepcopy
import re

class FirstNumberExtractorPostProcessor:
    def __init__(self, **kwargs):
        """
        Initialize FirstNumberExtractorPostProcessor with various text processing options.

        Supported arguments:
        - remove_non_digit: <PERSON><PERSON><PERSON> to remove all non-digit characters from the text.
        - remove_spaces: <PERSON><PERSON>an to remove spaces from the text.

        :param kwargs: Dictionary of processing options.
        """
        self.remove_non_digit = kwargs.get('remove_non_digit', False)
        self.remove_spaces = kwargs.get('remove_spaces', False)
        self.process_utils = ProcessUtils()

    def __call__(self, **kwargs):
        """
        Execute the configured text processing operations on the given elements.

        :param kwargs: Dictionary containing 'elements' which is a list of text elements.
        :return: PostProcessOutputFormat with the processed text and related information.
        """
        elements = kwargs.get("elements", [])
        success = False
        elements_new = deepcopy(elements)

        if self.remove_spaces:
            elements_new = ElementUtils.remove_spaces(elements_new)

        elements_new, success = self.find_first_number_substring_func(elements_new)

        value = ''.join([e["text"] for e in elements_new])
        bbox, line_number = self.process_utils.get_bbox_and_line_number(elements_new)

        return PostProcessOutputFormat(
            bbox=bbox,
            line_number=line_number,
            value=value,
            success=success,
            elements=elements_new,
        )

    def find_first_number_substring_func(self, elements):
        elements_new, found = ElementUtils.contains(elements, r'\d+')
        if found:
            for element in elements_new:
                match = re.search(r'\d+', element["text"])
                if match:
                    first_number = match.group()
                    element["text"] = first_number
                    return elements_new, True
        return elements_new, False

# Example usage:
if __name__ == "__main__":
    structured_elements = [
        {'text': 'Address Reference: 9158 Skywood Lane, Juneau, AK 99801', 'HPOS': 100, 'VPOS': 428,
         'WIDTH': 758, 'HEIGHT': 20, 'END_HPOS': 858, 'END_VPOS': 448, 'WORDS': [
            {'text': 'Address', 'HPOS': 100.0, 'VPOS': 428.66666666666663, 'WIDTH': 96.0,
             'HEIGHT': 20.666666666666668},
            {'text': 'Reference:', 'HPOS': 220.66666666666666, 'VPOS': 428.66666666666663,
             'WIDTH': 134.66666666666666, 'HEIGHT': 20.666666666666668},
            {'text': '9158', 'HPOS': 378.6666666666667, 'VPOS': 428.66666666666663, 'WIDTH': 46.0,
             'HEIGHT': 20.666666666666668},
            {'text': 'Skywood', 'HPOS': 448.00000000000006, 'VPOS': 428.0, 'WIDTH': 97.33333333333331,
             'HEIGHT': 20.666666666666668},
            {'text': 'Lane,', 'HPOS': 570.0, 'VPOS': 428.66666666666663, 'WIDTH': 61.33333333333337,
             'HEIGHT': 20.666666666666668},
            {'text': 'Juneau,', 'HPOS': 645.3333333333333, 'VPOS': 428.66666666666663,
             'WIDTH': 92.66666666666674, 'HEIGHT': 20.666666666666668},
            {'text': 'AK', 'HPOS': 749.3333333333334, 'VPOS': 428.66666666666663, 'WIDTH': 21.333333333333258,
             'HEIGHT': 20.666666666666668},
            {'text': '99801', 'HPOS': 795.3333333333334, 'VPOS': 428.66666666666663, 'WIDTH': 63.33333333333337,
             'HEIGHT': 20.666666666666668}
        ], 'LINE_NUMBER': 7},
        # Add more elements if needed
    ]

    number_extractor = FirstNumberExtractorPostProcessor(remove_spaces=True)
    result = number_extractor(elements=structured_elements)
    print("Result:")
    print(result)