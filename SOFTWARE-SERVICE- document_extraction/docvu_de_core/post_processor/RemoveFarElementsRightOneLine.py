from docvu_de_core.utils.distance_utils import DistanceUtils
from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.utils.process_utils import ProcessUtils


class RemoveFarElementsRightOneLine:
    def __init__(self, **kwargs):
        self.process_utils = ProcessUtils()

    def __call__(self, **kwargs):
        elements = kwargs.get("elements", [])
        success = False

        same_line_elements = []
        first_element = elements[0]
        same_line_elements.append(first_element)
        for element in elements[1:]:
            if DistanceUtils.on_same_vertical_line(first_element, element):
                same_line_elements.append(element)
            else:
                break

        new_elements = []
        prev_element = same_line_elements[0]
        new_elements.append(prev_element)
        for element in elements[1:]:
            if DistanceUtils.on_same_vertical_line(prev_element, element):
                new_elements.append(element)
                prev_element = element
                success = True
            else:
                break



        value = ''.join([e["text"] for e in new_elements])
        bbox, line_number = self.process_utils.get_bbox_and_line_number(new_elements)

        return PostProcessOutputFormat(
                bbox=bbox,
                line_number=line_number,
                value=value,
                success=success,
                elements=new_elements,
        )