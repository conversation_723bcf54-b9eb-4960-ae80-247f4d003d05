from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.utils.process_utils import ProcessUtils
from docvu_de_core.utils.element_utils import ElementUtils
from copy import deepcopy
import re

class AmountExtractorPostProcessor:
    def __init__(self, **kwargs):
        """
        Initialize AmountExtractorPostProcessor with various text processing options.

        Supported arguments:
        - remove_spaces: Boolean to remove spaces from the text.

        :param kwargs: Dictionary of processing options.
        """
        self.remove_spaces = kwargs.get('remove_spaces', False)
        self.process_utils = ProcessUtils()
        self.amount_pattern = r'\$\s?\d{1,3}(?:\s?,\s?\d{3})*(?:\s?\.\s?\d{2})?'

    def __call__(self, **kwargs):
        """
        Execute the configured text processing operations on the given elements.

        :param kwargs: Dictionary containing 'elements' which is a list of text elements.
        :return: PostProcessOutputFormat with the processed text and related information.
        """
        elements = kwargs.get("elements", [])
        success = False
        elements_new = deepcopy(elements)

        if self.remove_spaces:
            elements_new = ElementUtils.remove_spaces(elements_new)

        elements_new, success = self.find_amount_substring_func(elements_new)

        value = ''.join([e["text"] for e in elements_new])
        bbox, line_number = self.process_utils.get_bbox_and_line_number(elements_new)

        return PostProcessOutputFormat(
            bbox=bbox,
            line_number=line_number,
            value=value,
            success=success,
            elements=elements_new,
        )

    def find_amount_substring_func(self, elements):
        elements_new, found = ElementUtils.contains(elements, self.amount_pattern, case_sensitive=True)
        return elements_new, found

# Example usage:
if __name__ == "__main__":
    structured_elements = [
        {'text': 'Amount: $9,158.75', 'HPOS': 100, 'VPOS': 428, 'WIDTH': 758, 'HEIGHT': 20, 'END_HPOS': 858, 'END_VPOS': 448, 'WORDS': [
            {'text': 'Amount:', 'HPOS': 100.0, 'VPOS': 428.66666666666663, 'WIDTH': 96.0, 'HEIGHT': 20.666666666666668},
            {'text': '$9,158.75', 'HPOS': 220.66666666666666, 'VPOS': 428.66666666666663, 'WIDTH': 134.66666666666666, 'HEIGHT': 20.666666666666668}
        ], 'LINE_NUMBER': 7},
        # Add more elements if needed
    ]

    amount_extractor = AmountExtractorPostProcessor(remove_spaces=True)
    result = amount_extractor(elements=structured_elements)
    print("Result:")
    print(result)

    import re

    text = 'Amount: $9,158.75'
    match = re.search(r'\$\d{1,3}(?:,\d{3})*(?:\.\d{2})?', text)
    if match:
        amount = match.group()
        print(amount)  # Output: $4.63