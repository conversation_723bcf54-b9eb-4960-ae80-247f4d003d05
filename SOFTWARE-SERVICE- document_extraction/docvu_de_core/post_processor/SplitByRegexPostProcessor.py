#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""
from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.modules import SplitByRegEx

class SplitByRegexPostProcessor:
    """
    A class for post-processing text data by splitting it using regular expressions.

    Methods:
        __init__(**kwargs): Initializes the post-processor.
        __call__(**kwargs): Executes the text splitting process and returns the formatted output.
    """
    def __init__(self, **kwargs):
        """
        Initializes the SplitByRegexPostProcessor instance.

        Parameters:
            **kwargs: Additional keyword arguments for initialization (not used currently).
        """
        pass

    def __call__(self, **kwargs):
        """
        Splits the input text data using predefined regular expressions and formats the output.

        Parameters:
            **kwargs: Contains the following key:
                - elements (list): A list of text elements to be processed.

        Returns:
            PostProcessOutputFormat: A formatted object containing:
                - bbox (tuple): The bounding box of the extracted text, if applicable.
                - line_number (int): The line number of the extracted text, if applicable.
                - value (str): The extracted text value.
                - success (bool): Whether the operation was successful.
                - is_table (bool): Indicator if the data is a table structure.
        """
        elements = kwargs.get('elements')
        regex_list = [r"(?i)\bALTA\s+ENDORSEMENT\b"]

        if not elements:  # Check if elements is None or empty
            return PostProcessOutputFormat(
                bbox=None,
                line_number=None,
                value=None,
                success=False,
                is_table=True
            )

        # Split the text by the regex
        regex_splitter = SplitByRegEx(regex_list, consider_contents_before_found=False, keyword_zone='left')
        regex_split_output = regex_splitter.split_text(elements, get_elements=True, get_keyword_info=True)

        if not regex_split_output or not regex_split_output.success:  # Check if regex_split_output is None or unsuccessful
            return PostProcessOutputFormat(
                bbox=None,
                line_number=None,
                value=None,
                success=False,
                is_table=True
            )

        # Format the output if successful
        output_format = PostProcessOutputFormat(
            bbox=regex_split_output.bbox,
            line_number=regex_split_output.line_number,
            value=regex_split_output.value,
            success=True,
            is_table=True
        )

        return output_format
