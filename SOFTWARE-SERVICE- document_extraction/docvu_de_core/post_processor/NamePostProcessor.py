from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.utils.process_utils import ProcessUtils
from copy import deepcopy
import regex as re

class NamePostProcessor:
    def __init__(self, **kwargs):
        self.process_utils = ProcessUtils()
        self.get_si_borrower_name = kwargs.get('si_borrower_name', False)
        self.get_si_coborrower_name = kwargs.get('si_coborrower_name', False)
        self.get_note_borrower_name = kwargs.get('note_borrower_name', False)
        self.get_note_coborrower_name = kwargs.get('note_coborrower_name', False)
        self.get_flood_company = kwargs.get('extract_flood_company', False)
        self.get_norc_borrower_name=kwargs.get('extract_norc_borrower_name', False)
        self.get_norc_coborrower_name=kwargs.get('extract_norc_coborrower_name', False)
        self.get_sna_aka_1_name=kwargs.get('extract_sna_aka_1_name', False)
        self.get_sna_aka_2_name=kwargs.get('extract_sna_aka_2_name', False)
        self.get_sna_aka_3_name=kwargs.get('extract_sna_aka_3_name', False)
        self.get_sna_aka_4_name=kwargs.get('extract_sna_aka_4_name', False)
    
    def __call__(self, **kwargs):
        elements = kwargs.get("elements", [])
        success = False
        elements_new = deepcopy(elements)
        value = None

        value = ' '.join([e["text"] for e in elements_new])

        if self.get_si_borrower_name or self.get_si_coborrower_name:
            borrower_name, coborrower_name = self.security_instrument_extract_names(value)
            
            if self.get_si_borrower_name:
                value = borrower_name
            elif self.get_si_coborrower_name:
                value = coborrower_name
        
        elif self.get_note_borrower_name or self.get_note_coborrower_name:
            borrower_name, coborrower_name = self.note_extract_names(value)
            
            if self.get_note_borrower_name:
                value = borrower_name
            elif self.get_note_coborrower_name:
                value = coborrower_name

        elif self.get_flood_company:
            value = self.extract_flood_company(value)

        elif self.get_norc_borrower_name:
            value=self.extract_norc_borrower_name(value)

        elif self.get_norc_coborrower_name:
            value=self.extract_norc_coborrower_name(value)

        elif self.get_sna_aka_1_name or self.get_sna_aka_2_name or self.get_sna_aka_3_name or self.get_sna_aka_4_name:
            aka_1_name, aka_2_name, aka_3_name, aka_4_name= self.extract_sna_name(value)

            if self.get_sna_aka_1_name:
                value=aka_1_name
            elif self.get_sna_aka_2_name:
                value=aka_2_name
            elif self.get_sna_aka_3_name:
                value=aka_3_name
            elif self.get_sna_aka_4_name:
                value=aka_4_name

        bbox, line_number = self.process_utils.get_bbox_and_line_number(elements_new)

        return PostProcessOutputFormat(
            bbox=bbox,
            line_number=line_number,
            value=value,
            success=success,
            elements=elements_new,
        )
    
    def security_instrument_extract_names(self, text):
        borrower_name = None
        coborrower_name = None

        # == Pattern 1: Match any number followed by (Seal) =====
        #Example: TIM EDWARD BENBERRY 3-10-2025 (Seal) DATE BurE Bro A 3.10.2025 (Seal) BONNIE E BENBERRY DATE
        seal_number_pattern = re.findall(r'\b[\d/.-]+\s*\(Seal\)', text)
        
        if seal_number_pattern:
            # Find pattern, Fetch the name before it and Clean trailing DATE/DOT.
            first_index = text.find(seal_number_pattern[0])
            first_match = text[:first_index].strip()
            borrower_text = re.sub(r'\bDATE\.?$', '', first_match.strip(), flags=re.IGNORECASE)
            name1, _ = self.split_names_by_repeated_surname(borrower_text)
            borrower_name = name1.strip()
            
            # If second match exists, fetch name after it
            if len(seal_number_pattern) > 1:
                second_index = text.find(seal_number_pattern[1], first_index + 1)
                after_second = text[second_index + len(seal_number_pattern[1]):].strip()
                coborrower_text = re.sub(r'\bDATE\.?$', '', after_second.strip(), flags=re.IGNORECASE)
                name1, _ = self.split_names_by_repeated_surname(coborrower_text)
                coborrower_name = name1.strip()

        # == Pattern 2: Split based on the pattern -Borrower (Seal) =====
        #Example: Ruppert F Stanley 87 Hander -Borrower (Seal) Rosemarie U Stanley Stanley 2125/2035 -Borrower TEXAS Single Family    
        elif '-Borrower (Seal)' in text:
            parts = text.split('-Borrower (Seal)')

            if parts:
                # Find the number in first part to mark end of name
                borrower_match = re.search(r'^(.+?)\s+[\d./-]+', parts[0].strip())
                borrower_text = borrower_match.group(1).strip() if borrower_match else parts[0].strip()
                name1, _ = self.split_names_by_repeated_surname(borrower_text)
                borrower_name = name1.strip()

            if len(parts) > 1:
                #Extract from second part until next number + -Borrower
                coborrower_match = re.search(r'(.+?)\s+\d{4}/\d{4}\s+-Borrower', parts[1], re.DOTALL)
                if coborrower_match:
                    coborrower_text = coborrower_match.group(1).strip()
                    name1, _ = self.split_names_by_repeated_surname(coborrower_text)
                    coborrower_name = name1.strip()

        elif ("signature" or "Signature") in text:
            # Search case-insensitively for last occurrence of "signature" or "date"
            matches = list(re.finditer(r'(signature|date)', text, re.IGNORECASE))

            if matches:
                last_match = matches[-1]
                result = text[last_match.end():]  # Get text after last match

                borrower_name, coborrower_name = self.split_names_by_repeated_surname(result)

        elif "- BORROWER -" in text:
            if "DATE" in text:
                # Pattern: Match "BORROWER -", then capture any characters (non-greedy) until " - DATE"
                pattern = r'BORROWER\s*-\s*(.+?)\s*-\s*DATE'
                matches = re.findall(pattern, text)
                
                borrower_name = matches[0]
                coborrower_name = matches[1]
            
            else:
                # Regular expression to find names after '- BORROWER -'
                matches = re.findall(r'- BORROWER - ([A-Z]+ [A-Z]+)', text)
                
                borrower_name = matches[0]
                coborrower_name = matches[1]

        elif "-Borrower" in text:
            # Limit text to up to the second occurrence of "-Borrower"
            limited_text = re.split(r'-Borrower', text, maxsplit=2)
            borrower_name = limited_text[0]
            coborrower_name = limited_text[1]

        return borrower_name, coborrower_name
    
    def split_names_by_repeated_surname(self, name_str):
        # Use regex to find all words (including initials like J.)
        words = re.findall(r'\b[\w\'.-]+\b', name_str)
        
        # Normalize to lower case for comparison
        lower_words = [word.lower() for word in words]

        # Count occurrences of each word
        for i in range(len(words)):
            for j in range(i + 1, len(words)):
                if lower_words[i] == lower_words[j]:
                    # Found repeated word, assume it's the surname
                    first_name = ' '.join(words[:j])
                    second_name = ' '.join(words[j:])
                    return first_name.strip(), second_name.strip()

        # If no repetition found, return original
        return name_str.strip(), ''
    
    def note_extract_names(self, text):
        borrower_name = None
        coborrower_name = None

        if "- BORROWER -" in text:
            match = re.search(r'BORROWER\s*-\s*(.+)', text)
            if match:
                borrower_name = match.group(1).strip()
                coborrower_name = ""

        elif "(Seal)" in text:
            if "-Borrower" in text:
                pattern = r"([\p{Lu}][\p{L}'’\-]+(?: [\p{Lu}]\.?| [\p{Lu}][\p{L}'’\-]+)? [\p{Lu}][\p{L}'’\-]+)(?= ?-?Borrower)"
                # pattern = r"\b((?:\p{Lu}[\p{L}'’\-]+(?:\s+|$)){2,5})(?=\s*-?Borrower\b)"

                matches = re.findall(pattern, text, flags=re.IGNORECASE)

                if len(matches) == 1:
                    borrower_name = matches[0]
                    coborrower_name = ""
                    
                elif len(matches) > 1:
                    borrower_name = matches[0]
                    coborrower_name = matches[1]

                else:
                    pattern2 = r"\b((?:\p{Lu}[\p{L}'’\-]+(?:\s+|$)){2,5})(?=\s*-?Borrower\b)"

                    matches_2 = re.findall(pattern2, text, flags=re.IGNORECASE)

                    if len(matches_2) > 1:
                        borrower_name = matches_2[0]
                        coborrower_name = matches_2[1]

   
            else:
                # Regex to match two consecutive uppercase words (likely names)
                # matches = re.findall(r'\b(?:[A-Z]{2,}\s){1,2}[A-Z]{2,}\b', text)
                matches = re.findall(r'\b(?:[A-Z]+\s){1,2}[A-Z]{2,}\b', text)
                if len(matches) == 1:
                    borrower_name = matches[0]
                    coborrower_name = ""

                elif len(matches) > 1:
                    borrower_name = matches[0]
                    coborrower_name = matches[1]

        return borrower_name, coborrower_name

    def extract_flood_company(self, text):
        """
        Extracts flood company name until a repeated word appears (case-insensitive).

        Examples:
            - "CoreLogic Flood Services CoreLcglc" → "CoreLogic Flood Services"
            - "cactus Cactus" → "cactus"
        """
        
        if not isinstance(text, str):
            return str(text)

        words = text.strip().split()
        seen = set()
        result = []

        for word in words:
            word_lower = word.lower()
            if word_lower in seen:
                break
            seen.add(word_lower)
            result.append(word)

        extracted = " ".join(result)
        return extracted

    def extract_norc_borrower_name(self, text):
        """
        Extracts the name before the first occurrence of 'Date', ignoring case. 
        If 'Date' is at the beginning, extract the name after 'Date'.

        Examples:
            "Date EDWARD CROOK" -> "EDWARD CROOK"
            "EDWARD CROOK Date Tim John Date" -> "EDWARD CROOK"
        """
        if not isinstance(text, str):
            return ""

        # 1) Extract the name after the first occurrence of 'Date' (if 'Date' is at the start)
        match_after = re.search(r"^\s*\bdate\b\s+([\w'.\-\s]+)", text, flags=re.IGNORECASE)
        if match_after:
            return match_after.group(1).strip()

        # 2) Extract the name before the first occurrence of 'Date' (case-insensitive)
        match_before = re.search(r"^(.*?)\s+\bdate\b", text, flags=re.IGNORECASE)
        if match_before:
            borrower_name = match_before.group(1).strip()
            if borrower_name:
                return borrower_name

        # 3) If no 'Date' found, return empty string
        return ""

    def extract_norc_coborrower_name(self, text):
        """
        Extracts the name after the last 'Date' if present, otherwise extracts the name before 'Date',
        ignoring case, and removes the word 'Date' from output.

        Examples:
            "Consumer's Signature Date NICHOLAS R. WIVEL" -> "NICHOLAS R. WIVEL"
            "EDWARD CROOK DATE" -> "EDWARD CROOK"
            "Consumer's Signature Date NICHOLAS R. WIVEL Date" -> "NICHOLAS R. WIVEL"
        """
        if not isinstance(text, str):
            return ""

        # 1) Extract name after the last 'Date' (if any)
        match_after = re.search(r"\bdate\b\s+([\w'.\-\s]+)\s*\bdate\b$", text, flags=re.IGNORECASE)
        if match_after:
            name_after = match_after.group(1).strip()
            if name_after:
                return name_after

        # 2) Extract name after the first 'Date' (if any), ensuring no 'Date' at the end
        match_after = re.search(r"\bdate\b\s+([\w'.\-\s]+)$", text, flags=re.IGNORECASE)
        if match_after:
            name_after = match_after.group(1).strip()
            if name_after:
                return name_after

        # 3) Extract name before 'Date' (if any)
        match_before = re.search(r"([\w'.\-\s]+)\s+\bdate\b$", text, flags=re.IGNORECASE)
        if match_before:
            name_before = match_before.group(1).strip()
            if name_before:
                return name_before

        # 4) If no 'Date' found, return whole text as fallback (optional)
        return text

    def extract_sna_name(self, text):
        aka_1_name = None
        aka_2_name = None
        aka_3_name = None
        aka_4_name = None
        
        # Check if the text contains the word "Variation" for Variant 1
        if "Variation" in text:

            pattern = r"Name Variation \(Print\)"
            matches = list(re.finditer(pattern, text))
            left_parts = []
            prev_index = 0

            for i, match in enumerate(matches[:4]):  # Limit to first 4
                start = match.start()
                # Get the part of the text between the previous match and the current match
                part = text[prev_index:start].strip()
                # Remove unwanted strings from the part
                strings_to_remove = ["Sample Signature (Variation)", "NEVER KNOWN AS", "N/A", "as"]
                for s in strings_to_remove:
                    part = part.replace(s, "")
                part = part.strip()
                words = part.strip().split()
                # Filter out any words containing digits
                filtered_words = [word for word in words if not any(char.isdigit() for char in word)]
                part = ' '.join(filtered_words)
                word_count = len(part.split())

                aka_name = ""
                if word_count >= 2:
                    name_words = part.split()
                    if word_count == 4:
                        aka_name = ' '.join(name_words[-2:])
                    elif word_count in [3,5,6,7]:
                        aka_name = ' '.join(name_words[-3:])
                    else:
                        aka_name = ' '.join(name_words[-2:])
                    
                left_parts.append(aka_name)  # append aka_names to list
                prev_index = match.end()
            
            # Assign the values in a list to respective aka_names
            aka_1_name = left_parts[0] if len(left_parts) > 0 else ""
            aka_2_name = left_parts[1] if len(left_parts) > 1 else ""
            aka_3_name = left_parts[2] if len(left_parts) > 2 else ""
            aka_4_name = left_parts[3] if len(left_parts) > 3 else ""

            return aka_1_name, aka_2_name, aka_3_name, aka_4_name

        # Check if the text contains the word "DISCREPANCY" for Variant 2
        elif "DISCREPANCY" in text:
            pattern = r"SIGN MY NAME"
            matches = list(re.finditer(pattern, text))
            left_parts = []
            prev_index = 0

            for i, match in enumerate(matches[:4]):  # Limit to first 4
                start = match.start()
                part = text[prev_index:start].strip()
                # Check if the string contains unwanted text and remove"
                if "THAT I, " in part:
                    result = part.split("THAT I, ", 1)[1]
                elif "THAT I." in part:
                    result = part.split("THAT I.", 1)[1]
                elif "THATI," in part:
                    result = part.split("THATI,", 1)[1]
                else:
                    result = part  # No change if neither phrase is found
                left_parts.append(result)
                prev_index = match.end()
            
            aka_1_name = left_parts[0] if len(left_parts) > 0 else ""
            aka_2_name = left_parts[1] if len(left_parts) > 1 else ""
            aka_3_name = left_parts[2] if len(left_parts) > 2 else ""
            aka_4_name = left_parts[3] if len(left_parts) > 3 else ""
            
            return aka_1_name, aka_2_name, aka_3_name, aka_4_name
        
        # For Variant 3
        else:
            aka_names = []
            # Keep splitting as long as there is a comma or colon
            for _ in range(4):
                if ',' in text or ':' in text:
                    split_match = re.split(r'[,:]', text, maxsplit=1)
                    aka_names.append(split_match[0].strip())
                    if len(split_match) > 1:    
                        text = split_match[1].strip() 
                    else:
                        break
                else:
                    # No more separators; add what's left and break
                    aka_names.append(text.strip())
                    break

            aka_1_name = aka_names[0] if len(aka_names) > 0 else ""
            aka_2_name = aka_names[1] if len(aka_names) > 1 else ""
            aka_3_name = aka_names[2] if len(aka_names) > 2 else ""
            aka_4_name = aka_names[3] if len(aka_names) > 3 else ""

            return aka_1_name, aka_2_name, aka_3_name, aka_4_name
        
        return aka_1_name, aka_2_name, aka_3_name, aka_4_name