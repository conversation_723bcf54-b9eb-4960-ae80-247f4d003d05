config_params:
  page_boundaries:
    ## default page_boundary values
    header_ratio: 0.081  # assuming top ~8%
    footer_ratio: 0.9091 # assuming bottom ~10%
    header_ends: 0.18
    footer_starts: 0.85
    footer_percentage: 0.3

    452: ##Post Close
      header_ratio: 0.081  # assuming top ~8%
      footer_ratio: 0.9091 # assuming bottom ~10%
      header_ends: 0.18
      footer_starts: 0.95
      footer_percentage: 0.3

    403: ##UnderWritter
      header_ratio: 0.081  # assuming top ~8%
      footer_ratio: 0.9091 # assuming bottom ~10%
      header_ends: 0
      footer_starts: 0.95
      footer_percentage: 0.3

    402: ##SLS
      header_ratio: 0.081  # assuming top ~8%
      footer_ratio: 0.9091 # assuming bottom ~10%
      header_ends: 0.18
      footer_starts: 0.85
      footer_percentage: 0.3

    449: ##MrCooper
      header_ratio: 0.081  # assuming top ~8%
      footer_ratio: 0.9091 # assuming bottom ~10%
      header_ends: 0.18
      footer_starts: 0.9091
      footer_percentage: 0.3

    401: ##Round Point
      header_ratio: 0      # assuming top ~0%
      footer_ratio: 0.9091 # assuming bottom ~10%
      header_ends: 0.18
      footer_starts: 0.90
      footer_percentage: 0.3

    443: ##Docvu Demo
      header_ratio: 0.081  # assuming top ~8%
      footer_ratio: 0.9091 # assuming bottom ~10%
      header_ends: 0.18
      footer_starts: 0.95
      footer_percentage: 0.3

    440: ##valon
      header_ratio: 0      # assuming top ~0%
      footer_ratio: 0.9091 # assuming bottom ~10%
      header_ends: 0.18
      footer_starts: 0.95
      footer_percentage: 0.3

    439: ##Phl
      header_ratio: 0      # assuming top ~0%
      footer_ratio: 0.9091 # assuming bottom ~10%
      header_ends: 0.18
      footer_starts: 0.95
      footer_percentage: 0.3

    ## Add here for other clients if need else default will work

  pdf_to_ocr:
    dpi: 200

  xml_to_json:
    block_distance_threshold: 40
    space_threshold: 30

  parser:
    EXTRA_PAGES_TO_SEARCH: 10
    REMOVE_CHARS: ["", ":","*", "’", '”']
    OMR_MARKER_CHARS: ["@", "©", "®", "[X]", "[x]", "[X", "[x", "X]", "x]", "X", "x", "[]", "p", "P"]
    POST_PROCESS_REMOVE_CHARS: ["@", "©", "®", "_", ":", "?", "#", "~", "*"]
    REMOVE_VALUE: FALSE
    FOUND_OMR_TAG: "FOUND_OMR"
    CHECKED_OMR_ID: "1"
    UNCHECKED_OMR_ID: "0"
    REF_FUZZY_MATCH_SCORE: 90

  yolo:
    detection_confidence: 0.50
    save_images: False
    image_resolution: 640

  api:
    temp_path: "./temp"
    delete_temp_files: False
    host: "0.0.0.0"
    port: 8004
    reload: True

blob:
  # Azure Blob Storage setup
  azure_storage_connection_string: "DefaultEndpointsProtocol=https;AccountName=docvuaimlinternal;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
  model_blob_container_name: "docvu-release"
  blob_name_vm: 'docvu_de/version_manager/_version.py'

model_config:
  models:
    subdivision_model:
      model_name: "docvu-subdivision-model"
      model_blob_path: "docvu-release/docvu-subdivision-model/"
    yolo_v8_for_checkbox:
      model_name: "docvu-omr-model"
      model_blob_path: "docvu-release/docvu-omr-model/"
    image_matching_model:
      model_name: "docvu-image-embedding-model"
      model_blob_path: "docvu-release/docvu-image-embedding-model/"
    ner_model:
      model_name: "docvu-gliner-model"
      model_blob_path: "docvu-release/docvu-gliner-model/"
    yolo_v8_for_lbsd_model:
      model_name: "docvu-lbsd-model"
      model_blob_path: "docvu-release/docvu-lbsd-model"

## api config for ner service request
api_config:
  ner_model:
    server_url: "http://127.0.0.1:8000"
    end_point: "predict-ner"

##Open AI llm config
llm_config:
  endpoint_url: "https://vlmpoc.openai.azure.com/"
  deployment_name: "gpt-4o-mini"
  azure_openai_api_key: "1kTPt4J1vzjCkzsmusLkDKZFK67e8TWgn0FI3a5h0OuzPcnZDjWiJQQJ99BCACYeBjFXJ3w3AAABACOGhKVV"
  api_version: "2024-05-01-preview"
  temperature: 0.1
  top_p: 0.95
  max_tokens: 800