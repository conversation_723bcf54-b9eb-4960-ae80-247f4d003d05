import yaml
import json
import os
import platform
from docvu_de_core.config.us_address_map import *

config_path = os.path.join(os.path.dirname(__file__), 'config.yaml')

# Check the operating system
if platform.system() == "Linux":
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)
else:
    print("Platform??",platform.system())
    with open(config_path, "r", encoding='utf-8') as f:
        config = yaml.safe_load(f)

pdf2ocr_config = config['config_params']['pdf_to_ocr']
xml2json_config = config['config_params']['xml_to_json']
parser_config = config['config_params']['parser']
yolo_config = config['config_params']['yolo']
api_config = config['config_params']['api']
page_bound_configs = config['config_params']['page_boundaries']

azure_storage_connection_string = config['blob']['azure_storage_connection_string']
model_blob_container_name = config['blob']['model_blob_container_name']
blob_name_vm = config['blob']['blob_name_vm']

model_config = config['model_config']

server_url = config['api_config']['ner_model']['server_url']
end_point = config['api_config']['ner_model']['end_point']

llm_config = config['llm_config']