[{"id": 3000401, "key": ["Sign"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON><PERSON>'s name", "Preparers name"], "start_identifier": [""], "field_id": 3000401, "field_name": "form_1040_borrower_sign", "document_id": 9000001, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000404, "key": ["Sign"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Phone no.", "<PERSON><PERSON><PERSON>'s name", "Preparers name"], "start_identifier": [""], "field_id": 3000404, "field_name": "form_1040_coborrower_sign", "document_id": 9000001, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000402, "key": ["Sign"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Phone no.", "<PERSON><PERSON><PERSON>'s name", "Preparers name"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000402, "field_name": "form_1040_borrower_date", "document_id": 9000001}, {"id": 3000405, "key": ["Sign"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Phone no.", "<PERSON><PERSON><PERSON>'s name", "Preparers name"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000405, "field_name": "form_1040_coborrower_date", "document_id": 9000001, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]