[{"id": 3000513, "key": ["You must"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000513, "field_name": "notice_to_homeowner_borrower_sign", "document_id": 9000719, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000514, "key": ["You must"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000514, "field_name": "notice_to_homeowner_borrower_date", "document_id": 9000719, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}, {"id": 3000516, "key": ["You must"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000516, "field_name": "notice_to_homeowner_coborrower_sign", "document_id": 9000719, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000517, "key": ["You must"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000517, "field_name": "notice_to_homeowner_coborrower_date", "document_id": 9000719, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]