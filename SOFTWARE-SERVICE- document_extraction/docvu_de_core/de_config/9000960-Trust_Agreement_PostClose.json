[{"id": 3000856, "key": ["witness my hand and official seal", "this instrument was acknowledged", "given under my hand", "I certify under", "set their hands", "witness my hand and notarial seal", "acknowledged and subscribed", "WITNESS my hand and official scal", "personally known to me"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "probable_place": "Individual", "field_id": 3000856, "field_name": "trust_agreement_appeared_before", "document_id": 9000960, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "trust_agreement_appeared_before", "label": ["name"]}}}, {"id": 3000862, "key": ["witness my hand and official seal", "this instrument was acknowledged", "given under my hand", "I certify under", "set their hands", "witness my hand and notarial seal", "acknowledged and subscribed", "WITNESS my hand and official scal", "personally known to me"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000862, "field_name": "trust_agreement_notary_commission_expires_date", "document_id": 9000960, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000861, "key": ["official seal", "my commission expires", "physical presence of", "signed, sealed and delivered", "Signature Notary Public", "purposes and considerations", "evidence of identification", "no trust herein created", "nonliability of successor trustee"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000861, "field_name": "trust_agreement_notary_seal", "document_id": 9000960, "post_process": {"check_for_seal_post_processor": {}}}, {"id": 3000851, "key": ["Binding Effect", "first written", "first above written", "Court Instruction", "purpose and consideration", "evidence acceptance", "set their hands", "Beneficiaries should predecease", "Specific Provisions", "respects in accordance", "children and grandchildren living", "Nonliability of Successor Trustee", "capacity therein stated"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000851, "field_name": "trust_agreement_borrower_sign", "document_id": 9000960, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000854, "key": ["Binding Effect", "first written", "first above written", "Court Instruction", "purpose and consideration", "evidence acceptance", "set their hands", "Beneficiaries should predecease", "Specific Provisions", "respects in accordance", "children and grandchildren living", "Nonliability of Successor Trustee", "capacity therein stated"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000854, "field_name": "trust_agreement_coborrower_sign", "document_id": 9000960, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000857, "key": ["STATE of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["County of", ")", "$"], "start_identifier": [], "probable_place": "Individual", "field_id": 3000857, "field_name": "trust_agreement_state_filed", "document_id": 9000960}, {"id": 3000858, "key": ["County of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [")", "$", ","], "start_identifier": [], "probable_place": "Individual", "field_id": 3000858, "field_name": "trust_agreement_county", "document_id": 9000960}, {"id": 3000859, "key": ["County of"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Notary Public"], "start_identifier": [], "field_id": 3000859, "field_name": "trust_agreement_execution_date", "document_id": 9000960}, {"id": 3000860, "key": ["Signature of Notary", "Notary Public"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000860, "field_name": "trust_agreement_notary_acknowledgment", "document_id": 9000960, "post_process": {"check_for_signature_post_processor": {"signature_present": true, "sign_bbox_direction": "up", "vertical_threshold": 0, "horizontal_threshold": 450}}}]