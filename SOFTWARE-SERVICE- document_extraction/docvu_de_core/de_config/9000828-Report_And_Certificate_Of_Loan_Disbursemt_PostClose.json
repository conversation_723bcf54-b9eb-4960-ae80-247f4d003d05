[{"id": 103, "key": ["37A. VETERAN", "28A. VETERAN", "3M. VETERAN", "37k VETERAN"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["38A. CO-OBLIGOR", "29k COBORROWER", "29A. COBORROWER"], "start_identifier": [], "field_id": 103, "field_name": "borrower_race_save_section", "sub_keys": ["Report_And_Certificate_Of_Loan_Disbursemt_Borrower_Race_type_omr"], "document_id": 9000828}, {"id": 106, "key": ["38A. CO-OBLIGOR", "38A. CO-OBUGOR", "38A CO-OBLIGOR", "30A. CO-OBLIGUM", "29A COBORROWER", "CC-OELIGOR"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [], "field_id": 106, "field_name": "coborrower_race_save_section", "sub_keys": ["Report_And_Certificate_Of_Loan_Disbursemt_CoBorrower_Race_type_omr"], "document_id": 9000828}, {"id": 3000918, "key": ["G. <PERSON>", "C. T<PERSON>", "TERM OF LOAN", "LOAN (MONTHS)"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TYPE OF LIEN"], "start_identifier": [], "field_id": 3000918, "field_name": "va261820_9_term_months", "document_id": 9000828}, {"id": 3000901, "key": ["I AM AWARE THAT", "THAT VA DOES NOT", "I AM AWAF"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000901, "field_name": "va261820_borrower_sign", "document_id": 9000828, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000902, "key": ["Federal Statutes provide", "any fraud", "penalties for any"], "direction": "up", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000902, "field_name": "va261820_signature_date", "document_id": 9000828, "output_format": {"date_parser_output_format": {"coborrower_date": false}}}, {"id": 3000904, "key": ["I AM AWARE THAT", "THAT VA DOES NOT", "I AM AWAF"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000904, "field_name": "va261820_coborrower_sign", "document_id": 9000828, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000905, "key": ["ID NUMBER", "VA in NUMBER", "VA 10 NUMBER"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3000905, "field_name": "va261820_uw_name", "document_id": 9000828}, {"id": 3000906, "key": ["VA ID NUMBER", "VA in NUMBER", "VA 10 NUMBER", "UNDERWRITER ID NUMBER"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000906, "field_name": "va261820_uw_id", "document_id": 9000828}, {"id": 3000907, "key": ["32. <PERSON><PERSON><PERSON>", "NAME AND ADDRESS OF LENDER"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["34. DATE SIGNED", "34, <PERSON><PERSON><PERSON>", "34 DATE", "26A DATE", "26A. DATE"], "start_identifier": [], "field_id": 3000907, "field_name": "va261820_lender_name_address", "document_id": 9000828}, {"id": 3000908, "key": ["TELEPHONE NO OF LENDER", "TELEPHONE NUMBER OF LENDER", "33. TELEPHONE"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000908, "field_name": "va261820_lender_phone", "document_id": 9000828}, {"id": 3000909, "key": ["TITLE OF LENDER REPRESENTATIVE", "LENDER REPRESENTATIV"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["PRIVACY ACT NOTICE", "SECTION III", "SECTION .", "SECTION 11", "SECTION 18", "SECTION VETERAN'S"], "start_identifier": [], "field_id": 3000909, "field_name": "va261820_lender_signature_title", "document_id": 9000828}, {"id": 3000910, "key": ["34. D<PERSON><PERSON>", "26A DATE", "26A. DATE"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PRIVACY ACT"], "start_identifier": [""], "field_id": 3000910, "field_name": "va261820_lender_signature_date", "document_id": 9000828, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000911, "key": ["HYBRID-ARM", "C. DATE OF NOTE", "HYERID-ARM"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TYPE OF LIEN"], "start_identifier": [""], "field_id": 3000911, "field_name": "va261820_closing_date", "document_id": 9000828}, {"id": 3000912, "key": ["VA LOAN NUMBER", "LOAN NUMBER"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [" "], "start_identifier": [], "field_id": 3000912, "field_name": "va261820_va_loan_number", "document_id": 9000828, "output_format": {"string_operations_output_format": {"remove_till_from_end": [" "]}}}, {"id": 3000914, "key": ["LENDER'S LOAN", "LENDER LOAN NUMBER"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000914, "field_name": "va261820_lender_loan_number", "document_id": 9000828}, {"id": 3000916, "key": ["AMOUNT OF LOAN"], "direction": "just_down", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [], "field_id": 3000916, "field_name": "va261820_9_loan_amount", "document_id": 9000828, "output_format": {"string_operations_output_format": {"remove_spaces": true}}}, {"id": 3000917, "key": ["B. INTEREST RATE", "RATE OF INTEREST", "INTEREST PER ANNUM", "B. RATE OF"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000917, "field_name": "va261820_9_interest_rate", "document_id": 9000828, "output_format": {"string_operations_output_format": {"replace_within_string": [[",", "."]], "remove_characters": ["C", "c"], "remove_special_chars_from_end": true}}}, {"id": 100, "key": ["TERMS OF LOAN"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ARM TYPE"], "start_identifier": [""], "field_id": 100, "field_name": "Amortization_type_save_section", "sub_keys": ["Report_And_Certificate_Of_Loan_Disbursemt_Amortization_omr"], "document_id": 9000828}, {"id": 101, "key": ["TERMS OF LOAN"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["DATE LOAN PROCEEDS"], "start_identifier": [""], "field_id": 101, "field_name": "Arm_type_save_section", "sub_keys": ["Report_And_Certificate_Of_Loan_Disbursemt_Arm_type_omr"], "document_id": 9000828}, {"id": 3000921, "key": ["PRINCIPAL AND INTEREST", "F. PRINCIPAL"], "direction": "down_block", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["DATE LOAN PROCEEDS", "E. DATE LOAN WAS CLOSED"], "start_identifier": [""], "field_id": 3000921, "field_name": "va261820_9_pi_amount", "document_id": 9000828, "output_format": {"string_operations_output_format": {"remove_spaces": true}}}, {"id": 3000922, "key": ["HYBRID-ARM", "C. DATE OF NOTE"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TYPE OF LIEN"], "start_identifier": [""], "field_id": 3000922, "field_name": "va261820_9_closing_date", "document_id": 9000828}, {"id": 3000923, "key": ["DATE LOAN PROCEEDS"], "direction": "just_down", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000923, "field_name": "va261820_9_fully_paid_out_date", "document_id": 9000828, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 102, "key": ["37A. VETERAN", "28A. VETERAN", "3M. VETERAN", "37k VETERAN"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["38A. CO-OBLIGOR", "29k COBORROWER", "29A. COBORROWER"], "start_identifier": [], "field_id": 102, "field_name": "borrower_ethnicity_save_section", "sub_keys": ["Report_And_Certificate_Of_Loan_Disbursemt_Borrower_Ethnicity_type_omr"], "document_id": 9000828}, {"id": 104, "key": ["37A. VETERAN", "28A. VETERAN", "3M. VETERAN", "37k VETERAN"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["38A. CO-OBLIGOR", "29k COBORROWER", "29A. COBORROWER"], "start_identifier": [], "field_id": 104, "field_name": "borrower_gender_save_section", "sub_keys": ["Report_And_Certificate_Of_Loan_Disbursemt_Borrower_Gender_type_omr"], "document_id": 9000828}, {"id": 105, "key": ["38A. CO-OBLIGOR", "38A. CO-OBUGOR", "38A CO-OBLIGOR", "30A. CO-OBLIGUM", "29A COBORROWER"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [], "field_id": 105, "field_name": "coborrower_ethnicity_save_section", "sub_keys": ["Report_And_Certificate_Of_Loan_Disbursemt_CoBorrower_Ethnicity_type_omr"], "document_id": 9000828}, {"id": 107, "key": ["38A. CO-OBLIGOR", "38A. CO-OBUGOR", "38A CO-OBLIGOR", "30A. CO-OBLIGUM", "29A COBORROWER", "CC-OELIGOR"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [], "field_id": 107, "field_name": "coborrower_gender_save_section", "sub_keys": ["Report_And_Certificate_Of_Loan_Disbursemt_CoBorrower_Gender_type_omr"], "document_id": 9000828}]