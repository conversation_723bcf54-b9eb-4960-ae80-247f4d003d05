[{"id": 3000072, "key": ["receive notice", "account must"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000072, "field_name": "ach_debit_authorization_borrower_date", "document_id": 9000019}, {"id": 3000075, "key": ["receive notice"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000075, "field_name": "ach_debit_authorization_coborrower_date", "document_id": 9000019}, {"id": 3000071, "key": ["BORROWER", "Customer", "Note"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000071, "field_name": "ach_debit_authorization_borrower_sign", "document_id": 9000019, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "up", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000074, "key": ["BORROWER", "Customer", "Note"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000074, "field_name": "ach_debit_authorization_coborrower_sign", "document_id": 9000019, "post_process": {"check_for_signature_post_processor": {"check_closeness": true, "sign_bbox_direction": "up", "vertical_threshold": 0, "horizontal_threshold": 50}}}]