[{"id": 3006106, "key": ["SIGNATURE AFFIDAVIT"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["certify"], "start_identifier": ["AFFIDAVIT I,", "SIGNATURE AFFIDAVIT 1,"], "field_id": 3006106, "field_name": "Co_Borrower_Signature_Name", "document_id": 8011371}, {"id": 3000428, "key": ["loan documents", "as the name", "TYPED BELOW", "(Print or Type Name)", "other names", "correct signature", "same person", "printed variation", "I swear"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Be it remembered", "File No:"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000428, "field_name": "signature_and_name_affidavit_coborrower_sign", "document_id": 8011371, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000429, "key": ["referenced property", "loan documents", "as the name", "TYPED BELOW", "(Print or Type Name)"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["State of"], "start_identifier": [""], "field_id": 3000429, "field_name": "signature_and_name_affidavit_borrower_date", "document_id": 8011371, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]