[{"id": 3000665, "key": ["subscribed and sworn", "Sworn to and subscribed", "signed and sworn", "sworn to, subscribed", "TRUE AND ACCURATE SIGN"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000665, "field_name": "owners_affidavit_appeared_before", "document_id": 9000743, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "owners_affidavit_appeared_before", "label": ["name"]}}}, {"id": 3000671, "key": ["subscribed and sworn", "Sworn to and subscribed", "signed and sworn", "sworn to, subscribed", "TRUE AND ACCURATE SIGN"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["page"], "start_identifier": [], "field_id": 3000671, "field_name": "owners_affidavit_notary_commission_expires_date", "document_id": 9000743, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000669, "key": ["Notary Public", "Notary Signature", "My Commission", "Notary SK Public"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000669, "field_name": "owners_affidavit_notary_acknowledgment", "document_id": 9000743, "post_process": {"check_for_signature_post_processor": {"signature_present": true, "sign_bbox_direction": "up", "vertical_threshold": 0, "horizontal_threshold": 110}}}, {"id": 3000668, "key": ["signed and sworn", "Sworn to and subscribed", "Sworn to and me on subscribed", "i hereby certify", "subscribed before", "subscribed and sworn"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000668, "field_name": "owners_affidavit_execution_date", "document_id": 9000743}, {"id": 3000670, "key": ["My Commission", "My Comission Expires", "Notary Signature", "(Stamp/Seal)", "penalties as provided"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000670, "field_name": "owners_affidavit_notary_seal", "document_id": 9000743, "post_process": {"check_for_seal_post_processor": {}}}, {"id": 3000660, "key": ["WITNESS", "person executes", "singular", "accurate survey", "penalties", "SWORN STATEMENT", "solemnly swear"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000660, "field_name": "owners_affidavit_borrower_sign", "document_id": 9000743, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000663, "key": ["person executes", "singular", "accurate survey", "penalties", "SWORN STATEMENT", "solemnly swear"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000663, "field_name": "owners_affidavit_coborrower_sign", "document_id": 9000743, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000666, "key": ["State of", "Stateaf"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["County of"], "start_identifier": [], "probable_place": "Individual", "field_id": 3000666, "field_name": "owners_affidavit_state_filed", "document_id": 9000743}, {"id": 3000667, "key": ["County of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["On this", "Subscribed"], "start_identifier": [], "probable_place": "Individual", "field_id": 3000667, "field_name": "owners_affidavit_county", "document_id": 9000743}, {"id": 3000672, "key": ["My Comission Expires"], "direction": "inline", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000672, "field_name": "owners_affidavit_commission_expires_date_validation", "document_id": 9000743}, {"id": 3000661, "key": [""], "direction": "", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000661, "field_name": "owners_affidavit_borrower_date", "document_id": 9000743}, {"id": 3000664, "key": [""], "direction": "", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000664, "field_name": "owners_affidavit_coborrower_date", "document_id": 9000743}]