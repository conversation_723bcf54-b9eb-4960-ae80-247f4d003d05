[{"id": 80000964, "key": ["MIN:", "NUN:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Property Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000964, "field_name": "MERS/MIN #", "document_id": 9000671}, {"id": 80012037, "key": ["Note"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER'S PROMISE TO PAY", "<PERSON><PERSON><PERSON>'s Promise"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80012037, "field_name": "Origination_Date_VA_326034", "document_id": 9000671, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 80000849, "key": ["Note"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER'S PROMISE TO PAY", "<PERSON><PERSON><PERSON>'s Promise"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000849, "field_name": "note_document_date_VA_319119", "document_id": 9000671, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 80000852, "key": ["The Lender is", "received from", "The Lcndcr is", "The Lender (s"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["I will make all payments under", "I will make all", "D/", ". [ will", "I will", "(the", "D/", "I understand", " a Limited ", "1 will make all", "twill make all", "will make all payments"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000852, "field_name": "lender_originator_company_name_VA_319118", "document_id": 9000671, "output_format": {"address_parser_output_format": {"get_recipient": true, "from_field": "lender_originator_company_name_VA_319118"}, "string_operations_output_format": {"remove_from_end": ["\\.\\s*$", "\\.\\.$"], "remove_special_chars_from_end": true}}}, {"id": 80000851, "key": ["2.INTEREST", "2. INTEREST"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["The interest rate", " interest rate", "3.PAYMENTS", "3. PAYMENTS"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000851, "field_name": "Original_Interest_Rate_VA_325503", "document_id": 9000671, "output_format": {"number_parser_output_format": {"extract_multiple": false, "pattern_keys": ["percent_or_rate"]}, "string_operations_output_format": {"remove_spaces": true}}}, {"id": 80000852, "key": ["3.PAYMENTS", "3. PAYMENTS"], "direction": "down_block", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["4. B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4.B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4. BORROWER'S FAILURE", "4.BORROWER'S FAILURE", "4. <PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY", "4.<PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000852, "field_name": "First_Payment_Date_VA_325505", "document_id": 9000671, "output_format": {"date_parser_output_format": {"date_position": 1}}}, {"id": 80000850, "key": ["1. <PERSON><PERSON><PERSON><PERSON><PERSON>'S PROMISE TO PAY"], "direction": "down_block", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["INTEREST", "2.INTEREST", "2. INTEREST"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000850, "field_name": "Original_Loan_Amount_VA_326037", "document_id": 9000671, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"], "remove_spaces": true}}}, {"id": 80001196, "key": ["3. PAYMENTS", "3.PAYMENTS"], "direction": "down_block", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["4. B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4.B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4. BORROWER'S FAILURE", "4.BORROWER'S FAILURE", "4. <PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY", "4.<PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY"], "start_identifier": [], "possible_page_numbers": [1], "field_id": 80001196, "field_name": "Original_P_I_VA_326038", "document_id": 9000671, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"], "remove_spaces": true}}}, {"id": 80000853, "key": ["3.PAYMENTS", "3. PAYMENTS"], "direction": "down_block", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["4. B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4.B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4. BORROWER'S FAILURE", "4.BORROWER'S FAILURE", "4. <PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY", "4.<PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY"], "start_identifier": [""], "possible_page_numbers": [2], "field_id": 80000853, "field_name": "Original_Maturity_Date_VA_325504", "document_id": 9000671, "output_format": {"date_parser_output_format": {"date_position": 2}}}, {"id": 80011111, "key": ["(Property Address)", "[Property Address]", ";[Propel ty Address]", "[Property Address)"], "direction": "up", "type": "text", "return_type": "text", "multi_line_value": true, "probable_place": "just_above", "use_match": "fuzzy", "end_identifier": ["NOTE", "[State]", "[fie]", "[Stake]", "IStute", "(State)"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80011111, "field_name": "Property_Address_1_VA_326031", "document_id": 9000671, "output_format": {"string_operations_output_format": {"remove_from_end": ["NOTE", "[State]", "(State)"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}, "date_remover_output_format": {"custom": false}, "address_parser_output_format": {"get_line1_line2_city_state_zip": true, "from_field": "Property_Full_Address"}}}, {"id": 80000856, "key": ["B<PERSON>ROWER'S FAILURE TO PAY AS REQUIRED"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON><PERSON>", "(B) De<PERSON>ult"], "start_identifier": [""], "possible_page_numbers": [2], "field_id": 80000856, "field_name": "Late_Charge_Rate_VA_326040", "document_id": 9000671, "output_format": {"string_operations_output_format": {"contains": ["(\\d+\\.?\\d*)\\s*%"], "remove_from_end": ["\\s*%"], "remove_spaces": true}}}, {"id": 80000855, "key": ["B<PERSON>ROWER'S FAILURE TO PAY AS REQUIRED"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["calendar days", "<PERSON><PERSON><PERSON>", "(B) De<PERSON>ult", "calendardays"], "start_identifier": [""], "possible_page_numbers": [2], "field_id": 80000855, "field_name": "Late Charge Grace Days", "document_id": 9000671, "output_format": {"word_to_number_output_format": {}, "string_operations_output_format": {"contains": ["\\b(\\d+)\\b"], "remove_from_end": []}}}, {"id": 80001186, "key": ["B<PERSON>ROWER'S FAILURE TO PAY AS REQUIRED"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON><PERSON>", "(B) De<PERSON>ult"], "start_identifier": [""], "possible_page_numbers": [2], "field_id": 80001186, "field_name": "Late Charge Type", "document_id": 9000671, "output_format": {"Latechargetypeoutputformat": {"from_field": "Late Charge Type", "get_percentage": true, "get_amount": true}}}, {"id": 80012036, "key": ["Assignee"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": ["beginning on"], "possible_page_numbers": [1], "field_id": 80012036, "field_name": "Assignee_VA_326033", "document_id": 9000671}, {"id": 80012039, "key": ["Note Has Endorsement"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80012039, "field_name": "Note_Has_Endorsement_VA_326035", "document_id": 9000671}, {"id": 80012040, "key": ["Trustee"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["of"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80012040, "field_name": "Trustee_VA_326036", "document_id": 9000671}]