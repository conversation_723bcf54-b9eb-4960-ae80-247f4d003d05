[{"id": 80012124, "key": ["Homeowner Policy", "Homeowners Policy", "HOMEOWNER'S POLICY", "Homeowner’s Policy", "Owner's Policy", "OWNER'S POLICY", "Owner’s Policy", "Owners Policy", "Owner Policy", "ALTA Loan Policy", "Form T-1R", "ALTA Owner's", "ALTA Owner’s", "ALTA Owners", "ALTA Owner", "T-1R Form", "ALTA Residential", "With Florida Modification", "Owners Form", "STG"], "direction": "inline", "return_type": "text", "type": "text", "multi_line_value": false, "probable_type": "Footer", "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 80012124, "field_name": "Policy_Type_Text", "document_id": 9002236, "alternate_locations": [{"key": ["Homeowner Policy", "Homeowners Policy", "HOMEOWNER'S POLICY", "Homeowner’s Policy", "Owner's Policy", "OWNER'S POLICY", "Owner’s Policy", "Owners Policy", "Owner Policy", "ALTA Loan Policy", "Form T-1R", "ALTA Owner's", "ALTA Owner’s", "ALTA Owners", "ALTA Owner", "T-1R Form", "ALTA Residential", "With Florida Modification", "OWNER'S TITLE INSURANCE", "Owners Form", "STG"], "direction": "inline", "return_type": "text", "type": "text", "multi_line_value": false, "multi_page_value": false, "probable_type": "Header", "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 80012124, "field_name": "Policy_Type_Text", "document_id": 9002236}], "output_format": {"string_operations_output_format": {"remove_special_chars_from_end": true, "remove_special_chars_from_end_exceptions": [")"], "remove_special_chars_from_beginning": true, "remove_from_beginning": ["Attached to"], "remove_from_end": ["(?i)\\b- Schedule A\\b", "-- Schedule A", "- Schedule A", "- Schedule B", "- Sch A", "-- Sch A", "Schedule A", "Sch A", "Sch B", "FOR A ONE-TO-FOUR FAMILY RESIDENCE"]}}}, {"id": 80012126, "key": ["Name and address of title insurance company", "Names and address of title insurance company", "name and address of", "title insurance company"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["policy", "file"], "start_identifier": [""], "field_id": 80012126, "field_name": "Underwriter", "document_id": 9002236, "additional_info": {"nth_line": 1}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Stewart Title Guaranty Company"}}}, {"id": 80012125, "key": ["Owner's Policy of Title Insurance No:", "policy serial no", "Loan Policy number", "Loan Policy no", "Policy no", "Policy na", "policy number"], "direction": "right", "type": "text", "multi_line_value": false, "return_type": "text", "use_match": "fuzzy", "search_in_tables": true, "probable_place_in_table": ["same_row_value", "below_row_value"], "choose_row_in_table_based_on": "Full_Policy_Number", "probable_place": "Individual", "end_identifier": ["Loan", "No", "$", "Amount", "Effective", "Address", "file", "page", "policy", "date", "premium", "Agent ID", "Version"], "start_identifier": [""], "field_id": 80012125, "field_name": "Full_Policy_Number", "document_id": 9002236, "additional_info": {"alternate_locations": "in_footer", "found_val": []}, "post_process": {"remove_far_elements_right_one_line": {"custom": false}}, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "replace_from_beginning": [["D-", "O-"], ["Q-", "O-"]]}}}, {"id": 80012307, "key": ["Simultaneous Policy no", "Simultaneously with Policy No", "Si Policy No", "Issued with Policy No", "Loan Policy number", "Loan Policy no", "Issued with Policy Number", "S/I", "Reference No", "Other Policy", "Loan Policy Reference"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Loan", "No", "$", "Amount", "Effective", "Address", "file", "policy", "(<PERSON><PERSON>"], "start_identifier": [""], "field_id": 80012307, "field_name": "Reference_Full_Policy_Number", "document_id": 9002236, "additional_info": {"starts_with": ["M", "U", "m", "u"], "consider_only_starts_with": true, "found_val": []}, "output_format": {"string_operations_output_format": {"remove_spaces": true, "remove_from_beginning": ["(IfAny):"]}}, "post_process": {"remove_far_elements_right_one_line": {"custom": false}}}, {"id": 80012127, "key": ["File No", "File Number", "Contract No", "Title No", "Case Number"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "search_in_tables": true, "probable_place_in_table": ["same_row_value", "below_row_value"], "choose_row_in_table_based_on": "Full_Policy_Number", "probable_place": "Individual", "use_match": "fuzzy", "end_identifier": ["Policy", "Amount", "Premium", "ALTA"], "start_identifier": [""], "field_id": 80012127, "field_name": "File_Number", "document_id": 9002236, "additional_info": {"alternate_locations": "in_footer", "find_in_zone": true}, "post_process": {"remove_far_elements_right_one_line": {"custom": false}}, "output_format": {"schedule_a_output_format": {"custom": true}, "string_operations_output_format": {"remove_special_chars_from_end": true, "remove_special_chars_from_beginning": true, "remove_spaces": true}}}, {"id": 80012219, "key": ["Loan Number", "Loan No", "Loan#"], "direction": "right", "return_type": "text", "type": "text", "multi_line_value": false, "probable_place": "Individual", "use_match": "fuzzy", "end_identifier": ["Policy", "Amount", "Premium", "Contract", "street", "schedule", "alta", "of", "Date", "Address", "File", "Property"], "start_identifier": [""], "field_id": 80012219, "field_name": "Loan_Number", "document_id": 9002236, "post_process": {"remove_far_elements_right_one_line": {"custom": false}}, "output_format": {"schedule_a_output_format": {"custom": true}, "string_operations_output_format": {"remove_special_chars_from_end": true, "remove_special_chars_from_beginning": true}}}, {"id": 80012128, "key": ["Order Number", "Order No", "Agent Order"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Policy", "Amount", "Premium", "loan", "Contract", "street", "schedule", "alta", "of"], "start_identifier": [""], "additional_info": {"alternate_locations": "in_footer"}, "max_page_to_search": 1, "field_id": 80012128, "field_name": "Order_Number", "document_id": 9002236, "alternate_locations": [{"key": ["Order Number", "Order No", "Agent Order"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Policy", "Amount", "Premium", "loan", "Contract", "street", "schedule", "alta", "of"], "start_identifier": [""], "field_id": 80012128, "field_name": "Order_Number", "document_id": 9002236}], "post_process": {"remove_far_elements_right_one_line": {"custom": false}}, "output_format": {"schedule_a_output_format": {"custom": true}, "string_operations_output_format": {"remove_special_chars_from_end": true, "remove_special_chars_from_beginning": true}}}, {"id": 80012131, "key": ["street address of the property is", "street address of the properly is", "street address of the pmpeny is", "Address of Property"], "direction": "down_block", "type": "address", "return_type": "text", "multi_line_value": true, "end_identifier": ["County", "Tax Id", "The Company does not", "Countersigned", "Authorized Countersignature", "Copyright", "File No", "Page", "Condo/Subdiv", "This policy"], "start_identifier": [""], "field_id": 80012131, "field_name": "Property_Full_Address", "document_id": 9002236, "alternate_locations": [{"id": 80012131, "key": ["Address Reference", "Address for reference only", "Property Address of the Land", "Street Address of the land", "Property Address", "Address of Property", "Street Address of Land", "Street Address of the", "Reference: Address", "Address:", "Exhibit \"A\" (commonly known as", "Commonly known as:", "Premises known as"], "direction": "right", "type": "address", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Amount", "name", "premium", "Loan Number", "1. Name", "Issued by", "Pin/Tax"], "start_identifier": [""], "field_id": 80012131, "field_name": "Property_Full_Address", "document_id": 9002236}], "post_process": {"one_of": false, "remove_next_line_if_starts_with_point": {"custom": false}, "address_splitter_post_processor": {"return_first": true}}, "output_format": {"schedule_a_output_format": {"custom": true}, "string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["of the Land:", "(For identification purposes only):"], "remove_from_end": ["Reference:"], "clean_address": ["Street Name", "City/State/Zip", "City/State/Zipc", "Mitchell Subdivision"]}}, "sub_keys": ["address_information"]}, {"id": 80012136, "key": ["Amount of Insurance", "AMOUNT OF POLICY", "Policy Amount", "Amount of Ins"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "search_in_tables": true, "probable_place_in_table": ["same_row_value", "below_row_value"], "choose_row_in_table_based_on": "Full_Policy_Number", "probable_type": "Individual", "probable_place": "individual", "end_identifier": ["premium", "loan", "date", "policy", "Total Charge", "Order"], "start_identifier": [""], "field_id": 80012136, "field_name": "Policy_Liability", "document_id": 9002236, "alternate_locations": [{"key": ["Amount of Insurance", "Policy Amount", "Amount of Ins"], "direction": "just_down", "type": "amount", "return_type": "text", "multi_line_value": false, "probable_type": "Individual", "probable_place": "individual", "end_identifier": ["premium", "loan", "date", "policy", "Total Charge"], "start_identifier": [""], "field_id": 80012136, "field_name": "Policy_Liability", "document_id": 9002236}], "post_process": {"remove_far_elements_right_one_line": {"custom": false}}, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$"], "remove_substrings": true}}}, {"id": 80012129, "key": ["Date of policy", "Effective Date", "Date/Time of Policy", "Policy Date and Time", "Policy date", "Date of p"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": false, "search_in_tables": true, "probable_place_in_table": ["same_row_value", "below_row_value"], "choose_row_in_table_based_on": "Full_Policy_Number", "probable_place": "individual", "end_identifier": ["AM", "PM", "Name", "premium", "amount", "simultaneous"], "start_identifier": [""], "field_id": 80012129, "field_name": "Effective_Date", "document_id": 9002236, "alternate_locations": [{"key": ["Date of policy", "Effective Date", "Date/Time of Policy", "Policy Date and Time", "Policy date", "Date of p"], "direction": "just_down", "type": "date", "return_type": "text", "multi_line_value": false, "probable_place": "individual", "end_identifier": ["AM", "PM", "Name", "premium", "amount", "simultaneous"], "start_identifier": [""], "field_id": 80012129, "field_name": "Effective_Date", "document_id": 9002236}], "output_format": {"schedule_a_output_format": {"custom": true}, "date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 80012130, "key": ["Premium(Risk Rate)", "Premium", "Total Charge"], "direction": "right", "type": "number", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Date", "policy", "1. Name"], "start_identifier": [""], "field_id": 80012130, "field_name": "Policy_Premium", "document_id": 9002236, "output_format": {"schedule_a_output_format": {"custom": true}, "string_operations_output_format": {"remove_from_beginning": ["\\s*\\$", "^.*?\\$"], "remove_special_chars_from_end": true, "remove_special_chars_from_beginning": true, "remove_substrings": true, "remove_from_end": ["[a-zA-Z][a-zA-Z0-9]*$"]}}}, {"id": 80012137, "key": ["Name of Insured", "The Insured is"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["The estate", "your interest in", "we insure", "2. The", "2. "], "start_identifier": [], "field_id": 80012137, "field_name": "Insured_Full_Name", "document_id": 9002236, "alternate_locations": [{"key": ["Name of Insured", "The Insured is"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["The estate", "your interest in", "we insure", "2. The", "2. "], "start_identifier": [], "field_id": 80012137, "field_name": "Insured_Full_Name", "document_id": 9002236}], "additional_info": {"nth_line": 1}, "output_format": {"string_operations_output_format": {"remove_from_end": ["\\s+\\d\\.?\\s*", "^\\*?PATCH\\*?"], "remove_from_beginning": ["^\\*?PATCH\\*?"]}}, "post_process": {"remove_next_line_if_starts_with_point": {"custom": false}}}, {"id": 80012138, "key": ["The estate or interest", "estate or interest in the Land", "we insure your interest", "your interest"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Title is vested in", "title to the estate", "the land referred", "legal description", "3. Title is insured as vested"], "start_identifier": [""], "field_id": 80012138, "field_name": "Interest_Estate", "document_id": 9002236, "additional_info": {"search_in_key": true}, "post_process": {"string_operations_post_processor": {"contains": ["fee simple", "fee surface", "leasehold ownership", "fee/easement", "Contract Purchaser", "feesimple", "fee", "easement", "leashold", "equitable", "leasehold"]}}}, {"id": 80012139, "key": ["Title is vested in", "title to the estate", "land is vested in", "vested in", "Title is tested in"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["The land referred to in this policy", "Legal description of land", "The land is described as follows", "Property is located", "None", "4.The land", "4. The land", "4. The", "4. <PERSON><PERSON>", "The land", "4. Th", "3. Th"], "start_identifier": [""], "field_id": 80012139, "field_name": "Vesting", "document_id": 9002236, "alternate_locations": [{"key": ["vested in"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": ["The land referred to in this policy", "Legal description of land", "The land is described as follows", "None", "4.The land", "4. The land", "4. The", "4. <PERSON><PERSON>", "The land", "4. Th", "3. Th"], "start_identifier": [""], "field_id": 80012139, "field_name": "Vesting", "document_id": 9002236}], "additional_info": {"nth_line": 1}, "post_process": {"remove_next_line_if_starts_with_point": {"custom": false}}, "output_format": {"schedule_a_output_format": {"custom": true}, "copy_extraction_items_output_format": {"swap_with": "Insured_Full_Name", "found_str": ["insured", "The Named Insured"]}}}, {"id": 80012140, "key": ["The Land herein described is encumbered ", "The Insured Mortgage and its assignments", "The insured mortgage and assignments", "The Insured Mortgage and the assignments", "The insured Deed of Trust and its assignments", "The Security Instrument", "The mortgage and assignments", "The Insured Mortgage or Deed of Trust and assignments", "The mortgage, herein referred to as the insured mortgage,", "The mortgage or deed of trust and assignments", "The insured Security Deed and its assignments", "The Insured premises is"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["The Land referred to in this policy", "The Land referred to in this policy is described as follows:", "The land is described as follows", "COUNTERSIGNED", "Property Address", "5. Address of Property", "Address of Property", "This policy valid only if", "None", ""], "start_identifier": [""], "field_id": 80012140, "field_name": "Loan_Recording_Information", "document_id": 9002231, "additional_info": {"nth_line": 1}, "post_process": {"remove_next_line_if_starts_with_point": {"custom": false}}, "output_format": {"schedule_a_output_format": {"custom": true}, "string_operations_output_format": {"remove_till_from_beginning": ["follows", "assignments"], "remove_from_beginning": ["mortgages or trust deeds shown on Schedule B herein", "assignments", "assignments:", "and any shown on Schedule B", "as follows", "Street address of the land"], "remove_special_chars_from_beginning": true}}}, {"id": 80012147, "key": ["The land referred to in this policy", "Legal description of land", "The land is described as follows", "The land is situated in the", "The Land referred to herein"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "multi_page_value": true, "exclude_footer": true, "end_identifier": ["Address of Property (For Identification purposes only)", "Property Address as shown", "For Company Reference Purposes Only", "FOR COMPANY REFERENCE PURPOSE ONLY", "POR COMPANY REFERENCE PURPOSE ONLY", "(End of Legal Description)", "End of Legal Description", "copyright", "Authorized Countersignature", "Authorized Signatory", "The company will be compensated", "stewart title guaranty company", "File No.", "This Policy valid only if"], "start_identifier": [""], "field_id": 80012147, "field_name": "Legal_Description_Text", "document_id": 9002236, "additional_info": {"nth_line": 1}, "sub_keys": ["legal_subdivision_information"], "output_format": {"copy_extraction_items_output_format": {"swap_with": "Legal_Description_Text_Exhibit", "is_empty": true, "found_str": ["exhibit \"a\"", "exhibit\"a\"", "exhibit a", "exhibita", "schedule c", "schedule a", "Appendix A", "schedule \"a\"", "SCHEDULE \"C\"", "SCHEDULE “C”", "SEE DESCRIPTION SHEET", "ATTACHED LEGAL DESCRIPTION", "LEGAL DESCRIPTION ATTACHED"]}, "string_operations_output_format": {"remove_from_beginning": ["(Legal Description)", "and is described as follows:", "described as follows:", "Exhibit A", "Exhibit A - Legal Description", "- LEGAL DESCRIPTION"], "remove_till_from_beginning": ["follows:"], "remove_special_chars_from_beginning": true, "remove_from_end": ["(End of Legal Description)", "End of Legal Description"]}}}, {"id": 27, "key": ["Exhibit a legal description", "ADDENDUM", "ADDENDUM/Exhibit A", "EXHIBIT \"A\"", "Exhibit A", "SCHEDULE C", "SCHEDULE “C”", "SCHEDULE \"C\"", "Appendix A", "LEGAL DESCRIPTION", "SCHEDULE A - DESCRIPTION"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "multi_page_value": true, "exclude_footer": true, "probable_type": "Header", "max_cblock_iter": 4, "use_mst": true, "use_mst_for_start_identifiers": true, "page_range_before": -1, "use_sentence_transformer": true, "end_identifier": ["copyright", "For Company Reference Purposes Only", "FOR COMPANY REFERENCE PURPOSE ONLY", "POR COMPANY REFERENCE PURPOSE ONLY", "End of Legal Description", "FOR INFORMATION ONLY", "End Schedule A Description", "End of Exhibit", "American Land Title Association", ""], "start_identifier": ["LEGAL DESCRIPTION", "Policy Number", "policy no", "file number", "file no", "/Exhibit A", "DESCRIPTION"], "start_identifier_down": true, "field_id": 60000012, "field_name": "Legal_Description_Text_Exhibit", "document_id": 12345678, "additional_info": {"nth_line": 0}, "sub_keys": ["legal_subdivision_information"], "output_format": {"string_operations_output_format": {"remove_from_beginning": ["(Legal Description)", "LEGAL DESCRIPTION", "Exhibit A - Legal Description", "- LEGAL DESCRIPTION"], "remove_special_chars_from_beginning": true, "remove_from_end": ["(End of Legal Description)", "End of Legal Description"]}}}, {"id": 80012140, "key": ["Exhibit a legal description", "Exhibit A", "Exhibit", "LEGAL DESCRIPTION"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "probable_type": "Header", "max_cblock_iter": 4, "use_mst": true, "use_mst_for_start_identifiers": true, "end_identifier": ["copyright", ""], "start_identifier": ["legal description", "Policy Number", "policy no", "file number", "file no"], "field_id": 80012140, "field_name": "Loan_Recording_Information_Exhibit", "document_id": 9002236, "additional_info": {"nth_line": 1}, "sub_keys": ["legal_subdivision_information"]}, {"id": 80014059, "key": ["loan policy schedule b", "This policy does not insure against loss or damage", "homeowners' policy schedule b", "OWNER'S POLICY SCHEDULE B", "schedule b", "- OWNERS SCHEDULE B", "SCHEDULE \"B\"", "OWNER'S SCHEDULE B", "homeowner's policy schedule b"], "direction": "down_multi_page", "type": "text", "return_type": "text", "multi_line_value": true, "multi_page_value": true, "include_key": true, "probable_place": "Individual", "probable_type": "Header", "exclude_footer": true, "merge_with_sub_item": 1, "use_bullet_point_match": true, "save_page_no_for_next_key": true, "page_range_before": "to_start", "reprocess_if_not_found": true, "search_key_in_multi_page": true, "max_cblock_iter": 4, "end_identifier": ["copyright", "end of exceptions", "end of schedule", "Countersigned", "countersignature", "California Land Title Association. All rights reserved.", "MISSOURI ARBITRATION ENDORSEMENT"], "start_identifier": ["SCHEDULE B"], "field_id": 80014059, "field_name": "Exception_Header", "document_id": 9002236, "additional_info": {"search_key": ["SCHEDULE B"]}, "output_format": {"exception_output_format": {"custom": true}}, "sub_items": [{"id": 80012841, "key": ["reason of", "insured mortgage", "resulting from", "resultingfrom"], "type": "text", "use_match": "fuzzy", "return_type": "table", "start_identifier": ["insured mortgage", "resulting from", "policy number", "policy no", "file number", "exceptions", "1.", "stewart title guaranty company"], "field_id": 80012841, "field_name": "Exception_Text", "document_id": 9002236, "additional_info": {"split_by_bullets": true}, "sub_items": [{"id": 80012841, "type": "text", "return_type": "table", "key": [], "start_identifier": [], "field_id": 80012841, "field_name": "Exception_Type", "document_id": 9002236, "sub_item_processor": {"exception_type_processor": {"custom": true}}}]}]}, {"id": 80014060, "key": ["endorsement", "arbitration endorsements", "arbitration endorsement", "CO GAP OWNER", "CO GEC INFLATION", "OP DELETION OF EXCEPTION"], "direction": "inline", "return_type": "table", "type": "text", "multi_line_value": false, "multi_page_value": true, "probable_type": "Header", "search_key_in_multi_page": true, "search_key_in_all_pages": true, "max_cblock_iter": 4, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 80014060, "field_name": "Endorsement_Type", "document_id": 9002236, "output_format": {"table_output_format": {"remove_mostly_lower": true, "remove_duplicate_global": true}}}, {"key": ["Countersigned by", "Countersigned", "Authorized Signature", "Authorized Countersignature", "Authorized Signatory"], "direction": "down_block", "type": "text", "use_match": "fuzzy", "multi_line_value": true, "start_identifier": [""], "end_identifier": ["Copyright", "Policy number", "Policy serial", "page", "part 1 of", "Serial No", "If you want information", "Exclusions", "Exclusions from coverage", "File No", "File Number", "1.", "2.", "3.", "4.", "5.", "6.", "7.", "8.", "9.", "10."], "page_range_before": "to_start", "id": 80012124, "field_id": 80012124, "field_name": "Title_Company", "document_id": 9002236, "output_format": {"ner_service_req_output_format": {"text_field_name": "Title_Company", "label": ["ORGANIZATION"]}}}, {"id": 28, "key": ["For Company Reference Purpose Only", "For Company Reference Purposes Only", "Company Reference Purpose"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "multi_page_value": false, "max_cblock_iter": 4, "page_range_before": "to_start", "end_identifier": ["copyright", "Countersigned", "Authorized Signature", "Authorized Countersignature", "The Company does not", ""], "start_identifier": [""], "field_id": 60003212, "field_name": "Legal_Description_Text_Footer", "document_id": 12345678}, {"id": 80014043, "key": ["Tax I. D. Number", "Tax ID Number", "TAX PARCEL NO", "Tax Map No", "Tax I. D. #", "Pin/Tax ID#", "Serial Number", "Tax ID No", "Tax ID", "Agent ID", "Parcel ID", "RPC Number", "PIN/APN", "PIN/Tax", "PinfTax", "Parcel ID / Tax ID Number", "PPN", "TMS", "APN", "A.P.N.", "PIDN", "Folio No", "Pa<PERSON>el <PERSON>", "Parcel No", "PP #", "Tax 1D#", "P1N/APN", "(PIN:", "PIN:"], "direction": "right", "type": "number", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["", ",", "The", "being", "number", "Property", "Policy Number", "Issued by", "Otter Tail", "county", "and", "This policy", ";"], "start_identifier": [""], "field_id": 80014043, "field_name": "Property_APN", "document_id": 9002236, "alternate_locations": [{"key": ["PIN", "ID:", "S/B/L", "GEO #", "GEO", "Gpin", "Serial No"], "direction": "right", "type": "number", "multi_line_value": false, "probable_place": "Individual", "use_match": "fuzzy", "end_identifier": ["", ",", "The", "being", "number", "Property", "Policy Number", "Issued by", "Otter Tail", "county", "This policy", ";"], "start_identifier": [""], "field_id": 80014043, "field_name": "Property_APN", "document_id": 9002236}], "output_format": {"string_operations_output_format": {"remove_till_from_beginning": [":"], "remove_special_chars_from_end": true, "remove_special_chars_from_beginning": true}}}]