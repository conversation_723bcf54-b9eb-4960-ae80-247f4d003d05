[{"id": 3000033, "key": ["REAL ESTATE LIEN NOTE"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["For value"], "start_identifier": [], "field_id": 3000033, "field_name": "note_loan_amount", "document_id": 9000671}, {"id": 3000024, "key": ["Managing Member"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["seal"], "start_identifier": [], "field_id": 3000024, "field_name": "note_borrower_sign", "document_id": 9000671, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000027, "key": ["Managing Member"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["seal"], "start_identifier": [], "field_id": 3000027, "field_name": "note_coborrower_sign", "document_id": 9000671, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000031, "key": ["Case Number:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["BORROWER'S PROMISE TO PAY", "<PERSON><PERSON><PERSON>'s Promise"], "start_identifier": [""], "field_id": 3000031, "field_name": "note_case_number", "document_id": 9000671}, {"id": 3000034, "key": ["Note"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER'S PROMISE TO PAY", "<PERSON><PERSON><PERSON>'s Promise"], "start_identifier": [""], "field_id": 3000034, "field_name": "note_closing_date", "document_id": 9000671, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000036, "key": ["(Property Address)", "[Property Address]", ";[Propel ty Address]", "[Property Address)", "[Propei ty Addi ess]"], "direction": "up", "type": "text", "return_type": "text", "multi_line_value": true, "probable_place": "just_above", "use_match": "fuzzy", "end_identifier": ["NOTE", "[State]", "[fie]", "[Stake]", "IStute", "(State)"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000036, "field_name": "note_street_address", "document_id": 9000671, "output_format": {"string_operations_output_format": {"remove_from_end": ["NOTE", "[State]", "(State)"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}, "date_remover_output_format": {"custom": false}, "address_parser_output_format": {"get_line1_and_line2": true, "from_field": "note_street_address"}}}, {"id": 3000037, "key": ["(Property Address)", "[Property Address]", ";[Propel ty Address]", "[Property Address)", "[Propei ty Addi ess]"], "direction": "up", "type": "text", "return_type": "text", "multi_line_value": true, "probable_place": "just_above", "use_match": "fuzzy", "end_identifier": ["NOTE", "[State]", "[fie]", "[Stake]", "IStute", "(State)"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000037, "field_name": "note_city", "document_id": 9000671, "output_format": {"string_operations_output_format": {"remove_from_end": ["NOTE", "[State]", "(State)"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}, "date_remover_output_format": {"custom": false}, "address_parser_output_format": {"get_city": true, "from_field": "note_city"}}}, {"id": 3000038, "key": ["(Property Address)", "[Property Address]", ";[Propel ty Address]", "[Property Address)", "[Propei ty Addi ess]"], "direction": "up", "type": "text", "return_type": "text", "multi_line_value": true, "probable_place": "just_above", "use_match": "fuzzy", "end_identifier": ["NOTE", "[State]", "[fie]", "[Stake]", "IStute", "(State)"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000038, "field_name": "note_state", "document_id": 9000671, "output_format": {"string_operations_output_format": {"remove_from_end": ["NOTE", "[State]", "(State)"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}, "date_remover_output_format": {"custom": false}, "address_parser_output_format": {"get_state": true, "from_field": "note_state"}}}, {"id": 3000039, "key": ["(Property Address)", "[Property Address]", ";[Propel ty Address]", "[Property Address)", "[Propei ty Addi ess]"], "direction": "up", "type": "text", "return_type": "text", "multi_line_value": true, "probable_place": "just_above", "use_match": "fuzzy", "end_identifier": ["NOTE", "[State]", "[fie]", "[Stake]", "IStute", "(State)"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000039, "field_name": "note_zip_code", "document_id": 9000671, "output_format": {"string_operations_output_format": {"remove_from_end": ["NOTE", "[State]", "(State)"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}, "date_remover_output_format": {"custom": false}, "address_parser_output_format": {"get_zip_code": true, "from_field": "note_zip_code"}}}, {"id": 3000044, "key": ["INTEREST"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["The interest rate", " interest rate"], "start_identifier": ["rate of"], "possible_page_numbers": [1], "field_id": 3000044, "field_name": "note_interest_rate", "document_id": 9000671, "output_format": {"string_operations_output_format": {"contains": ["(\\d+\\.?\\d*)\\s*%"], "remove_from_end": ["\\s*%"], "remove_special_chars_from_end": true}}}, {"id": 3000029, "key": ["THE UNDERSIGNED."], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Loan Originator"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000029, "field_name": "note_borrower_name", "document_id": 9000671, "output_format": {"name_parser_output_format": {"get_borrower_name": true, "from_field": "note_borrower_name"}}}, {"id": 3000030, "key": ["THE UNDERSIGNED."], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Loan Originator"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000030, "field_name": "note_coborrower_name", "document_id": 9000671, "output_format": {"name_parser_output_format": {"get_co_borrower_name": true, "from_field": "note_coborrower_name"}}}, {"id": 3000927, "key": ["Amount of Monthly Payments"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [".This payment", "4. BORROWER"], "start_identifier": ["the amount of U.S."], "possible_page_numbers": [1], "field_id": 3000927, "field_name": "note_pi_amount", "document_id": 9000671, "alternate_locations": [{"id": 3000927, "key": ["Amount of Monthly Payments", "the amount of U.S.", "amount of U. S. $", "the amount of U.S. $"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["This payment"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000927, "field_name": "note_pi_amount", "document_id": 9000671}, {"id": 3000927, "key": ["My Monthly Payment will be in the amount of U.S."], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [". This payment"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000927, "field_name": "note_pi_amount", "document_id": 9000671}], "post_process": {"amount_extractor_post_processor": {}}, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3000928, "key": ["If, on", "If. on", "[f, on"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["I still owe"], "start_identifier": [""], "possible_page_numbers": [2], "field_id": 3000928, "field_name": "note_maturity_date", "document_id": 9000671, "alternate_locations": [{"id": 3000928, "key": ["before Principal. If, on", "Principal. If, on"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["I still owe"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000928, "field_name": "note_maturity_date", "document_id": 9000671}]}]