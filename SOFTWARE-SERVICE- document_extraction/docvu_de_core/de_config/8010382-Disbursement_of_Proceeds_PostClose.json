[{"id": 3006095, "key": ["Dated this"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["page"], "start_identifier": [""], "field_id": 3006095, "field_name": "Borrower Signature Name", "document_id": 8010382, "output_format": {"ner_service_req_output_format": {"text_field_name": "Borrower Signature Name", "label": ["name"]}}}, {"id": 3006096, "key": ["Dated this"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["page"], "start_identifier": [""], "field_id": 3006096, "field_name": "Co-Borrower Signature Name", "document_id": 8010382, "output_format": {"name_parser_output_format": {"get_disburs_coborrower_name": true, "from_field": "Co-Borrower Signature Name"}, "ner_service_req_output_format": {"text_field_name": "Co-Borrower Signature Name", "label": ["name"]}}}, {"id": 800, "key": ["hereby authorize and instruct WFG"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Bank Name"], "start_identifier": [""], "field_id": 800, "field_name": "wire_save_section", "sub_keys": ["Disbursement_of_Proceeds_PostClose_omr"], "document_id": 8010382}, {"id": 3005935, "key": ["bank name"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["bank address"], "start_identifier": [""], "field_id": 3005935, "field_name": "bank_name", "document_id": 8010382}, {"id": 3005936, "key": ["bank address"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["bank phone number"], "start_identifier": [""], "field_id": 3005936, "field_name": "bank_address", "document_id": 8010382}, {"id": 3005937, "key": ["bank phone number"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["bank aba routing number"], "start_identifier": [""], "field_id": 3005937, "field_name": "bank_phone", "document_id": 8010382}, {"id": 3005938, "key": ["bank aba routing number"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["bank account number"], "start_identifier": [""], "field_id": 3005938, "field_name": "bank_routing_number", "document_id": 8010382}, {"id": 3005939, "key": ["bank account number"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["name on account to credit"], "start_identifier": [""], "field_id": 3005939, "field_name": "bank_account_number", "document_id": 8010382}, {"id": 3005940, "key": ["name on account to credit"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["intermediary bank", "bank"], "start_identifier": [""], "field_id": 3005940, "field_name": "bank_holder_name", "document_id": 8010382}, {"id": 3005925, "key": ["STATE OF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["county", "this", "certificate", "on this", "that", "and", "being"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3005925, "field_name": "disbursement_of_proceeds_state_filed", "document_id": 8010382}, {"id": 3005926, "key": ["COUNTY OF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["THIS", "this instrument"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3005926, "field_name": "disbursement_of_proceeds_county", "document_id": 8010382}, {"id": 3005927, "key": ["before me this"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["by", "notary public"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3005927, "field_name": "disbursement_of_proceeds_execution_date", "document_id": 8010382}, {"id": 3005928, "key": ["notary public", "Juk Public"], "direction": "up", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "probable_place": "Individual", "field_id": 3005928, "field_name": "disbursement_of_proceeds_notary_acknowledgment", "document_id": 8010382, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3005930, "key": ["My Commission Expires", "My Comm Expires"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3005930, "field_name": "disbursement_of_proceeds_notary_commission_expires_date", "document_id": 8010382}, {"id": 3005932, "key": ["My Commission Expires", "My Comm Expires"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3005932, "field_name": "disbursement_of_proceeds_printed_commission_expires_date", "document_id": 8010382}, {"id": 3005929, "key": ["This instrument was acknowledged before me", "My Commission Expires", "My Comm Expires"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3005929, "field_name": "disbursement_of_proceeds_notary_seal", "document_id": 8010382, "post_process": {"check_for_seal_post_processor": {}}}, {"id": 3005924, "key": ["This instrument was acknowledged before me", "My Commission Expires", "My Comm Expires"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3005924, "field_name": "disbursement_of_proceeds_appeared_before", "document_id": 8010382, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "disbursement_of_proceeds_appeared_before", "label": ["name"]}}}]