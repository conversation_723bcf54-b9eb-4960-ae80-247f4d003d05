[{"id": 3000495, "key": ["private sector"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000495, "field_name": "no_hud_warranty_disclosure_borrower_sign", "document_id": 9000670, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000498, "key": ["private sector"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000498, "field_name": "no_hud_warranty_disclosure_coborrower_sign", "document_id": 9000670, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000496, "key": ["private sector"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["NHW.LSR"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000496, "field_name": "no_hud_warranty_disclosure_borrower_date", "document_id": 9000670, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000499, "key": ["private sector"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["NHW.LSR"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000499, "field_name": "no_hud_warranty_disclosure_coborrower_date", "document_id": 9000670, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]