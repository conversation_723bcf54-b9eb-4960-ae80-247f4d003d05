[{"id": 3000585, "key": ["I HEREBY CERTIFY"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000585, "field_name": "va260592_counseling_checklist_borrower_sign", "document_id": 9001003, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000586, "key": ["I HEREBY CERTIFY"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000586, "field_name": "va260592_counseling_checklist_borrower_date", "document_id": 9001003, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}, {"id": 3000588, "key": ["I HEREBY CERTIFY"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000588, "field_name": "va260592_counseling_checklist_coborrower_sign", "document_id": 9001003, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000589, "key": ["I HEREBY CERTIFY"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000589, "field_name": "va260592_counseling_checklist_coborrower_date", "document_id": 9001003, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]