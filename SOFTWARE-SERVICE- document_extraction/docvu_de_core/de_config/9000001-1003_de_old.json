[{"id": 101, "key": ["Legal"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["original cost"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 103, "field_name": "occupancy_checkbox_save_section", "sub_keys": ["occupancy_old_omr"], "document_id": 9000001}, {"id": 80001336, "key": ["DOB", "<PERSON><PERSON><PERSON>'s Name", "Home Phone"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Married"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80001336, "field_name": "Borrower Date of Birth", "document_id": 9000001}, {"id": 80003214, "key": ["DOB", "<PERSON><PERSON><PERSON>'s Name", "Home Phone"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Married"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80003214, "field_name": "Co-Borrower Date of Birth", "document_id": 9000001, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}, {"id": 80000982, "key": ["ACKNOWLEDGEMENT AND AGREEMENT"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["INFORMATION FOR GO<PERSON><PERSON><PERSON>MENT MONITORING PURPOSES"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000982, "field_name": "Application Date", "document_id": 9000001, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]