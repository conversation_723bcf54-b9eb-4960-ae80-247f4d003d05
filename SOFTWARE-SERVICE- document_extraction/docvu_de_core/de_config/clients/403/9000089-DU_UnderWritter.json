[{"id": 3004752, "key": ["Recommendation"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Primary Borrower"], "start_identifier": [""], "field_id": 3004752, "field_name": "3004752 -  LP Result/DU Result", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_numbers": true, "remove_special_chars": true, "remove_from_beginning": ["^\\d+"], "remove_from_end": ["\\d+$"], "retain_only_alpha": true}}}, {"id": 3005735, "key": ["Submission Date"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["DU Version"], "start_identifier": [], "field_id": 3005735, "field_name": "3005735 - Document Date", "document_id": 9000089}, {"id": 3002152, "key": ["Note Rate"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Loan Type"], "start_identifier": [""], "field_id": 3002152, "field_name": "3002152 - Interest Rate", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\.", "\\s*%"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3004751, "key": ["<PERSON>an <PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Amortization Type"], "start_identifier": [""], "field_id": 3004751, "field_name": "3004751 - <PERSON><PERSON> (Months)", "document_id": 9000089}, {"id": 3002599, "key": ["Amortization Type"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>"], "start_identifier": [""], "field_id": 3002599, "field_name": "3002599  - Amortization Type", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S", "\\s*v$"]}}}, {"id": 3002146, "key": ["Sales Price (purchase transactions)", "(purchase"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>", "transactions)"], "start_identifier": [""], "field_id": 3002146, "field_name": "3002146 - Sales Price", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["[^0-9.,]+$"]}}}, {"id": 3004750, "key": ["<PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Property Information", "Qualifying Rate"], "start_identifier": [], "field_id": 3004750, "field_name": "3004750 - Refinance Purpose", "document_id": 9000089}, {"id": 3004754, "key": ["First P&I (Qualifying)", " (Qualifying)"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Negative Net", "Second P&I"], "start_identifier": [], "field_id": 3004754, "field_name": "3004754 - Monthly PI", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"]}}}, {"id": 1001, "key": ["PROPOSED MONTHLY PAYMENT"], "direction": "down_multi_page", "type": "text", "return_type": "text", "probable_place": "individual", "multi_line_value": true, "multi_page_value": true, "use_match": "fuzzy", "end_identifier": ["Funds"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 1001, "field_name": "Proposed_Monthly_Payment_save", "sub_keys": ["UW_Mortgage_insurance_save"], "document_id": 9000001, "additional_info": {"search_key": ["Mortgage Insurance"]}}]