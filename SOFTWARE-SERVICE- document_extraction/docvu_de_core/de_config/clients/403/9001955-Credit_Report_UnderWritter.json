[{"id": 3004740, "key": ["DATA SOURCES SCORE INFORMATION"], "type": "table_new", "end_identifier": ["SCORE RANGE"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004740, "field_name": "3004740 - Equifax", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "FACTA", "field_string_col": "FACTA", "probable_field_value_col": "FACTA", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": 1}}, "table_header_list": ["Item #", "Product Score", "Factor Information", "Data Source", "Applicant Identifier"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^\\["], "remove_from_end": ["\\]$"], "remove_spaces": true}}}, {"id": 3004739, "key": ["DATA SOURCES SCORE INFORMATION"], "type": "table_new", "end_identifier": ["TRANS UNION FICO"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004739, "field_name": "3004739 - <PERSON><PERSON><PERSON>", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "EXPERIAN FAIR ISAAC V2", "field_string_col": "EXPERIAN FAIR ISAAC V2", "probable_field_value_col": "EXPERIAN FAIR ISAAC V2", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": 1}}, "table_header_list": ["Item #", "Product Score", "Factor Information", "Data Source", "Applicant Identifier"], "table_structure": "vertical"}, {"id": 3004741, "key": ["DATA SOURCES SCORE INFORMATION"], "type": "table_new", "end_identifier": [], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004741, "field_name": "3004741 - Transunion", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "SCORE CLASSIC", "field_string_col": "SCORE CLASSIC", "probable_field_value_col": "SCORE CLASSIC", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": 1}}, "table_header_list": ["Item #", "Product Score", "Factor Information", "Data Source", "Applicant Identifier"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^.*?:\\s*"], "remove_from_end": ["\\D.*$"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 101, "key": ["CREDIT HISTORY"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 101, "field_name": "dummy_section1_1", "document_id": 9001955}, {"id": 3005721, "key": ["<PERSON><PERSON><PERSON>"], "type": "table_new", "end_identifier": ["SCORE RANGE"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3005721, "field_name": "3005721 - Equifax", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "FACTA", "field_string_col": "FACTA", "probable_field_value_col": "FACTA", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": 1}}, "table_header_list": ["Item #", "Product Score", "Factor Information", "Data Source", "Applicant Identifier"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S", "^\\["], "remove_from_end": ["\\s*", "\\.", "\\]"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3005720, "key": ["<PERSON><PERSON><PERSON>"], "type": "table_new", "end_identifier": ["TRANS UNION FICO"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3005720, "field_name": "3005720 - <PERSON><PERSON><PERSON>", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "EXPERIAN", "field_string_col": "EXPERIAN", "probable_field_value_col": "EXPERIAN", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": 1}}, "table_header_list": ["Item #", "Product Score", "Factor Information", "Data Source", "Applicant Identifier"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^.*?:\\s*"], "remove_from_end": ["\\D.*$"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3005722, "key": ["<PERSON><PERSON><PERSON>"], "type": "table_new", "end_identifier": ["CREDIT HISTORY"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3005722, "field_name": "3005722 - Transunion", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "SCORE CLASSIC", "field_string_col": "SCORE CLASSIC", "probable_field_value_col": "SCORE CLASSIC", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": 1}}, "table_header_list": ["Item #", "Product Score", "Factor Information", "Data Source", "Applicant Identifier"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^.*?:\\s*"], "remove_from_end": ["\\D.*$"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004743, "key": ["AKA INFORMATION"], "type": "table_new", "return_type": "table", "end_identifier": ["EMPLOYMENT INFORMATION"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004743, "field_name": "3004743  - AKA Names", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Names", "field_string_col": "Names", "probable_field_value_col": "Names", "probable_place_in_table": "below_row", "probable_field_value_next_row": 3, "value_return_as_table": true}}, "table_header_list": ["Item #", "Names", "Data Source", "Applicant Identifier"], "table_structure": "vertical"}, {"id": 3004793, "key": ["Order#"], "direction": "up", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ADDRESS INFORMATION"], "start_identifier": ["INQUIRIES MADE IN THE LAST 120 DAYS"], "field_id": 3004793, "field_name": "3004793 - Institution Name", "document_id": 9001955}, {"id": 3004791, "key": ["Order#"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["C<PERSON> <PERSON>an"], "start_identifier": [""], "field_id": 3004791, "field_name": "3004791 - Credit Report Number:", "document_id": 9001955, "output_format": {"string_operations_output_format": {"remove_from_end": ["\\s*\\d{2}/\\d{2}/\\d{4}$"]}}}, {"id": 3004792, "key": ["Order#"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["C<PERSON> <PERSON>an"], "start_identifier": [], "field_id": 3004792, "field_name": "3004792 - Credit Report Date:", "document_id": 9001955}, {"id": 3004794, "key": ["Inquiry Date"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ADDRESS INFORMATION"], "start_identifier": [], "field_id": 3004794, "field_name": "3004794 - Inquiry Date", "document_id": 9001955}, {"id": 3004795, "key": ["ADDRESS INFORMATION"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["CURRENT"], "start_identifier": ["Address"], "field_id": 3004795, "field_name": "3004795  - Address Reported", "document_id": 9001955, "output_format": {"address_parser_output_format": {"get_line1_line2_city_state_zip": true, "from_field": "3004795  - Address Reported"}}}, {"id": 3004796, "key": ["ADDRESS INFORMATION"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["FORMER"], "start_identifier": ["Date Reported", "Reported"], "field_id": 3004796, "field_name": "3004796 - Month & Year Reported", "document_id": 9001955, "output_format": {"date_parser_output_format": {"date_position": 2}}}]