{"max_upper_block": 3, "use_upper_split_percentage": 0.45, "max_lines_for_header": 5, "max_upper_lines_for_key_search": 8, "debug": true, "default_return": "9000449-Hazard_Insurance_Policy_UnderWritter.json", "document_types": {"new_form": {"return": "9000449-Hazard_Insurance_Policy_UnderWritter_1.json", "header": {"include_strings": ["Kin.", "Automatic Renewal", "Universal Property"], "exclude_strings": [""], "length_comparison": false}, "body": {"include_strings": ["Kin.", "Automatic Renewal", "Universal Property"], "exclude_strings": [""], "length_comparison": false}}}}