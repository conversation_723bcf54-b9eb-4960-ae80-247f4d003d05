[{"id": 3004752, "key": ["Assessment Summary"], "type": "table_new", "end_identifier": ["<PERSON>an <PERSON>"], "keep_start_key": false, "hpos_end_identifier": true, "hpos_end_key": ["Representation & Warranty", "COLLATERAL R&W* RELIEF"], "consider_on_after_hpos": false, "field_id": 3004752, "field_name": "3004752  - LP Result/DU Result1", "document_id": 9000618, "table_processor": {"extract_data_from_table_without_lines": {"field_string": ["RISK CLASS"], "field_string_col": ["RISK CLASS"], "probable_field_value_col": ["RISK CLASS"], "probable_place_in_table": "below_row", "probable_field_value_next_row": 1}}, "output_format": {"string_operations_output_format": {"remove_from_beginning": [".*?(?=ACCEPT)"], "remove_from_end": ["(?<=ACCEPT).*"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3002599, "key": ["Amortization type", "AMORTISATION TYPE"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PURCHASE PRICE"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3002599, "field_name": "3002599 - Amortization Type", "document_id": 9000618}, {"id": 3004751, "key": ["Amortization months"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ESTIMATED PROPERTY VALUE"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3004751, "field_name": "3004751  - <PERSON><PERSON> (Months)", "document_id": 9000618}, {"id": 3002152, "key": ["Interest rate", "TEREST RATE"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["NUMBER OF UNITS"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3002152, "field_name": "3002152  - Interest Rate", "document_id": 9000618, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3002146, "key": ["PURCHASE PRICE"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["OCCUPANCY"], "start_identifier": [""], "field_id": 3002146, "field_name": "3002146 - Sales Price", "document_id": 9000618, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3004750, "key": ["REFINANCE TYPE"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["SALES CONCESSIONS"], "start_identifier": [""], "field_id": 3004750, "field_name": "3004750 - Refinance Purpose", "document_id": 9000618}]