[{"id": 3005861, "key": ["THIS DECLARATION PAGE IS EFFECTIVE", "Effective Date:", "From:"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Residence Premises", "NOTE:", "Limits of Liability", "described location"], "start_identifier": [""], "field_id": 3005861, "field_name": "Document Date", "document_id": 9000449, "alternate_locations": [{"id": 3005861, "key": ["Policy Period", "Policy Effective Date", "POLICY START DATE", "Current Term"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["MEMBERSHIP NUMSER", "MEMBERSHIP NUMBER", "Policy Expiration Date", "standard time", "required premium", "NAME OF INSURED", "Insurance Agent"], "start_identifier": [""], "field_id": 3005861, "field_name": "Document Date", "document_id": 9000449}, {"id": 3005861, "key": ["Effective Date"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Expiration Date"], "start_identifier": [""], "field_id": 3005861, "field_name": "Document Date", "document_id": 9000449}], "output_format": {"date_parser_output_format": {"date_position": 1}}}, {"id": 3004834, "key": ["Total Policy Premium", "Total Poliy c Premium"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Property Location", "Policy Number", "AGENCY:", "Property Insured", "You Know", "Agent Signature", "Premium Includes Discounts"], "start_identifier": [""], "field_id": 3004834, "field_name": "Annual Hazard Insurance Premium", "document_id": 9000449, "alternate_locations": [{"id": 3004834, "key": ["Total Premium:", "Total Premium", "Total Basic Premium", "Annual Premium", "FIRST ANNUAL PREMUM", "Total Annual Premium:", "Premium Paid By"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["MORTGAGEE(S)", "NON-HURRICANE", "Premium Includes Discounts", "Status:", "Amended to:", "please refer"], "start_identifier": [""], "field_id": 3004834, "field_name": "Annual Hazard Insurance Premium", "document_id": 9000449}, {"id": 3004834, "key": ["Trust Fund Fee", "Policy Premium"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["First Mortgagee"], "start_identifier": ["TOTAL"], "field_id": 3004834, "field_name": "Annual Hazard Insurance Premium", "document_id": 9000449}], "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\.", "\\s*%"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}]