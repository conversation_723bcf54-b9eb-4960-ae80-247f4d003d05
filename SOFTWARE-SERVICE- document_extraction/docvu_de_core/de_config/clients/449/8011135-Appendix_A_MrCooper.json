[{"id": 3002396, "key": ["“Affiliate”", "Affiliate"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Aggregate Voting Interests", "Aggregate Expense Rate", "Aggregate Servicing Fee", "Agent Member", "Aggregate Stated Principal <PERSON>"], "start_identifier": ["Affiliate"], "field_id": 3002396, "field_name": "Definition_of_Affiliates", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002397, "key": ["“Ancillary Income”", "Ancillary Income"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Annual Cap", "Applied Realized Loss Amounts", "Asset Repurchase Activity"], "start_identifier": [""], "field_id": 3002397, "field_name": "Ancillary_Income", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 1, "key": ["Capitalized Advance Amount", "Citadel Mortgage Loans", "Class XS Payment Amount"], "direction": "down", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": false, "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 1, "field_name": "Temporary_Field1", "document_id": 8011135}, {"id": 3002398, "key": ["“Closing Date”", "Closing Date"], "direction": "right", "type": "date", "return_type": "date", "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "include_key": false, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002398, "field_name": "Closing_Date", "document_id": 8011135}, {"id": 3002410, "key": ["“Collection Period”", "Collection Period", "“Due period”", "Due period"], "direction": "down_multi_page", "type": "text", "return_type": "text", "probable_place": "individual", "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 1, "include_key": true, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Commission", "Condemnation Proceeds", "eCommerce Laws", "eligible account", "edgar"], "start_identifier": [""], "field_id": 3002410, "field_name": "Due_Period", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002427, "key": ["“Collection Period”", "Collection Period", "“Due period”", "Due period"], "direction": "down_multi_page", "type": "text", "return_type": "text", "probable_place": "individual", "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 1, "include_key": true, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Commission", "Condemnation Proceeds", "eCommerce Laws", "eligible account", "edgar"], "start_identifier": [""], "field_id": 3002427, "field_name": "Prepayment_Period_Language", "document_id": 8011135, "sub_keys": ["mr_cooper_appendix_a_prepayment_period_date_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002401, "key": ["“Controlling Holder”", "Controlling Holder"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "exclude_footer": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Corporate Trust Office", "Corresponding Mortgage"], "start_identifier": ["Controlling Holder"], "field_id": 3002401, "field_name": "Controlling_Holder", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002402, "key": ["<PERSON><PERSON><PERSON><PERSON><PERSON>”", "<PERSON><PERSON><PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "end_identifier": ["<PERSON><PERSON><PERSON><PERSON>", "Cut-off Date"], "start_identifier": [""], "field_id": 3002402, "field_name": "Custodian_Names", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Custodian_Names", "label": ["ORGANIZATION"]}}}, {"id": 3002399, "key": ["“Cut-off Date”", "Cut-off Date"], "direction": "down_multi_page", "type": "date", "return_type": "date", "include_key": true, "probable_place": "individual", "max_page_limit": 1, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["DBRS Morningstar", "Data Room", "DBRS", "Default Interest", "cut-off date pool principal balance"], "start_identifier": [""], "field_id": 3002399, "field_name": "Cut-Off_Date", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002405, "key": ["“Delaware Trustee”", "Delaware Trustee"], "direction": "right", "multi_line_value": false, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "type": "text", "end_identifier": ["Delegated Authority", "Delinquency Trigger"], "start_identifier": [""], "field_id": 3002405, "field_name": "Delaware_Trustee", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Delaware_Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3002406, "key": ["“Delinquent”", "Delinquent", "“Delinquency Rate”", "Delinquency Rate", "“Delinquency Trigger Event”", "Delinquency Trigger Event"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Depositor", "Denomination", "Department of Treasury"], "start_identifier": [""], "field_id": 3002406, "field_name": "Delinquency_Method", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"contains": ["MBA", "ABS"]}}}, {"id": 3002407, "key": ["“Depositor”", "Depositor"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "exact_match": true, "multi_line_value": false, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3002407, "field_name": "Depositor", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Depositor", "label": ["ORGANIZATION"]}}}, {"id": 3002408, "key": ["“Determination Date”", "Determination Date"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Directing Noteholders", "Disqualified Organization"], "start_identifier": [""], "field_id": 3002408, "field_name": "Determination_Date", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002411, "key": ["“Eligible Account”", "Eligible Account"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Eligible Deposit Account", "EPD event", "erisa"], "start_identifier": ["Eligible Account"], "field_id": 3002411, "field_name": "Eligible_Institution_Language", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 2, "key": ["Final Private Placement Memorandum", "Grantor Trust"], "direction": "down", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": false, "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 2, "field_name": "Temporary_Field2", "document_id": 8011135}, {"id": 3002412, "key": ["“Indenture Trustee”", "Indenture Trustee"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": false, "end_identifier": [", a", "Indenture Trustee Fee"], "start_identifier": [""], "field_id": 3002412, "field_name": "Indenture_Trustee", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Indenture_Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3, "key": ["Interest Payment Amount", "Interest Remittance Amount", "Interest Shortfall"], "direction": "down", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": false, "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3, "field_name": "Temporary_Field3", "document_id": 8011135}, {"id": 3002413, "key": ["“Issuer”", "Issuer"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": false, "end_identifier": ["Issuer Request", "Latest Possible Maturity Date", "Liquidat"], "start_identifier": [""], "field_id": 3002413, "field_name": "Issuer", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Issuer", "label": ["ORGANIZATION"]}}}, {"id": 3002413, "key": ["Loan Data Agent"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": false, "end_identifier": ["Fee", "Loan Data Agent <PERSON>e", "Loan Data Agreement", "Lower-Tier"], "start_identifier": [""], "field_id": 3002413, "field_name": "Loan_Data_Agent", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Loan_Data_Agent", "label": ["ORGANIZATION"]}}}, {"id": 4, "key": ["Majority Certificateholder", "Majority-Owned Affiliate", "Majority Noteholders"], "direction": "down", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": false, "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 4, "field_name": "Temporary_Field4", "document_id": 8011135}, {"id": 3002418, "key": ["“Master Servicing Fee Rate”", "Master Servicing Fee Rate"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Master Servicing Transfer Costs", "Master Servicer Remittance Date", "Maturity"], "start_identifier": [""], "field_id": 3002418, "field_name": "Master_Servicing_Fee_Definition/MS_Rate", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 5, "key": ["Non-REMIC Qualified Mortgage Loan", "Notional Principal Arrangement"], "direction": "down", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": false, "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 5, "field_name": "Temporary_Field5", "document_id": 8011135}, {"id": 3002420, "key": ["“Owner”", "Owner"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Owner Trustee", "Ownership Interest", "Payahead"], "start_identifier": [""], "field_id": 3002420, "field_name": "Owner", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002423, "key": ["“Owner Trustee”", "Owner Trustee"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": false, "end_identifier": ["Owner <PERSON>ee <PERSON><PERSON>"], "start_identifier": [""], "field_id": 3002423, "field_name": "Owner_Trustee", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Owner_Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3002430, "key": ["“Owner Trustee”", "Owner Trustee"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": false, "end_identifier": ["Owner <PERSON>ee <PERSON><PERSON>"], "start_identifier": [""], "field_id": 3002430, "field_name": "Securities_Admin/Paying_Agent", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Securities_Admin/Paying_Agent", "label": ["ORGANIZATION"]}}}, {"id": 3002466, "key": ["“P&I Advance”", "P&I Advance"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Payahead", "Paying Agent"], "start_identifier": [""], "field_id": 3002466, "field_name": "Master_Servicer_Delinquency_Advances_Date_to_Advance", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002409, "key": ["“Payment Date”", "Payment Date"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Depositor", "Denomination", "Department of Treasury", "payment date statement", "Percentage Interest", "Periodic Rate Cap"], "start_identifier": [""], "field_id": 3002409, "field_name": "Distribution_Date/Payment_Date", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002424, "key": ["“Permitted Investments”", "Permitted Investments"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 6, "multi_line_value": true, "end_identifier": ["Permitted Transferee", "Person"], "start_identifier": [""], "field_id": 3002424, "field_name": "Permitted_Investments", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002425, "key": ["“Prepayment Interest Shortfall”", "Prepayment Interest Shortfalls"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Prepayment Premium", "Prepayment Period", "Principal Prepayment"], "start_identifier": [""], "field_id": 3002425, "field_name": "Prepayment_Interest_Shortfall", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002428, "key": ["“Rating Agency”", "Rating Agency"], "direction": "right", "type": "text", "return_type": "text", "exact_match": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": false, "end_identifier": [", or any", "rating agency information", "rating agency confirmation"], "start_identifier": [""], "field_id": 3002428, "field_name": "Rating_Agencies", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Rating_Agencies", "label": ["ORGANIZATION"]}}}, {"id": 3002433, "key": ["“Realized Losses”", "Realized Loss"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Record Date", "Redemption Price", "Registered Holder", "Record Date:"], "start_identifier": [""], "field_id": 3002433, "field_name": "Realized_Loss_Definition", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 6, "key": ["Securities Intermediary", "Selene Servicing Agreement"], "direction": "down", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": false, "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 6, "field_name": "Temporary_Field6", "document_id": 8011135}, {"id": 3002431, "key": ["<PERSON>Seller”", "<PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "exact_match": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": false, "end_identifier": [", a", "Servicer"], "start_identifier": [""], "field_id": 3002431, "field_name": "<PERSON><PERSON>", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "<PERSON><PERSON>", "label": ["ORGANIZATION"]}}}, {"id": 3002434, "key": ["“Seller” or “Sponsor”", "“Sponsor”", "Sponsor"], "direction": "right", "type": "text", "return_type": "text", "exact_match": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": false, "end_identifier": [", a", ", or any", "Stated Principal <PERSON>"], "start_identifier": [""], "field_id": 3002434, "field_name": "Sponsor", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Sponsor", "label": ["ORGANIZATION"]}}}, {"id": 3002414, "key": ["“Stated Final Maturity Date”", "Stated Final Maturity Date"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": false, "end_identifier": ["Step-up Rate", "Sub-Servicer", "Subordinate Notes"], "start_identifier": [""], "field_id": 3002414, "field_name": "Latest_Maturity_Date", "document_id": 8011135, "output_format": {"date_finder_output_format": {"return_format": "%m/%Y"}}}]