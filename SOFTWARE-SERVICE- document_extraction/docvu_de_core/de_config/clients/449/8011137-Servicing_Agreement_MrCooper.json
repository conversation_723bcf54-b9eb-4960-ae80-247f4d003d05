[{"id": 1, "key": ["as Master Servicer", "as Issuer", "as Paying Agent", "as Depositor", "EXECUTION VERSION"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 1, "field_name": "Temporary_Field1", "document_id": 8011137}, {"id": 3002895, "key": ["EXECUTION VERSION", "EXECUTION"], "direction": "down_block", "type": "text", "return_type": "text", "use_match": "fuzzy", "use_prev_key_page_no": true, "exclude_header": true, "exclude_footer": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3002895, "field_name": "Servicing_Agreement_Name", "document_id": 8011137, "alternate_locations": [{"id": 3002895, "key": [""], "direction": "full_page", "type": "text", "return_type": "text", "use_match": "fuzzy", "use_prev_key_page_no": true, "exclude_header": true, "exclude_footer": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3002895, "field_name": "Servicing_Agreement_Name", "document_id": 8011137}]}, {"id": 3003227, "key": ["Agreement"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "inline", "include_key": true, "use_prev_key_page_no": true, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003227, "field_name": "Servicer_Name", "document_id": 8011137}, {"id": 3003232, "key": ["Agreement"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "inline", "include_key": true, "use_prev_key_page_no": true, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003232, "field_name": "Servicer_Agreement_Type", "document_id": 8011137}, {"id": 3002900, "key": ["Ancillary Fees", "Ancillary Income"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Anti-Money", "Anti-Terrorism Laws", "Applicable Laws"], "start_identifier": [""], "field_id": 3002900, "field_name": "Servicer_Ancillary_Income", "document_id": 8011137, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 2, "key": ["Corporate Trust Office"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 2, "field_name": "Temporary_Field2", "document_id": 8011137}, {"id": 3003237, "key": ["“Cut-off Date”", "Cut-off Date"], "direction": "down_multi_page", "type": "date", "return_type": "date", "include_key": true, "probable_place": "individual", "max_page_limit": 1, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Deboarding Fee", "Debt Service Reduction", "<PERSON><PERSON>", "Delegated Authority Matrix", "Delinquent"], "start_identifier": [""], "field_id": 3003237, "field_name": "Servicer_Cutoff_Date", "document_id": 8011137, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003246, "key": ["“Determination Date”", "Determination Date"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Due Date", "Due Period", "Eligible Account"], "start_identifier": [""], "field_id": 3003246, "field_name": "Servicer_Determination_Date", "document_id": 8011137, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003247, "key": ["“Due period”", "Due period"], "direction": "down_multi_page", "type": "text", "return_type": "text", "probable_place": "individual", "exclude_footer": true, "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 1, "include_key": true, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Commission", "Condemnation Proceeds", "eCommerce Laws", "eligible account", "edgar"], "start_identifier": [""], "field_id": 3003247, "field_name": "Servicer_Due_Period", "document_id": 8011137, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003240, "key": ["“Eligible Account”", "Eligible Account"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Eligible Investments", "<PERSON>scrow Account", "Escrow Payments"], "start_identifier": ["Eligible Account"], "field_id": 3003240, "field_name": "Servicer_Eligible_Institution", "document_id": 8011137, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003244, "key": ["Monitored Mortgage Loan"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Monthly Payment"], "start_identifier": [""], "field_id": 3003244, "field_name": "Servicer_Additional_Info_Notes", "document_id": 8011137, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003252, "key": ["Principal Prepayment"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Prohibited Jurisdiction", "Prohibited Person", "Qualified Insurer", "Rating Agency"], "start_identifier": [""], "field_id": 3003252, "field_name": "Servicer_Prepayment_Partial", "document_id": 8011137, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003235, "key": ["blanket hazard", "capitalize arrearages"], "direction": "right", "type": "text", "return_type": "text", "include_key": false, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": true, "end_identifier": ["with any risk", "; (f) to withdraw"], "start_identifier": ["Eligible Account", "Mortgage Loans; (e)"], "field_id": 3003235, "field_name": "Servicer_Retained_Interest", "document_id": 8011137, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\.", "\\("], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3003258, "key": ["Treasury temporary"], "direction": "right", "type": "text", "return_type": "text", "include_key": false, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": true, "end_identifier": ["REO Disposition"], "start_identifier": ["from time to time"], "field_id": 3003258, "field_name": "Date_Servicer_Remittance_due_to _MS/Trustee", "document_id": 8011137, "output_format": {"ner_service_req_output_format": {"text_field_name": "Date_Servicer_Remittance_due_to _MS/Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3003255, "key": ["(1) the aggregate", "that accrued on"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "save_page_no_for_next_key": true, "use_prev_field_page_no": true, "multi_line_value": true, "end_identifier": ["Agreement", "Servicing File"], "start_identifier": ["Aggregate Servicing Fee Rate.", "under Subsection 2.05."], "field_id": 3003255, "field_name": "Servicing_Fee_Rate", "document_id": 8011137, "output_format": {"ner_service_req_output_format": {"text_field_name": "Servicing_Fee_Rate", "label": ["ORGANIZATION"]}}}]