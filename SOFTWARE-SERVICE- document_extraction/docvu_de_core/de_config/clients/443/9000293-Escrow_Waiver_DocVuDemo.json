[{"id": 3000361, "key": ["The undersigned hereby agrees to", "The miclersigraed hereby agrees to", "to these terms and conditions"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000361, "field_name": "escrow_waiver_borrower_sign", "document_id": 9000293, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000364, "key": ["The undersigned hereby agrees to", "The miclersigraed hereby agrees to", "to these terms and conditions"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000364, "field_name": "escrow_waiver_coborrower_sign", "document_id": 9000293, "post_process": {"check_for_signature_post_processor": {"check_closeness": true, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000362, "key": ["The undersigned hereby agrees to", "The miclersigraed hereby agrees to", "to these terms and conditions"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ESCROW WAIVER"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000362, "field_name": "escrow_waiver_borrower_date", "document_id": 9000293, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000365, "key": ["The undersigned hereby agrees to", "The miclersigraed hereby agrees to", "to these terms and conditions"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ESCROW WAIVER"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000365, "field_name": "escrow_waiver_coborrower_date", "document_id": 9000293, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]