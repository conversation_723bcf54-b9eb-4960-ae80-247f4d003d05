[{"id": 3004752, "key": ["Recommendation"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Primary Borrower", "Submission Number"], "start_identifier": [""], "field_id": 3004752, "field_name": "LP Result/DU Result", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_numbers": true, "remove_special_chars": true, "remove_from_beginning": ["^\\d+"], "remove_from_end": ["\\d+$"], "retain_only_alpha": true}}}, {"id": 3002152, "key": ["Note Rate"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Loan Type"], "start_identifier": [""], "field_id": 3002152, "field_name": "Interest Rate", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\.", "\\s*%"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3004751, "key": ["<PERSON>an <PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Amortization Type"], "start_identifier": [""], "field_id": 3004751, "field_name": "Loan Term - Months", "document_id": 9000089}, {"id": 3002599, "key": ["Amortization Type"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>"], "start_identifier": [""], "field_id": 3002599, "field_name": "Amortization Type", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S", "\\s*v$"]}}}, {"id": 3002146, "key": ["(purchase"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["transactions)", "<PERSON><PERSON>"], "start_identifier": [""], "field_id": 3002146, "field_name": "Sales Price", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["[^0-9.,]+$"]}}}, {"id": 3004750, "key": ["<PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Bought Down rate", "Property Information", "Qualifying Rate"], "start_identifier": [], "field_id": 3004750, "field_name": "Refinance Purpose", "document_id": 9000089}, {"id": 1001, "key": ["Proposed Monthly Payment"], "direction": "down_multi_page", "type": "text", "return_type": "text", "multi_line_value": true, "multi_page_value": true, "include_key": true, "search_key_in_multi_page": true, "use_match": "fuzzy", "end_identifier": ["Funds"], "start_identifier": [""], "field_id": 1001, "field_name": "Proposed_Monthly_Payment_save", "sub_keys": ["UW_Mortgage_insurance_save"], "document_id": 9000001, "additional_info": {"search_key": ["Mortgage Insurance"]}}]