[{"id": 123456, "key": ["(F) \"Riders\"", "(I) \"Riders\"", "(1) \"Riders\""], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["(J)", "applicable law"], "start_identifier": [], "field_id": 123456, "field_name": "si_other_rider_name", "sub_keys": ["si_other_rider_name_omr"], "document_id": 9000869}, {"id": 3000813, "key": ["Personalized Seal", "Given under my hand", "This instrument was acknowledged", "Personally appeared before me", "This record was acknowledged"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000813, "field_name": "security_instrument_notary_commission_expires_date", "document_id": 9000869, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000820, "key": ["(SEAL)"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000820, "field_name": "security_instrument_witness2_signature_fl", "document_id": 9000869, "post_process": {"check_for_signature_post_processor": {"sign_bbox_direction": "right", "vertical_threshold": 80, "horizontal_threshold": 0, "signature_present": true}}}, {"id": 3000822, "key": ["(SEAL)"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000822, "field_name": "security_instrument_witness2_signature_sc", "document_id": 9000869, "post_process": {"check_for_signature_post_processor": {"sign_bbox_direction": "right", "vertical_threshold": 80, "horizontal_threshold": 0, "signature_present": true}}}, {"id": 3000824, "key": ["(SEAL)"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000824, "field_name": "security_instrument_witness2_signature_ct", "document_id": 9000869, "post_process": {"check_for_signature_post_processor": {"sign_bbox_direction": "right", "vertical_threshold": 80, "horizontal_threshold": 0, "signature_present": true}}}, {"id": 3000826, "key": ["(SEAL)"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000826, "field_name": "security_instrument_witness2_signature_ga", "document_id": 9000869, "post_process": {"check_for_signature_post_processor": {"sign_bbox_direction": "right", "vertical_threshold": 80, "horizontal_threshold": 0, "signature_present": true}}}, {"id": 3000828, "key": ["(SEAL)"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000828, "field_name": "security_instrument_witness2_signature_la", "document_id": 9000869, "post_process": {"check_for_signature_post_processor": {"sign_bbox_direction": "right", "vertical_threshold": 80, "horizontal_threshold": 0, "signature_present": true}}}, {"id": 3000819, "key": ["witness"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "probable_place": "individual", "field_id": 3000819, "field_name": "security_instrument_witness1_signature_fl", "document_id": 9000869, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000821, "key": ["witness"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "probable_place": "individual", "field_id": 3000821, "field_name": "security_instrument_witness1_signature_sc", "document_id": 9000869, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000823, "key": ["witness"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "probable_place": "individual", "field_id": 3000823, "field_name": "security_instrument_witness1_signature_ct", "document_id": 9000869, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000825, "key": ["witness"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "probable_place": "individual", "field_id": 3000825, "field_name": "security_instrument_witness1_signature_ga", "document_id": 9000869, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000827, "key": ["witness"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "probable_place": "individual", "field_id": 3000827, "field_name": "security_instrument_witness1_signature_la", "document_id": 9000869, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000704, "key": ["(B) \"Borrower\" is", "(A) \"Borrower\" is", "The mortgagor(s) is(are)"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON> <PERSON><PERSON><PERSON>", ". currently", ", currently", "borrower is", "texas home", "currently residing", "("], "start_identifier": [], "field_id": 3000704, "field_name": "security_instrument_vesting", "document_id": 9000869}, {"id": 3000807, "key": ["(D) \"Trustee\" is", "(C) \"Trustee\" is"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["trustee's address", ", trustee's address", "Ph.", "<PERSON><PERSON>'s address"], "start_identifier": [], "field_id": 3000807, "field_name": "security_instrument_trustee_name", "document_id": 9000869}, {"id": 3000806, "key": ["County of", "Countyof", "CO&nty of", "COUNTY OF", "Situated in", "W.M., in", "Register's Office for", "PLAT RECORDS"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["County", "Tennessee", ")", ":", "$"], "start_identifier": [], "possible_page_numbers": [], "probable_place": "individual", "field_id": 3000806, "field_name": "security_instrument_county", "document_id": 9000869}, {"id": 3000809, "key": ["State of", "STATE OF", "SYIt of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Al! rights", "County of", "and described in", "according", "to which", "All rights", ")", "$"], "start_identifier": [], "possible_page_numbers": [], "probable_place": "individual", "field_id": 3000809, "field_name": "security_instrument_state_filed", "document_id": 9000869}, {"id": 3000801, "key": ["Loan #:", "Loan", "Loan #", "Loan Number"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": ["Number"], "field_id": 3000801, "field_name": "security_instrument_loan_number", "document_id": 9000869}, {"id": 3000802, "key": ["Case #:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000802, "field_name": "security_instrument_case_number", "document_id": 9000869}, {"id": 3000808, "key": ["Personalized Seal", "Given under my hand", "This instrument was acknowledged", "personally known to me"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000808, "field_name": "security_instrument_appeared_before", "document_id": 9000869, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "security_instrument_appeared_before", "label": ["name"]}}}, {"id": 3005918, "key": ["Personalized Seal", "seal of office", "This instrument was acknowledged", "witness my hand and official seal", "Personally appeared before me", "This record was acknowledged"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3005918, "field_name": "security_instrument_printed_commission_expires_date", "document_id": 9000869, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000811, "key": ["My commission expires", "Title of Officer", "NMLSR ID"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000811, "field_name": "security_instrument_notary_acknowledgment", "document_id": 9000869, "post_process": {"check_for_signature_post_processor": {"signature_present": true, "sign_bbox_direction": "up", "vertical_threshold": 0, "horizontal_threshold": 600}}}, {"id": 3000800, "key": ["which is dated", "rider is made", "(this \"Instrument\") is dated as of"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000800, "field_name": "security_instrument_closing_date", "document_id": 9000869, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000814, "key": ["My Commission Expires"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000814, "field_name": "security_instrument_commission_expires_date_validation", "document_id": 9000869, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000812, "key": ["(Personalized Seal)", "seal of office", "commission expires", "executed the instrument", "authorized to execute"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000812, "field_name": "security_instrument_notary_seal", "document_id": 9000869, "post_process": {"check_for_seal_post_processor": {}}}, {"id": 3000810, "key": ["before me on", "office this", "before me on this", "on this"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000810, "field_name": "security_instrument_execution_date", "document_id": 9000869}, {"id": 3000791, "key": ["RESCIND", "By signing"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000791, "field_name": "security_instrument_borrower_sign", "document_id": 9000869, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000792, "key": ["RESCIND", "By signing"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000792, "field_name": "security_instrument_borrower_date", "document_id": 9000869, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000794, "key": ["RESCIND", "By signing"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000794, "field_name": "security_instrument_coborrower_sign", "document_id": 9000869, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000795, "key": ["RESCIND", "By signing"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000795, "field_name": "security_instrument_coborrower_date", "document_id": 9000869, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000099, "key": ["located at"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["(Property Address)"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000099, "field_name": "security_instrument_street_address", "document_id": 9000869, "alternate_locations": [{"id": 3000099, "key": ["the address of", "whose address is"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TOGETHER WITH all", "as mortgagor", "(Property Address)"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000099, "field_name": "security_instrument_street_address", "document_id": 9000869}], "output_format": {"address_parser_output_format": {"get_line1_and_line2": true, "from_field": "security_instrument_street_address"}}}, {"id": 3000100, "key": ["located at"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["(Property Address)"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000100, "field_name": "security_instrument_city", "document_id": 9000869, "alternate_locations": [{"id": 3000100, "key": ["the address of", "whose address is"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TOGETHER WITH all", "as mortgagor", "(“Property Address”)"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000100, "field_name": "security_instrument_city", "document_id": 9000869}], "output_format": {"address_parser_output_format": {"get_city": true, "from_field": "security_instrument_city"}}}, {"id": 3000101, "key": ["located at"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["(Property Address)"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000101, "field_name": "security_instrument_state", "document_id": 9000869, "alternate_locations": [{"id": 3000101, "key": ["the address of", "whose address is"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TOGETHER WITH all", "as mortgagor"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000101, "field_name": "security_instrument_state", "document_id": 9000869}], "output_format": {"address_parser_output_format": {"get_state": true, "from_field": "security_instrument_state"}}}, {"id": 3000102, "key": ["located at"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["(Property Address)"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000102, "field_name": "security_instrument_zip_code", "document_id": 9000869, "alternate_locations": [{"id": 3000102, "key": ["the address of", "whose address is"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TOGETHER WITH all", "as mortgagor"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000102, "field_name": "security_instrument_zip_code", "document_id": 9000869}], "output_format": {"address_parser_output_format": {"get_zip_code": true, "from_field": "security_instrument_zip_code"}}}, {"id": 3000121, "key": ["owes Lender", "amount of", "sum of"], "direction": "right", "type": "amount", "return_type": "amount", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["plus interest", "as evidenced"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000121, "field_name": "security_instrument_loan_amount", "document_id": 9000869, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true}}}]