[{"id": 80000829, "key": ["ADDRESS OF PROPERTY APPRAISED"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["APPRAISED VALUE OF SUB<PERSON>ECT PROPERTY"], "start_identifier": [], "field_id": 80000829, "field_name": "Address of Property Appraised", "document_id": 9000069}, {"id": 80000830, "key": ["Census Tract"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": [""], "start_identifier": [], "field_id": 80000830, "field_name": "Census Tract", "document_id": 9000069}, {"id": 80000827, "key": ["Effective Date of Appraisal"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "end_identifier": ["State Certification"], "start_identifier": [], "field_id": 80000827, "field_name": "Effective Date of Appraisal", "document_id": 9000069}, {"id": 80000828, "key": ["APPRAISED VALUE OF SUB<PERSON>ECT PROPERTY"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": [], "start_identifier": [], "field_id": 80000828, "field_name": "Appraised Value of Subject Property", "document_id": 9000069, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^[^0-9]+", "\\s*\\$", "^\\$"], "remove_special_chars_from_beginning": true, "remove_from_end": ["[^0-9]+$", "[,\\s]+$"]}}}]