[{"id": 80000849, "key": ["Note"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER'S PROMISE TO PAY", "<PERSON><PERSON><PERSON>'s Promise"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000849, "field_name": "(Note) Date", "document_id": 9000671}, {"id": 80000825, "key": ["THE UNDERSIGNED.", "THE UNDERSIGNED"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["individually", "<PERSON><PERSON>", "[Sign Original Only]", "Loan Originator"], "start_identifier": ["EXERCISE THIS RIGHT"], "field_id": 80000825, "field_name": "Borrower 1 Name", "document_id": 9000671, "post_process": {"name_post_processor": {"note_borrower_name": true}}}, {"id": 80000826, "key": ["THE UNDERSIGNED.", "THE UNDERSIGNED"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Loan Originator", "[Sign Original Only]", "individually"], "start_identifier": ["exercise this right"], "field_id": 80000826, "field_name": "Borrower 2 Name", "document_id": 9000671, "post_process": {"name_post_processor": {"note_coborrower_name": true}}, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["<PERSON><PERSON><PERSON>"]}}}, {"id": 80000852, "key": ["3.PAYMENTS", "3. PAYMENTS"], "direction": "down_block", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["4. B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4.B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4. BORROWER'S FAILURE", "4.BORROWER'S FAILURE", "4. <PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY", "4.<PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000852, "field_name": "First Payment Due Date", "document_id": 9000671, "output_format": {"date_parser_output_format": {"date_position": 1}}}, {"id": 80000855, "key": ["B<PERSON>ROWER'S FAILURE TO PAY AS REQUIRED"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["calendar days", "<PERSON><PERSON><PERSON>", "(B) De<PERSON>ult", "calendardays"], "start_identifier": [""], "possible_page_numbers": [2], "field_id": 80000855, "field_name": "Grace Days", "document_id": 9000671, "output_format": {"word_to_number_output_format": {}, "string_operations_output_format": {"contains": ["\\b(\\d+)\\b"], "remove_from_end": []}}}, {"id": 80000851, "key": ["INTEREST"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["The interest rate", " interest rate"], "start_identifier": ["rate of"], "possible_page_numbers": [1], "field_id": 80000851, "field_name": "Interest Rate", "document_id": 9000671, "output_format": {"string_operations_output_format": {"contains": ["(\\d+\\.?\\d*)\\s*%"], "remove_from_end": ["\\s*%"], "remove_special_chars_from_end": true}}}, {"id": 80000856, "key": ["B<PERSON>ROWER'S FAILURE TO PAY AS REQUIRED"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON><PERSON>", "(B) De<PERSON>ult"], "start_identifier": [""], "possible_page_numbers": [2], "field_id": 80000856, "field_name": "Late Charge Rate", "document_id": 9000671, "output_format": {"string_operations_output_format": {"contains": ["(\\d+\\.?\\d*)\\s*%"], "remove_from_end": ["\\s*%"], "remove_spaces": true}}}, {"id": 80000853, "key": ["3.PAYMENTS", "3. PAYMENTS"], "direction": "down_block", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["4. B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4.B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4. BORROWER'S FAILURE", "4.BORROWER'S FAILURE", "4. <PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY", "4.<PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY"], "start_identifier": [""], "possible_page_numbers": [2], "field_id": 80000853, "field_name": "Maturity Date", "document_id": 9000671, "output_format": {"date_parser_output_format": {"date_position": 2}}}, {"id": 80000850, "key": ["1. <PERSON><PERSON><PERSON><PERSON><PERSON>'S PROMISE TO PAY"], "direction": "down_block", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["INTEREST", "2.INTEREST", "2. INTEREST"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000850, "field_name": "Original Loan Amount", "document_id": 9000671, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"], "remove_spaces": true}}}, {"id": 80000854, "key": ["3. PAYMENTS", "3.PAYMENTS"], "direction": "down_block", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["4. B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4.B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4. BORROWER'S FAILURE", "4.BORROWER'S FAILURE", "4. <PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY", "4.<PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY"], "start_identifier": [], "possible_page_numbers": [1], "field_id": 80000854, "field_name": "Principal and Interest", "document_id": 9000671, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"], "remove_spaces": true}}}]