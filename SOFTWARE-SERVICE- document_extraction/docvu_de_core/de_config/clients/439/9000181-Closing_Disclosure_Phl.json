[{"id": 100, "key": ["Loan Information"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Sale Price"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 100, "field_name": "Loan_Type_omr_save_section", "sub_keys": ["Phl_Mortgage_Type_omr"], "document_id": 9000181}, {"id": 80000832, "key": ["Closing Date"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000832, "field_name": "Closing Date", "document_id": 9000181}, {"id": 80000833, "key": ["Disbursement Date"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000833, "field_name": "Disbursement Date", "document_id": 9000181}, {"id": 80000838, "key": ["Date Issued"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": ["<PERSON>an <PERSON>"], "possible_page_numbers": [1], "field_id": 80000838, "field_name": "<PERSON>an <PERSON>", "document_id": 9000181}, {"id": 80000837, "key": ["Estimated Escrow"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000837, "field_name": "Estimated Escrow", "document_id": 9000181}, {"id": 80000831, "key": ["Prepayment Penalty", "these features?"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Balloon Payment"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000831, "field_name": "Prepayment Penalty", "document_id": 9000181}, {"id": 80000836, "key": ["<PERSON><PERSON> (Points)"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [2], "field_id": 80000836, "field_name": "Points Paid by <PERSON><PERSON><PERSON>", "document_id": 9000181, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["$"], "remove_special_chars_from_beginning": true}}}, {"id": 80000834, "key": ["Mortgage Insurance Premium ( mo.)"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["03 Prepaid Interest"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000834, "field_name": "MI Premium Payment Amount", "document_id": 9000181}]