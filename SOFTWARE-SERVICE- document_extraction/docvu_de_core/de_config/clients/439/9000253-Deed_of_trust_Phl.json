[{"id": 80000844, "key": ["MIN"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": ["MERS"], "start_identifier": [], "field_id": 80000844, "field_name": "MERS/MIN Number", "document_id": 9000253}, {"id": 80000845, "key": ["has the address of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TOGETHER with"], "start_identifier": [""], "field_id": 80000845, "field_name": "Property Address", "document_id": 9000253, "output_format": {"string_operations_output_format": {"remove_from_end": ["]"], "remove_special_chars_from_end": true}, "address_parser_output_format": {"get_line1_and_line2": true, "from_field": "Property Address"}}}, {"id": 80000846, "key": ["has the address of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TOGETHER with"], "start_identifier": [""], "field_id": 80000846, "field_name": "Property City", "document_id": 9000253, "output_format": {"address_parser_output_format": {"get_city": true, "from_field": "Property City"}}}, {"id": 80000847, "key": ["has the address of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 80000847, "field_name": "Property State", "document_id": 9000253, "output_format": {"address_parser_output_format": {"get_state": true, "from_field": "Property State"}}}, {"id": 80000848, "key": ["has the address of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 80000848, "field_name": "Property Zip", "document_id": 9000253, "output_format": {"address_parser_output_format": {"get_zip_code": true, "from_field": "Property Zip"}}}]