[{"id": 3000868, "key": ["foregoing paragraph", "notary public", "under my hand", "behalf of the Authority", "certification thereof", "WITNESS my hand", "WITNESS my hand and official seal", "corporate seal"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000868, "field_name": "subordination_agreement_appeared_before", "document_id": 9000820, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "subordination_agreement_appeared_before", "label": ["name"]}}}, {"id": 3000874, "key": ["foregoing paragraph", "notary public", "under my hand", "behalf of the Authority", "certification thereof", "WITNESS my hand", "WITNESS my hand and official seal", "corporate seal"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000874, "field_name": "subordination_agreement_notary_commission_expires_date", "document_id": 9000820, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000876, "key": ["amended loan exceed", "amount not to exceed", "principal indebtedness of", "current lien amount", "principal amount of", "dollars", "note in the sum of"], "direction": "right", "type": "amount", "return_type": "text", "probable_place": "individual", "end_identifier": ["the"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000876, "field_name": "subordination_agreement_loan_amount", "document_id": 9000820}, {"id": 3000866, "key": ["witness my hand and official stamp or seal", "witness the following signatures and seals", "notary public", "witness my hand and official seal"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000866, "field_name": "subordination_agreement_lender_sign", "document_id": 9000820, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000867, "key": ["SUBORDINATING LENDER"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["in"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000867, "field_name": "subordination_agreement_lender_date", "document_id": 9000820, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000870, "key": ["COUNTY OF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "probable_place": "individual", "use_match": "fuzzy", "end_identifier": ["The foregoing", "The"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000870, "field_name": "subordination_agreement_county", "document_id": 9000820, "output_format": {"string_operations_output_format": {"remove_from_end": ["ss.", " SUBORDINATION", "S.S", " S.S", "IN/AI", ". IN/AI", "O14d 6ab", "'rs It", "rs", "'rs", "612E", " ss", "described", "t2SI", " Is.s", "t2SI", "SUBORDINATION", " 13", ":SS", ": SS"], "remove_special_chars_from_end": true}}}, {"id": 3000869, "key": ["STATE OF"], "direction": "right", "type": "text", "return_type": "text", "probable_place": "individual", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["county", "this", "certificate", "on this", "that", "and", "being"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000869, "field_name": "subordination_agreement_state_filed", "document_id": 9000820, "output_format": {"string_operations_output_format": {"remove_from_end": ["ss.", "S.S", " S.S", "IN/AI", ". IN/AI", "'rs It", "rs", "'rs", "O14d 6ab", "t2SI", "SUBORDINATION", "described", " Is.s", "t2SI", "612E", " ss", " 13", ":SS", ": SS"], "remove_special_chars_from_end": true}}}, {"id": 3000871, "key": ["county of"], "direction": "down", "type": "date", "return_type": "date", "probable_place": "individual", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["before me", "by", "notary public", "witness my hand and official seal", "signature"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000871, "field_name": "subordination_agreement_execution_date", "document_id": 9000820, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000872, "key": ["witness my hand and official stamp or seal", "witness the following signatures and seals", "notary public", "witness my hand and official seal"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000872, "field_name": "subordination_agreement_notary_acknowledgment", "document_id": 9000820, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000873, "key": ["foregoing paragraph", "notary public", "under my hand", "behalf of the Authority", "certification thereof", "WITNESS my hand", "WITNESS my hand and official seal", "corporate seal"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000873, "field_name": "subordination_agreement_notary_seal", "document_id": 9000820, "post_process": {"check_for_seal_post_processor": {}}}]