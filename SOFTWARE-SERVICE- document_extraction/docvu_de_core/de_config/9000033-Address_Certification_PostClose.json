[{"id": 3000054, "key": [""], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000054, "field_name": "add_cert_borrower_date", "document_id": 9000033}, {"id": 3000057, "key": [""], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000057, "field_name": "add_cert_coborrower_date", "document_id": 9000033}, {"id": 3000053, "key": ["PROPERTY"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000053, "field_name": "add_cert_borrower_sign", "document_id": 9000033, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000056, "key": ["PROPERTY"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000056, "field_name": "add_cert_coborrower_sign", "document_id": 9000033, "post_process": {"check_for_signature_post_processor": {"check_closeness": true, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}]