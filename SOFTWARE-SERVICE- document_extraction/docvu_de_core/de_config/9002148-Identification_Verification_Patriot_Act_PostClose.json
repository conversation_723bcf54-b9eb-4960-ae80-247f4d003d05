[{"id": 3000775, "key": ["Social Security Card"], "direction": "right", "type": "number", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Other:"], "start_identifier": [""], "field_id": 3000775, "field_name": "patriot_act_identification_borrower_id2", "document_id": 9002148, "output_format": {"id_number_Extractor_output_format": {}, "string_operations_output_format": {"remove_alpha_from_beginning": true, "remove_alpha_from_end": true, "remove_spaces": true}}}, {"id": 3000776, "key": ["U.S./Foreign Passport"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Military ID"], "start_identifier": [""], "field_id": 3000776, "field_name": "patriot_act_identification_borrower_id2_expiration_date", "document_id": 9002148, "output_format": {"date_parser_output_format": {"date_position": 2}}}, {"id": 3000782, "key": ["Signature"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["notary", "Notary", "Attorm", "Loan", "Green"], "start_identifier": [""], "field_id": 3000782, "field_name": "patriot_act_identification_borrower_notary_name", "document_id": 9002148, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["Date"]}}}, {"id": 3000784, "key": ["CERTIFICATION"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Public CUSTOMER IDENTIFICATION", "CUSTOMER IDENTIFICATION"], "start_identifier": [""], "field_id": 3000784, "field_name": "patriot_act_identification_borrower_notary_date", "document_id": 9002148, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000782, "key": ["signature"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["name", "CUSTOMER", "CUSTOMER IDENTIFICATION VERIFICATION"], "start_identifier": [""], "field_id": 3000782, "field_name": "patriot_act_identification_borrower_notary_title", "document_id": 9002148, "output_format": {"name_parser_output_format": {"get_identification_Name_Matcher": true, "from_field": "patriot_act_identification_borrower_notary_title"}}}]