[{"id": 3000374, "key": ["the undersigned certify that", "undersigned certify"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000374, "field_name": "fha_appraisal_certification_borrower_sign", "document_id": 9000323, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000377, "key": ["the undersigned certify that", "undersigned certify"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000377, "field_name": "fha_appraisal_certification_coborrower_sign", "document_id": 9000323, "post_process": {"check_for_signature_post_processor": {"check_closeness": true, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000375, "key": ["the undersigned certify that", "undersigned certify"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Government Lending", "Lending"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000375, "field_name": "fha_appraisal_certification_borrower_date", "document_id": 9000323, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000378, "key": ["the undersigned certify that", "undersigned certify"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Government Lending", "Lending"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000378, "field_name": "fha_appraisal_certification_coborrower_date", "document_id": 9000323, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]