[{"id": 3000592, "key": ["proceed with", "by signing below", "understand and confirm", "loan and understand"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000592, "field_name": "net_tangible_benefit_disclosure_borrower_date", "document_id": 9000663}, {"id": 3000595, "key": ["proceed with", "by signing below", "understand and confirm", "loan and understand"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000595, "field_name": "net_tangible_benefit_disclosure_coborrower_date", "document_id": 9000663, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}, {"id": 3000591, "key": ["proceed with", "by signing below", "signature"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000591, "field_name": "net_tangible_benefit_disclosure_borrower_sign", "document_id": 9000663, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000594, "key": ["proceed with", "by signing below", "signature"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000594, "field_name": "net_tangible_benefit_disclosure_coborrower_sign", "document_id": 9000663, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}]