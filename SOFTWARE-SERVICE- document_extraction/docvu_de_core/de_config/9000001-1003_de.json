[{"id": 101, "key": ["borrower information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 101, "field_name": "dummy_section1_1", "document_id": 9000001}, {"id": 80001336, "key": ["Date of Birth"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Non-Permanent Resident Alien", "type of credit"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80001336, "field_name": "Borrower Date of Birth", "document_id": 9000001}, {"id": 103, "key": ["number of units"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["mixed-use"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 103, "field_name": "occupancy_checkbox_save_section", "sub_keys": ["occupancy_omr"], "document_id": 9000001}, {"id": 106, "key": ["section 6"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 106, "field_name": "dummy_section9_4", "document_id": 9000001}, {"id": 80000982, "key": ["Borrower Signature"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Additional Borrower Signature", "Borrower Name"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80000982, "field_name": "Application Date", "document_id": 9000001}, {"id": 104, "key": ["section 8", "Demographic Information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 104, "field_name": "dummy_section8_3", "document_id": 9000001}, {"id": 105, "key": ["please check below", "please chock", "the law also provides"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["to be completed"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 105, "field_name": "demographic_checkbox_save_section", "additional_info": {"search_dummy_found_page_only": true}, "sub_keys": ["demographic_information_omr"], "document_id": 9000001}, {"id": 1011, "key": ["Citizenship"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 1011, "field_name": "dummy_section1_co-borrower_info", "document_id": 9000001}, {"id": 80003214, "key": ["Date of Birth"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Non-Permanent Resident Alien", "type of credit"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80003214, "field_name": "Co-Borrower Date of Birth", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 108, "key": ["section 8", "Demographic Information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 108, "field_name": "dummy_section8_6", "document_id": 9000001}, {"id": 109, "key": ["please check below", "please chock", "select the"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["to be completed"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 109, "field_name": "demographic_checkbox_save_section", "sub_keys": ["co_borrower_demographic_information_omr"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 115, "key": ["Original Cost"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Title to the Property Will"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 115, "field_name": "property_save_section", "sub_keys": ["project_type_omr"], "document_id": 9000001}, {"id": 80012042, "key": ["Language"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80012042, "field_name": "<PERSON><PERSON><PERSON> Preferred Language", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80012043, "key": ["Language"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80012043, "field_name": "Co-<PERSON><PERSON><PERSON> Preferred Language", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}]