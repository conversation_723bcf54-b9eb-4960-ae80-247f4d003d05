[{"id": 3000414, "key": ["acceptable policy"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["date"], "start_identifier": [], "possible_page_numbers": [1], "field_id": 3000414, "field_name": "hazard_insurance_authorization_disclosure_borrower_date", "document_id": 9000448}, {"id": 3000417, "key": ["acceptable policy"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["date"], "start_identifier": [], "possible_page_numbers": [1], "field_id": 3000417, "field_name": "hazard_insurance_authorization_disclosure_coborrower_date", "document_id": 9000448, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}, {"id": 3000413, "key": ["acceptable policy"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000413, "field_name": "hazard_insurance_authorization_disclosure_borrower_sign", "document_id": 9000448, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000416, "key": ["acceptable policy"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000416, "field_name": "hazard_insurance_authorization_disclosure_coborrower_sign", "document_id": 9000448, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}]