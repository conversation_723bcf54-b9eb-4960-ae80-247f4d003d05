{"address_information": [{"id": 80012132, "key": ["Street_Address1"], "direction": "inline", "type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [",", ""], "start_identifier": [""], "field_id": 80012132, "field_name": "Property_Full_Street_Address_line1", "document_id": 12345678, "output_format": {"address_parser_output_format": {"get_line1_and_line2": true, "from_field": "Property_Full_Address"}, "string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 80012132, "key": ["Street_Address2"], "direction": "inline", "type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [",", ""], "start_identifier": [""], "field_id": 80014042, "field_name": "Property_Full_Street_Address_line2", "document_id": 12345678, "output_format": {"address_parser_output_format": {"get_line2": true, "from_field": "Property_Full_Address"}, "string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_all_func": true}}}, {"id": 80012133, "key": ["City_Address"], "direction": "inline", "type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [",", ""], "start_identifier": [""], "field_id": 80012133, "field_name": "Property_City", "document_id": 12345678, "output_format": {"address_parser_output_format": {"get_city": true, "from_field": "Property_Full_Address"}, "string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 80012134, "key": ["State_Address"], "direction": "inline", "type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [",", ""], "start_identifier": [""], "field_id": 80012134, "field_name": "Property_State_Code", "document_id": 12345678, "output_format": {"address_parser_output_format": {"get_state": true, "from_field": "Property_Full_Address"}, "string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 80012135, "key": ["Zipcode_Address"], "direction": "inline", "type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [",", ""], "start_identifier": [""], "field_id": 60000013, "field_name": "Property_ZIPcode", "document_id": 12345678, "output_format": {"string_operations_output_format": {"replace_if_match": [["0", ""]], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "replace_within_string": [["o", "0"], ["O", "0"]]}, "address_parser_output_format": {"get_zip_code": true, "from_field": "Property_Full_Address"}}}, {"id": 80014044, "key": ["Sequence_Number"], "direction": "inline", "type": "text", "multi_line_value": false, "end_identifier": [",", ""], "start_identifier": [""], "field_id": 60000013, "field_name": "Property_Sequence_Number", "document_id": 12345678}], "loan_information": [{"id": 80012141, "key": ["Trust dated", "dated", "date", "executed on", "recorded"], "direction": "right", "type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["in favor", "by"], "start_identifier": [""], "field_id": 80012141, "field_name": "Loan_Date", "document_id": 12345678, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}, "schedule_a_output_format": {"custom": true}}}, {"id": 80012146, "key": ["Trust from", "executed by", "mortgage from", "mortgagor", "from", "grantor", "made by", "<PERSON><PERSON>/Borrower", "trustor", "mortgage by", "mortgage of", "favor of", "Deed from", " Mortgage granted by", "Borrower(s)"], "direction": "right", "type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["as grantor", "trustee", "address", "executed", " to ", "Amount", "(MERS)", "solely", "dated", "mortgagee", "securing", " recorded on", "in the original", " in the principal", "in favor of", "on"], "start_identifier": [""], "field_id": 80012146, "field_name": "Borrower_Full_Name", "document_id": 12345678, "output_format": {"string_operations_output_format": {"remove_special_chars_from_end": true, "remove_special_chars_from_beginning": true}, "schedule_a_output_format": {"custom": true}}}, {"id": 80012143, "key": ["Serial Number", "Serial No", "document number", "document no", "Document ID No", "instrument no", "instrument number", "Instr. No.", "reception no", "auditor's file no", "entry no", "instrument", "file no", "Document", "MIN", "Mortgage Record", "Recording No.", "INSTRUMENT", "Doc. No."], "direction": "right", "type": "text", "multi_line_value": false, "end_identifier": [",", "in", "book", "of", "secures", "Amount", "securing", " Loan No.", "executed by", "records", "and also filed", "Assignment", "to secure", "<PERSON><PERSON>", "among the land records"], "start_identifier": [""], "field_id": 80012143, "field_name": "Loan_Document_Number", "document_id": 12345678, "output_format": {"schedule_a_output_format": {"custom": true}}}, {"id": 80012142, "key": ["Recorded", "Record", "filed"], "direction": "right", "type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["at", ""], "start_identifier": [""], "field_id": 80012142, "field_name": "Loan_Recording_Date", "document_id": 12345678, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}, "schedule_a_output_format": {"custom": true}}}, {"id": 80012145, "key": ["Amount of", "Amount", "sum of", "payment of", "to secure", "indebtedness", "securing", "secures", "mortgage for", "Trust for", " single lien of"], "direction": "right", "type": "amount", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["and", "dated", "plus", "-", "together with any other amounts"], "start_identifier": ["thereby"], "field_id": 80012145, "field_name": "Loan_Amount", "document_id": 12345678, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$", "^.*?\\$"], "remove_special_chars_from_end": true, "remove_special_chars_from_beginning": true, "remove_substrings": true, "remove_from_end": ["[a-zA-Z][a-zA-Z0-9]*$"]}, "schedule_a_output_format": {"custom": true}}}], "book_information": [{"id": 80014040, "key": ["MOB", "in Liber", "Book/Page", "Book Number", "Book no", "Book", "Volume"], "direction": "right", "type": "text", "multi_line_value": false, "use_match": "fuzzy", "use_first_word": true, "end_identifier": [",", "at", "page", "pages"], "start_identifier": [""], "field_id": 80014040, "field_name": "Loan_Book_Number", "document_id": 12345678}, {"id": 80014039, "key": [], "direction": "right", "type": "text", "multi_line_value": false, "use_match": "fuzzy", "use_first_word": true, "end_identifier": [], "start_identifier": [""], "field_id": 80014039, "field_name": "Loan_Book_Page_Number", "document_id": 12345678}, {"id": 80014041, "key": ["Page", "Folio", "Pages"], "direction": "right", "type": "text", "multi_line_value": false, "use_match": "fuzzy", "use_first_word": true, "end_identifier": [".", ",", "of"], "start_identifier": [""], "field_id": 80014041, "field_name": "Loan_Page_Number", "document_id": 12345678}], "legal_subdivision_information": [{"id": 80012148, "key": ["of", "in", "subdivision", "tract"], "direction": "right", "type": "text", "multi_line_value": false, "include_key": true, "use_match": "fuzzy", "end_identifier": ["Addition", "Unit", "County", "City", "State", "according", "APN", "Book", "<PERSON><PERSON>", "thereof"], "start_identifier": [""], "field_id": 80012148, "field_name": "Legal_Subdivision", "document_id": 12345678, "output_format": {"subdivision_output_format": {"text_field_name": "Legal_Description_Text"}}}, {"id": 80012149, "key": ["county"], "direction": "right", "type": "county", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 80012149, "field_name": "Legal_County_State", "document_id": 12345678, "output_format": {"string_operations_output_format": {"remove_special_chars_from_end": true, "remove_special_chars_from_beginning": true}}}], "tax_apn_information": [{"id": 80014043, "key": ["Tax I. D. Number", "Tax ID Number", "TAX PARCEL NO", "Tax Map No", "Tax I. D. #", "Tax ID No", "Tax ID", "Agent ID", "Parcel ID", "RPC Number", "PIN/Tax", "PIN", "PPN", "TMS", "APN", "A.P.N.", "PIDN", "Folio No", "Pa<PERSON>el <PERSON>", "Parcel No", "parcel"], "direction": "right", "type": "number", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [". ", ",", "The", "being", "number", "Property", "Policy Number", "Issued by", "Otter Tail", "All that", "county", "This policy"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 80014043, "field_name": "Property_APN", "document_id": 12345678, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["ft:"]}}}], "vesting_information": [{"id": 80012324, "key": ["dated", "date"], "direction": "right", "type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["in favor", ""], "start_identifier": [""], "field_id": 80012324, "field_name": "Vesting_Deed_Date", "document_id": 12345678, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}, "schedule_a_output_format": {"custom": true}}}, {"id": 80012326, "key": ["Serial Number", "document number", "Instrument Number", "Reception Number", "Instrument No", "Serial No", "document no", "instrument", "Instr No", "Inst.", "AFN", "File #", "#"], "direction": "right", "type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [",", "in", "of", "at", "vest", "among", "Consider"], "start_identifier": [""], "field_id": 80012326, "field_name": "Vesting_Deed_Document_Number", "document_id": 12345678, "output_format": {"string_operations_output_format": {"remove_special_chars_from_end": true, "remove_special_chars_from_beginning": true, "remove_alpha_from_beginning_func": true, "remove_alpha_from_end_func": true, "contains": ["\\b[A-Za-z\\d]+(?:[-./\\s][A-Za-z\\d]+)*\\b"]}, "schedule_a_output_format": {"custom": true}}}, {"id": 80012325, "key": [" Recorded", "Record", "filed"], "direction": "right", "type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["at", ""], "start_identifier": [""], "field_id": 80012325, "field_name": "Vesting_Deed_Recording_Date", "document_id": 12345678, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}, "schedule_a_output_format": {"custom": true}}}, {"id": 80012144, "key": [" Book", "Liber"], "direction": "right", "type": "text", "multi_line_value": false, "use_match": "fuzzy", "use_first_word": true, "end_identifier": [",", "at", "page", "pages"], "start_identifier": [""], "field_id": 80012144, "field_name": "Vesting_Deed_Book_Number", "document_id": 12345678, "output_format": {"schedule_a_output_format": {"custom": true}}}, {"id": 80012838, "key": ["Page", "Folio"], "direction": "right", "type": "page_val", "multi_line_value": false, "use_match": "fuzzy", "use_first_word": true, "end_identifier": [".", ","], "start_identifier": [""], "field_id": 80012838, "field_name": "Vesting_Deed_Page_Number", "document_id": 12345678, "output_format": {"schedule_a_output_format": {"custom": true}}}], "occupancy_omr": [{"id": 80011369, "key": ["occupancy"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [5], "field_id": 80011369, "field_name": "Property Occupancy Type", "document_id": 9000001, "additional_info": {"field_options": ["Primary Residence", "Second Home", "Investment Property", "FHA Secondary Residence"], "zone_coords": [750, 8]}}], "project_type_omr": [{"id": 80012035, "key": ["project"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 80012035, "field_name": "property_type_VA_319117", "document_id": 9000001, "additional_info": {"field_options": ["Condominium", "Cooperative", "Planned Unit Development (PUD)", "Property is not located in a project"], "zone_coords": [850, 12]}}], "demographic_information_omr": [{"id": 80022803, "key": ["race"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 80022803, "field_name": "B1 Section 8 Demographic Information - Race", "document_id": 9000001, "additional_info": {"field_options": ["American Indian or Alaska or Alaska Native", "Asian", "Asian Indian", "Chinese", "Filipino", "Japanese", "Korean", "Vletnamese", "Other Asian", "Black or African American", "Native Hawaiian Or Other Pacific Islander", "Native Hawaiian", "Guamanian or Chamorro", "Samoan", "Other pacific Islander", "White", "I do not wish to provide this information"], "zone_coords": [565, 240]}}, {"id": 80022802, "key": ["ethnicity"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 80022802, "field_name": "B1 Section 8 Demographic Information - Ethnicity", "document_id": 9000001, "additional_info": {"field_options": ["Hispanic or Latino", "Mexican", "Puerto Rican", "Cuban", "Other Hispanic or Latino", "Not Hispanic or Latino", "I do not wish to provide this information"], "zone_coords": [270, 140]}}, {"id": 80022804, "key": ["sex"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 80022804, "field_name": "B1 Section 8 Demographic Information - Sex", "document_id": 9000001, "additional_info": {"field_options": ["Female", "male", "I do not wish to provide this information"], "zone_coords": [253, 100]}}, {"id": 80022819, "key": ["Face-to-Face"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [6], "field_id": 80022819, "field_name": "B1-The Demographic Information was provided through", "document_id": 9000001, "additional_info": {"field_options": ["Face-to-Face Interview (includes Electronic Media w/ Video Component)", "Telephone Interview", "Fax or Mail", "Email or Internet"], "zone_coords": [750, 16]}}], "co_borrower_demographic_information_omr": [{"id": 80022852, "key": ["race"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [13], "field_id": 80022852, "field_name": "Co-Bo<PERSON><PERSON> Race", "document_id": 9000001, "additional_info": {"field_options": ["American Indian or Alaska or Alaska Native", "Asian", "Asian Indian", "Chinese", "Filipino", "Japanese", "Korean", "Vletnamese", "Other Asian", "Black or African American", "Native Hawaiian Or Other Pacific Islander", "Native Hawaiian", "Guamanian or Chamorro", "Samoan", "Other pacific Islander", "White", "I do not wish to provide this information"], "zone_coords": [588, 286]}}, {"id": 80022851, "key": ["ethnicity"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [13], "field_id": 80022851, "field_name": "Co-Borrower Ethnicity", "document_id": 9000001, "additional_info": {"field_options": ["Hispanic or Latino", "Mexican", "Puerto Rican", "Cuban", "Other Hispanic or Latino", "Not Hispanic or Latino", "I do not wish to provide this information"], "zone_coords": [253, 146]}}, {"id": 80022853, "key": ["sex"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [13], "field_id": 80022853, "field_name": "Co-Borrower <PERSON>", "document_id": 9000001, "additional_info": {"field_options": ["Female", "male", "I do not wish to provide this information"], "zone_coords": [253, 146]}}, {"id": 80022762, "key": ["Face-to-Face"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [6], "field_id": 80022762, "field_name": "Applicant_Method_Collected_CoBr_1_MB_318872", "document_id": 9000001, "additional_info": {"field_options": ["Face-to-Face Interview (includes Electronic Media w/ Video Component)", "Telephone Interview", "Fax or Mail", "Email or Internet"], "zone_coords": [750, 16]}}], "property_type_omr": [{"id": 80012035, "key": ["as applicable"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80011374, "field_name": "property_type_VA_319117", "document_id": 9000253, "additional_info": {"field_options": ["Adjustable Rate Rider", "Condominium Rider", "Asian Indian", "Second Home Rider", "Balloon Rider", "Planned Unit Development Rider", "Other(s) [specify]", "1-4 Family Rider", "V.A. Rider", "Biweekly Payment Rider"], "zone_coords": [1000, 630]}}], "underwriting_loan_purpose": [{"id": 80001082, "key": ["Payments"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "end_identifier": [""], "start_identifier": ["<PERSON><PERSON>"], "possible_page_numbers": [], "field_id": 80001082, "field_name": "loan_purpose_VA_319137", "document_id": 9000003, "additional_info": {"field_options": ["Purchase", "Cash-Out Refinance", "Limited Cash-Out Refinance(Fannie)", "No Cash-Out Refinance(Freddie)", "Home Improvement", "Construction Conversion/Construction to Permanent"], "zone_coords": [266, 79]}}], "underwriting_ltv": [{"id": 80011749, "key": ["LTV", "LTVICLTVIHCLTV", "LTV/CCTV", "LTVlCLTV", "LTVICLTV", "LW", "LN"], "direction": "right", "type": "number", "return_type": "", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["%", "Supplemental Property Insurance", "Lease", "First", "Lease/Ground Rent", "x <PERSON><PERSON>'<PERSON>,u'd", "Homeowner's Insurance", "First Mortgage P&I", "CLTV/TLTV", "/ $ Total Obligations", "$"], "start_identifier": [], "possible_page_numbers": [], "field_id": 80011749, "field_name": "Original_LTV_VA_326039", "document_id": 9000003}], "underwriting_occupancy_status": [{"id": 80018278, "key": ["occupancy"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "end_identifier": [""], "start_identifier": ["Occupancy Status"], "possible_page_numbers": [], "field_id": 80018278, "field_name": "occupancy_status_VA_80018278", "document_id": 9000003, "additional_info": {"field_options": ["Primary Residence", "Second Home", "Investment Property"], "zone_coords": [170, 50]}}], "flood_cert_sfha_omr": [{"id": 80003318, "key": ["LETTERS"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "possible_page_numbers": [], "field_id": 80003318, "field_name": "sfha_zone_VA_319110", "document_id": 9000377, "additional_info": {"field_options": ["YES", "NO"], "zone_coords": [285, 12]}, "output_format": {"Omr_Checkbox_Output_Format": {"checkbox_type": true, "default_value_if_unchecked": "NO"}}}], "cd_loan_type_omr": [{"id": 3000372, "key": ["Product"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "end_identifier": [""], "start_identifier": [], "possible_page_numbers": [], "field_id": 3000372, "field_name": "cd_loan_type", "document_id": 9000181, "additional_info": {"field_options": ["Conventional", "FHA", "VA"], "zone_coords": [942, 91]}}], "flood_cert_date_save": [{"id": 80001155, "key": ["DATE OF DETERMINATION"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": [""], "start_identifier": [], "possible_page_numbers": [], "field_id": 80001155, "field_name": "Flood Certificate Date", "document_id": 9000377}], "flood_cert_number_save": [{"id": 80001155, "key": ["ORDER NUMBER"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": [""], "start_identifier": [], "possible_page_numbers": [], "field_id": 80001155, "field_name": "Flood Certificate Number", "document_id": 9000377}], "property_omr": [{"id": 80000005, "key": ["property will be"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000005, "field_name": "property_value", "document_id": 9000001, "additional_info": {"field_options": ["Primary Residence", "Secondary Residence", "Investment"], "zone_coords": [400, 25]}}], "occupancy_old_omr": [{"id": 80011369, "key": ["explain"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [5], "field_id": 80011369, "field_name": "Property Occupancy Type", "document_id": 9000001, "additional_info": {"field_options": ["Primary Residence", "Second Home", "Investment Property", "FHA Secondary Residence"], "zone_coords": [750, 30]}}], "PROP_TYPE_DFCU_326122_OMR": [{"id": 80011718, "key": ["Type"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80001091, "field_name": "PROP_TYPE_DFCU_326122", "document_id": 9000069, "additional_info": {"field_options": ["Det.", "Att.", "S-DetJEnd Unit"], "zone_coords": [5, 5]}}], "late_charge_rate": [{"id": 80000856, "key": ["B<PERSON>ROWER'S FAILURE TO PAY AS REQUIRED"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON><PERSON>", "(B) De<PERSON>ult"], "start_identifier": [""], "possible_page_numbers": [2], "field_id": 80000856, "field_name": "Late_Charge_Rate_VA_326040", "document_id": 9000671, "output_format": {"string_operations_output_format": {"contains": ["(\\d+\\.?\\d*)\\s*%"], "remove_from_end": ["\\s*%"]}}}], "former_address_save": [{"id": 80022785, "key": ["Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unit #"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022785, "field_name": "B1 Former Address Street", "document_id": 9000001}, {"id": 302, "key": ["Unit #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["City"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 302, "field_name": "<PERSON><PERSON><PERSON>_FormerAddress_Unit", "document_id": 9000001}, {"id": 80022783, "key": ["City"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022783, "field_name": "B1 Former Address City", "document_id": 9000001}, {"id": 80022784, "key": ["State"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ZIP"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022784, "field_name": "B1 Former Address State", "document_id": 9000001}, {"id": 80022786, "key": ["ZIP"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Country"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022786, "field_name": "B1 Former Address Zip", "document_id": 9000001}, {"id": 306, "key": ["Country"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["How Long"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 306, "field_name": "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_Country", "document_id": 9000001}, {"id": 555555, "key": ["Housing"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 555555, "field_name": "Housing", "document_id": 9000001, "additional_info": {"field_options": ["No primary housing expense", "Own", "Rent ($ /month) "], "zone_coords": [750, 30]}}], "mailling_address_save": [{"id": 80022793, "key": ["Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unit #"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022793, "field_name": "B1 MAILING-Street", "document_id": 9000001}, {"id": 222, "key": ["Unit #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["City"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 222, "field_name": "Borrower_mailling_Address_Unit", "document_id": 9000001}, {"id": 80022790, "key": ["City"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022790, "field_name": "B1 Mailing Address_City", "document_id": 9000001}, {"id": 80022792, "key": ["State"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ZIP"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022792, "field_name": "B1 MAILING-STATE", "document_id": 9000001}, {"id": 80022791, "key": ["Mailing Address"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Country"], "start_identifier": ["ZIP"], "possible_page_numbers": [1], "field_id": 80022791, "field_name": "B1 Mailing Address_Zipcode", "document_id": 9000001}, {"id": 224, "key": ["Country"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Does notapply", "Does not apply"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 224, "field_name": "Borrower__mailling_Address_Country", "document_id": 9000001}], "Section1_omr_save": [{"id": 80022770, "key": ["Citizenship"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022770, "field_name": "B1 Citizenship", "document_id": 9000001, "additional_info": {"field_options": ["U.S. Citizen", "Permanent Resident Alien", "Non-Permanent Resident Alien"], "zone_coords": [190, 50]}}, {"id": 80022795, "key": ["<PERSON><PERSON>"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022795, "field_name": "B1 Martial Status", "document_id": 9000001, "additional_info": {"field_options": ["Married", "Separated", "Unmarried"], "zone_coords": [190, 50]}}, {"id": 80022879, "key": ["Type"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 80022879, "field_name": "Type_of_credit", "document_id": 9000001, "additional_info": {"field_options": ["I am applying for individual credit.", "I am applying for joint credit."], "zone_coords": [400, 40]}}], "Employer_or_Business": [{"id": 80022768, "key": ["Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Gross"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022768, "field_name": "B1 Bus Phone", "document_id": 9000001}, {"id": 80022818, "key": ["Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Gross"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022818, "field_name": "B1_Employer_phonenumber_TO_320961", "document_id": 9000001}, {"id": 602, "key": ["Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unit", "Unit#"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 602, "field_name": "Employer_or_Business_street", "document_id": 9000001}, {"id": 557, "key": ["Unit #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Base"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 557, "field_name": "employer_business_Unit", "document_id": 9000001}, {"id": 80022778, "key": ["City"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022778, "field_name": "B1 Employer City", "document_id": 9000001}, {"id": 80022780, "key": ["State"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ZIP"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022780, "field_name": "B1 EmployerState", "document_id": 9000001}, {"id": 80022781, "key": ["ZIP"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Country"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022781, "field_name": "B1 EmployerZipCode", "document_id": 9000001}, {"id": 561, "key": ["Country"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Overtime"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 561, "field_name": "employer_business_Country", "document_id": 9000001}, {"id": 80022798, "key": ["Position or Title"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Check if"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022798, "field_name": "B1 position\title", "document_id": 9000001}, {"id": 609, "key": ["Start Date"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["(mm"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 609, "field_name": "Employer_or_Business_start_date", "document_id": 9000001}, {"id": 666, "key": ["Base"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/month"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 666, "field_name": "Employer_or_Business_Base", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}], "Gross_monthly_income_save": [{"id": 80022861, "key": ["TOTAL"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/month"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022861, "field_name": "Employer_or_Business_TOTAL", "document_id": 9000001}], "Section4_loan_property_information": [{"id": 80022871, "key": ["occupancy"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [5], "field_id": 80022871, "field_name": "Property Occupancy Type", "document_id": 9000001, "additional_info": {"field_options": ["Primary Residence", "Second Home", "Investment Property", "FHA Secondary Residence"], "zone_coords": [775, 12]}}, {"id": 80022869, "key": ["Purpose"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 80022869, "field_name": "<PERSON><PERSON>", "document_id": 9000001, "additional_info": {"field_options": ["Purchase", "Refinance", "Other (specify)"], "zone_coords": [450, 12]}}, {"id": 80011393, "key": ["<PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 80011393, "field_name": "Loan_and_Property_Information_Loan_Amount_DFCU_326016", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 80022874, "key": ["Property Address Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unit"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 80022874, "field_name": "Property Address Street", "document_id": 9000001}, {"id": 80022872, "key": ["City"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 80022872, "field_name": "Property Address City", "document_id": 9000001}, {"id": 80022873, "key": ["State"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ZIP"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 80022873, "field_name": "Property Address State", "document_id": 9000001}, {"id": 80022875, "key": ["ZIP"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Country", "County"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 80022875, "field_name": "Property Address Zip", "document_id": 9000001}, {"id": 80022876, "key": ["Country", "County"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Number of Units"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 80022876, "field_name": "Property County", "document_id": 9000001}, {"id": 80022877, "key": ["Number of Units"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Property Value"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 80022877, "field_name": "Property_Units", "document_id": 9000001}, {"id": 80022878, "key": ["Property Value"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Occupancy"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 80022878, "field_name": "Property Value", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 80011900, "key": ["shop"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 80011900, "field_name": "Mixed_use_property", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [520, 10]}}, {"id": 8001100, "key": ["chassis"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 8001100, "field_name": "Manufactured_Home", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [470, 15]}}], "Section5_Declaration_omr": [{"id": 80011718, "key": ["residence?"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80011718, "field_name": "Borrower_Declarations_A1_DFCU_326186", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [550, 15]}}, {"id": 80011967, "key": ["three"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80011967, "field_name": "Borrower_Declarations_A2_DFCU_326187", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [550, 15]}}, {"id": 80022807, "key": ["property?"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80022807, "field_name": "B1_Declarations_B_YES or No", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [550, 15]}}, {"id": 80022808, "key": ["payment)"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80022808, "field_name": "B1_Declarations_C_YES or No", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [550, 15]}}, {"id": 80011715, "key": ["loan)"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80011715, "field_name": "Borrower_Declarations_D1_DFCU_326184", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [550, 15]}}, {"id": 80011966, "key": ["new"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80011966, "field_name": "Borrower_Declarations_D2_DFCU_326185", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [660, 15]}}, {"id": 80022809, "key": ["lien"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80022809, "field_name": "B1_Declarations_E_YES or No", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [665, 15]}}, {"id": 80022810, "key": ["guarantor"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80022810, "field_name": "B1_Declarations_F_YES or No", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [800, 15]}}, {"id": 80022811, "key": ["outstanding"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80022811, "field_name": "B1_Declarations_G_YES or No", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [800, 15]}}, {"id": 80022812, "key": ["delinquent"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80022812, "field_name": "B1_Declarations_H_YES or No", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [800, 15]}}, {"id": 80022813, "key": ["potentially"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80022813, "field_name": "B1_Declarations_I_YES or No", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [800, 15]}}, {"id": 80022814, "key": ["conveyed"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80022814, "field_name": "B1_Declarations_J_YES or No", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [800, 15]}}, {"id": 80022815, "key": ["sold"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80022815, "field_name": "B1_Declarations_K_YES or No", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [800, 15]}}, {"id": 80022816, "key": ["foreclosed"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80022816, "field_name": "B1_Declarations_L_YES or No", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [800, 15]}}, {"id": 80022817, "key": ["past 7"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80022817, "field_name": "B1_Declarations_M_YES or No", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [900, 15]}}], "Borrower_Demographic_information2": [{"id": 80022820, "key": ["ethnicity"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80022820, "field_name": "B1-Was the ethnicity of the <PERSON>rrower collected on the bases of visual observation or surname?", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [900, 15]}}, {"id": 80022822, "key": ["sex"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80022822, "field_name": "B1-Was the sex of the <PERSON>rrower collected on the bases of visual observation or surname?", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [900, 15]}}, {"id": 80022821, "key": ["race"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80022821, "field_name": "B1-Was the race of the Borrower collected on the bases of visual observation or surname?", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [900, 15]}}], "Loan_originator_information": [{"id": 7000, "key": ["Name"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Address"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 7000, "field_name": "Loan_Originator_Organization_Name", "document_id": 9000001}, {"id": 7001, "key": ["Address"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Loan"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 7001, "field_name": "Loan_Originator_Organization_Address", "document_id": 9000001}, {"id": 7002, "key": ["ID#"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State License"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 7002, "field_name": "Loan_Originator_Organization_NMLSR_ID", "document_id": 9000001}, {"id": 7003, "key": ["State License ID#"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Loan Originator Name"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 7003, "field_name": "Loan_Originator_Organization_State_License_id1", "document_id": 9000001}, {"id": 7004, "key": ["Loan Originator Name"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Loan"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 7004, "field_name": "Loan_Originator_Name", "document_id": 9000001}, {"id": 7005, "key": ["Loan Originator NMLSR ID#"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 7005, "field_name": "Loan_Originator_NMLSR_ID", "document_id": 9000001}, {"id": 7006, "key": ["State License ID#"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Email"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 7006, "field_name": "Loan_Originator_Organization_State_License_id2", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^.*State License ID#\\s*"], "retain_only_numbers": true, "remove_special_chars_from_beginning": true}}}, {"id": 7007, "key": ["Email"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Phone"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 7007, "field_name": "Loan_Originator_information_email", "document_id": 9000001}, {"id": 7008, "key": ["Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["DocuSigned", "Signature"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 7008, "field_name": "Loan_Originator_information_phone", "document_id": 9000001}, {"id": 7008, "key": ["Date"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Borrower Name"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 7008, "field_name": "Loan_Originator_information_Date", "document_id": 9000001, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}], "Mortgage_Loan_info_omr": [{"id": 80011703, "key": ["Mortgage"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 80011703, "field_name": "Loan_Type_DFCU_326014", "document_id": 9000001, "additional_info": {"field_options": ["Conventional", "USDA-RD", "FHA", "VA", "Other"], "zone_coords": [250, 50]}}], "Amortization_info_omr": [{"id": 80022758, "key": ["Amortization"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 80022758, "field_name": "Amortization Term", "document_id": 9000001, "additional_info": {"field_options": ["Fixed Rate", "Other (explain)", "Adjustable Rate"], "zone_coords": [250, 50]}}, {"id": 80017337, "key": ["Terms"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 80017337, "field_name": "Mortgage Lien Type", "document_id": 9000001, "additional_info": {"field_options": ["First Lien", "Subordinate Lien"], "zone_coords": [450, 50]}}], "Loan_Refinance_Type": [{"id": 45678, "key": ["Refinance"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 45678, "field_name": "Lona_refinance_type", "document_id": 9000001, "additional_info": {"field_options": ["No Cash Out", "Limited Cash Out", "Cash Out"], "zone_coords": [109, 65]}}, {"id": 456788, "key": ["Refinance Program"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 456788, "field_name": "Lona_refinance_Program", "document_id": 9000001, "additional_info": {"field_options": ["Full Documentation", "Interest Rate Reduction", "Streamlined without Appraisal", "Other"], "zone_coords": [109, 65]}}], "L2_title_information": [{"id": 4567899, "key": ["Estate"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 4567899, "field_name": "<PERSON><PERSON>_will_be_held", "document_id": 9000001, "additional_info": {"field_options": ["Fee Simple", "Leasehold Expiration Date / / (mm/dd/yyyy)"], "zone_coords": [200, 55]}}, {"id": 4567890, "key": ["<PERSON><PERSON>"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 4567890, "field_name": "manner_in_which_title", "document_id": 9000001, "additional_info": {"field_options": ["Sole Ownership", "Joint Tenancy with Right of Survivorship", "Life Estate", "Tenancy by the Entirety", "Tenancy in Common", "Other"], "zone_coords": [400, 70]}}], "L3_proposed_monthly_payment_for_property": [{"id": 79991, "key": ["First Mortgage"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Adjustable Rate"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 79991, "field_name": "first_mortgage_p&I", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$", "^\\$", "^S", "\\s*[^0-9\\.]+$"]}}}, {"id": 79992, "key": ["Subordinate"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["If Adjustable"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 79992, "field_name": "subordinate_lies_p&I", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 79993, "key": ["Homeowner's"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Initial Period", "Subsequent"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 79993, "field_name": "homeowners_insurance", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 79994, "key": ["Supplemental"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Subsequent", "Loan Features"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 79994, "field_name": "supplemental_property_insurance", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 79995, "key": ["Taxes"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Loan Features", "Balloon/"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 79995, "field_name": "property_taxes", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 79996, "key": ["Mortgage"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Balloon /", "Interest"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 79996, "field_name": "mortgage_insurance", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 79996, "key": ["Association"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Negative", "Prepayment"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 79996, "field_name": "association_project_dues", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 80022756, "key": ["Other"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Prepayment", "Temporary"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 80022756, "field_name": "All Other Monthly Payments", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 79996, "key": ["Total"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Temporary", "Other"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 79996, "field_name": "Total", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}], "L4_Qualifying_borrower_minimum_required_funds": [{"id": 8801, "key": ["Contract Price"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["B. Improvements"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8801, "field_name": "A_Sales_Contract_Price", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 80022757, "key": ["Repairs"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["C. <PERSON>"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 80022757, "field_name": "Alterations, improvements, repairs", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 8803, "key": ["separately)"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["D. For Refinance"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8803, "field_name": "C_Land", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 8804, "key": ["You Own)"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["E. Credit"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8804, "field_name": "D_For_Refinance", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 8805, "key": ["<PERSON> Owe)"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8805, "field_name": "E_Credit_Cards_and_Other_Debts_Paid_Off", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 8806, "key": ["Escrow Payments)"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8806, "field_name": "F_Borrower_Closing_Costs", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 8807, "key": ["Points $"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["H. <PERSON>"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8807, "field_name": "G_Discount_Points", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 8808, "key": ["H. <PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["TOTAL MORTGAGE"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8808, "field_name": "H_TOTAL", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 8809, "key": ["Refinancing)"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["K. <PERSON>"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8809, "field_name": "J_<PERSON>_New_Mortgage_Loans", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 8810, "key": ["K. <PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["TOTAL CREDITS"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8810, "field_name": "K_TOTAL_MORTGAGE_LOANS", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 8811, "key": ["<PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8811, "field_name": "L_Seller_Credits", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 8812, "key": ["Housing"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Lease", "N. TOTAL"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8812, "field_name": "M_Other_Credits", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 8813, "key": ["N. TOTAL"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["CALCULATION"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8813, "field_name": "N_TOTAL_CREDITS", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 8814, "key": ["TOTAL DUE"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["LESS TOTAL"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8814, "field_name": "TOTAL_DUE_FROM_BORROWER(s)", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 8815, "key": ["LESS TOTAL"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Cash", "$ Cash"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8815, "field_name": "LESS_TOTAL_MORTGAGE_LOANS", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 8816, "key": ["be verified."], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Borrower Name(s)"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 8816, "field_name": "Cash_From_To_the_Borrower", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}], "co_borrower_page1_omr_save": [{"id": 800106722, "key": ["Citizenship"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 800106722, "field_name": "co-Borrower_Citizenship", "document_id": 9000001, "additional_info": {"field_options": ["U.S. Citizen", "Permanent Resident Alien", "Non-Permanent Resident Alien"], "zone_coords": [190, 50]}}, {"id": 800113477, "key": ["<PERSON><PERSON>"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 800113477, "field_name": "co-Bo<PERSON><PERSON>_Martial_<PERSON>", "document_id": 9000001, "additional_info": {"field_options": ["Married", "Separated", "Unmarried"], "zone_coords": [190, 50]}}, {"id": 99900, "key": ["Type"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 99900, "field_name": "co-borrower_Type_of_credit", "document_id": 9000001, "additional_info": {"field_options": ["I am applying for individual credit.", "I am applying for joint credit."], "zone_coords": [400, 40]}}], "co_borrower_former_address_save": [{"id": 3011, "key": ["Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unit #"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3011, "field_name": "<PERSON>_<PERSON><PERSON><PERSON>_FormerAddress_street", "document_id": 9000001}, {"id": 3022, "key": ["Unit #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["City"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3022, "field_name": "co_<PERSON><PERSON><PERSON>_FormerAddress_Unit", "document_id": 9000001}, {"id": 3033, "key": ["City"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3033, "field_name": "co_<PERSON><PERSON><PERSON>_FormerAddress_city", "document_id": 9000001}, {"id": 3044, "key": ["State"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ZIP"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3044, "field_name": "<PERSON>_<PERSON><PERSON><PERSON>_FormerAddress_State", "document_id": 9000001}, {"id": 3055, "key": ["ZIP"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Country"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3055, "field_name": "<PERSON>_<PERSON><PERSON><PERSON>_FormerAddress_ZIP", "document_id": 9000001}, {"id": 3066, "key": ["Country"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["How Long"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3066, "field_name": "<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_Country", "document_id": 9000001}], "co_borrower_mailling_address_save": [{"id": 80022846, "key": ["Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unit #"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022846, "field_name": "co_<PERSON><PERSON><PERSON>_mailling_Address_street", "document_id": 9000001}, {"id": 2222, "key": ["Unit #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["City"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 2222, "field_name": "co_<PERSON>rrower_mailling_Address_Unit", "document_id": 9000001}, {"id": 80022843, "key": ["City"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022843, "field_name": "co_<PERSON><PERSON>er_Mailing_Address_City_DFCU_326053", "document_id": 9000001}, {"id": 80022845, "key": ["State"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ZIP"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022845, "field_name": "co_<PERSON><PERSON><PERSON>_mailling_Address_State", "document_id": 9000001}, {"id": 80022844, "key": ["Mailing Address"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Country"], "start_identifier": ["ZIP"], "possible_page_numbers": [1], "field_id": 80022844, "field_name": "co_<PERSON><PERSON><PERSON>_Mailing_Address_Zipcode_DFCU_326052", "document_id": 9000001}, {"id": 2244, "key": ["Country"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Does notapply", "Does not apply"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 2244, "field_name": "co_<PERSON><PERSON><PERSON>__mailling_Address_Country", "document_id": 9000001}], "Co_borrower_Employer_or_Business": [{"id": 80022830, "key": ["Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Gross"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022830, "field_name": "Co_Borrower_Business_Phone_DFCU_326041", "document_id": 9000001}, {"id": 6022, "key": ["Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unit", "Unit#"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 6022, "field_name": "Co_borrower_Employer_or_Business_street", "document_id": 9000001}, {"id": 5577, "key": ["Unit #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Base"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 5577, "field_name": "Co_borrower_employer_business_Unit", "document_id": 9000001}, {"id": 80022829, "key": ["City"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022829, "field_name": "Co_borrower_employer_business_city", "document_id": 9000001}, {"id": 80022831, "key": ["State"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ZIP"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022831, "field_name": "Co_borrower_employer_business_State", "document_id": 9000001}, {"id": 80022832, "key": ["ZIP"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Country"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022832, "field_name": "Co_borrower_employer_business_ZIP", "document_id": 9000001}, {"id": 5611, "key": ["Country"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Overtime"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 5611, "field_name": "Co_borrower_employer_business_Country", "document_id": 9000001}, {"id": 80022847, "key": ["Position or Title"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Check if"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022847, "field_name": "Co_borrower_Employer_or_Business_Position_or_Title", "document_id": 9000001}, {"id": 6099, "key": ["Start Date"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["(mm"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 6099, "field_name": "Co_borrower_Employer_or_Business_start_date", "document_id": 9000001}, {"id": 6666, "key": ["Base"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/month"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 6666, "field_name": "Co-borrower_Employer_or_Business_Base", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}], "Co_borrower_Gross_monthly_income_save": [{"id": 6555, "key": ["TOTAL"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/month"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 6555, "field_name": "Co_borrower_Employer_or_Business_TOTAL", "document_id": 9000001}], "Co_Borrower_Demographic_information2": [{"id": 80011666, "key": ["ethnicity"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 80011666, "field_name": "Co_borrower_Was_the_ethnicity_omr", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [900, 15]}}, {"id": 800117786, "key": ["sex"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 800117786, "field_name": "Co_borrower_Was_the_sex_omr", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [900, 15]}}, {"id": 8001178, "key": ["race"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 8001178, "field_name": "Co_borrower_Was_the_race_omr", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [900, 15]}}], "UW_Amortization_info_omr": [{"id": 3002598, "key": ["Amortization"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 3002598, "field_name": "Amortization Term", "document_id": 9000001, "additional_info": {"field_options": ["Fixed Rate", "Other (explain)", "Adjustable Rate"], "zone_coords": [250, 50]}}, {"id": 80017337, "key": ["Terms"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 80017337, "field_name": "Mortgage Lien Type", "document_id": 9000001, "additional_info": {"field_options": ["First Lien", "Subordinate Lien"], "zone_coords": [450, 50]}}], "UW_Loan_Refinance_Type": [{"id": 3001669, "key": ["Refinance"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 3001669, "field_name": "Lona_refinance_type", "document_id": 9000001, "additional_info": {"field_options": ["No Cash Out", "Limited Cash Out", "Cash Out"], "zone_coords": [109, 65]}}], "UW_L4_Qualifying_borrower_minimum_required_funds": [{"id": 3002601, "key": ["Contract Price"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["B. Improvements"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 3002601, "field_name": "A_Sales_Contract_Price", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 3002603, "key": ["Refinancing)"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["K. <PERSON>"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 3002603, "field_name": "J_<PERSON>_New_Mortgage_Loans", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 3002595, "key": ["<PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>"], "start_identifier": [""], "possible_page_numbers": [10], "field_id": 3002595, "field_name": "L_Seller_Credits", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"], "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}], "UW_OLD_occupancy_old_omr": [{"id": 3002586, "key": ["explain"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Complete this line"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3002586, "field_name": "Occupancy_Status", "document_id": 9000001, "additional_info": {"field_options": ["Primary Residence", "Secondary Residence", "Investment"], "zone_coords": [600, 50]}}], "UW_OLD_loan_purpose_omr": [{"id": 3001667, "key": ["Purpose"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Complete this line"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001667, "field_name": "PurposeLoan", "document_id": 9000001, "additional_info": {"field_options": ["Purchase", "Construction", "Other (explain):", "Refinance", "Construction-Permanent"], "zone_coords": [300, 50]}}], "UW_OLD_Mortgage_applied_for_save": [{"id": 3002590, "key": ["Mortgage"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Amount"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3002590, "field_name": "Loan_program_mortgage_applied", "document_id": 9000001, "additional_info": {"field_options": ["VA", "Conventional", "Other (explain):", "FHA", "USDA/Rural Housing Service"], "zone_coords": [300, 20]}}], "UW_OLD_amortization_type_omr": [{"id": 3002598, "key": ["Months"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3002598, "field_name": "AmortizationTypeOMR", "document_id": 9000001, "additional_info": {"field_options": ["Fixed Rate", "Other (explain):", "GPM", "ARM (type):"], "zone_coords": [400, 30]}}], "PC_Section1_omr_save": [{"id": 3000003, "key": ["Type"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 3000003, "field_name": "urla_credit_type", "document_id": 9000001, "additional_info": {"field_options": ["I am applying for individual credit.", "I am applying for joint credit."], "zone_coords": [400, 40]}}], "PC_Section4_loan_property_information": [{"id": 3000015, "key": ["<PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3000015, "field_name": "urla_loan_amount", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 3000095, "key": ["Property Address Street", "PropertyAddress Street", "Property Addross Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unit", "city"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3000095, "field_name": "urla_street_address", "document_id": 9000001, "output_format": {"address_parser_output_format": {"get_line1": true, "from_field": "ProStreet Address - 3000095"}, "string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3000096, "key": ["City"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3000096, "field_name": "urla_city", "document_id": 9000001}, {"id": 3000097, "key": ["State"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ZIP"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3000097, "field_name": "urla_state", "document_id": 9000001}, {"id": 3000098, "key": ["ZIP"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Country", "County"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3000098, "field_name": "urla_zip_code", "document_id": 9000001}, {"id": 3000879, "key": ["occupancy"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000879, "field_name": "urla_occupancy", "document_id": 9000001, "additional_info": {"field_options": ["Primary Residence", "Second Home", "Investment Property", "FHA Secondary Residence"], "zone_coords": [775, 12]}}, {"id": 3002584, "key": ["Number of Units"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Property Value", "Occupancy"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3002584, "field_name": "# of Units - 3002584", "document_id": 9000001}], "PC_Loan_originator_information": [{"id": 3000112, "key": ["Name"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Address"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 3000112, "field_name": "urla_loan_originator_organization_name", "document_id": 9000001}, {"id": 3000113, "key": ["Address"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Loan"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 3000113, "field_name": "urla_loan_originator_organization_address", "document_id": 9000001}, {"id": 3000114, "key": ["NMLSR ID #", "ID#"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State License"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 3000114, "field_name": "urla_loan_originator_organization_nmls", "document_id": 9000001}, {"id": 3000115, "key": ["State License ID#", "State License ID #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Loan Originator Name"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 3000115, "field_name": "urla_loan_originator_organization_state_license", "document_id": 9000001}, {"id": 3000116, "key": ["Loan Originator Name"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Loan"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 3000116, "field_name": "urla_loan_originator_name", "document_id": 9000001}, {"id": 3000117, "key": ["Loan Originator NMLSR ID#", "Loan Originator NMLSR ID #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 3000117, "field_name": "urla_loan_originator_nmls", "document_id": 9000001}, {"id": 3000118, "key": ["State License ID#", "State License ID #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Email"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 3000118, "field_name": "urla_loan_originator_state_license", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^.*State License ID#\\s*", "^.*State License ID #\\s*"], "retain_only_numbers": true, "remove_special_chars_from_beginning": true}}}, {"id": 3000119, "key": ["Email"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Phone"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 3000119, "field_name": "urla_loan_originator_email", "document_id": 9000001}, {"id": 3000120, "key": ["Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["DocuSigned", "Signature"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 3000120, "field_name": "urla_loan_originator_phone", "document_id": 9000001, "output_format": {"Phoneparseroutputformat": {"return_numeric_only": true, "extract_multiple": false}}}], "address_information_PostClose": [{"id": 3000278, "key": ["Street_Address1"], "direction": "inline", "type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [",", ""], "start_identifier": [""], "field_id": 3000278, "field_name": "addendum_to_cd_street_address", "document_id": 9000029, "output_format": {"address_parser_output_format": {"get_line1_and_line2": true, "from_field": "addendum_to_cd_street_address_pc"}, "string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3000279, "key": ["City_Address"], "direction": "inline", "type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [",", ""], "start_identifier": [""], "field_id": 3000279, "field_name": "addendum_to_cd_city", "document_id": 9000029, "output_format": {"address_parser_output_format": {"get_city": true, "from_field": "addendum_to_cd_street_address_pc"}, "string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3000280, "key": ["State_Address"], "direction": "inline", "type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [",", ""], "start_identifier": [""], "field_id": 3000280, "field_name": "addendum_to_cd_state", "document_id": 9000029, "output_format": {"address_parser_output_format": {"get_state": true, "from_field": "addendum_to_cd_street_address_pc"}, "string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3000281, "key": ["Zipcode_Address"], "direction": "inline", "type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [",", ""], "start_identifier": [""], "field_id": 3000281, "field_name": "addendum_to_cd_zip_code", "document_id": 9000029, "output_format": {"string_operations_output_format": {"replace_if_match": [["0", ""]], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "replace_within_string": [["o", "0"], ["O", "0"]]}, "address_parser_output_format": {"get_zip_code": true, "from_field": "addendum_to_cd_street_address_pc"}}}], "UW_Section1_omr_save": [{"id": 3004737, "key": ["Citizenship"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3004737, "field_name": "Citizenship - 3004737", "document_id": 9000001, "additional_info": {"field_options": ["U.S. Citizen", "Permanent Resident Alien", "Non-Permanent Resident Alien"], "zone_coords": [190, 50]}}, {"id": 3004738, "key": ["<PERSON><PERSON>"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3004738, "field_name": "Marital Status - 3004738", "document_id": 9000001, "additional_info": {"field_options": ["Married", "Separated", "Unmarried"], "zone_coords": [190, 50]}}], "UW_current_address_section": [{"id": 3000879, "key": ["Months"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000879, "field_name": "Occupancy Type - 3000879", "document_id": 9000001, "additional_info": {"field_options": ["No primary housing expense", "Own", "Rent"], "zone_coords": [400, 20]}}, {"id": 3004748, "key": ["Rent ($", "Rent"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["/", "month", "/month", "If at Current"], "start_identifier": [""], "field_id": 3004748, "field_name": "Rent Amount -3004748", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S", "x"], "remove_from_end": ["\\s*", "\\.", "[^\\d]*$"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}], "UW_Gross_monthly_income_save": [{"id": 3004770, "key": ["base"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Street", "/", "/ Overtime"], "start_identifier": [""], "field_id": 3004770, "field_name": "Base Income: - 3004770", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["[^\\d,.]+$"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3004771, "key": ["Overtime"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "Bonus"], "start_identifier": [""], "field_id": 3004771, "field_name": "Overtime Income: - 3004771", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["[^\\d,.]+$"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3004772, "key": ["Bonus"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "Position", "Commission"], "start_identifier": [""], "field_id": 3004772, "field_name": "Bonus Income: - 3004772", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["[^\\d,.]+$"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3004773, "key": ["Commission"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "/I Military Entitlements"], "start_identifier": [""], "field_id": 3004773, "field_name": "Commission Income: - 3004773", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["[^\\d,.]+$"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3004777, "key": ["Loss) Other"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "TOTAL"], "start_identifier": [""], "field_id": 3004777, "field_name": "Other: - 3004777", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["[^\\d,.]+$"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3004778, "key": ["TOTAL"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "/month"], "start_identifier": [""], "field_id": 3004778, "field_name": "Total Income - 3004778", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["[^\\d,.]+$"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}], "UW_Ownership_Interest_save": [{"id": 3004767, "key": ["Business"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["total"], "start_identifier": [""], "field_id": 3004767, "field_name": "Ownership Interest:- 3004767", "document_id": 9000001, "additional_info": {"field_options": ["I have an ownership share of less than 25%", "I have an ownership share of 25% or more"], "zone_coords": [310, 30]}}, {"id": 3004765, "key": ["How"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["total"], "start_identifier": [""], "field_id": 3004765, "field_name": "3004765 - Employment Type:", "document_id": 9000001, "additional_info": {"field_options": ["YES"], "zone_coords": [180, 55]}, "output_format": {"Omr_Checkbox_Output_Format": {"checkbox_type": true, "default_value_if_unchecked": "NO"}}}], "UW_Financial_Information_Real_Estate_save": [{"id": 3004809, "key": ["Address Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unit#", "Unit #"], "start_identifier": [""], "field_id": 3004809, "field_name": "Street Address - 3004809", "document_id": 9000001}, {"id": 3004810, "key": ["Unit #", "Unit#"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["City"], "start_identifier": [""], "field_id": 3004810, "field_name": "Unit/Bldg/Apt - 3004810", "document_id": 9000001}, {"id": 3004811, "key": ["City"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "field_id": 3004811, "field_name": "City - 3004811", "document_id": 9000001}, {"id": 3004812, "key": ["State"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ZIP", "Z p"], "start_identifier": [""], "field_id": 3004812, "field_name": "State - 3004812", "document_id": 9000001}, {"id": 3004813, "key": ["ZIP"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Country"], "start_identifier": [""], "field_id": 3004813, "field_name": "Zip Code - 3004813", "document_id": 9000001}, {"id": 3004814, "key": ["Country"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Intended Occupancy", "Monthly Insurance"], "start_identifier": [""], "field_id": 3004814, "field_name": "County - 3004814", "document_id": 9000001}], "UW_Section4_loan_property_information": [{"id": 3005717, "key": ["occupancy"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [5], "field_id": 3005717, "field_name": "Occupancy Type - 3005717", "document_id": 9000001, "additional_info": {"field_options": ["Primary Residence", "Second Home", "Investment Property", "FHA Secondary Residence"], "zone_coords": [775, 12]}}, {"id": 3004733, "key": ["Amount"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3004733, "field_name": "Transaction Type  - 3004733", "document_id": 9000001, "additional_info": {"field_options": ["Purchase", "Refinance", "Other (specify)"], "zone_coords": [800, 12]}}, {"id": 3004749, "key": ["Amount"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3004749, "field_name": "Transaction Type  - 3004749", "document_id": 9000001, "additional_info": {"field_options": ["Purchase", "Refinance", "Other (specify)"], "zone_coords": [800, 12]}}, {"id": 3000015, "key": ["<PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>", "Loan Propose"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3000015, "field_name": "Loan Amount - 3000015", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["[^0-9.,]", "\\s*[^0-9]$"]}}}, {"id": 3000095, "key": ["Property Address Street", "PropertyAddress Street", "Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unit", "city"], "start_identifier": [""], "field_id": 3000095, "field_name": "Street Address - 3000095", "document_id": 9000001, "output_format": {"address_parser_output_format": {"get_line1": true, "from_field": "ProStreet Address - 3000095"}, "string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3004829, "key": ["Unit#", "Unit #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["City"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3004829, "field_name": "Unit - 3004829", "document_id": 9000001}, {"id": 3000096, "key": ["City"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3000096, "field_name": "City - 3000096", "document_id": 9000001}, {"id": 3000097, "key": ["State"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ZIP", "71P", "LIP"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3000097, "field_name": "State - 3000097", "document_id": 9000001}, {"id": 3000098, "key": ["ZIP", "71P", "LIP"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["County", "Country"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3000098, "field_name": "Zip Code - 3000098", "document_id": 9000001}, {"id": 3004830, "key": ["County", "Country"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "propable_place": "individual", "use_match": "fuzzy", "end_identifier": ["Number of Units", "Property Value"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3004830, "field_name": "County - 3004830", "document_id": 9000001}, {"id": 3002584, "key": ["Number of Units"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Property Value", "Occupancy"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 3002584, "field_name": "# of Units - 3002584", "document_id": 9000001}, {"id": 3004734, "key": ["Property Address Street", "Property Addross Street", "PropertyAddress Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["County"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3004734, "field_name": "Property Address - 3004734", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_keywords": ["Unit #", "City", "State", "ZIP", "Zip"]}}}], "UW_Co_borrower_Section1_omr_save": [{"id": 3005718, "key": ["Citizenship"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3005718, "field_name": "Co-Borrower Citizenship - 3005718", "document_id": 9000001, "additional_info": {"field_options": ["U.S. Citizen", "Permanent Resident Alien", "Non-Permanent Resident Alien"], "zone_coords": [190, 50]}}, {"id": 3005719, "key": ["<PERSON><PERSON>"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3005719, "field_name": "Co-Borrower Marital Status - 3005719", "document_id": 9000001, "additional_info": {"field_options": ["Married", "Separated", "Unmarried"], "zone_coords": [190, 50]}}], "UW_Co_borrower_Gross_monthly_income_save": [{"id": 3005725, "key": ["base"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "/ Overtime"], "start_identifier": [""], "field_id": 3005725, "field_name": "Base Income: - 3005725", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3005726, "key": ["Overtime"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "Bonus"], "start_identifier": [""], "field_id": 3005726, "field_name": "Overtime Income: - 3005726", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3005727, "key": ["Bonus"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "Position", "Commission"], "start_identifier": [""], "field_id": 3005727, "field_name": "Bonus Income: - 3005727", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3005728, "key": ["Commission"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "/I Military Entitlements"], "start_identifier": [""], "field_id": 3005728, "field_name": "Commission Income: - 3005728", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3004777, "key": ["Loss) Other"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "TOTAL"], "start_identifier": [""], "field_id": 3004777, "field_name": "Other: - 3004777", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3005731, "key": ["TOTAL"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "/month"], "start_identifier": [""], "field_id": 3005731, "field_name": "Total Income - 3005731", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}], "UW_project_type_omr": [{"id": 3004832, "key": ["project"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 3004832, "field_name": "Property Type - 3004832", "document_id": 9000001, "additional_info": {"field_options": ["Condominium", "Cooperative", "Planned Unit Development (PUD)", "Property is not located in a project"], "zone_coords": [850, 12]}}], "UW_Mortgage_Loan_info_omr": [{"id": 3002597, "key": ["Lien Type"], "direction": "right", "type": "number", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["FHA"], "start_identifier": [""], "field_id": 3002597, "field_name": "Interest Rate  - 3002597", "document_id": 9000001, "output_format": {"number_parser_output_format": {"extract_multiple": false, "pattern_keys": ["percent_or_rate"]}}}], "UW_Mortgage_insurance_save": [{"id": 3004754, "key": ["First P&I (Qualifying)", " (Qualifying)"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Total Housing Payments", "Second P&I", "Negative Net"], "start_identifier": [], "field_id": 3004754, "field_name": "Monthly PI", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"]}}}, {"id": 3004756, "key": ["Mortgage Insurance"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Total Expense Payments", "HOA Fees"], "start_identifier": [], "field_id": 3004756, "field_name": "Annual MI Premium", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"]}}}, {"id": 3004763, "key": ["Mortgage Insurance"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Total Expense Payments", "HOA Fees"], "start_identifier": [], "field_id": 3004763, "field_name": "3004763 - Annual MI Premium", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"]}}}], "Disbursement_of_Proceeds_PostClose_omr": [{"id": 3005934, "key": ["Primary"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Bank Name"], "start_identifier": [""], "field_id": 3005934, "field_name": "wire_present", "document_id": 8010382, "additional_info": {"field_options": ["Wire proceeds per the instructions provided below - DO NOT attach voided checks or deposit slips"], "zone_coords": [-340, -40]}, "output_format": {"Omr_Flagger_Output_Format": {"checkbox_type": true, "default_value_if_unchecked": "NO"}}}], "UW_Borrower_RealEstate_OMR_save": [{"id": 3004808, "key": ["them"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 3004808, "field_name": "3004808 -Does the borrower own any real estate?", "document_id": 9000001, "additional_info": {"field_options": ["YES"], "zone_coords": [800, 12]}, "output_format": {"Omr_Checkbox_Output_Format": {"checkbox_type": true, "default_value_if_unchecked": "NO"}}}], "UW_Mortgage_property_OMR_save": [{"id": 3004822, "key": ["Loans"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 3004822, "field_name": "3004822 -Do you have mortgage on this property?", "document_id": 9000001, "additional_info": {"field_options": ["YES"], "zone_coords": [800, 12]}, "output_format": {"Omr_Checkbox_Output_Format": {"checkbox_type": true, "default_value_if_unchecked": "NO"}}}], "UW_4B_mortgage_subject_property_OMR_save": [{"id": 3004764, "key": ["Refinancing"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 3004764, "field_name": "3004764 - Is there additional mortgage on the subject property?", "document_id": 9000001, "additional_info": {"field_options": ["YES"], "zone_coords": [800, 12]}, "output_format": {"Omr_Checkbox_Output_Format": {"checkbox_type": true, "default_value_if_unchecked": "NO"}}}], "UW_3b_own_additional_properties_save1": [{"id": 3004828, "key": ["Property"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 3004828, "field_name": "Do you own any additional properties? - 3004828", "document_id": 9000001, "additional_info": {"field_options": ["YES"], "zone_coords": [800, 12]}, "output_format": {"Omr_Checkbox_Output_Format": {"checkbox_type": true, "default_value_if_unchecked": "NO"}}}], "UW_Borrower_additional_info_OMR_save1": [{"id": 3005732, "key": ["income"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 3005732, "field_name": "Does coborrower have additional income? - 3005732", "document_id": 9000001, "additional_info": {"field_options": ["YES"], "zone_coords": [220, 12]}, "output_format": {"Omr_Checkbox_Output_Format": {"checkbox_type": true, "default_value_if_unchecked": "NO"}}}], "UW_property_flood_zone_save": [{"id": 3004835, "key": ["LETTERS"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 3004835, "field_name": "3004835 - Is the property located in flood zone?", "document_id": 9000001, "additional_info": {"field_options": ["YES", "NO"], "zone_coords": [280, 12]}}], "cd_cash_from_borrower_omr": [{"id": 3005890, "key": ["Close"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3005890, "field_name": "cash_from_borrower", "document_id": 9000181, "additional_info": {"field_options": ["From", "To"], "zone_coords": [280, 30]}, "get_checked_box_amount": ["From"], "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^.*?Cash to Close[^\\d\\$]*\\$?\\s*"], "remove_from_end": ["\\s.*$"], "remove_special_chars_from_beginning": false, "remove_special_chars_from_end": false}}}], "si_other_rider_name_omr": [{"id": 3000805, "key": ["Riders"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000805, "field_name": "other_rider_name", "document_id": 9000869, "additional_info": {"field_options": ["Adjustable Rate Rider", "Condominium Rider", "1-4 Family Rider", "Planned Unit Development Rider", "MERS Rider", "Other(s) [specify]", "Second Home Rider"], "zone_coords": [700, 120], "field_options_variant": true}, "get_checked_box_amount": ["Other(s) [specify]"], "output_format": {"string_operations_output_format": {"remove_till_from_beginning": ["specify"], "remove_special_chars_from_beginning": true}}}], "mr_cooper_appendix_a_prepayment_period_date_information": [{"id": 3002426, "key": ["Date"], "direction": "right", "include_key": true, "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3002426, "field_name": "Prepayment_Period_date_range", "document_id": 8011135, "output_format": {"string_operations_output_format": {"contains": ["10-9", "15-14", "16-15", "17-16", "18-17", "20-19", "21-20", "2-1", "2BD of receipt", "3BD of receipt", "5BD of receipt", "Calendar Month", "Interest is paid through the date of application of payoff", "Interest is paid through the date of application of proceeds", "Shellpoint: 15-14, Other Servicers: Calendar month"]}}}], "mr_cooper_msa_servicer_eod_information": [{"id": 3002546, "key": ["The Master Servicer, for the benefit of the Indenture"], "direction": "right", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["Upon any termination by the Master Servicer"], "start_identifier": [""], "field_id": 3002546, "field_name": "Servicer_EOD_Language-Servicer_EOD", "document_id": 8011135}, {"id": 3002545, "key": ["The Master Servicer, for the benefit of the Indenture"], "direction": "right", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["SECTION 3.02"], "start_identifier": [""], "field_id": 3002545, "field_name": "Servicer_Non-Compliance_Language", "document_id": 8011135}], "mr_cooper_msa_ms_retained_information": [{"id": 3002438, "key": ["to pay to itself any investment"], "direction": "down", "type": "text", "return_type": "text", "include_key": true, "use_match": "fuzzy", "end_identifier": ["the cost of any enforcement", "to pay to the Seller"], "start_identifier": [""], "field_id": 3002438, "field_name": "Master_Servicer_Retained_Interest", "document_id": 8011135, "output_format": {"string_operations_output_format": {"remove_from_end": ["[;:\\s]*[\\(\\[]?[ivxlcIVXLC\\d]+[\\)\\]]?\\s*$"]}}}], "mr_cooper_msa_custodial_account_deposit_information": [{"id": 3002465, "key": ["On each Master Servicer Remittance Date"], "direction": "down", "type": "text", "return_type": "text", "include_key": true, "end_identifier": ["Collection Account", "Collection Period"], "start_identifier": [""], "field_id": 3002465, "field_name": "Custodial_Account_Deposit_Time-when_must_master_servicer_deposit", "document_id": 8011135}], "mr_cooper_msa_ms_remittance_report_information": [{"id": 3002462, "key": ["Not later than"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["Section 3.08", "Annual Statement as to Compliance"], "start_identifier": [""], "field_id": 3002462, "field_name": "Master_Servicer_Remittance_Report_Due_Language", "document_id": 8011135}, {"id": 3002506, "key": ["a monthly remittance advice"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["Collection Period"], "start_identifier": [""], "field_id": 3002506, "field_name": "MS_Default_Reporting", "document_id": 8011135}], "mr_cooper_msa_annual_cert_information": [{"id": 3002450, "key": ["On or prior to"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["Section 3.09", "Master Servicing Compensation"], "start_identifier": [""], "field_id": 3002450, "field_name": "Annual_Officer_Cert_Language", "document_id": 8011135}, {"id": 3002448, "key": ["On or prior to"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": false, "end_identifier": ["thereafter", "Master Servicer"], "start_identifier": [""], "field_id": 3002448, "field_name": "Annual_Officer_Cert_Due_Date?", "document_id": 8011135, "output_format": {"date_parser_output_format": {"secondary_date": true, "return_format": "%m/%d/%Y", "date_format": "\\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\\s+\\d{1,2}(?:st|nd|rd|th)?(?:,?\\s+\\d{4})?"}, "string_operations_output_format": {"update_year_to_next": true}}}, {"id": 3002449, "key": ["deliver to"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": false, "end_identifier": ["a statement"], "start_identifier": [""], "field_id": 3002449, "field_name": "Annual_Officer_Cert_Deliver_To_Whom?", "document_id": 8011135}], "mr_cooper_msa_merge_consolidation_information": [{"id": 3002491, "key": ["Any Person into which the Master Servicer"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["SECTION 6.02"], "start_identifier": [""], "field_id": 3002491, "field_name": "Merger_or_Consolidation", "document_id": 8011135}], "mr_cooper_msa_section_vi_information": [{"id": 3002519, "key": ["The Master Servicer shall"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["SECTION 6.04", "Assignment or Delegation of Duties by the Master Service"], "start_identifier": [""], "field_id": 3002519, "field_name": "MS_Resignation_Language", "document_id": 8011135}, {"id": 3002492, "key": ["Except as expressly provided herein"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["SECTION 6.05", "Indemnification; Third-Party Claims", "Indemnification by the Master Servicer", "Indemnification"], "start_identifier": [""], "field_id": 3002492, "field_name": "Assign_rights_to_Master_Servicing_Comp_language", "document_id": 8011135}, {"id": 3002495, "key": ["Except as expressly provided herein"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["SECTION 6.05", "Indemnification; Third-Party Claims", "Indemnification by the Master Servicer", "Indemnification"], "start_identifier": [""], "field_id": 3002495, "field_name": "Master_Servicer_Assignment_Language", "document_id": 8011135}, {"id": 3002497, "key": ["Except as expressly provided herein"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["SECTION 6.05", "Indemnification; Third-Party Claims", "Indemnification by the Master Servicer", "Indemnification"], "start_identifier": [""], "field_id": 3002497, "field_name": "Restrictions_on_Use_of_Affiliate_to_Perform_Certain_Third-Party_Services", "document_id": 8011135}, {"id": 3002499, "key": ["Except as expressly provided herein"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["SECTION 6.05", "Indemnification; Third-Party Claims", "Indemnification by the Master Servicer", "Indemnification"], "start_identifier": [""], "field_id": 3002499, "field_name": "Terms_and_Conditions_of_Delegation_of_Duties_to_an_Affiliate", "document_id": 8011135}], "mr_cooper_msa_eod_deposit_payments_information": [{"id": 3002524, "key": ["any failure by the Master Servicer"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["any failure on the part"], "start_identifier": [""], "field_id": 3002524, "field_name": "EOD-Deposit_payments_cure_period", "document_id": 8011135}, {"id": 3002550, "key": ["all authority and power of the Master Servicer"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["If a successor master servicer", "any established housing and home finance"], "start_identifier": [""], "field_id": 3002550, "field_name": "Who_has_the_right_to_appoint_a_successor_master_servicer?", "document_id": 8011135}], "mr_cooper_msa_limited_liability_ms_information": [{"id": 3002520, "key": ["The appointment of a successor master servicer"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["The predecessor Master Servicer and the successor master servicer ", "The predecessor Master Servicer any successor master servicer"], "start_identifier": [""], "field_id": 3002520, "field_name": "Limited_liability_of_Successor_Master_Servicer", "document_id": 8011135}], "mr_cooper_msa_amendment_restrictions_information": [{"id": 3002501, "key": ["Subject to"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["SECTION 8.13", "Entire Agreement"], "start_identifier": [""], "field_id": 3002501, "field_name": "Amendment_Restrictions", "document_id": 8011135}], "mr_cooper_ind_ucc_information": [{"id": 3002477, "key": ["all filings under the UCC"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": [";", "take any action or fail"], "start_identifier": [""], "field_id": 3002477, "field_name": "UCC_Language", "document_id": 8011135}], "mr_cooper_psa_control_holder_information": [{"id": 3002065, "key": ["the majority"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["If the Class Principal"], "start_identifier": [""], "field_id": 3002065, "field_name": "Controlling_Holder", "document_id": 8011134}], "mr_cooper_psa_custodian_information": [{"id": 3002066, "key": ["Custodial Agreement"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": false, "end_identifier": ["<PERSON><PERSON><PERSON><PERSON>", "Exhibit D"], "start_identifier": [""], "field_id": 3002066, "field_name": "Custodian_Names", "document_id": 8011134, "output_format": {"ner_service_req_output_format": {"text_field_name": "Custodian_Names", "label": ["ORGANIZATION"]}}}], "mr_cooper_psa_annual_statement_compliance_information": [{"id": 3002110, "key": ["make available to", "make available) to"], "direction": "right", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": false, "end_identifier": ["on or before"], "start_identifier": [""], "field_id": 3002110, "field_name": "Annual_Documents_Due_to_Relationships", "document_id": 8011134}, {"id": 3002111, "key": ["Officer’s Certificate", "Officer's Certificate"], "direction": "right", "type": "contains", "return_type": "text", "use_match": "fuzzy", "include_key": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002111, "field_name": "Are_Annual_Officers_Certificates_Due?", "document_id": 8011134, "output_format": {"string_operations_output_format": {"replace_with_custom_value": "Yes", "set_default_if_empty": "No"}}}, {"id": 3002112, "key": ["on or before"], "direction": "right", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": false, "end_identifier": ["an officer"], "start_identifier": [""], "field_id": 3002112, "field_name": "Annual_Officer_Cert_Due_Date?", "document_id": 8011134, "output_format": {"llm_operations_output_format": {"custom": false}}}, {"id": 3002113, "key": ["make available to", "make available) to"], "direction": "right", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": false, "end_identifier": ["on or before"], "start_identifier": [""], "field_id": 3002113, "field_name": "Annual_Officer_Cert_Deliver_To_Whom?", "document_id": 8011134}], "mr_cooper_psa_merger_consolidation_information": [{"id": 3002155, "key": ["Any Person into which the Master Servicer"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": true, "end_identifier": ["Section 9.06", "Resignation of Master Servicer"], "start_identifier": [""], "field_id": 3002155, "field_name": "Merger_or_Consolidation", "document_id": 8011134}], "mr_cooper_psa_amendment_information": [{"id": 3002165, "key": ["Amendment"], "direction": "down", "type": "text", "return_type": "text", "use_match": "fuzzy", "include_key": false, "end_identifier": ["Section 11.04", "Voting Rights", "Section 11.05", "Provision of Information"], "start_identifier": [""], "field_id": 3002165, "field_name": "Amendment_Restrictions", "document_id": 8011134}], "UW_Employment_EndDate_save1": [{"id": 3004769, "key": ["End Date"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["(mm/dd/yyyy)", "(mm"], "start_identifier": [], "field_id": 3004769, "field_name": "3004769 - Employment End Date:", "document_id": 9000001}], "UW_Borrower_additional_info_OMR_save": [{"id": 3004779, "key": ["income"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 3004779, "field_name": "3004779 - Does borrower have additional income?", "document_id": 9000001, "additional_info": {"field_options": ["YES"], "zone_coords": [220, 12]}, "output_format": {"Omr_Checkbox_Output_Format": {"checkbox_type": true, "default_value_if_unchecked": "NO"}}}], "Report_And_Certificate_Of_Loan_Disbursemt_Amortization_omr": [{"id": 3000919, "key": ["AMORTIZATION"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [], "possible_page_numbers": [], "field_id": 3000919, "field_name": "va261820_9_amortization_type", "document_id": 9000828, "additional_info": {"field_options": ["FIXED", "ADJUSTABLE", "HYBRID-ARM"], "zone_coords": [942, 91]}}], "Report_And_Certificate_Of_Loan_Disbursemt_Arm_type_omr": [{"id": 3000920, "key": ["ARM TYPE"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [], "possible_page_numbers": [], "field_id": 3000920, "field_name": "va261820_9_arm_type", "document_id": 9000828, "additional_info": {"field_options": ["3/1", "5/1", "7/1", "10/1"], "zone_coords": [700, 50]}}], "Report_And_Certificate_Of_Loan_Disbursemt_Borrower_Ethnicity_type_omr": [{"id": 3000930, "key": ["INITIALS"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000930, "field_name": "va261820_ethnicity_borrower", "document_id": 9000828, "additional_info": {"field_options": ["HISPANIC OR LATINO", "NOT HISPANIC OR LATINO"], "zone_coords": [140, 60]}}], "Report_And_Certificate_Of_Loan_Disbursemt_Borrower_Race_type_omr": [{"id": 3000931, "key": ["BLACK"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [], "possible_page_numbers": [], "field_id": 3000931, "field_name": "va261820_race_borrower", "document_id": 9000828, "additional_info": {"field_options": ["AMERICAN INDIAN OR ALASKAN NATIVE", "ASIAN", "BLACK OR AFRICAN AMERICAN", "NATIVE HAWAIIAN OR OTHER PACIFIC ISLANDER", "WHITE"], "zone_coords": [-230, 50]}}], "Report_And_Certificate_Of_Loan_Disbursemt_Borrower_Gender_type_omr": [{"id": 3000932, "key": ["BLACK"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [], "possible_page_numbers": [], "field_id": 3000932, "field_name": "va261820_gender_borrower", "document_id": 9000828, "additional_info": {"field_options": ["MALE", "FEMALE"], "zone_coords": [200, 60]}}], "Report_And_Certificate_Of_Loan_Disbursemt_CoBorrower_Ethnicity_type_omr": [{"id": 3000935, "key": ["INITIALS"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000935, "field_name": "va261820_ethnicity_coborrower", "document_id": 9000828, "additional_info": {"field_options": ["HISPANIC OR LATINO", "NOT HISPANIC OR LATINO"], "zone_coords": [140, 70]}}], "Report_And_Certificate_Of_Loan_Disbursemt_CoBorrower_Race_type_omr": [{"id": 3000936, "key": ["BLACK"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [], "possible_page_numbers": [], "field_id": 3000936, "field_name": "va261820_race_coborrower", "document_id": 9000828, "additional_info": {"field_options": ["AMERICAN INDIAN OR ALASKAN NATIVE", "ASIAN", "BLACK OR AFRICAN AMERICAN", "NATIVE HAWAIIAN OR OTHER PACIFIC ISLANDER", "WHITE"], "zone_coords": [-230, 50]}}], "Report_And_Certificate_Of_Loan_Disbursemt_CoBorrower_Gender_type_omr": [{"id": 3000937, "key": ["BLACK"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [], "field_id": 3000937, "field_name": "va261820_gender_coborrower", "document_id": 9000828, "additional_info": {"field_options": ["MALE", "FEMALE"], "zone_coords": [200, 50]}}], "UW_cash_from_borrower_omr": [{"id": 3005733, "key": ["Close"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3005733, "field_name": "3005733 - Cash From Borrower", "document_id": 9000181, "additional_info": {"field_options": ["From", "<PERSON>"], "zone_coords": [280, 12]}, "get_checked_box_amount": ["From"], "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^.*?Cash to Close[^\\d\\$]*\\$?\\s*"], "remove_from_end": ["\\s.*$"], "remove_special_chars_from_beginning": false, "remove_special_chars_from_end": false}}}, {"id": 3005831, "key": ["Close"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3005831, "field_name": "3005831 - <PERSON>", "document_id": 9000181, "additional_info": {"field_options": ["From", "<PERSON>"], "zone_coords": [280, 12]}, "get_checked_box_amount": ["<PERSON>"], "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^.*?Cash to Close[^\\d\\$]*\\$?\\s*"], "remove_from_end": ["\\s.*$"], "remove_special_chars_from_beginning": false, "remove_special_chars_from_end": false}}}], "UW_EMD_save": [{"id": 3005730, "key": ["<PERSON><PERSON><PERSON><PERSON>"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>", "01 Excess Deposit", "Excess Deposit"], "start_identifier": [], "possible_page_numbers": [1], "field_id": 3005730, "field_name": "3005730 - EMD", "document_id": 9000181, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3005731, "key": ["02 Closing Costs Paid at Closing (J)", "Closing Costs Paid"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["03 Existing <PERSON><PERSON>(s)", "Existing Loan(s)"], "start_identifier": [], "possible_page_numbers": [1], "field_id": 3005731, "field_name": "3005731 - Closing Cost Paid by <PERSON><PERSON>", "document_id": 9000181, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}], "cash_close_save111": [{"id": 11111, "key": ["close"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 11111, "field_name": "3004835 - Is the property located in flood zone?", "document_id": 9000181, "additional_info": {"field_options": ["From", "To"]}}], "rp_underwriting_ltv": [{"id": 3006128, "key": ["LTV", "LTVICLTVIHCLTV", "LTV/CCTV", "LTVlCLTV", "LTVICLTV", "LW", "LN"], "direction": "right", "type": "number", "return_type": "", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["HOA", "Supplemental Property Insurance", "Lease", "First", "Lease/Ground Rent", "x <PERSON><PERSON>'<PERSON>,u'd", "Homeowner's Insurance", "First Mortgage P&I", "CLTV/TLTV", "/ $ Total Obligations"], "start_identifier": [], "possible_page_numbers": [1], "field_id": 3006128, "field_name": "LTV", "document_id": 9000959, "output_format": {"string_operations_output_format": {"remove_from_end": ["%", "$"], "remove_special_chars_from_end": true}}}], "RP_share_information_save": [{"id": 3005884, "key": ["Form"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3005884, "field_name": "Do not share information about my creditworthiness with your affiliates for their everyday business purposes.", "document_id": 9002254, "additional_info": {"field_options": ["Yes", "No"], "zone_coords": [-120, 310]}, "output_format": {"Omr_Checkbox_Output_Format": {"checkbox_type": true, "default_value_if_unchecked": "NO"}}}, {"id": 3005885, "key": ["Form"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3005885, "field_name": "Do not allow your affiliates to use my personal information to market to me.", "document_id": 9002254, "additional_info": {"field_options": ["Yes", "No"], "zone_coords": [-120, 310]}, "output_format": {"Omr_Checkbox_Output_Format": {"checkbox_type": true, "default_value_if_unchecked": "NO"}}}, {"id": 3005886, "key": ["Form"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3005886, "field_name": "Do not share my personal information with nonaffiliates to market their products and services to me.", "document_id": 9002254, "additional_info": {"field_options": ["Yes", "No"], "zone_coords": [-120, 310]}, "output_format": {"Omr_Checkbox_Output_Format": {"checkbox_type": true, "default_value_if_unchecked": "NO"}}}], "Valon_Wisconsin_Tax_Election_Option_save": [{"id": 80022279, "key": ["Election"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 80022279, "field_name": "Wisconsin_Tax_Election_Option", "document_id": 9002254, "additional_info": {"field_options": ["Option 1", "Option 2", "Option 3"], "zone_coords": [-50, 300]}}], "Phl_marital_status": [{"id": 80000911, "key": ["<PERSON><PERSON>"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 80000911, "field_name": "Borrower Marital Status", "document_id": 9000001, "additional_info": {"field_options": ["Married", "Separated", "Unmarried"], "zone_coords": [190, 50]}}], "Phl_mailling_address_save": [{"id": 80000812, "key": ["Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unit #"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000812, "field_name": "(Mailing) Street", "document_id": 9000001}, {"id": 80000813, "key": ["City"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000813, "field_name": "(Mailing) City", "document_id": 9000001}, {"id": 80000814, "key": ["State"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ZIP"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000814, "field_name": "(Mailing) State", "document_id": 9000001}, {"id": 80000815, "key": ["Mailing Address"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Country"], "start_identifier": ["ZIP"], "possible_page_numbers": [1], "field_id": 80000815, "field_name": "(Mailing) Zip", "document_id": 9000001}], "Phl_occupancy_old_omr": [{"id": 80000807, "key": ["occupancy"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [5], "field_id": 80000807, "field_name": "Occupancy", "document_id": 9000001, "additional_info": {"field_options": ["Primary Residence", "Second Home", "Investment Property", "FHA Secondary Residence"], "zone_coords": [775, 12]}}], "Phl_loan_purpose_omr": [{"id": 80022869, "key": ["Purpose"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 80022869, "field_name": "<PERSON><PERSON>", "document_id": 9000001, "additional_info": {"field_options": ["Purchase", "Refinance", "Other (specify)"], "zone_coords": [450, 12]}}], "Phl_Borrower_Self_employed": [{"id": 80000824, "key": ["How"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["total"], "start_identifier": [""], "field_id": 80000824, "field_name": "Primary Borrower Self-Employed", "document_id": 9000001, "additional_info": {"field_options": ["YES"], "zone_coords": [180, 55]}, "output_format": {"Omr_Checkbox_Output_Format": {"checkbox_type": true, "default_value_if_unchecked": "NO"}}}], "Phl_Section5_Borrower_Homebuyer": [{"id": 80000816, "key": ["ownership"], "direction": "right", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 80000816, "field_name": "First Time Homebuyer", "document_id": 9000001, "additional_info": {"field_options": ["NO", "YES"], "zone_coords": [770, 15]}, "output_format": {"Omr_Checkbox_Output_Format": {"checkbox_type": true, "default_value_if_unchecked": "NO"}}}], "Phl_Lien_Type_omr": [{"id": 80000914, "key": ["Terms"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 80000914, "field_name": "Lien Position", "document_id": 9000001, "additional_info": {"field_options": ["First Lien", "Subordinate Lien"], "zone_coords": [450, 50]}}], "Phl_Co_Borrower_marital_status": [{"id": 80000916, "key": ["<PERSON><PERSON>"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 80000916, "field_name": "Co-Borrower Marital Status", "document_id": 9000001, "additional_info": {"field_options": ["Married", "Separated", "Unmarried"], "zone_coords": [190, 50]}}], "Phl_Mortgage_Type_omr": [{"id": 80000839, "key": ["Product"], "direction": "down", "type": "checkbox", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000839, "field_name": "Mortgage Type", "document_id": 9000181, "additional_info": {"field_options": ["Conventional", "FHA", "VA", "USDA-RD", "Other"], "zone_coords": [200, 100]}}]}