[{"id": 80012124, "key": ["issued by", "schedule a"], "direction": "up_till_start", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 80012124, "field_name": "Policy_Type_Text", "document_id": 9002233, "additional_info": {"has_keyword": ["short"]}, "post_process": {"policy_type_text_post_processor": {"custom": true}}, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["[a-z\\s\\W]+"], "remove_from_beginning_ignore_case": false, "remove_special_chars_from_beginning": true, "output_format": {"remove_from_end": ["(?i)\\b- Schedule A\\b", "-- Schedule A", "Schedule A", "- Schedule A", "- Schedule B", "Sch A", "Sch B", "- Sch A", "-- Sch A", "(?i)\\*patch\\*"]}}}}, {"id": 80012125, "key": ["Loan Policy number", "Loan Policy no", "Policy no", "Policy na", "policy number", "Lenders Policy", "Policy #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "probable_place": "Individual", "use_match": "fuzzy", "end_identifier": ["Loan", "Amount", "Effective", "reference", "all", "office", "endorsements", "file", "issued", "associated", "address", "issued", "patch", "lender", "title", "date", "customer", "case", "property", "this policy", "Schedule", "agent"], "start_identifier": [""], "field_id": 80012125, "field_name": "Full_Policy_Number", "document_id": 9002233, "additional_info": {"alternate_locations": "in_footer", "starts_with": ["M", "m", "U", "u"], "found_val": []}, "post_process": {"remove_far_elements_right_one_line": {"custom": false}}, "output_format": {"string_operations_output_format": {"remove_special_chars_from_end": true}}}, {"id": 80012127, "key": ["File Number", "File No", "Contract Number", "contract no", "case number", "Case no", "case"], "direction": "right", "return_type": "text", "type": "text", "multi_line_value": false, "probable_place": "Individual", "use_match": "fuzzy", "end_identifier": ["Policy", "Amount", "Premium", "loan", "Contract", "street", "schedule", "alta", "of", "DATE"], "search_in_tables": true, "probable_place_in_table": ["same_row_value", "below_row_value"], "choose_row_in_table_based_on": "Full_Policy_Number", "start_identifier": [""], "additional_info": {"alternate_locations": "in_footer"}, "field_id": 80012127, "field_name": "File_Number", "document_id": 9002233, "post_process": {"remove_far_elements_right_one_line": {"custom": false}}, "output_format": {"string_operations_output_format": {"remove_spaces": true}}}, {"id": 80012219, "key": ["Loan Number", "Loan No"], "direction": "right", "return_type": "text", "type": "text", "multi_line_value": false, "probable_place": "Individual", "use_match": "fuzzy", "end_identifier": ["Policy", "Amount", "Premium", "loan", "Contract", "street", "schedule", "alta", "of", "Date", "Address", "File"], "start_identifier": [""], "field_id": 80012219, "field_name": "Loan_Number", "document_id": 9002233, "max_page_to_search": 2, "post_process": {"remove_far_elements_right_one_line": {"custom": false}}}, {"id": 80012128, "key": ["Order Number", "Order No"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "probable_place": "Individual", "use_match": "fuzzy", "end_identifier": ["Policy", "Amount", "Premium", "loan", "Contract", "street", "schedule", "alta", "of"], "start_identifier": [""], "field_id": 80012128, "field_name": "Order_Number", "document_id": 9002233, "post_process": {"remove_far_elements_right_one_line": {"custom": false}}}, {"id": 80012129, "key": ["Date of policy", "Effective Date"], "direction": "right", "type": "date", "return_type": "text", "search_in_tables": true, "probable_place_in_table": ["same_row_value", "below_row_value"], "choose_row_in_table_based_on": "Full_Policy_Number", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["AM", "PM", "Name", "premium", "amount", "loan", "1. name"], "start_identifier": [""], "field_id": 80012129, "field_name": "Effective_Date", "document_id": 9002233, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 80012136, "key": ["Amount of Insurance", "amount f insurance", "mortgage amount", "policy amount", "Amount of Ins"], "direction": "right", "type": "number", "return_type": "text", "search_in_tables": true, "probable_place_in_table": ["same_row_value", "below_row_value"], "choose_row_in_table_based_on": "Full_Policy_Number", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["premium", "mortgage", "loan", "date", "total", "address", "name", "whichever"], "start_identifier": [""], "field_id": 80012136, "field_name": "Policy_Liability", "document_id": 9002233, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$", "^.*?\\$"], "remove_spaces": true, "remove_substrings": true}}}, {"id": 80012130, "key": ["Premium Amount", "Premium(Risk Rate)", "Premium"], "direction": "right", "type": "number", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["File", "Mortgage", "Date", "amount", "premium", "1. Name"], "start_identifier": [""], "field_id": 80012130, "field_name": "Policy_Premium", "document_id": 9002233, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$", "^.*?\\$"], "remove_special_chars_from_end": true, "remove_special_chars_from_beginning": true, "remove_substrings": true, "remove_from_end": ["[a-zA-Z][a-zA-Z0-9]*$"]}}}, {"id": 80012137, "key": ["Name of Insured"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Name of borrower"], "start_identifier": [""], "field_id": 80012137, "field_name": "Insured_Full_Name", "document_id": 9002233, "post_process": {"remove_next_line_if_starts_with_point": {"custom": false}}}, {"id": 80012138, "key": ["The estate or interest"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Title is vested in:", "title to the estate"], "start_identifier": ["insured mortgage is"], "field_id": 80012138, "field_name": "Interest_Estate", "document_id": 9002233, "additional_info": {"search_in_key": true}, "post_process": {"string_operations_post_processor": {"contains": ["fee simple", "leasehold ownership", "Contract Purchaser", "fee/easement", "feesimple", "fee", "easement", "leashold", "equitable", "leasehold"]}}}, {"id": 80012146, "key": ["Name of <PERSON><PERSON><PERSON>(s)", "Name of <PERSON><PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 80012146, "field_name": "Borrower_Full_Name", "document_id": 9002233}, {"id": 80012139, "key": ["Name of <PERSON><PERSON><PERSON>(s)", "Name of <PERSON><PERSON><PERSON>"], "direction": "right", "type": "text", "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 80012139, "field_name": "Vesting", "document_id": 9002233, "post_process": {"remove_next_line_if_starts_with_point": {"custom": false}}, "sub_keys": ["vesting_information"]}, {"id": 80012219, "key": ["Mortgage Amount"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "probable_place": "Individual", "end_identifier": ["Mortgage date", "date", "Mortgage", ""], "start_identifier": [""], "field_id": 80012219, "field_name": "Loan_Amount", "document_id": 9002233, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$", "^.*?\\$"], "remove_special_chars_from_end": true, "remove_special_chars_from_beginning": true, "remove_substrings": true, "remove_from_end": ["[a-zA-Z][a-zA-Z0-9]*$"]}}}, {"id": 80012141, "key": ["Mortgage Date"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": false, "end_identifier": ["Date"], "start_identifier": [""], "field_id": 80012141, "field_name": "Loan_Date", "document_id": 9002233, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 80012126, "key": ["name and address of title insurance company", "Name and Address of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": ["File", ""], "start_identifier": [""], "additional_info": {"nth_line": 1}, "field_id": 80012126, "field_name": "Underwriter", "document_id": 9002233}, {"id": 80012131, "key": ["Address Reference", "Address for reference only", "Address of Property", "Property Address", "street address", "Address:"], "direction": "right", "type": "address", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Amount", " Amount of Insurance", "premium", "This policy incorporates", "(For Company Reference Purposes Only)", "The estate", "County and State", "insured mortgage"], "start_identifier": ["", "as shown on the"], "field_id": 80012131, "field_name": "Property_Full_Address", "document_id": 9002233, "additional_info": {"search_after": ["street address", "street-address", "county and state", "(For identification purposes only)"]}, "post_process": {"one_of": false, "remove_next_line_if_starts_with_point": {"custom": false}, "string_operations_post_processor": {"remove_from_beginning": ["of the Land:", "(For identification purposes only):"]}, "address_splitter_post_processor": {"return_first": true}}, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["of the Land"]}}, "sub_keys": ["address_information"]}, {"id": 80012149, "key": ["County and State"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": ["Amount"], "start_identifier": [""], "field_id": 80012149, "field_name": "Legal_County_State", "document_id": 9002233, "sub_keys": ["legal_subdivision_information"]}, {"id": 20, "key": ["Addendum attached"], "direction": "left", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": ["X", "_", "Y"], "start_identifier": [""], "field_id": 9002233, "field_name": "Addendum_attached", "document_id": 1234567}, {"id": 12345678, "key": ["endorsements are incorporated"], "direction": "down_block", "return_type": "table", "type": "text", "multi_line_value": true, "end_identifier": ["This policy incorporates", "The endorsements checked below", "Countersigned", "exceptions"], "start_identifier": [""], "field_id": 12345678, "field_name": "Endorsement_2", "document_id": 9002233, "post_process": {"split_by_regex_post_processor": {"custom": false}}}, {"id": 80014060, "key": ["endorsements are incorporated", "endorsements indicated below", "endorsements checked below", "endorsements marked below", "endorsements selected below"], "direction": "down_multi_page", "type": "checkbox", "return_type": "table", "multi_line_value": true, "multi_page_value": true, "multi_page_max_count": 1, "use_match": "fuzzy", "end_identifier": ["Countersigned by", "copyright", "schedule"], "start_identifier": [""], "field_id": 80014060, "field_name": "Endorsement_Type", "document_id": 9002233, "additional_info": {"search_key": ["ENDORSEMENT"], "search_after": [""], "consider_complete_line": true}, "output_format": {"concat_extraction_items_output_format": {"from_field": "Endorsement_2", "get_field1_and_field2": true}}}, {"id": 80014075, "key": ["loan policy schedule b", "schedule b"], "direction": "down_multi_page", "type": "text", "return_type": "text", "multi_line_value": true, "multi_page_value": true, "include_key": true, "exclude_footer": true, "probable_type": "Header", "probable_place": "Individual", "merge_with_sub_item": 1, "save_page_no_for_next_key": true, "page_range_before": "to_start", "reprocess_if_not_found": true, "use_bullet_point_match": true, "search_key_in_multi_page": true, "max_cblock_iter": 4, "end_identifier": ["copyright", "end of exceptions", "end of schedule", "countersignature", "issuing agent", "California Land Title Association. All rights reserved."], "start_identifier": ["SCHEDULE B"], "field_id": 80014075, "field_name": "Exception_Header", "document_id": 9002233, "additional_info": {"search_key": ["SCHEDULE B"]}, "output_format": {"exception_output_format": {"custom": true}}, "sub_items": [{"id": 80012840, "key": ["reason of", "insured mortgage", "resulting from", "resultingfrom"], "type": "text", "use_match": "fuzzy", "return_type": "table", "start_identifier": ["insured mortgage", "resulting from", "policy number", "policy no", "file number", "exceptions", "1.", "stewart title guaranty company"], "field_id": 80012840, "field_name": "Exception_Text", "document_id": 9002233, "additional_info": {"split_by_bullets": true}, "sub_keys": ["tax_apn_information"], "sub_items": [{"id": 80012841, "type": "text", "return_type": "table", "key": [], "start_identifier": [], "field_id": 80012841, "field_name": "Exception_Type", "document_id": 9002233, "sub_item_processor": {"exception_type_processor": {"custom": true}}}]}]}, {"key": ["Countersigned by", "Countersigned", "Authorized Signature", "Authorized Countersignature", "Authorized Signatory"], "direction": "down_block", "type": "text", "use_match": "fuzzy", "multi_line_value": true, "start_identifier": [""], "end_identifier": ["Copyright", "Policy number", "Policy serial", "page", "part 1 of", "Serial No", "If you want information", "Exclusions", "Exclusions from coverage", "File No", "File Number"], "page_range_before": "to_start", "id": 80012124, "field_id": 80012124, "field_name": "Title_Company", "document_id": 9002233, "output_format": {"ner_service_req_output_format": {"text_field_name": "Title_Company", "label": ["ORGANIZATION"]}}}]