[{"id": 3000288, "key": ["Borrower(s)"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Order No", "Escrow No.:", ","], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000288, "field_name": "closing_instructions_borrower_name", "document_id": 9000189}, {"id": 3000289, "key": ["Borrower(s)", "RE: <PERSON><PERSON><PERSON>(s)"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Order No", "Escrow No.:", "Property Address"], "start_identifier": [], "possible_page_numbers": [1], "field_id": 3000289, "field_name": "closing_instructions_coborrower_name", "document_id": 9000189, "output_format": {"name_parser_output_format": {"get_co_borrower_name": true, "from_field": "closing_instructions_coborrower_name"}}}, {"id": 3000283, "key": ["read and acknowledged", "If you have any"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000283, "field_name": "closing_instructions_borrower_sign", "document_id": 9000189, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000286, "key": ["BORROWER ACKNOWLEDGMENT", "If you have any"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ACKNOWLEDGED AND AGREED"], "start_identifier": [""], "field_id": 3000286, "field_name": "closing_instructions_coborrower_sign", "document_id": 9000189, "post_process": {"check_for_signature_post_processor": {"signature_present": true, "sign_bbox_direction": "right", "vertical_threshold": 220, "horizontal_threshold": 0, "closing_instruction_coborrower_sign": true}}}, {"id": 3000284, "key": ["read and acknowledged", "If you have any"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ACKNOWLEDGED AND AGREED"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000284, "field_name": "closing_instructions_borrower_date", "document_id": 9000189, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000287, "key": ["read and acknowledged", "If you have any"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ACKNOWLEDGED AND AGREED"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000287, "field_name": "closing_instructions_coborrower_date", "document_id": 9000189, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}, {"id": 3000290, "key": ["Closing Date"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000290, "field_name": "closing_instructions_closing_date", "document_id": 9000189, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000291, "key": ["Loan No"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000291, "field_name": "closing_instructions_loan_number", "document_id": 9000189}, {"id": 3000293, "key": ["<PERSON><PERSON>"], "direction": "right", "type": "number", "return_type": "number", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["or the full replacement value of the property", "or the full replacement value of the roperty"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000293, "field_name": "closing_instructions_loan_amount", "document_id": 9000189, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3000294, "key": ["Property Address"], "direction": "right", "type": "address", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000294, "field_name": "closing_instructions_street_address_pc", "document_id": 9000189, "sub_keys": ["closing_instruction_address_information_PostClose"], "post_process": {"address_splitter_post_processor": {"return_first": true}}}]