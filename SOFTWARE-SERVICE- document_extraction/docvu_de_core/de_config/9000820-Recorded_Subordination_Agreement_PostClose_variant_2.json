[{"id": 3000868, "key": ["foregoing paragraph", "notary public", "under my hand", "behalf of the Authority", "certification thereof", "WITNESS my hand", "WITNESS my hand and official seal", "corporate seal"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000868, "field_name": "subordination_agreement_appeared_before", "document_id": 9000820, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "subordination_agreement_appeared_before", "label": ["name"]}}}, {"id": 3000874, "key": ["foregoing paragraph", "notary public", "under my hand", "behalf of the Authority", "certification thereof", "WITNESS my hand", "WITNESS my hand and official seal", "corporate seal"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000874, "field_name": "subordination_agreement_notary_commission_expires_date", "document_id": 9000820, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000876, "key": ["in the principal amount of", "not to exceed the sun of", "sum of not to exceed", "original principal sum of", "in the amount of"], "direction": "right", "type": "amount", "return_type": "text", "probable_place": "individual", "end_identifier": ["the"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000876, "field_name": "subordination_agreement_loan_amount", "document_id": 9000820}, {"id": 3000866, "key": ["in witness whereof", "witness my hand and official stamp or seal", "witness the following signatures and seals", "notary public", "notary signature", "witness my hand and official seal"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000866, "field_name": "subordination_agreement_lender_sign", "document_id": 9000820, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000871, "key": ["county of"], "direction": "down", "type": "date", "return_type": "date", "probable_place": "individual", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000871, "field_name": "subordination_agreement_execution_date", "document_id": 9000820, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]