[{"id": 3006125, "key": ["cash to <PERSON><PERSON><PERSON>", "(cash to <PERSON><PERSON><PERSON>):"], "direction": "right", "type": "amount", "return_type": "amount", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Total Loan Amount", "Total Loan Amount:"], "start_identifier": [], "field_id": 3006125, "field_name": "Intial_Draw", "document_id": 9000468, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"], "remove_spaces": true}}}, {"id": 3006126, "key": ["Total Loan Amount", "Total Loan Amount:"], "direction": "right", "type": "amount", "return_type": "amount", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Credit Limit", "Credit Limit:"], "start_identifier": [], "field_id": 3006126, "field_name": "Total Loan Amount 3006126", "document_id": 9000468, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"], "remove_spaces": true}}}, {"id": 3006082, "key": ["Credit Limit", "Credit Limit:"], "direction": "right", "type": "amount", "return_type": "amount", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Additional Draw Limit", "Additional Draw Limit:"], "start_identifier": [], "field_id": 3006082, "field_name": "Original_Principal_Limit", "document_id": 9000468, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"], "remove_spaces": true}}}, {"id": 3006085, "key": ["Additional Draw Limit:", "Draw Limit:"], "direction": "right", "type": "amount", "return_type": "amount", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Initial ANNUAL", "Initial ANNUAL PERCENTAGE RATE:"], "start_identifier": [], "field_id": 3006085, "field_name": "Original_LOC_Reserve", "document_id": 9000468, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"], "remove_spaces": true}}}, {"id": 3006087, "key": ["<PERSON><PERSON>", "Margin:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Term", "Term:"], "start_identifier": [""], "field_id": 3006087, "field_name": "Intmargin 3006087", "document_id": 9000468, "output_format": {"number_parser_output_format": {"extract_multiple": false, "pattern_keys": ["percent_or_rate"]}, "string_operations_output_format": {"remove_spaces": true}}}, {"id": 3006088, "key": ["Property", "Property:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Estimated Initial", "Estimated Initial Monthly Payment:"], "start_identifier": [""], "field_id": 3006088, "field_name": "propertyAddress 3006088", "document_id": 9000468}, {"id": 3006089, "key": ["Property:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Estimated Initial", "Estimated Initial Monthly Payment:"], "start_identifier": [""], "field_id": 3006089, "field_name": "propertyCity 3006089", "document_id": 9000468, "output_format": {"address_parser_output_format": {"get_city": true, "from_field": "propertyAddress 3006088"}, "string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3006090, "key": ["Property:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Estimated Initial", "Estimated Initial Monthly Payment:"], "start_identifier": [""], "field_id": 3006090, "field_name": "propertyState 3006090", "document_id": 9000468, "output_format": {"address_parser_output_format": {"get_state": true, "from_field": "propertyAddress 3006088"}, "string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3006091, "key": ["Property:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Estimated Initial", "Estimated Initial Monthly Payment:"], "start_identifier": [""], "field_id": 3006091, "field_name": "ZIP 3006091", "document_id": 9000468, "output_format": {"address_parser_output_format": {"get_zip_code": true, "from_field": "propertyAddress 3006088"}, "string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}]