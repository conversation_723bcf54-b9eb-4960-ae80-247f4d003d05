[{"id": 3006103, "key": ["Borrower(s):"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Description"], "start_identifier": [""], "field_id": 3006103, "field_name": "Borrower Signature Name", "document_id": 9000879, "output_format": {"ner_service_req_output_format": {"text_field_name": "Borrower Signature Name", "label": ["name"]}}}, {"id": 3006104, "key": ["Borrower(s):"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Description"], "start_identifier": ["and"], "field_id": 3006104, "field_name": "Co-Borrower Signature Name", "document_id": 9000879, "output_format": {"ner_service_req_output_format": {"text_field_name": "Co-Borrower Signature Name", "label": ["name"]}}}, {"id": 3000555, "key": ["certify that", "Borrower's Estimated Settlement Statement", "Approved and Accepted", "APPROVED AND ACCEPTED", "I further certify that"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000555, "field_name": "borrower_settlement_statement_borrower_sign", "document_id": 9000879, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000558, "key": ["certify that", "Borrower's Estimated Settlement Statement", "Approved and Accepted", "APPROVED AND ACCEPTED", "I further certify that"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000558, "field_name": "borrower_settlement_statement_coborrower_sign", "document_id": 9000879, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000556, "key": ["certify that", "settlement agent", "Borrower's Estimated Settlement Statement", "Approved and Accepted", "I further certify that"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000556, "field_name": "borrower_settlement_statement_borrower_date", "document_id": 9000879}, {"id": 3000559, "key": ["certify that", "settlement agent", "Borrower's Estimated Settlement Statement", "Approved and Accepted", "I further certify that"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000559, "field_name": "borrower_settlement_statement_coborrower_date", "document_id": 9000879}, {"id": 3005904, "key": ["Ticor Title of Nevada, Inc., Settlement Agent", "Ticor Title Company of Oregon, Settlement Agent", "Settlement Agent", "Sattlement Agent"], "direction": "up", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3005904, "field_name": "settlement_statement_agent_signature", "document_id": 9000879, "alternate_locations": [{"id": 3005904, "key": ["settlement coordinator"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3005904, "field_name": "settlement_statement_agent_signature", "document_id": 9000879}], "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}]