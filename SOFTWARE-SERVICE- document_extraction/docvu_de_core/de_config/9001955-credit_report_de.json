[{"id": 80011226, "key": ["APPLICANT"], "direction": "down", "type": "number", "return_type": "text", "multi_line_value": false, "end_identifier": ["DOB"], "start_identifier": ["SOC SEC #"], "possible_page_numbers": [], "field_id": 80011226, "field_name": "Borrower_1_SSN_DFCU_326173", "document_id": 9001955}, {"id": 80012113, "key": ["SOC SEC #"], "direction": "right", "type": "number", "return_type": "text", "multi_line_value": false, "end_identifier": [], "start_identifier": ["SOC SEC #"], "possible_page_numbers": [], "field_id": 80012113, "field_name": "Borrower_2_SSN_DFCU_326182", "document_id": 9001955, "output_format": {"string_operations_output_format": {"remove_from_beginning": [], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\bDOB.*"], "remove_only_if_empty": ["^\\s*DOB\\s*$"]}}}, {"id": 80011820, "key": ["RQD' BY"], "direction": "down", "type": "date", "return_type": "text", "multi_line_value": false, "end_identifier": [], "start_identifier": ["DATE ORDERED"], "possible_page_numbers": [], "field_id": 80011820, "field_name": "Date_Requested_DFCU_326175", "document_id": 9001955}, {"id": 80011817, "key": ["FILE #"], "direction": "right", "type": "number", "return_type": "text", "multi_line_value": false, "end_identifier": ["FNMA #"], "start_identifier": [], "possible_page_numbers": [], "field_id": 80011817, "field_name": "Reference_Number_DFCU_326171", "document_id": 9001955}, {"id": 80001102, "key": ["Based on a complete visual"], "direction": "right", "probable_type": "Footer", "type": "amount", "return_type": "text", "multi_line_value": true, "end_identifier": ["which is the", ", as of", ", asof"], "start_identifier": ["subject of this report is"], "possible_page_numbers": [], "field_id": 80001102, "field_name": "Current_Appraised_Amount_DFCU_326126", "document_id": 9000069, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^[^0-9]+", "\\s*\\$", "^\\$"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*,?\\s*as\\s*of.*", "\\s*,?\\s*asof.*", "[,\\s]+$"]}}}, {"id": 80001102, "key": ["Based on a complete visual"], "direction": "right", "probable_type": "Footer", "type": "date", "return_type": "text", "multi_line_value": true, "end_identifier": ["effective date of this appraisal"], "start_identifier": [], "possible_page_numbers": [], "field_id": 80001102, "field_name": "Current_Appraised_Date_DFCU_326125", "document_id": 9000069}, {"id": 80000828, "key": ["Date of Signature and Report"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": true, "end_identifier": ["LENDER/CLIENT"], "start_identifier": ["APPRAISED VALUE OF SUB<PERSON>ECT PROPERTY"], "possible_page_numbers": [], "field_id": 80000828, "field_name": "PROPERTY VALUE DFCU326123", "document_id": 9000069, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^[^0-9]+", "\\s*\\$", "^\\$"], "remove_special_chars_from_beginning": true, "remove_from_end": ["[^0-9]+$", "[,\\s]+$"]}}}]