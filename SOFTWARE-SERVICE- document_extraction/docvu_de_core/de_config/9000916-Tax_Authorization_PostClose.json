[{"id": 3000531, "key": ["Owner's Signature", "owner of the property"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Bank Code #:"], "start_identifier": [""], "field_id": 3000531, "field_name": "tax_authorization_borrower_sign", "document_id": 9000916, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000534, "key": ["Owner's Signature", "owner of the property"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Type Name:"], "start_identifier": [""], "field_id": 3000534, "field_name": "tax_authorization_coborrower_sign", "document_id": 9000916, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000532, "key": ["owner's signature"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Mortgagee's Authorization"], "start_identifier": [], "field_id": 3000532, "field_name": "tax_authorization_borrower_date", "document_id": 9000916}, {"id": 3000535, "key": ["owner's signature"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Foreclosure Notice Request"], "start_identifier": [], "field_id": 3000535, "field_name": "tax_authorization_coborrower_date", "document_id": 9000916, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]