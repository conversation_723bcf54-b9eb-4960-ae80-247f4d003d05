[{"id": 3000396, "key": ["pay"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000396, "field_name": "flood_hazard_determination_borrower_date", "document_id": 9000377}, {"id": 3000399, "key": ["pay"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Rev", "<PERSON>"], "start_identifier": [], "field_id": 3000399, "field_name": "flood_hazard_determination_coborrower_date", "document_id": 9000377, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}, {"id": 3000395, "key": ["pay"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000395, "field_name": "flood_hazard_determination_borrower_sign", "document_id": 9000377, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000398, "key": ["pay"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000398, "field_name": "flood_hazard_determination_coborrower_sign", "document_id": 9000377, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}]