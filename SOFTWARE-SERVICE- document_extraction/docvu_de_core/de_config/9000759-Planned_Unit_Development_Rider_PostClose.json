[{"id": 3000597, "key": ["BY SIGNING BELOW", "SIGNING BELOW"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000597, "field_name": "pud_rider_borrower_sign", "document_id": 9000759, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000600, "key": ["BY SIGNING BELOW", "SIGNING BELOW"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000600, "field_name": "pud_rider_coborrower_sign", "document_id": 9000759, "post_process": {"check_for_signature_post_processor": {"check_closeness": true, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000598, "key": ["BY SIGNING BELOW", "SIGNING BELOW"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["trustee", "MULTISTATE", "Multistate PUD Rider", "PUD"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000598, "field_name": "pud_rider_borrower_date", "document_id": 9000759, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000601, "key": ["BY SIGNING BELOW", "SIGNING BELOW"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["trustee", "MULTISTATE", "Multistate PUD Rider", "PUD"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000601, "field_name": "pud_rider_coborrower_date", "document_id": 9000759, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]