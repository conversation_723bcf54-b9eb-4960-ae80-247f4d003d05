[{"id": 3000502, "key": ["aplification"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["date"], "start_identifier": [], "field_id": 3000502, "field_name": "mineral_rights_acknowledgment_borrower_date", "document_id": 9000634}, {"id": 3000505, "key": ["aplification"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["date"], "start_identifier": [], "field_id": 3000505, "field_name": "mineral_rights_acknowledgment_coborrower_date", "document_id": 9000634, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}, {"id": 3000501, "key": ["SUBSCRIBED AND SWORN", "executed", "date"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000501, "field_name": "mineral_rights_acknowledgment_borrower_sign", "document_id": 9000634, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000504, "key": ["SUBSCRIBED AND SWORN", "executed", "date"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000504, "field_name": "mineral_rights_acknowledgment_coborrower_sign", "document_id": 9000634, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}]