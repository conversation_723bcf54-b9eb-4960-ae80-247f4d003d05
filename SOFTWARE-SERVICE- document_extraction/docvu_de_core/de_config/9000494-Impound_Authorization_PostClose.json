[{"id": 3000549, "key": ["account be established"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000549, "field_name": "impound_authorization_borrower_sign", "document_id": 9000494, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000552, "key": ["account be established"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000552, "field_name": "impound_authorization_coborrower_sign", "document_id": 9000494, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000550, "key": ["account be established"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["IA.MSC"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000550, "field_name": "impound_authorization_borrower_date", "document_id": 9000494, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000553, "key": ["account be established"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["IA.MSC"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000553, "field_name": "impound_authorization_coborrower_date", "document_id": 9000494, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]