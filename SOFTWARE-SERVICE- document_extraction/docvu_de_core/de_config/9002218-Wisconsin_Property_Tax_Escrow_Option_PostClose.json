[{"id": 3000537, "key": ["The undersigned hereby acknowledges receipt"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000537, "field_name": "wi_tax_escrow_option_election_disclosure_borrower_sign", "document_id": 9002218, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000538, "key": ["The undersigned hereby acknowledges receipt"], "direction": "down", "type": "text", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Elie Mac. Inc", "Inc"], "start_identifier": [""], "field_id": 3000538, "field_name": "wi_tax_escrow_option_election_disclosure_borrower_date", "document_id": 9002218, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000540, "key": ["The undersigned hereby acknowledges receipt"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000540, "field_name": "wi_tax_escrow_option_election_disclosure_coborrower_sign", "document_id": 9002218, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000541, "key": ["The undersigned hereby acknowledges receipt"], "direction": "down", "type": "text", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Elie Mac. Inc", "Inc"], "start_identifier": [""], "field_id": 3000541, "field_name": "wi_tax_escrow_option_election_disclosure_coborrower_date", "document_id": 9002218, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]