[{"id": 3000609, "key": ["Stamp/seal", "this record was acknowledged", "seal", "stamp", "this instrument was acknowledged"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000609, "field_name": "e&o_compliance_agreement_appeared_before", "document_id": 9000045, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "e&o_compliance_agreement_appeared_before", "label": ["name"]}}}, {"id": 3000615, "key": ["witness my hand and official seal", "Notary's Signature", "personally known to me", "the foregoing instrument", "notary seal", "stamp", "this instrument was acknowledged", "[Seal of Office]", "this record was acknowledged", "sworn to and subscribed"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ICE Mortgage Technology", "Mortgage Technology"], "start_identifier": [], "field_id": 3000615, "field_name": "e&o_compliance_agreement_notary_commission_expires_date", "document_id": 9000045, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3005907, "key": ["witness my hand and official seal", "Notary's Signature", "personally known to me", "the foregoing instrument", "notary seal", "stamp", "this instrument was acknowledged", "[Seal of Office]", "this record was acknowledged", "sworn to and subscribed"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ICE Mortgage Technology", "Mortgage Technology"], "start_identifier": [], "field_id": 3005907, "field_name": "error_omissions_compliance_agreement_printed_commission_expires_date", "document_id": 9000045, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000614, "key": ["STATE of", "County of"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000614, "field_name": "e&o_compliance_agreement_notary_seal", "document_id": 9000045, "post_process": {"check_for_seal_post_processor": {}}}, {"id": 3000605, "key": ["DATED", "DATED effective"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "probable_place": "Individual", "field_id": 3000605, "field_name": "e&o_compliance_agreement_borrower_date", "document_id": 9000045}, {"id": 3000608, "key": ["DATED", "DATED effective"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ERRORS AND OMISSIONS"], "start_identifier": ["DATE"], "probable_place": "Individual", "field_id": 3000608, "field_name": "e&o_compliance_agreement_coborrower_date", "document_id": 9000045}, {"id": 3000604, "key": ["before", "marketable title"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000604, "field_name": "e&o_compliance_agreement_borrower_sign", "document_id": 9000045, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000607, "key": ["before", "marketable title"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000607, "field_name": "e&o_compliance_agreement_coborrower_sign", "document_id": 9000045, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000610, "key": ["STATE of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["County of"], "start_identifier": [""], "probable_place": "Individual", "field_id": 3000610, "field_name": "e&o_compliance_agreement_state_filed", "document_id": 9000045}, {"id": 3000611, "key": ["County of", "county or city of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["The foregoing", "This Instrument"], "start_identifier": [""], "probable_place": "Individual", "field_id": 3000611, "field_name": "e&o_compliance_agreement_county", "document_id": 9000045}, {"id": 3000612, "key": ["before me this", "before me on", "foregoing"], "direction": "inline", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["by"], "start_identifier": [""], "field_id": 3000612, "field_name": "e&o_compliance_agreement_execution_date", "document_id": 9000045}, {"id": 3000613, "key": ["signature", "Notary Public", "Notary's Signature", "My Commission Expires", "Notary Public Signature"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000613, "field_name": "e&o_compliance_agreement_notary_acknowledgment", "document_id": 9000045, "post_process": {"check_for_signature_post_processor": {"signature_present": true, "sign_bbox_direction": "up", "vertical_threshold": 0, "horizontal_threshold": 70}}}, {"id": 3000616, "key": ["My Commission Expires"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000616, "field_name": "e&o_compliance_agreement_commission_expires_date_validation", "document_id": 9000045}]