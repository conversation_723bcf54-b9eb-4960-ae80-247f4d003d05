[{"id": 3000190, "key": ["Witness my hand and official seal", "who is personally known to me", "who has/have produced"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000190, "field_name": "affixation_affidavit_notary_commission_expires_date", "document_id": 9002153, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000184, "key": ["Witness my hand and official seal", "who is personally known to me", "who has/have produced"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000184, "field_name": "affixation_affidavit_appeared_before", "document_id": 9002153, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "affixation_affidavit_appeared_before", "label": ["name"]}}}, {"id": 3000188, "key": ["Commission Expires"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000188, "field_name": "affixation_affidavit_notary_acknowledgment", "document_id": 9002153, "post_process": {"check_for_signature_post_processor": {"signature_present": true, "sign_bbox_direction": "up", "vertical_threshold": 0, "horizontal_threshold": 750}}}, {"id": 3000187, "key": ["acknowledged before me this", "home are located", "notarization, this"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000187, "field_name": "affixation_affidavit_execution_date", "document_id": 9002153, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000185, "key": ["The State of", "STATE OF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000185, "field_name": "affixation_affidavit_state_filed", "document_id": 9002153}, {"id": 3000186, "key": ["County of", "COUNTY OF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000186, "field_name": "affixation_affidavit_county", "document_id": 9002153}, {"id": 3000190, "key": ["My Commission Expires"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000190, "field_name": "affixation_affidavit_notary_commission_expires_date", "document_id": 9002153, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000189, "key": ["personally known", "foregoing instrument"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000189, "field_name": "affixation_affidavit_notary_seal", "document_id": 9002153, "post_process": {"check_for_seal_post_processor": {}}}]