[{"id": 3000470, "key": ["this consent is valid for"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000470, "field_name": "ssa89_form_borrower_sign", "document_id": 9001964, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000471, "key": ["this consent is valid for"], "direction": "down", "type": "text", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Relationship"], "start_identifier": [""], "field_id": 3000471, "field_name": "ssa89_form_borrower_date", "document_id": 9001964, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]