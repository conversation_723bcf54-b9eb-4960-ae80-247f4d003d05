[{"id": 3000226, "key": ["acknowledge that I have received", "acknowledge receipt of", "acknowledge receiving", "acknowledge that"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000226, "field_name": "adjustable_rate_disclosure_borrower_sign", "document_id": 9000036, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000229, "key": ["acknowledge that I have received", "acknowledge receipt of", "acknowledge receiving", "acknowledge that"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000229, "field_name": "adjustable_rate_disclosure_coborrower_sign", "document_id": 9000036, "post_process": {"check_for_signature_post_processor": {"check_closeness": true, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000227, "key": ["acknowledge that I have received", "acknowledge receipt of", "acknowledge receiving", "acknowledge that"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000227, "field_name": "adjustable_rate_disclosure_borrower_date", "document_id": 9000036, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000230, "key": ["acknowledge that I have received", "acknowledge receipt of", "acknowledge receiving", "acknowledge that"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000230, "field_name": "adjustable_rate_disclosure_coborrower_date", "document_id": 9000036, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]