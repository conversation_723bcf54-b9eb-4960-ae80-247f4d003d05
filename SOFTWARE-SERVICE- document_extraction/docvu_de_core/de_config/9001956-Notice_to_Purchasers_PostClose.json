[{"id": 3000519, "key": ["before signing", "sales contract", "Purchase-<PERSON><PERSON><PERSON><PERSON>"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000519, "field_name": "notice_to_purchasers_disclosure_borrower_sign", "document_id": 9001956, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000522, "key": ["before signing", "sales contract", "Purchase-<PERSON><PERSON><PERSON><PERSON>"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000522, "field_name": "notice_to_purchasers_disclosure_coborrower_sign", "document_id": 9001956, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000520, "key": ["before signing", "sales contract", "Purchase-<PERSON><PERSON><PERSON><PERSON>"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000520, "field_name": "notice_to_purchasers_disclosure_borrower_date", "document_id": 9001956, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000523, "key": ["before signing", "sales contract", "Purchase-<PERSON><PERSON><PERSON><PERSON>"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000523, "field_name": "notice_to_purchasers_disclosure_coborrower_date", "document_id": 9001956, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]