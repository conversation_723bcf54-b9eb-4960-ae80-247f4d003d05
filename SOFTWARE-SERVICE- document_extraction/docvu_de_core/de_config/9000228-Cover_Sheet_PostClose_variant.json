[{"id": 3005942, "key": ["SUMMIT-", "TITLE2020-"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": ["MTG", "AMR"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3005942, "field_name": "cover_sheet_lender_name", "document_id": 9000228}, {"id": 3005943, "key": ["Reference Number"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": ["Date"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3005943, "field_name": "cover_sheet_loan_number", "document_id": 9000228}, {"id": 3005944, "key": ["Borrower:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": ["Date", "State/County", "stacking order"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3005944, "field_name": "cover_sheet_borrower_name", "document_id": 9000228}, {"id": 3005945, "key": ["State/County:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": ["/"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3005945, "field_name": "cover_sheet_state", "document_id": 9000228}, {"id": 3005946, "key": ["State/County:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": ["Disbursement", "Stacking", "- Check Image"], "start_identifier": ["/"], "possible_page_numbers": [1], "field_id": 3005946, "field_name": "cover_sheet_county", "document_id": 9000228, "output_format": {"string_operations_output_format": {"remove_till_from_beginning": ["/"]}}}]