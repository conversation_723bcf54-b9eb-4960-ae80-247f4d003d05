[{"id": 3000737, "key": ["the Borrower accepts", "I will pay this"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000737, "field_name": "note_addendum_borrower_sign", "document_id": 9000672, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000738, "key": ["the Borrower accepts", "I will pay this"], "direction": "down", "type": "text", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["DATE", "Date"], "start_identifier": [""], "field_id": 3000738, "field_name": "note_addendum_borrower_date", "document_id": 9000672, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000742, "key": ["the Borrower accepts", "I will pay this"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["LATE FEE", "(seal)"], "start_identifier": ["<PERSON><PERSON><PERSON>"], "field_id": 3000742, "field_name": "note_addendum_borrower_name", "document_id": 9000672, "output_format": {"ner_service_req_output_format": {"text_field_name": "note_addendum_borrower_name", "label": ["name"]}}}, {"id": 3000746, "key": ["is made this"], "direction": "right", "type": "text", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000746, "field_name": "note_addendum_closing_date", "document_id": 9000672, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000747, "key": ["located at:", "Address:"], "direction": "right", "type": "address", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["THIS ADDENDUM TO", "TIIIS LATE FEE"], "start_identifier": [], "field_id": 3000747, "field_name": "note_addendum_street_address", "document_id": 9000672, "output_format": {"address_parser_output_format": {"get_line1": true, "from_field": "note_addendum_street_address"}}}, {"id": 3000748, "key": ["located at:", "Address:"], "direction": "right", "type": "address", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["THIS ADDENDUM TO", "TIIIS LATE FEE"], "start_identifier": [], "field_id": 3000748, "field_name": "note_addendum_city", "document_id": 9000672, "output_format": {"address_parser_output_format": {"get_city": true, "from_field": "note_addendum_city"}}}, {"id": 3000749, "key": ["located at:", "Address:"], "direction": "right", "type": "address", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["THIS ADDENDUM TO", "TIIIS LATE FEE"], "start_identifier": [], "field_id": 3000749, "field_name": "note_addendum_state", "document_id": 9000672, "output_format": {"address_parser_output_format": {"get_state": true, "from_field": "note_addendum_state"}}}, {"id": 3000750, "key": ["located at:", "Address:"], "direction": "right", "type": "address", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["THIS ADDENDUM TO", "TIIIS LATE FEE"], "start_identifier": [], "field_id": 3000750, "field_name": "note_addendum_zip_code", "document_id": 9000672, "output_format": {"address_parser_output_format": {"get_zip_code": true, "from_field": "note_addendum_zip_code"}}}]