[{"id": 3000349, "key": ["which you are applying", "which you are relying"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000349, "field_name": "equal_credit_opportunity_act_borrower_sign", "document_id": 9000283, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000352, "key": ["which you are applying", "which you are relying"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000352, "field_name": "equal_credit_opportunity_act_coborrower_sign", "document_id": 9000283, "post_process": {"check_for_signature_post_processor": {"check_closeness": true, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000350, "key": ["which you are applying", "which you are relying"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Equal Credit Opportunity"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000350, "field_name": "equal_credit_opportunity_act_borrower_date", "document_id": 9000283, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000353, "key": ["which you are applying", "which you are relying"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Equal Credit Opportunity"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000353, "field_name": "equal_credit_opportunity_act_coborrower_date", "document_id": 9000283, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]