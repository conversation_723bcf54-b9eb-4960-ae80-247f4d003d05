[{"id": 3000172, "key": ["undersigned represents", "Name", "By signing below", "Owner", "Agreement", "space provided", "transaction", "disclosure"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Borrower Signature"], "start_identifier": [""], "field_id": 3000172, "field_name": "addendum_to_loan_application_borrower_sign", "document_id": 9000031, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000175, "key": ["undersigned represents", "this disclosure", "BORROWER", "Owner"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Co-Borrower Signature"], "start_identifier": [""], "field_id": 3000175, "field_name": "addendum_to_loan_application_coborrower_sign", "document_id": 9000031, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000173, "key": ["verify them", "undersigned", "By signing below", "parties"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["married applicant"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000173, "field_name": "addendum_to_loan_application_borrower_date", "document_id": 9000031, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000176, "key": ["verify them", "undersigned", "parties"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["married applicant"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000176, "field_name": "addendum_to_loan_application_coborrower_date", "document_id": 9000031, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]