[{"id": 3000477, "key": ["successors and assigns", "every business tax"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000477, "field_name": "taxpayer_consent_form_borrower_sign", "document_id": 9000932, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000480, "key": ["successors and assigns", "every business tax"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000480, "field_name": "taxpayer_consent_form_coborrower_sign", "document_id": 9000932, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000478, "key": ["successors and assigns", "every business tax"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000478, "field_name": "taxpayer_consent_form_borrower_date", "document_id": 9000932, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000481, "key": ["successors and assigns", "every business tax"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000481, "field_name": "taxpayer_consent_form_coborrower_date", "document_id": 9000932, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]