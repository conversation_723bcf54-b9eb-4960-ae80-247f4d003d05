[{"id": 3006101, "key": ["I WISH TO CANCEL", "Each of the undersigned"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "possible_page_numbers": [1], "end_identifier": ["ACKNOWLEDGMENT", "LEDGMENT", "Joint owners"], "start_identifier": ["Consumer's Signature"], "field_id": 3006101, "field_name": "Borrower_Signature_Name", "document_id": 9000709, "post_process": {"name_post_processor": {"extract_norc_borrower_name": true}}}, {"id": 3000899, "key": ["how to cancel"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["you may use"], "start_identifier": [], "field_id": 3000899, "field_name": "nortc_how_to_cancel_empty", "document_id": 9000709, "post_process": {"string_operations_post_processor": {"check_text_present": true}}}, {"id": 3000898, "key": ["how to cancel"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["I wish to cancel"], "start_identifier": [], "field_id": 3000898, "field_name": "nortc_recession_period_end", "document_id": 9000709}, {"id": 3000897, "key": ["your right to cancel"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["received"], "start_identifier": [], "field_id": 3000897, "field_name": "nortc_document_date", "document_id": 9000709}, {"id": 3000896, "key": ["Case #:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["YOUR RIGHT TO CANCEL"], "start_identifier": [], "field_id": 3000896, "field_name": "nortc_case_number", "document_id": 9000709}, {"id": 3000895, "key": ["Loan Number", "Loan Number:", "Loan #:", "Loan#"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["MIN", "Borrowers", "YOUR RIGHT TO CANCEL"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000895, "field_name": "nortc_loan_number", "document_id": 9000709, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^\\d+\\s?/\\s?"], "retain_only_numbers": false, "remove_special_chars_from_beginning": true}}}, {"id": 3000890, "key": ["Property Address:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["YOUR RIGHT"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000890, "field_name": "nortc_street_address", "document_id": 9000709, "output_format": {"address_parser_output_format": {"get_line1_and_line2": true, "from_field": "nortc_street_address"}}}, {"id": 3000891, "key": ["Property Address:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["YOUR RIGHT"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000891, "field_name": "nortc_city", "document_id": 9000709, "output_format": {"address_parser_output_format": {"get_city": true, "from_field": "nortc_city"}}}, {"id": 3000892, "key": ["Property Address:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["YOUR RIGHT"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000892, "field_name": "nortc_state", "document_id": 9000709, "output_format": {"address_parser_output_format": {"get_state": true, "from_field": "nortc_state"}}}, {"id": 3000893, "key": ["Property Address:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["YOUR RIGHT"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000893, "field_name": "nortc_zip_code", "document_id": 9000709, "output_format": {"address_parser_output_format": {"get_zip_code": true, "from_field": "nortc_zip_code"}}}, {"id": 3000887, "key": ["BORROWERS/OWNERS:", "Borrowers:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Property Address", "PROPERTY ADDRESS", "YOUR RIGHT TO", "YOUR RIGHT TO CANCEL"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000887, "field_name": "nortc_borrower_name", "document_id": 9000709, "output_format": {"name_parser_output_format": {"get_borrower_name": true, "from_field": "nortc_borrower_name"}}}, {"id": 3000881, "key": ["I WISH TO CANCEL", "acknowledge", "Business days include"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000881, "field_name": "notice_of_right_to_cancel_borrower_sign", "document_id": 9000709, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000882, "key": ["acknowledge", "Business days include", "ACKNOWLEDGMENT OF RECEIPT"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["CANCEL/RESCISSION"], "start_identifier": [""], "field_id": 3000882, "field_name": "notice_of_right_to_cancel_borrower_date", "document_id": 9000709, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000894, "key": ["notice no", "mail or ", "midnight or", "MIDNIGHT of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["that time"], "start_identifier": [""], "field_id": 3000894, "field_name": "nortc_closing_date", "document_id": 9000709, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]