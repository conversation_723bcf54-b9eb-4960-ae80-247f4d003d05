[{"id": 3000208, "key": ["Broker Entity Name", "Company Name", "loan originator", "satisfaction"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000208, "field_name": "anti_steering_borrower_sign", "document_id": 9000050, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000211, "key": ["Broker Entity Name", "satisfaction"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000211, "field_name": "anti_steering_coborrower_sign", "document_id": 9000050, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000209, "key": ["Broker Entity Name", "Company Name", "satisfaction"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000209, "field_name": "anti_steering_borrower_date", "document_id": 9000050, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000212, "key": ["Broker Entity Name", "loan originator", "satisfaction"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000212, "field_name": "anti_steering_coborrower_date", "document_id": 9000050, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]