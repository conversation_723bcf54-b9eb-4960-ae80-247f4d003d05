[{"id": 3000252, "key": ["attached document", "foregoing paragraph", "Commission Expires", "true and correct"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000252, "field_name": "certificate_of_trust_appeared_before", "document_id": 9000961, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "certificate_of_trust_appeared_before", "label": ["name"]}}}, {"id": 3000258, "key": ["attached document", "foregoing paragraph", "Commission Expires", "true and correct"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000258, "field_name": "certificate_of_trust_notary_commission_expires_date", "document_id": 9000961, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000257, "key": ["attached document", "foregoing paragraph", "Commission Expires", "true and correct"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000257, "field_name": "certificate_of_trust_notary_seal", "document_id": 9000961, "post_process": {"check_for_seal_post_processor": {}}}, {"id": 3000255, "key": ["Certification of Trust on", "Executed this", "instrument this", "notary public this"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["in"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000255, "field_name": "certificate_of_trust_execution_date", "document_id": 9000961, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000254, "key": ["COUNTY OF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["on this", "on", "subscribed", "certificate", "this", "that", "and", "being"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000254, "field_name": "certificate_of_trust_county", "document_id": 9000961, "output_format": {"string_operations_output_format": {"remove_from_end": ["ss.", "O14d 6ab", " 13", ":SS", ": SS"], "remove_special_chars_from_end": true}}}, {"id": 3000253, "key": ["STATE OF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["county", "this", "certificate", "on this", "that", "and", "being"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000253, "field_name": "certificate_of_trust_state_filed", "document_id": 9000961, "output_format": {"string_operations_output_format": {"remove_from_end": ["ss.", "O14d 6ab", " 13", ":SS", ": SS"], "remove_special_chars_from_end": true}}}, {"id": 3000256, "key": ["notary public", "notary"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000256, "field_name": "certificate_of_trust_notary_acknowledgment", "document_id": 9000961, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}]