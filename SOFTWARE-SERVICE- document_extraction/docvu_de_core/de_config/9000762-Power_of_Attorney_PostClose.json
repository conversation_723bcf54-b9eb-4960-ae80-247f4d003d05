[{"id": 3000643, "key": ["before me", "seal of office", "witness my hand", "voluntary act and deed"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000643, "field_name": "power_of_attorney_notary_commission_expires_date", "document_id": 9000762, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000637, "key": ["signed and sealed", "the foregoing instrument", "witness my hand and official seal", "principal signature", "in witness whereof", "given under my hand", "before me personally appeared", "in witness thereof", "signed on the preceding", "IN WETNESS WHEREOF", "signed and delivered"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000637, "field_name": "power_of_attorney_appeared_before", "document_id": 9000762, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "power_of_attorney_appeared_before", "label": ["name"]}}}, {"id": 3000642, "key": ["notary seal", "notary stamp", "my commission expires", "Notary Public", "acknowledged the same to be", "Signed, sealed, and detached"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000642, "field_name": "power_of_attorney_notary_seal", "document_id": 9000762, "post_process": {"check_for_seal_post_processor": {}}}, {"id": 3000638, "key": ["STATE of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["County of"], "start_identifier": [], "probable_place": "Individual", "field_id": 3000638, "field_name": "power_of_attorney_state_filed", "document_id": 9000762}, {"id": 3000639, "key": ["County of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "probable_place": "Individual", "field_id": 3000639, "field_name": "power_of_attorney_county", "document_id": 9000762}, {"id": 3000632, "key": ["Acknowledged"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000632, "field_name": "power_of_attorney_borrower_sign", "document_id": 9000762, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000635, "key": ["Acknowledged"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000635, "field_name": "power_of_attorney_coborrower_sign", "document_id": 9000762, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000633, "key": ["no effect"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["state of"], "start_identifier": ["signed"], "field_id": 3000633, "field_name": "power_of_attorney_borrower_date", "document_id": 9000762}, {"id": 3000636, "key": [""], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000636, "field_name": "power_of_attorney_coborrower_date", "document_id": 9000762}, {"id": 3000640, "key": ["Acknowledged", "given my hand"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000640, "field_name": "power_of_attorney_execution_date", "document_id": 9000762}, {"id": 3000641, "key": ["Notary Public", "Nory Public"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000641, "field_name": "power_of_attorney_notary_acknowledgment", "document_id": 9000762, "post_process": {"check_for_signature_post_processor": {"signature_present": true, "sign_bbox_direction": "up", "vertical_threshold": 0, "horizontal_threshold": 200}}}]