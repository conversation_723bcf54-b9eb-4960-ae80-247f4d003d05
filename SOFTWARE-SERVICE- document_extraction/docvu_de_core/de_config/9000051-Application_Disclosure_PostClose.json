[{"id": 3000065, "key": ["acknowledge", "by signing below"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000065, "field_name": "application_disclosure_borrower_sign", "document_id": 9000051, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "up", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000068, "key": ["acknowledge", "by signing below"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000068, "field_name": "application_disclosure_coborrower_sign", "document_id": 9000051, "post_process": {"check_for_signature_post_processor": {"check_closeness": true, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000066, "key": ["by signing below"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON><PERSON>"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000066, "field_name": "application_disclosure_borrower_date", "document_id": 9000051}, {"id": 3000069, "key": ["by signing below"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000069, "field_name": "application_disclosure_coborrower_date", "document_id": 9000051, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]