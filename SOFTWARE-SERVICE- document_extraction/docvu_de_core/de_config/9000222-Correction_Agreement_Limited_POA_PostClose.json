[{"id": 3000326, "key": ["this instrument was acknowledged", "the foregoing instrument was acknowledged", "witness my hand and official seal", "foregoing instrument", "personally appeared before", "(seal)", "signed or attested before me", "by (customer)"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ICE Mortgage Technology"], "start_identifier": [], "field_id": 3000326, "field_name": "correction_agreeement_poa_notary_commission_expires_date", "document_id": 9000222, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000323, "key": ["County"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["witness my hand", "notary seal", "identification", "signature", "seal"], "start_identifier": [], "probable_place": "Individual", "field_id": 3000323, "field_name": "correction_agreeement_poa_execution_date", "document_id": 9000222}, {"id": 3000320, "key": ["before me"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["a notary public", "this", "on", "by means"], "start_identifier": [""], "field_id": 3000320, "field_name": "correction_agreeement_poa_appeared_before", "document_id": 9000222}, {"id": 3000324, "key": ["Notary Public", "Notary Signature", "Signature of notarial officer", "Sighature of notarial", "signature", "Notary Public Signature", "Printed Name", "seal"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000324, "field_name": "correction_agreeement_poa_notary_acknowledgment", "document_id": 9000222, "post_process": {"check_for_signature_post_processor": {"signature_present": true, "sign_bbox_direction": "up", "vertical_threshold": 0, "horizontal_threshold": 250}}}, {"id": 3000325, "key": ["State of"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000325, "field_name": "correction_agreeement_poa_notary_seal", "document_id": 9000222, "post_process": {"check_for_seal_post_processor": {}}}, {"id": 3000321, "key": ["State of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["County"], "start_identifier": [], "probable_place": "Individual", "field_id": 3000321, "field_name": "correction_agreeement_poa_state_filed", "document_id": 9000222}, {"id": 3000322, "key": ["County of", "County ss:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["}"], "start_identifier": [], "probable_place": "Individual", "field_id": 3000322, "field_name": "correction_agreeement_poa_county", "document_id": 9000222}]