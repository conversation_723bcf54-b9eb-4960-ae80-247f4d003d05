[{"id": 3006093, "key": ["<PERSON><PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON>an <PERSON>"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3006093, "field_name": "Borrower Signature Name", "document_id": 9000181, "output_format": {"name_parser_output_format": {"get_borrower_name": true, "from_field": "Borrower Signature Name"}}}, {"id": 3006094, "key": ["By signing"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["CLOSING DISCLOSURE", "page"], "start_identifier": ["this form", "this-form", "this", "form"], "field_id": 3006094, "field_name": "Co-Borrower Signature Name", "document_id": 9000181, "post_process": {"string_operations_post_processor": {"cd_coborrower_name": true}}}, {"id": 123456, "key": ["Seller at Closing (N)", "Payoffs and Payments (K)"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 123456, "field_name": "cd_cash_from_borrower", "sub_keys": ["cd_cash_from_borrower_omr"], "document_id": 9000181}, {"id": 3000292, "key": ["Loan ID #", "Loan ID N", "Loan ID *", "Loan ID U"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000292, "field_name": "cd_loan_number", "document_id": 9000181}, {"id": 100, "key": ["Loan Information"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["<PERSON><PERSON>"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 100, "field_name": "cd_loan_type_save", "sub_keys": ["cd_loan_type_omr"], "document_id": 9000377}, {"id": 3000149, "key": ["Contact Information", "federal income taxes"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt", "CLOSING DISCLOSURE"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000149, "field_name": "cd_Settlement_Agent_Name", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Name", "field_string_col": "Name", "probable_field_value_col": ["Settlement Agent", "Settlement"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "Address"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000150, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000141, "field_name": "cd_Settlement_Agent_Address", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Address", "field_string_col": "Address", "probable_field_value_col": ["Settlement Agent", "Settlement"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "NMLS ID"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000151, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000151, "field_name": "cd_Settlement_Agent_NMLS_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "NMLS ID", "field_string_col": "NMLS ID", "probable_field_value_col": ["Settlement Agent", "Settlement"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000152, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000143, "field_name": "cd_Settlement_Agent_ST_License_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "^\\w+ License ID$", "field_string_col": "License ID", "probable_field_value_col": ["Settlement Agent", "Settlement"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0, "pattern_field": true}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000153, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000153, "field_name": "cd_Settlement_Agent_Contact", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact", "field_string_col": "Contact", "probable_field_value_col": ["Settlement Agent", "Settlement"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000153, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000153, "field_name": "cd_Settlement_Agent_Contact_NMLS_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact NMLS ID", "field_string_col": "Contact NMLS ID", "probable_field_value_col": ["Settlement Agent", "Settlement"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000154, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000154, "field_name": "cd_Settlement_Agent_Contact_ST_License_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact NMLS ID", "field_string_col": "License ID", "probable_field_value_col": ["Settlement Agent", "Settlement"], "probable_place_in_table": "below_next_row", "probable_field_value_next_row": "Email"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000155, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000155, "field_name": "cd_Settlement_Agent_Email", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Email", "field_string_col": "Email", "probable_field_value_col": ["Settlement Agent", "Settlement"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "Phone"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_spaces": true}}}, {"id": 3000156, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000156, "field_name": "cd_Settlement_Agent_Phone", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Phone", "field_string_col": "Phone", "probable_field_value_col": ["Settlement Agent", "Settlement"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000140, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000140, "field_name": "cd_Real_Estate_Broker_S_Name", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Name", "field_string_col": "Name", "probable_field_value_col": ["Real Estate Broker (S)", "Real Estate Broker (5)", "Real EstateBroker (S)", "(S)"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "Address"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000141, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000141, "field_name": "cd_Real_Estate_Broker_S_Address", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Address", "field_string_col": "Address", "probable_field_value_col": ["Real Estate Broker (S)", "Real EstateBroker (S)", "Real Estate Broker (5)", "(S)"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "NMLS ID"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000142, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000142, "field_name": "cd_Real_Estate_Broker_S_NMLS_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "NMLS ID", "field_string_col": "NMLS ID", "probable_field_value_col": ["Real Estate Broker (S)", "(S)"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000143, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000143, "field_name": "cd_Real_Estate_Broker_S_ST_License_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "^\\w+ License ID$", "field_string_col": "License ID", "probable_field_value_col": ["Real Estate Broker (S)", "Real EstateBroker (S)", "Real Estate Broker (5)", "(S)"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0, "pattern_field": true}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000144, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000144, "field_name": "cd_Real_Estate_Broker_S_Contact", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact", "field_string_col": "Contact", "probable_field_value_col": ["Real Estate Broker (S)", "Real Estate Broker (5)", "Real EstateBroker (S)", "(S)"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000145, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000145, "field_name": "cd_Real_Estate_Broker_S_Contact_NMLS_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact NMLS ID", "field_string_col": "Contact NMLS ID", "probable_field_value_col": ["Real Estate Broker (S)", "(S)"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000146, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000146, "field_name": "cd_Real_Estate_Broker_S_Contact_ST_License_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact NMLS ID", "field_string_col": "License ID", "probable_field_value_col": ["Real Estate Broker (S)", "Real EstateBroker (S)", "Real Estate Broker (5)", "(S)"], "probable_place_in_table": "below_next_row", "probable_field_value_next_row": "Email"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000147, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000147, "field_name": "cd_Real_Estate_Broker_S_Email", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Email", "field_string_col": "Email", "probable_field_value_col": ["Real Estate Broker (S)", "(S)"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "Phone"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_spaces": true}}}, {"id": 3000148, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000148, "field_name": "cd_Real_Estate_Broker_S_Phone", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Phone", "field_string_col": "Phone", "probable_field_value_col": ["Real Estate Broker (S)", "Real EstateBroker (S)", "Real Estate Broker (5)", "(S)"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000131, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000131, "field_name": "cd_Real_Estate_Broker_B_Name", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Name", "field_string_col": "Name", "probable_field_value_col": ["Real Estate Broker (B)", "(B)"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "Address"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000132, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [], "consider_on_after_hpos": false, "field_id": 3000132, "field_name": "cd_Real_Estate_Broker_B_Address", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Address", "field_string_col": "Address", "probable_field_value_col": ["Real Estate Broker (B)", "(B)"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "NMLS ID"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000133, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000133, "field_name": "cd_Real_Estate_Broker_B_NMLS_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "NMLS ID", "field_string_col": "NMLS ID", "probable_field_value_col": ["Real Estate Broker (B)", "(B)"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000134, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000134, "field_name": "cd_Real_Estate_Broker_B_ST_License_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "^\\w+ License ID$", "field_string_col": "License ID", "probable_field_value_col": ["Real Estate Broker (B)", "(B)"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0, "pattern_field": true}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000135, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000135, "field_name": "cd_Real_Estate_Broker_B_Contact", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact", "field_string_col": "Contact", "probable_field_value_col": ["Real Estate Broker (B)", "(B)"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000136, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000136, "field_name": "cd_Real_Estate_Broker_B_Contact_NMLS_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact NMLS ID", "field_string_col": "Contact NMLS ID", "probable_field_value_col": ["Real Estate Broker (B)", "(B)"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000137, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000137, "field_name": "cd_Real_Estate_Broker_B_Contact_ST_License_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact NMLS ID", "field_string_col": "License ID", "probable_field_value_col": ["Real Estate Broker (B)", "(B)"], "probable_place_in_table": "below_next_row", "probable_field_value_next_row": "Email"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000138, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000138, "field_name": "cd_Real_Estate_Broker_B_Email", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Email", "field_string_col": "Email", "probable_field_value_col": ["Real Estate Broker (B)", "(B)"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "Phone"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_spaces": true}}}, {"id": 3000139, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000139, "field_name": "cd_Real_Estate_Broker_B_Phone", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Phone", "field_string_col": "Phone", "probable_field_value_col": ["Real Estate Broker (B)", "(B)"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000122, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000122, "field_name": "cd_Mortgage_Broker_Name", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Name", "field_string_col": "Name", "probable_field_value_col": ["Mortgage Broker", "Mortgage broker"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "Address"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000123, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000123, "field_name": "cd_Mortgage_Broker_Address", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Address", "field_string_col": "Address", "probable_field_value_col": ["Mortgage Broker", "Mortgage broker"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "NMLS ID"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000124, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000124, "field_name": "cd_Mortgage_Broker_NMLS_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "NMLS ID", "field_string_col": "NMLS ID", "probable_field_value_col": ["Mortgage Broker", "Mortgage broker"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000125, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000125, "field_name": "cd_Mortgage_Broker_ST_License_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "^\\w+ License ID$", "field_string_col": "License ID", "probable_field_value_col": "Mortgage Broker", "probable_place_in_table": "same_row", "probable_field_value_next_row": 0, "pattern_field": true}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000126, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000126, "field_name": "cd_Mortgage_Broker_Contact", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact", "field_string_col": "Contact", "probable_field_value_col": ["Mortgage Broker", "Mortgage broker"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000127, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000127, "field_name": "cd_Mortgage_Broker_Contact_NMLS_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact NMLS ID", "field_string_col": "Contact NMLS ID", "probable_field_value_col": ["Mortgage Broker", "Mortgage broker"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000128, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000128, "field_name": "cd_Mortgage_Broker_Contact_ST_License_ID", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact NMLS ID", "field_string_col": "License ID", "probable_field_value_col": "Mortgage Broker", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": "Email"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000129, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000129, "field_name": "cd_Mortgage_Broker_Email", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Email", "field_string_col": "Email", "probable_field_value_col": ["Mortgage Broker", "Mortgage broker"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "Phone"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_spaces": true}}}, {"id": 3000130, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["By signing", "Confirm Receipt"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000130, "field_name": "cd_Mortgage_Broker_Phone", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Phone", "field_string_col": "Phone", "probable_field_value_col": ["Mortgage Broker", "Mortgage broker"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000103, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt", "By signing"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000103, "field_name": "cd_lender_name", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Name", "field_string_col": "Name", "probable_field_value_col": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "Address"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000104, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt", "By signing"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000104, "field_name": "cd_lender_address", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Address", "field_string_col": "Address", "probable_field_value_col": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "NMLS ID"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000105, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt", "By signing"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000105, "field_name": "cd_lender_nmls", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "NMLS ID", "field_string_col": "NMLS ID", "probable_field_value_col": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000106, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt", "By signing"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000106, "field_name": "cd_lender_state_license", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "^\\w+ License ID$", "field_string_col": "License ID", "probable_field_value_col": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0, "pattern_field": true}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000107, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt", "By signing"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000107, "field_name": "cd_lender_contact", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact", "field_string_col": "Contact", "probable_field_value_col": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000108, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt", "By signing"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000108, "field_name": "cd_lender_contact_nmls", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact NMLS ID", "field_string_col": "Contact NMLS ID", "probable_field_value_col": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000109, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt", "By signing"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000109, "field_name": "cd_lender_contact_state_license", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Contact NMLS ID", "field_string_col": "Contact NMLS ID", "probable_field_value_col": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "probable_place_in_table": "below_next_row", "probable_field_value_next_row": "Email", "pattern_field": false}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000110, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt", "By signing"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000110, "field_name": "cd_lender_contact_email", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Email", "field_string_col": "Email", "probable_field_value_col": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "probable_place_in_table": "below_row", "probable_field_value_next_row": "Phone"}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_spaces": true}}}, {"id": 3000111, "key": ["federal income taxes", "federal"], "type": "table_new", "end_identifier": ["Mortgage Information", "Confirm Receipt", "By signing"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000111, "field_name": "cd_lender_contact_phone", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Phone", "field_string_col": "Phone", "probable_field_value_col": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["<PERSON><PERSON>", "Mortgage Broker", "Real Estate Broker (B)", "Real Estate Broker (S)", "Settlement Agent"], "table_structure": "vertical"}, {"id": 3000090, "key": ["Phone", "Email"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000090, "field_name": "cd_borrower_sign", "document_id": 9000181, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000093, "key": ["Phone", "Email"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000093, "field_name": "cd_coborrower_sign", "document_id": 9000181, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000091, "key": ["Phone", "By signing"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000091, "field_name": "cd_borrower_date", "document_id": 9000181}, {"id": 3000094, "key": ["Phone", "By signing"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000094, "field_name": "cd_coborrower_date", "document_id": 9000181, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}, {"id": 3000013, "key": ["<PERSON><PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON>an <PERSON>"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000013, "field_name": "cd_borrower_name", "document_id": 9000181, "output_format": {"name_parser_output_format": {"get_borrower_name": true, "from_field": "cd_borrower_name"}}}, {"id": 3000014, "key": ["<PERSON><PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON>an <PERSON>"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000014, "field_name": "cd_coborrower_name", "document_id": 9000181, "output_format": {"name_parser_output_format": {"get_co_borrower_name": true, "from_field": "cd_coborrower_name"}}}, {"id": 3000016, "key": ["<PERSON><PERSON>"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["NO"], "start_identifier": ["<PERSON><PERSON>"], "possible_page_numbers": [1], "field_id": 3000016, "field_name": "cd_loan_amount", "document_id": 9000181, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true}}}, {"id": 3000032, "key": ["MIC#", "MIC #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000032, "field_name": "cd_case_number", "document_id": 9000181}, {"id": 3000035, "key": ["Closing Date"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Purpose"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000035, "field_name": "cd_closing_date", "document_id": 9000181, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000040, "key": ["Disbursement Date"], "type": "table_new", "end_identifier": ["Mortgage Information", "Loan <PERSON>"], "keep_start_key": false, "use_match": "fuzzy", "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3000040, "field_name": "cd_street_address", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Property", "field_string_col": "Property", "probable_field_value_col": 1, "probable_place_in_table": "same_row", "probable_field_value_next_row": 1}}}, {"id": 3000041, "key": ["Property"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>", "Sale", "Loan'ID#", "Appraised Prop"], "start_identifier": ["VA"], "possible_page_numbers": [1], "field_id": 3000041, "field_name": "cd_city", "document_id": 9000181, "output_format": {"address_parser_output_format": {"get_city": true, "from_field": "cd_city"}}}, {"id": 3000042, "key": ["Property"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>", "Sale", "Loan'ID#", "Appraised Prop"], "start_identifier": [], "possible_page_numbers": [1], "field_id": 3000042, "field_name": "cd_state", "document_id": 9000181, "output_format": {"address_parser_output_format": {"get_state": true, "from_field": "cd_state"}}}, {"id": 3000043, "key": ["Property"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>", "Sale", "Loan'ID#", "Appraised Prop"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000043, "field_name": "cd_zip_code", "document_id": 9000181, "output_format": {"address_parser_output_format": {"get_zip_code": true, "from_field": "cd_zip_code"}}}, {"id": 3000045, "key": ["Interest Rate"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["NO"], "start_identifier": ["Interest Rate"], "possible_page_numbers": [1], "field_id": 3000045, "field_name": "cd_interest_rate", "document_id": 9000181, "output_format": {"string_operations_output_format": {"contains": ["(\\d+\\.?\\d*)\\s*%"], "remove_from_end": ["\\s*%"]}}}]