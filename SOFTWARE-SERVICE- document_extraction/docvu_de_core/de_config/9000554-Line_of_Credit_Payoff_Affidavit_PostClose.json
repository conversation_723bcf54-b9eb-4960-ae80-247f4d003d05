[{"id": 3006109, "key": ["before me this", "beforo me this", "to before", "PAYOFF FIGURES", "Return To:", "Borrower(s):"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Notary"], "start_identifier": [""], "field_id": 3006109, "field_name": "Borrower_Signature_Name", "document_id": 9000554, "output_format": {"string_operations_output_format": {"remove_till_from_beginning": ["by", "falling"], "remove_till_from_end": ["and", "Sworn", "Date"]}, "ner_service_req_output_format": {"text_field_name": "Borrower_Signature_Name", "label": ["name"]}}}, {"id": 3006110, "key": ["before me this", "beforo me this", "to before", "PAYOFF FIGURES", "Return To:", "Borrower(s):"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Notary"], "start_identifier": ["and"], "field_id": 3006110, "field_name": "Co-Borrower_Signature_Name", "document_id": 9000554, "output_format": {"string_operations_output_format": {"remove_till_from_beginning": ["Date"], "remove_till_from_end": ["Sworn"]}, "ner_service_req_output_format": {"text_field_name": "Co-Borrower_Signature_Name", "label": ["name"]}}}, {"id": 3005962, "key": ["Subscribed and sworn", "Sworn before me"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3005962, "field_name": "line_of_credit_payoff_affidavit_appeared_before", "document_id": 9000554, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "line_of_credit_payoff_affidavit_appeared_before", "label": ["name"]}}}, {"id": 3005968, "key": ["Subscribed and sworn", "Expires:"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3005968, "field_name": "line_of_credit_payoff_affidavit_notary_commission_expires_date", "document_id": 9000554, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3005970, "key": ["Subscribed and sworn", "Expires:"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3005970, "field_name": "line_of_credit_payoff_affidavit_printed_commission_expires_date", "document_id": 9000554, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3005957, "key": ["By signing below", "acknowledged", "by all borrowers."], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3005957, "field_name": "line_of_credit_payoff_affidavit_borrower_sign", "document_id": 9000554, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3005958, "key": ["before me this", "before me on this", "by all borrowers."], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Notary"], "start_identifier": [""], "field_id": 3005958, "field_name": "line_of_credit_payoff_affidavit_borrower_date", "document_id": 9000554, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3005960, "key": ["By signing below", "acknowledged", "by all borrowers."], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["County of"], "start_identifier": [""], "field_id": 3005960, "field_name": "line_of_credit_payoff_affidavit_coborrower_sign", "document_id": 9000554, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3005961, "key": ["before me this", "before me on this", "by all borrowers."], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Notary"], "start_identifier": [""], "field_id": 3005958, "field_name": "line_of_credit_payoff_affidavit_coborrower_date", "document_id": 3005961, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3005963, "key": ["State of", "STATE OF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3005963, "field_name": "line_of_credit_payoff_affidavit_state_filed", "document_id": 9000554}, {"id": 3005964, "key": ["County of", "COUNTY OF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3005964, "field_name": "line_of_credit_payoff_affidavit_county", "document_id": 9000554}, {"id": 3005965, "key": ["before me this", "before me on this", "by all borrowers.", "it are true."], "direction": "right", "type": "text", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Notary"], "start_identifier": [""], "field_id": 3005965, "field_name": "line_of_credit_payoff_affidavit_execution_date", "document_id": 9000554, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3005966, "key": ["Subscribed and sworn", "before me this", "official seal."], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3005966, "field_name": "line_of_credit_payoff_affidavit_notary_acknowledgment", "document_id": 9000554, "post_process": {"check_for_signature_post_processor": {"sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 150, "signature_present": true}}}, {"id": 3005967, "key": ["Subscribed and", "Expires:"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3005967, "field_name": "line_of_credit_payoff_affidavit_notary_seal", "document_id": 9000554, "post_process": {"check_for_seal_post_processor": {}}}]