#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""

import os, json
import cv2
import difflib
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from docvu_de_core.config import *
class ComputeTextSimilarity:
    """
    A class to compute text similarity using both cosine similarity and sequence matching techniques.
    """

    def __init__(self, debug=False):
        """
        Initializes the ComputeTextSimilarity class.

        Args:
            debug (bool): Enables debug mode to print detailed information during execution.
        """
        self.matching_ratio = 0.9
        self.matching_ratio_lower_th = 0.45
        self.debug = debug
        pass

    @staticmethod
    def get_raw_text_corrected(footer_index, raw_text):
        """
        Corrects the raw text by slicing it from the footer index onwards.

        Args:
            footer_index (int): The starting index for slicing the raw text.
            raw_text (list): The list of text data.

        Returns:
            list: The corrected raw text starting from the footer index.
        """
        text_len = 0
        for index, text in enumerate(raw_text):
            text_len += len(text["text"])
            if text_len < footer_index-1:
                continue
            else:
                return raw_text[index:]
        return raw_text

    def are_cosine_similar(self, prev_footer, curr_footer):
        """
        Computes cosine similarity between two footers.

        Args:
            prev_footer (str): The footer text from the previous page.
            curr_footer (str): The footer text from the current page.

        Returns:
            bool: True if similarity is above the threshold, otherwise False.
        """
        vectorizer = TfidfVectorizer()
        try:
            vectors = vectorizer.fit_transform([prev_footer, curr_footer])
        except ValueError:
            return False
        similarity = cosine_similarity(vectors[0], vectors[1])[0][0]
        if self.debug:
            print(f'Cosine Similarity = {similarity}')
        if similarity >= self.matching_ratio:
            return True

        return False

    def get_similarity_with_correction(self, prev_footer, curr_footer, prev_raw_text, curr_raw_text):
        """
        Matches two footers using sequence matching and cosine similarity, with corrections applied as needed.

        Args:
            prev_footer (str): The footer text from the previous page.
            curr_footer (str): The footer text from the current page.
            prev_raw_text (list): The raw text from the previous page.
            curr_raw_text (list): The raw text from the current page.

        Returns:
            tuple: A tuple containing match result, corrected footers, and corrected raw text data.
        """
        match = difflib.SequenceMatcher(None, prev_footer, curr_footer, autojunk=True)
        if self.are_cosine_similar(prev_footer, curr_footer):
            return True, prev_footer, curr_footer, prev_raw_text, curr_raw_text

        longest_match = match.find_longest_match()
        matching_blocks = match.get_matching_blocks()
        if self.debug:
            print(f"Matching Blocks: {matching_blocks}, Longest Match: {longest_match}")
        if longest_match.size != 0:
            longest_match_index = matching_blocks.index(longest_match)
        else:
            longest_match_index = -1
        matching_ratio = np.round(match.ratio(), 2)
        if self.debug:
            print(f"Matching Ratio: {matching_ratio}, Matching Blocks: {matching_blocks}, Longest Match: {longest_match}")
            print(f"Text1 = {prev_footer}\nText2 = {curr_footer}")
        for blocks in matching_blocks:
            if self.debug:
                print(f"Block A Index: {blocks.a}, Block B Index: {blocks.b}")
            if blocks.a == 0 and blocks.b == 0:
                if matching_ratio >= self.matching_ratio:
                    return True, prev_footer, curr_footer, prev_raw_text, curr_raw_text
                else:
                    continue
            else:
                prev_corrected, curr_corrected = prev_footer[blocks.a:], curr_footer[blocks.b:]
                if self.debug:
                    print(f"Prev_Corrected = {prev_corrected}\nCurr_Corrected = {curr_corrected}")
                if blocks.a != 0:
                    prev_raw_text_corrected = self.get_raw_text_corrected(blocks.a, prev_raw_text)
                else:
                    prev_raw_text_corrected = prev_raw_text
                if blocks.b != 0:
                    curr_raw_text_corrected = self.get_raw_text_corrected(blocks.b, curr_raw_text)
                else:
                    curr_raw_text_corrected = curr_raw_text

                match = difflib.SequenceMatcher(None, prev_corrected, curr_corrected)
                matching_ratio = np.round(match.ratio(), 2)
                if self.debug:
                    print(f"Corrected Text Matching_ratio = {matching_ratio}")
                if matching_ratio >= self.matching_ratio:
                    return True, prev_corrected, curr_corrected, prev_raw_text_corrected, curr_raw_text_corrected
                elif ((longest_match_index == 0 and matching_ratio >= self.matching_ratio_lower_th) or
                      self.are_cosine_similar(prev_corrected, curr_corrected)):
                    return True, prev_corrected, curr_corrected, prev_raw_text_corrected, curr_raw_text_corrected

        return False, prev_footer, curr_footer, prev_raw_text, curr_raw_text


class FooterDetection:
    """
    A class to detect the footer in document images based on text content and footer location.
    """

    def __init__(self, save_image=False, debug=False):
        """
        Initializes the FooterDetection class.

        Args:
            save_image (bool): If True, saves the footer-detection images.
            debug (bool): Enables debug mode to print detailed information during execution.
        """
        self.footer_percentage = page_bound_configs["footer_percentage"]
        self.match = ComputeTextSimilarity()
        self.save_image = save_image
        self.debug = debug
        pass

    @staticmethod
    def get_footer_text(json_data, footer_index):
        """
        Extracts footer text from the JSON data of the document.

        Args:
            json_data (dict): The JSON data containing text blocks.
            footer_index (int): The vertical position (VPOS) where the footer starts.

        Returns:
            tuple: A tuple containing the footer text as a string and the raw footer text data.
        """
        footer_text = []
        raw_footer_text = []
        for block_index, item in enumerate(json_data["COMPOSED_BLOCKS"]):
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    for ts_id, text_string in enumerate(text_line["STRING"]):
                        if text_string["VPOS"] >= footer_index:
                            footer_text.append(text_string["text"])
                            raw_footer_text.append(text_string)
        return " ".join(footer_text), raw_footer_text

    def detect_footer(self, src_dir):
        """
        Detects footers in the given directory containing document pages in JSON format.

        Args:
            src_dir (str): The directory path containing the document JSON files and images.

        Returns:
            dict: A dictionary containing page numbers as keys and footer starting positions as values.
        """
        combined_json = os.path.join(src_dir, 'combined.json')
        if os.path.isfile(combined_json):
            with open(combined_json) as temp_f:
                json_data = json.loads(temp_f.read())
        else:
            return None
        keys = sorted(map(int, json_data.keys()))
        footer_stats = {}
        prev_footer = None
        prev_raw_text = None
        prev_k = None
        page_w = None
        k = 0
        while k < len(keys):
            k += 1
            data = json_data[str(k)]
            page_height = data['PAGE_HEIGHT']
            if page_w is None:
                page_w = data['PAGE_WIDTH']

            footer_starts = int(page_height * (1.0 - self.footer_percentage))
            if self.debug:
                print(f"Page Height: {page_height}, Page Width: {page_w}, Footer Starts at: {footer_starts}")
            curr_footer, curr_raw_text = self.get_footer_text(data, footer_starts)
            if self.debug:
                print(f"Page {k}: Curr Footer = {curr_footer}, len = {len(curr_footer)}")
            if len(curr_footer) == 0:
                prev_footer = None
                continue

            if prev_footer is None:
                prev_footer = curr_footer
                prev_raw_text = curr_raw_text
                prev_k = k
                footer_stats[k] = [footer_starts, curr_raw_text]
            else:

                match, prev_footer_corrected, curr_footer_corrected, prev_raw_text_corrected, curr_raw_text_corrected \
                    = self.match.get_similarity_with_correction(prev_footer, curr_footer, prev_raw_text, curr_raw_text)

                if self.debug:
                    print(f'\nPage {k - 1}, Page {k}')
                    print(f'Page {prev_k} Corrected VPOS: {prev_raw_text_corrected[0]["VPOS"]}, '
                          f'Original VPOS: {prev_raw_text[0]["VPOS"]}')
                    print(f'Page {k} Corrected VPOS: {curr_raw_text_corrected[0]["VPOS"]}, ' 
                          f'Original VPOS: {curr_raw_text[0]["VPOS"]}')

                    print(f'Page {prev_k} Corrected Text: {prev_footer_corrected}')
                    print(f'Page {k} Corrected Text: {curr_footer_corrected}')
                    print(f'Footer Stats of prev {prev_k} VPOS = {footer_stats[prev_k][0]}')
                    print(f'Prev Raw text of page {prev_k} VPOS = {prev_raw_text_corrected[0]["VPOS"]}')

                if abs(footer_stats[prev_k][0] - prev_raw_text_corrected[0]["VPOS"]) > 100:
                    footer_stats[prev_k] = [prev_raw_text_corrected[0]["VPOS"], prev_raw_text_corrected]
                if abs(prev_raw_text_corrected[0]["VPOS"] - curr_raw_text_corrected[0]["VPOS"]) > 200:
                    k = k-1
                    prev_footer = None
                    continue

                footer_stats[k] = [curr_raw_text_corrected[0]["VPOS"], curr_raw_text_corrected]
                prev_k = k
                prev_footer = curr_footer_corrected
                prev_raw_text = curr_raw_text_corrected

        if self.save_image:
            src_name = src_dir.split('/')[-1]
            dest_path = os.path.join('./../results', src_name)
            if not os.path.isdir(dest_path):
                os.mkdir(dest_path)

            for page_num, value in footer_stats.items():
                print(f"Page {page_num}: Footer Starts: {value[0]}")
                img = cv2.imread(os.path.join(src_dir, str(page_num) + '.jpg'))
                cv2.line(img, (0, value[0]), (page_w, value[0]), (0, 0, 255), 2)
                cv2.imwrite(os.path.join(dest_path, str(page_num) + '.jpg'), img)

        stats = {}
        for k, v in footer_stats.items():
            stats[k] = v[0]
        return stats


if __name__ == '__main__':
    ft = FooterDetection(save_image=True, debug=True)
    samples_dir = './../samples/'
    samples = ['36627_ALTA_Owners_Policy_Schedules_with_Jacket_6-17-06',
               '50592_T-1R_OP_Jacket_and_Schedules_r_1_14',
               'STEWARTECM_PROD000049914330_15.38.21.883',
               'STEWARTECM_PROD000015191037',
               '2013414385.Owner_Policy.360379',
               '2006347118.Owner_Policy.237431',
               '2009850585.Owner_Policy.302188',
               '2001951597.Owner_Policy.192621',
               '2004728867.Owner_Policy.213439']

    run_all = False
    if run_all:
        for i in range(len(samples)):
            text_file = samples_dir + samples[i] # 5 wrong
            print(f'Processing {text_file}')
            stats = ft.detect_footer(text_file)
    else:
        text_file = samples_dir + samples[8]  # 5 wrong
        print(f'Processing {text_file}')
        stats = ft.detect_footer(text_file)
        print(stats)
        print(stats.keys())
