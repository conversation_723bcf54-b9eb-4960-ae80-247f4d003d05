import json
import numpy as np
from sklearn.cluster import KMeans
from typing import List, Tuple, Dict


class BigFontClusterer:
    def __init__(self, height_threshold=0.5, num_clusters=3):
        """
        Initialize the BigFontClusterer with parameters for clustering based on height.

        :param height_threshold: Threshold to consider a text line as potentially large.
        :param num_clusters: Fixed number of clusters to use for KMeans. Default is 3.
        """
        self.height_threshold = height_threshold
        self.num_clusters = num_clusters

    def load_ocr_data(self, file_path: str) -> Dict:
        """
        Load OCR data from a JSON file.

        :param file_path: Path to the OCR JSON file.
        :return: Parsed OCR data.
        """
        with open(file_path, 'r') as file:
            ocr_data = json.load(file)
        return ocr_data

    def extract_features(self, ocr_data: Dict) -> List[Tuple[int]]:
        """
        Extract text line features (height) from OCR data.

        :param ocr_data: OCR data dictionary.
        :return: List of tuples containing (height).
        """
        features = []

        for block in ocr_data["COMPOSED_BLOCKS"]:
            for text_block in block["TEXT_BLOCK"]:
                for text_line in text_block["TEXT_LINE"]:
                    height = text_line["HEIGHT"]
                    features.append((height,))
        return features

    def cluster_text_lines(self, features: List[Tuple[int]]) -> List[int]:
        """
        Cluster text lines using KMeans based on height to identify large fonts.

        :param features: List of tuples containing (height).
        :return: List of cluster labels for each feature.
        """
        kmeans = KMeans(n_clusters=self.num_clusters, random_state=42)
        labels = kmeans.fit_predict(features)
        return labels

    def identify_large_font_clusters(self, features: List[Tuple[int]], labels: List[int]) -> int:
        """
        Identify which cluster represents large fonts based on height.

        :param features: List of tuples containing (height).
        :param labels: List of cluster labels for each feature.
        :return: Index of the large font cluster.
        """
        cluster_heights = {i: [] for i in range(max(labels) + 1)}

        for i, label in enumerate(labels):
            height = features[i][0]
            cluster_heights[label].append(height)

        # Calculate average height for each cluster
        avg_heights = {k: np.mean(v) for k, v in cluster_heights.items()}

        # Assume the highest average height cluster is the large font cluster
        large_font_cluster = max(avg_heights, key=avg_heights.get)

        return large_font_cluster

    def extract_large_font_text(self, ocr_data: Dict, features: List[Tuple[int]], labels: List[int]) -> List[str]:
        """
        Extract text lines identified as large fonts based on clustering.

        :param ocr_data: OCR data dictionary.
        :param features: List of tuples containing (height).
        :param labels: List of cluster labels for each feature.
        :return: List of extracted large font texts.
        """
        large_font_texts = []

        large_font_cluster = self.identify_large_font_clusters(features, labels)

        index = 0
        for block in ocr_data["COMPOSED_BLOCKS"]:
            for text_block in block["TEXT_BLOCK"]:
                for text_line in text_block["TEXT_LINE"]:
                    if index >= len(features):
                        break
                    if labels[index] == large_font_cluster:
                        for string in text_line["STRING"]:
                            large_font_texts.append(string["text"])
                    index += 1

        return large_font_texts

    def process_document(self, file_path: str) -> List[str]:
        """
        Process the document to extract large font texts.

        :param file_path: Path to the OCR JSON file.
        :return: List of extracted large font texts.
        """
        ocr_data = self.load_ocr_data(file_path)
        features = self.extract_features(ocr_data)
        labels = self.cluster_text_lines(features)
        large_font_texts = self.extract_large_font_text(ocr_data, features, labels)
        return large_font_texts

    def process_document_from_ocr_dict(self, ocr_data) -> List[str]:
        """
        Process the document to extract large font texts.

        :param file_path: Path to the OCR JSON file.
        :return: List of extracted large font texts.
        """
        features = self.extract_features(ocr_data)
        labels = self.cluster_text_lines(features)
        large_font_texts = self.extract_large_font_text(ocr_data, features, labels)
        return large_font_texts

# Usage example
if __name__ == '__main__':
    big_font_clusterer = BigFontClusterer(height_threshold=0.5, num_clusters=2)
    file_path = '/Users/<USER>/Repos/SOFTWARE-SERVICE-%20document_extraction/ocr_output/33003_ALTA_Owners_Policy_Comm_Schedules_with_Jacket_6-17-06/1.json'  # Path to your OCR JSON file
    large_font_texts = big_font_clusterer.process_document(file_path)

    print("Extracted Large Font Texts:")
    for text in large_font_texts:
        print(text)