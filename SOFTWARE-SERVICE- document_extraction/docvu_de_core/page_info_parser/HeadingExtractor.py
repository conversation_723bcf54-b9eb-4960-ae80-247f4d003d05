import json
import numpy as np
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
from typing import List, Tuple, Dict, Optional

from docvu_de_core.modules.KofaxXML2JSONConverter import XMLToJSONConverter


class HeadingExtractor:
    def __init__(self, max_clusters=3, top_section_percentage=0.25):
        """
        Initialize the HeadingExtractor with parameters for clustering and top section detection.

        :param max_clusters: Maximum number of clusters to try for KMeans. Default is 3.
        :param top_section_percentage: Percentage of the page height to consider as the top section.
        """
        self.max_clusters = max_clusters
        self.top_section_percentage = top_section_percentage

    def load_ocr_data(self, file_path: str) -> Dict:
        """
        Load OCR data from a JSON file.

        :param file_path: Path to the OCR JSON file.
        :return: Parsed OCR data.
        """
        with open(file_path, 'r') as file:
            ocr_data = json.load(file)
        return ocr_data

    def extract_features(self, ocr_data: Dict) -> List[Tuple[int, int]]:
        """
        Extract text line features (height, vertical position) from OCR data.

        :param ocr_data: OCR data dictionary.
        :return: List of tuples containing (height, VPOS).
        """
        features = []
        page_height = ocr_data.get("PAGE_HEIGHT", 2200)
        max_upper_vpos_permissible = int(page_height * self.top_section_percentage)

        for block in ocr_data["COMPOSED_BLOCKS"]:
            for text_block in block["TEXT_BLOCK"]:
                for text_line in text_block["TEXT_LINE"]:
                    line_height = text_line["HEIGHT"]
                    vpos = text_line["VPOS"]
                    # Consider only text lines in the top section of the page
                    if vpos <= max_upper_vpos_permissible:
                        features.append((line_height, vpos))
        return features

    def determine_number_of_clusters(self, features: List[Tuple[int, int]]) -> int:
        """
        Determine the number of clusters to use for KMeans based on silhouette scores.

        :param features: List of tuples containing (height, VPOS).
        :return: Optimal number of clusters.
        """
        best_num_clusters = 2
        best_silhouette = -1

        for n_clusters in range(2, self.max_clusters + 1):
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            labels = kmeans.fit_predict(features)
            silhouette_avg = silhouette_score(features, labels)

            if silhouette_avg > best_silhouette:
                best_silhouette = silhouette_avg
                best_num_clusters = n_clusters

        return best_num_clusters

    def cluster_text_lines(self, features: List[Tuple[int, int]], n_clusters: int) -> List[int]:
        """
        Cluster text lines using KMeans to identify headings and subheadings.

        :param features: List of tuples containing (height, VPOS).
        :param n_clusters: Number of clusters to use for KMeans.
        :return: List of cluster labels for each feature.
        """
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        labels = kmeans.fit_predict(features)
        return labels

    def identify_heading_clusters(self, features: List[Tuple[int, int]], labels: List[int]) -> Tuple[
        int, Optional[int]]:
        """
        Identify which clusters represent headings and subheadings based on text height.

        :param features: List of tuples containing (height, VPOS).
        :param labels: List of cluster labels for each feature.
        :return: Indices of heading and subheading clusters.
        """
        cluster_heights = {i: [] for i in range(max(labels) + 1)}

        for i, label in enumerate(labels):
            height = features[i][0]
            cluster_heights[label].append(height)

        # Calculate average height for each cluster
        avg_heights = {k: np.mean(v) for k, v in cluster_heights.items()}

        # Sort clusters by average height (descending)
        sorted_clusters = sorted(avg_heights.items(), key=lambda item: item[1], reverse=True)

        # Assume the highest average height cluster is the heading cluster
        heading_cluster = sorted_clusters[0][0]

        # Check for subheading cluster if there are more than two clusters
        subheading_cluster = sorted_clusters[1][0] if len(sorted_clusters) > 2 else None

        return heading_cluster, subheading_cluster

    def extract_headings_and_subheadings(self, ocr_data: Dict, features: List[Tuple[int, int]], labels: List[int]) -> \
    Tuple[List[str], List[str]]:
        """
        Extract headings and subheadings based on clustering results.

        :param ocr_data: OCR data dictionary.
        :param features: List of tuples containing (height, VPOS).
        :param labels: List of cluster labels for each feature.
        :return: Tuple of lists containing extracted headings and subheadings.
        """
        headings = []
        subheadings = []

        heading_cluster, subheading_cluster = self.identify_heading_clusters(features, labels)

        max_height = 0
        max_height_texts = []

        index = 0
        for block in ocr_data["COMPOSED_BLOCKS"]:
            for text_block in block["TEXT_BLOCK"]:
                for text_line in text_block["TEXT_LINE"]:
                    if index >= len(features):
                        break
                    # Check if this line belongs to the heading cluster
                    if labels[index] == heading_cluster:
                        line_height = text_line["HEIGHT"]
                        for string in text_line["STRING"]:
                            text_content = string["text"]
                            # Identify maximum height text
                            if line_height > max_height:
                                max_height = line_height
                                max_height_texts = [text_content]
                            elif line_height == max_height:
                                max_height_texts.append(text_content)
                            else:
                                subheadings.append(text_content)
                    index += 1

        headings = max_height_texts  # Max height text(s) are considered headings

        return headings, subheadings

    def process_document(self, file_path: str) -> Tuple[List[str], List[str]]:
        """
        Process the document to extract headings and subheadings.

        :param file_path: Path to the OCR JSON file.
        :return: Tuple of lists containing extracted headings and subheadings.
        """
        ocr_data = self.load_ocr_data(file_path)
        features = self.extract_features(ocr_data)
        num_clusters = self.determine_number_of_clusters(features)
        labels = self.cluster_text_lines(features, num_clusters)
        headings, subheadings = self.extract_headings_and_subheadings(ocr_data, features, labels)
        return headings, subheadings

    def process_document_from_xml(self, page_xml: str) -> Tuple[List[str], List[str]]:
        """
        Process the document to extract headings and subheadings.

        :param file_path: Path to the OCR JSON file.
        :return: Tuple of lists containing extracted headings and subheadings.
        """
        converter = XMLToJSONConverter()
        ocr_data = converter.convert_to_json_new(xml_text=page_xml,
                                                     enable_vertical_separation=False,
                                                     iW=None, iH=None)
        features = self.extract_features(ocr_data)
        num_clusters = self.determine_number_of_clusters(features)
        labels = self.cluster_text_lines(features, num_clusters)
        headings, subheadings = self.extract_headings_and_subheadings(ocr_data, features, labels)
        return headings, subheadings


# Usage example
if __name__ == '__main__':
    heading_extractor = HeadingExtractor(max_clusters=3, top_section_percentage=0.25)
    file_path = '/Users/<USER>/Repos/SOFTWARE-SERVICE-%20document_extraction/ocr_output/64b8ec43-9d76-4e6d-a7a6-e4aa75060d2d/5.json'  # Path to your OCR JSON file
    headings, subheadings = heading_extractor.process_document(file_path)

    print("Extracted Headings:")
    for heading in headings:
        print(heading)

    print("\nExtracted Subheadings:")
    for subheading in subheadings:
        print(subheading)