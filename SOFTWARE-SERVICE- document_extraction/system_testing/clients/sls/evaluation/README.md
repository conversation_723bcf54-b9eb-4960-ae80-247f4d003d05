# SLS Evaluation Guide

## Setup Environment
To install the required dependencies, run:
```bash
pip install -r requirements.txt
```

---

## Step 1: Download Required Files

Download the following files from the blob storage:
- **PDF Files**
- **OCR Files**
- **Mismatch File**
- **Ground Truth File**

**Blob Storage Details:**
- **Container Name**: `doc-de`
- **Folder Name**: `sls_evaluation_files`

![img.png](img.png)

---

## Step 2: Configure `local_test_params.yml`

Update the `local_test_params.yml` file with the paths of the downloaded files:

```yaml
settings:
  mode: "all"
  type: "eval"
  mismatch_file: "/Users/<USER>/Downloads/SLS_UT/sls_ut_mismatch_file.xlsx"

directories:
  ocr:
    eval: "/Users/<USER>/Downloads/SLS_UT/ocr/"
  pdf:
    eval: "/Users/<USER>/Downloads/SLS_UT/pdf/"
```

---

## Step 3: Run Evaluation to Generate Predicted CSV

Run the following script to generate the predicted CSV file:
```bash
python local_test.py
```

The generated CSV file will be saved in:
```
./de_results/500_newsls/
```

Update the **SLS_PREDICTED_FILE_PATH** inside the `de_eval.sh` file with the generated CSV file path.

---

## Step 4: Run Evaluation

Before executing the evaluation script, update the paths for the **Ground Truth (GT) file** and **Predicted file**:

```bash
SLS_REPORT_FILE_PATH="/Users/<USER>/Downloads/SLS_UT/SLS_UT_GT.xlsx"
SLS_PREDICTED_FILE_PATH="../de_results/500_newsls/merged3_pushed_code_stewart_form_de_new_SLS_execution_01072024_None.csv"
```

Run the evaluation script:
```bash
sh de_eval.sh
```

---
## Results & Analysis

- **The evaluation results will be stored in:**

    "./system_testing/clients/sls/evaluation/eval_results_field_level_evaluation"

- **It provides accuracy metrics for each field**

- **If a field has low accuracy, check the mismatch file to compare the ground truth and predicted values**

- **The mismatch file includes Levenshtein distance calculations for error analysis**

- **Field-wise accuracy results are available here:** https://visionetsys.sharepoint.com/:x:/s/EdgeData/EThgrkQ_HxpCrMdmjIsIxTAB2PmmJ7i9D5asUraQJ8ymbQ?e=NfMjc7
--- 

## Notes
- Ensure all file paths are correctly set before execution.
- If any errors occur, verify that the required files are present in the specified directories.
---

