import os
import argparse
import pandas as pd
from sconf import Config
from ast import literal_eval

from utils.process_eval_utils import Utils


class FieldLevelAcc:

    def __init__(self, sls_docvu_merged, field_configs, save_dir, doc_type, total_records,
                 save_mismatch_results=True) -> None:
        self.sls_docvu_merged = sls_docvu_merged
        self.save_dir = save_dir
        self.doc_type = doc_type
        self.process_utils = Utils(save_mismatch_results=save_mismatch_results, save_dir=save_dir, suffix_name = doc_type)
        self.field_configs = field_configs
        self.total_records = total_records

    def custom_field_eval(self, gt_field, pred_field, type, compute_distance):
        df_eval = self.sls_docvu_merged
        if gt_field not in df_eval:
            df_eval[gt_field] = 'not_found'
        if pred_field not in df_eval:
            df_eval[pred_field] = 'not_found'

        df_eval[gt_field].fillna('not_found', inplace=True)
        df_eval[pred_field].fillna('not_found', inplace=True)
        df_eval[gt_field] = df_eval[gt_field].astype('str')
        df_eval[pred_field] = df_eval[pred_field].astype('str')
        df_eval[gt_field + '_formatted'] = df_eval[gt_field].apply(
            lambda x: self.process_utils.preprocess_text(x, type))
        df_eval[pred_field + '_formatted'] = df_eval[pred_field].apply(
            lambda x: self.process_utils.preprocess_text(x, type))
        field_name = gt_field.replace('_gt', '')
        if compute_distance:
            total_records, records_found_docvu, no_of_matched_records, no_of_unfound_records, results_dict = self.process_utils.compare_columns_distances(
                df_eval, gt_field, pred_field)
            return field_name, total_records, records_found_docvu, no_of_matched_records, no_of_unfound_records, results_dict
        else:
            results_dict = None
            total_records, records_found_docvu, no_of_matched_records, no_of_unfound_records = self.process_utils.compare_two_columns(
                df_eval, gt_field, pred_field)
            return field_name, total_records, records_found_docvu, no_of_matched_records, no_of_unfound_records, results_dict

    def evaluate_sls_fields(self):

        results = []
        for field_name in self.field_configs.keys():
            print("computing field:", field_name)
            field_type = self.field_configs[field_name]['type']
            compute_dis = self.field_configs[field_name]['compute_distance']
            results.append(self.custom_field_eval(field_name + '_gt', field_name + '_pred', field_type, compute_dis))

        df_results = pd.DataFrame(results, columns=['Field_Name', 'Total_Records_found_by_SLS',
                                                    'Records_Found_AI/ML_where_SLS_None', 'Correct_AI/ML',
                                                    'Records_not_found_AI/ML', 'Partial_Match'])
        df_results['Incorrect_AI/ML'] = df_results['Total_Records_found_by_SLS'] - (
                    df_results['Correct_AI/ML'] + df_results['Records_not_found_AI/ML'])
        df_results['Accuracy_AI/ML'] = (df_results['Correct_AI/ML'] / df_results['Total_Records_found_by_SLS']) * 100
        df_results['Precision'] = (df_results['Correct_AI/ML'] + df_results[
            'Records_Found_AI/ML_where_SLS_None']) * 100 / 503
        df_results['Total_Records_found_by_AI/ML'] = (
                    df_results['Total_Records_found_by_SLS'] + df_results['Records_Found_AI/ML_where_SLS_None'] -
                    df_results['Records_not_found_AI/ML'])
        df_results['Extraction_Accuracy'] = df_results['Total_Records_found_by_AI/ML'] * 100 / self.total_records
        final_df_results = df_results[['Field_Name', 'Total_Records_found_by_SLS', 'Total_Records_found_by_AI/ML',
                                       'Records_Found_AI/ML_where_SLS_None', 'Correct_AI/ML', 'Incorrect_AI/ML',
                                       'Records_not_found_AI/ML', 'Partial_Match', 'Accuracy_AI/ML',
                                       'Extraction_Accuracy', 'Precision']]
        print(final_df_results)
        final_df_results.to_excel(f'{self.save_dir}/1_{self.doc_type}_form_de_acc_results.xlsx', index=False)


def create_config_fields(all_config, fields):
    config = all_config

    if fields:
        print("FIELDS FOR EVALUATION", fields)
        # Create a new dictionary for the extracted fields
        extracted_fields = {}
        # Iterate over each field in all_lender_config
        for field_name, field_info in all_config.items():
            if field_name in fields:
                extracted_fields[field_name] = field_info
        return extracted_fields
    return config


def cumulative_config_fields(lender_config, owner_config, certain_fields):
    extracted_fields = {}
    if certain_fields:
        for field_name, field_info in lender_config.items():
            if field_name in certain_fields:
                extracted_fields[field_name] = field_info
        for field_name, field_info in owner_config.items():
            if field_name not in extracted_fields and field_name in certain_fields:
                extracted_fields[field_name] = field_info
    else:
        for field_name, field_info in lender_config.items():
            extracted_fields[field_name] = field_info
        for field_name, field_info in owner_config.items():
            if field_name not in extracted_fields:
                extracted_fields[field_name] = field_info
    return extracted_fields


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--fields_for_eval', nargs='+', default=[], help='list of fields to be evaluated')
    parser.add_argument('--sls_report_file_path', type=str)
    parser.add_argument('--sls_predicted_file_path', type=str)
    parser.add_argument('--suffix_name_results', type=str)
    args = parser.parse_args()

    fields_for_eval = args.fields_for_eval
    sls_report_file_path = args.sls_report_file_path
    sls_predicted_file_path = args.sls_predicted_file_path
    suffix_name_results = args.suffix_name_results
    all_lender_config = Config('./configs/lender_config_eval.yaml')
    all_owner_config = Config('./configs/owner_config_eval.yaml')

    cummulative_config = cumulative_config_fields(all_lender_config, all_owner_config, fields_for_eval)
    sls_report_endorsement = pd.read_excel(sls_report_file_path, sheet_name='Endorsement')
    sls_report_exception = pd.read_excel(sls_report_file_path, sheet_name='Exception')
    predicted_form = pd.read_csv(sls_predicted_file_path)
    predicted_form['Short_Form_Flag'] = predicted_form['Form_type']
    predicted_form['Short_Form_Flag'] = predicted_form['Short_Form_Flag'].apply(lambda x: 'short_form' if x=='short_form' else 'None')
    sls_report = pd.read_excel(sls_report_file_path)

    sls_report['Original_Image_Name'] = sls_report['Original_Image_Name'].apply(Utils().rename_image_name)
    sls_report_endorsement['Original_Image_Name'] = sls_report_endorsement['Original_Image_Name'].apply(
        Utils().rename_image_name)
    sls_report_exception['Original_Image_Name'] = sls_report_exception['Original_Image_Name'].apply(
        Utils().rename_image_name)
    total_records = len(sls_report)
    predicted_form['Exception_Header'] = predicted_form['Exception_Header'].replace('Not_Found', '[]')
    predicted_form['Exception_Text'] = predicted_form['Exception_Text'].replace('Not_Found', '[]')
    predicted_form['Endorsement_Type'] = predicted_form['Endorsement_Type'].replace('Not_Found', '[]').fillna('[]').replace('nan','[]')
    predicted_form['Exception_Header'] = predicted_form['Exception_Header'].apply(literal_eval)
    predicted_form['Exception_Header'] = predicted_form['Exception_Header'].apply(lambda x: sorted(list(set(x))))
    predicted_form['Exception_Text'] = predicted_form['Exception_Text'].apply(literal_eval)
    predicted_form['Exception_Header_Sequence_Number'] = predicted_form['Exception_Header'].apply(lambda x: len(x))
    predicted_form['Exception_Sequence_Number'] = predicted_form['Exception_Text'].apply(lambda x: sum(len(i) for i in x if i is not None))
    predicted_form['Endorsement_Type'] = predicted_form['Endorsement_Type'].apply(literal_eval)
    predicted_form['Endorsement_Sequence_Number'] = predicted_form['Endorsement_Type'].apply(lambda x: len(x))

    save_results_dir = f"./{suffix_name_results}_field_level_evaluation"
    os.makedirs(save_results_dir, exist_ok=True)

    # renaming sls_report column names
    sls_report.rename(columns={"Property_City ": "Property_City", "Property_State_Code ": "Property_State_Code",
                               "Property_ZIPcode ": "Property_ZIPcode",
                               "Property_Full_Address ": "Property_Full_Address",
                               "Legal_County_State ": "Legal_County_State"}, inplace=True)
    if 'Short_Form_Flag' in sls_report.columns:
        sls_report['Short_Form_Flag'].fillna('None', inplace=True)
        sls_report['Short_Form_Flag'] = sls_report['Short_Form_Flag'].apply(lambda x: 'short_form' if x==1 else 'None')

    print("No of records predicted:", len(predicted_form))
    sls_report_exception_sorted = sls_report_exception.sort_values(by='Exception_Header').reset_index(drop=True)
    gt_excep_header = sls_report_exception_sorted.drop_duplicates(subset=['Original_Image_Name', 'Exception_Header'])[
        ['Original_Image_Name', 'Exception_Header']]
    gt_excep_header = gt_excep_header.groupby('Original_Image_Name')['Exception_Header'].apply(list).reset_index()
    gt_excep_header['Exception_Header_Sequence_Number'] = gt_excep_header['Exception_Header'].apply(lambda x: len(x))
    gt_excep_text_header_grouped = sls_report_exception.groupby(['Original_Image_Name','Exception_Header'])['Exception_Text'].apply(lambda x: [i for i in x]).reset_index()
    gt_excep_text = gt_excep_text_header_grouped.groupby('Original_Image_Name')['Exception_Text'].apply(lambda x: [i for i in x]).reset_index()
    gt_excep_text['Exception_Sequence_Number'] = gt_excep_text['Exception_Text'].apply(lambda x: sum(len(i) for i in x))
    gt_endors_type = sls_report_endorsement.groupby('Original_Image_Name')['Endorsement_Type'].apply(
        lambda x: [[i] for i in x]).reset_index()
    gt_endors_type['Endorsement_Sequence_Number'] = gt_endors_type['Endorsement_Type'].apply(lambda x: len(x))

    sls_report_with_excep_header = pd.merge(sls_report, gt_excep_header, how='outer', on='Original_Image_Name')
    sls_report_with_excep_text = pd.merge(sls_report_with_excep_header, gt_excep_text, how='outer',
                                          on='Original_Image_Name')
    sls_report_with_endors = pd.merge(sls_report_with_excep_text, gt_endors_type, how='outer', on='Original_Image_Name')
    sls_report = sls_report_with_endors
    print("No of records in SLS:", len(sls_report))
    # merging dataframe
    sls_docvu_merged = pd.merge(sls_report, predicted_form, how='inner', on='Original_Image_Name',
                                suffixes=('_gt', '_pred'))
    print("No of records after merging based on image name", len(sls_docvu_merged))
    # calculate and save field acc
    lender_field_acc = FieldLevelAcc(sls_docvu_merged, cummulative_config, save_results_dir, suffix_name_results, total_records)
    lender_field_acc.evaluate_sls_fields()