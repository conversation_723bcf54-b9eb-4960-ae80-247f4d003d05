# FIELDS_FOR_EVAL=Loan_Date
FIELDS_FOR_EVAL=''
SLS_REPORT_FILE_PATH="/Users/<USER>/Downloads/SLS_UT/SLS_UT_GT_4.xlsx"
SLS_PREDICTED_FILE_PATH="../de_results/500_newsls/SLS_500_Satish_dev_Mar19.csv"
SUFFIX_NAME_RESULTS=eval_results
EVALUATE_ON_ALL_FIELDS=true

if $EVALUATE_ON_ALL_FIELDS; then
python de_eval.py   --sls_report_file_path "$SLS_REPORT_FILE_PATH" \
                --sls_predicted_file_path "$SLS_PREDICTED_FILE_PATH" \
                --suffix_name_results $SUFFIX_NAME_RESULTS
else 
python de_eval.py  --fields_for_eval "${FIELDS_FOR_EVAL[@]}" \
                --sls_report_file_path "$SLS_REPORT_FILE_PATH" \
                --sls_predicted_file_path $SLS_PREDICTED_FILE_PATH \
                --suffix_name_results $SUFFIX_NAME_RESULTS
fi
