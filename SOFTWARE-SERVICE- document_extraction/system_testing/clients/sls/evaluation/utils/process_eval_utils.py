import time
import Levenshtein
import ast
import numpy as np
import pandas as pd
import json
import os
import re

class Utils:
    
    def __init__(self, save_mismatch_results=True, save_dir="./save_de_eval_results_dir", suffix_name = "SLS") -> None:
        self.save_mismatch_results = save_mismatch_results
        self.save_dir = save_dir
        self.suffix_name = suffix_name
        # Define a dictionary mapping Greek letters to English alphabet equivalents
        self.greek_to_english = {
            'α': 'a', 'β': 'b', 'γ': 'g', 'δ': 'd', 'ε': 'e',
            'ζ': 'z', 'η': 'h', 'θ': 'th', 'ι': 'i', 'κ': 'k',
            'λ': 'l', 'μ': 'm', 'ν': 'n', 'ξ': 'x', 'ο': 'o',
            'π': 'p', 'ρ': 'r', 'σ': 's', 'τ': 't', 'υ': 'u',
            'φ': 'ph', 'χ': 'ch', 'ψ': 'ps', 'ω': 'o'
        }
        
    
    # Function to replace Greek letters with English equivalents using regular expressions
    def convert_greek_to_english_regex(self, text):
        pattern = re.compile('|'.join(map(re.escape, self.greek_to_english.keys())), re.IGNORECASE)
        return pattern.sub(lambda m: self.greek_to_english[m.group(0).lower()], text)
            
    def remove_punctuations(self, text):
        processed_text = re.sub(r'[^\w\s]','',text)
        return processed_text
    
    def date_format_process(self, date_str):
        date_str = str(date_str).split('at')[0].split('@')[0]
        formatted_date_str = self.remove_until_number(date_str[::-1])[::-1]
        formatted_date_str = formatted_date_str.replace(",", ", ").replace("00:00:00", "").replace("/","-").strip().lower()
        print("formatted date str", formatted_date_str)
        try:
            if date_str != 'not_found':
                # if ',' in date_str:
                #     formatted_date_str = formatted_date_str.replace(",", ", ").repalce("00:00:00", "").replace("/","-")       
                # timestamp = pd.to_datetime(formatted_date_str).strftime("%Y-%m-%d")
                # print("timestamp", timestamp)
                date_elem_list = len(formatted_date_str.split('-'))
                if date_elem_list == 3:
                    sorted_ts_list = sorted([int(t) for t in formatted_date_str.split('-')])
                    format_timestamp = "-".join([str(t) for t in sorted_ts_list])
                    print("formatted time stamp", format_timestamp)
                    # return timestamp.strftime("%Y-%m-%d")
                    return format_timestamp
            else:
                return formatted_date_str
        except Exception as e:
            print("exception text", date_str)
            return date_str.lower()
        
    @staticmethod
    def convert_value(text_value):
        try:
            # Convert the numerical strings containing , and / to float/int Eg: 122,240.00 and 1443/121
            float_value = float(text_value.replace(',', '').split('/')[0])
            # Check if the value is an integer or has no decimals
            if float_value.is_integer():
                return int(float_value)
            else:
                return float_value
        except ValueError:
            # If conversion fails, return original value
            return text_value
    
    def count_list_elements(self, list_element):
        try:
            no_of_lines = len(ast.literal_eval(list_element))
            return str(no_of_lines)
        except Exception as e:
            return 'not_found'
        
    def preprocess_text(self, text, text_type, hardcoded_testing=True):
        
        if text_type == 'text':
            proc_text = text
            # for policy premium, policy liability and loan amount replace $  in the start
            if text !=0 and text!="0":
                    
                proc_text = str(text).replace('\n','').replace(" ", "").replace("$", "").lstrip('0').lower()
                proc_text = self.convert_greek_to_english_regex(proc_text)
            
            if hardcoded_testing:
                if 'schedulebpart' in proc_text:
                    proc_text = proc_text.replace('1', 'i').replace('2', 'ii')
                if 'altaloanpolicy' in proc_text:
                    proc_text = proc_text.replace('06','6')
            if proc_text == '' or proc_text == 'nan' or proc_text == 'not_found':
                return 'not_found'
            
            try:
                # For Ground truth Loan number to change the values from 988.0 to 988
                # For Prediction policy premium, policy liability and loan amount to change the values from 988.0 to 988
                num_proc_text = str(self.convert_value(proc_text))
                if num_proc_text == 'inf':
                    return proc_text
                processed_text = self.remove_punctuations(num_proc_text)

                return processed_text
            except Exception as e:
                processed_text = self.remove_punctuations(proc_text)
                return processed_text
        elif text_type == 'date':           
            if text == 'nan' or text == '' or text == 'Not_Found' or text=='NaT':
                return 'not_found'
            formatted_date = self.date_format_process(text)
            
            return formatted_date
        elif text_type == 'list':
            processed_text = self.get_first_element(text)
            return processed_text
        elif text_type == 'list_length':
            get_list_count  = self.count_list_elements(text)
            return get_list_count
    
    @staticmethod
    def rename_image_name(name):
        if name.startswith('ECM') or name.startswith('STEWART'):
            new_name = name.split('_')[0] +'_'+ name.split('_')[1] 
            new_name = new_name.split('.')[0].strip()
        elif name.startswith('_ECM') or name.startswith('_STEWART'):
            new_name = name.split('_')[1] +'_'+ name.split('_')[2] 
            new_name = new_name.split('.')[0].strip()
        else:
            new_name = name.replace(' ', '_')
            new_name = new_name.replace('.pdf','').replace('.PDF', '').split('.')[0].replace('_20','')
        return new_name
    
    @staticmethod
    def rename_image_name_lender(name):
        new_name = None
        if name.startswith('ECM') or name.startswith('STEWART'):
            new_name = name.split('_')[0] +'_'+ name.split('_')[1] 
            new_name = new_name.strip()
        elif name.startswith('_ECM') or name.startswith('_STEWART'):
            new_name = name.split('_')[1] +'_'+ name.split('_')[2] 
            new_name = new_name.strip()
        return new_name

    @staticmethod
    def rename_image_name_owner(name):
        new_name = "_".join(name.split('.')[0].split(" "))[:-3]
        return new_name
        
    def compare_two_columns(self, df, gt_column, pred_column):
        formatted_gt_column = gt_column + '_formatted'
        formatted_pred_column = pred_column + '_formatted'
        pred_found_gt_null = len(df[(df[formatted_gt_column]=='not_found') & (df[formatted_pred_column] != 'not_found')])
        gt_drop_null_df = df[df[formatted_gt_column] != 'not_found']
        no_of_unfound_records = len(gt_drop_null_df[gt_drop_null_df[formatted_pred_column] == 'not_found'])
        matched_df = gt_drop_null_df[gt_drop_null_df[formatted_pred_column] == gt_drop_null_df[formatted_gt_column]]
        mismatched_df = df[df[formatted_pred_column] != df[formatted_gt_column]]
        if self.save_mismatch_results:
            file_name = pred_column.replace('_pred','') +'_'+ self.suffix_name+'_mismatched_results.xlsx'
            mismatched_df[['Original_Image_Name', pred_column, formatted_pred_column, gt_column, formatted_gt_column]].to_excel(os.path.join(self.save_dir,file_name) ,index=False)
        no_of_matched_records = len(matched_df)
        total_records = len(gt_drop_null_df)
        return total_records, pred_found_gt_null, no_of_matched_records, no_of_unfound_records
    
    def compare_columns_distances(self, df, gt_column, pred_column, distance_column='levenstein'):
        
        formatted_gt_column = gt_column + '_formatted'
        formatted_pred_column = pred_column + '_formatted'
        df[distance_column] = df.apply(lambda x: self.levenshtein_similarity(x[formatted_gt_column], x[formatted_pred_column]), axis = 1)
        pred_found_gt_null = len(df[(df[formatted_gt_column]=='not_found') & (df[formatted_pred_column] != 'not_found')])
        gt_drop_null_df = df[df[formatted_gt_column] != 'not_found']
        no_of_unfound_records = len(gt_drop_null_df[gt_drop_null_df[formatted_pred_column] == 'not_found'])
        no_of_matched_records = len(gt_drop_null_df[gt_drop_null_df[distance_column]==0])
        mismatched_df = df[df[distance_column] != 0]
        if self.save_mismatch_results:
            file_name = pred_column.replace('_pred','') + '_'+ self.suffix_name+ '_mismatched_results.xlsx'
            mismatched_df[['Original_Image_Name', pred_column, gt_column, distance_column]].to_excel(os.path.join(self.save_dir, file_name), index=False)

        total_records = len(gt_drop_null_df)
        
        partial_match_records_df = df[(df[gt_column] != 'not_found') & (df[pred_column]!= 'not_found')]
        
        result_dict = {}
        if not partial_match_records_df.empty:
            max_distance = partial_match_records_df[distance_column].max()
            print(max_distance)
            if max_distance >25:
                if max_distance !=26:
                    bins = np.array([1,6,11,16,21,26, max_distance])
                else:
                    bins = np.array([1,6,11,16,21,26])
            else:
                bins = np.arange(1, max_distance, 5)
            # Use pd.cut to bin the data and count occurrences
            ranges = pd.cut(partial_match_records_df[distance_column], bins=bins).value_counts()

            # Create a dictionary from the ranges
            result_dict = {f"{int(bin.left)}-{int(bin.right)}": count for bin, count in zip(ranges.index, ranges)}

        return total_records, pred_found_gt_null, no_of_matched_records, no_of_unfound_records, json.dumps(result_dict)

    def levenshtein_similarity(self, text1, text2):
        """
        Computes the normalized Levenshtein distance (edit distance) between two texts,
        indicating their similarity.

        Parameters:
        - text1, text2 (str): The input texts to compare.

        Returns:
        - float: Normalized similarity score based on Levenshtein distance.
        """
        if text1 is None:
            text1 = ''
        if text2 is None:
            text2 = ''
        distance = Levenshtein.distance(text1, text2)
        max_len = max(len(text1), len(text2))
        if max_len == 0: return 1.0  # Avoid division by zero for empty strings
    #     return 1 - distance / max_len
        return distance
    
    @staticmethod
    def remove_until_number(input_string):
        if len(input_string) <=50:
            for char in input_string:
                if char.isdigit():
                    return input_string[input_string.index(char):]
            return input_string  # Return the original string if no number is found
        else:
            return input_string
    
    def get_first_element(self, x):
        try:
            text = ast.literal_eval(x)
            if isinstance(text, list) and text:
                if len(text) == 0:
                    return 'not_found'
                else:
                    return str(text[0]).lower().strip().replace(" ", "").rstrip('.')
            else:
                return str(x).lower().strip().replace(" ", "")
        except Exception as e:
            return str(x).lower().strip().replace(" ", "")
        
