"""
### **Seal Processing Script: Removing Borders, Rotating, and Blending Back**

#### **Objective:**
This script is designed to process images containing rectangular seals by performing the following operations:
1. **Border Removal:** Detect and remove the surrounding rectangular border of a seal while preserving its inner content.
2. **Seal Rotation:** Rotate the extracted seal by specified angles.
3. **Blending Back:** Overlay the rotated seal back onto the original image while maintaining proper alignment.

The main goal is to simulate natural variations in seal orientation for applications such as document forgery detection, OCR improvement, and dataset augmentation.

---

### **Workflow Explanation:**

#### **Step 1: Read and Preprocess the Image**
- Load the input image from the specified path.
- Convert the image to grayscale to simplify processing.
- Apply **adaptive thresholding** to enhance edges and highlight the seal’s border.
- Use **morphological operations** to clean up noise and fill small gaps in detected contours.

#### **Step 2: Detect and Remove the Seal Border**
- Identify the largest external rectangular contour in the thresholded image.
- Draw a mask covering the detected border.
- Expand the mask slightly to ensure full border removal.
- Use **OpenCV’s inpainting** technique to replace the border pixels with surrounding background textures.
- Extract the seal region for further processing.

#### **Step 3: Rotate the Extracted Seal**
- Define a rotation transformation around the seal’s center.
- Calculate the new bounding dimensions after rotation.
- Apply **affine transformation** to rotate the seal while maintaining image quality.

#### **Step 4: Blend the Rotated Seal Back onto the Original Image**
- Resize the rotated seal to match the original bounding box size.
- Create a mask to assist in seamless blending.
- Replace the original seal region with the rotated version while ensuring a clean overlay.

#### **Step 5: Save the Processed Images**
- The final processed image is saved in the output directory with the filename indicating the applied rotation angle.
- The script can process multiple images and apply multiple rotation angles in one execution.

---

### **Applications:**
✅ **Dataset Augmentation:** Generates variations of seals for training machine learning models.  
✅ **Forgery Detection:** Simulates different seal orientations for fraud detection algorithms.  
✅ **OCR Enhancement:** Helps OCR systems learn to recognize rotated and misaligned seals.  
✅ **Automated Document Processing:** Improves seal extraction accuracy in scanned documents.  

This script is highly flexible and can be customized for different document types and seal structures.
"""

import cv2
import numpy as np
import os

def remove_seal_border(image_path):
    """Remove only the border of a rectangular seal while keeping the seal intact."""
    img = cv2.imread(image_path)
    if img is None:
        print(f"Error: Could not read {image_path}")
        return None, None, None
    
    # Convert image to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Apply adaptive thresholding to detect strong edges
    thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                   cv2.THRESH_BINARY_INV, 21, 10)

    # Use morphological operations to remove small noise
    kernel = np.ones((5,5), np.uint8)
    morph = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel, iterations=2)

    # Find contours in the thresholded image
    contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Create a mask for the detected border
    mask = np.zeros_like(gray)
    seal_bbox = None

    for contour in contours:
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)

        if len(approx) >= 4:  # Ensure it's a rectangle
            cv2.drawContours(mask, [approx], -1, 255, thickness=15)  # Adjust thickness to match border width
            x, y, w, h = cv2.boundingRect(approx)
            seal_bbox = (x, y, w, h)

    if seal_bbox is None:
        print("No rectangular seal detected")
        return None, None, None

    # Expand the mask slightly to ensure full border removal
    mask = cv2.dilate(mask, np.ones((10,10), np.uint8), iterations=2)

    # Inpaint the border using surrounding pixels
    img_cleaned = cv2.inpaint(img, mask, inpaintRadius=10, flags=cv2.INPAINT_TELEA)

    # Extract only the cleaned seal
    x, y, w, h = seal_bbox
    extracted_seal = img_cleaned[y:y+h, x:x+w].copy()

    return img, extracted_seal, seal_bbox  # Return the original image, extracted seal, and its position

def rotate_seal(seal, angle):
    """Rotate only the extracted seal while keeping the original dimensions."""
    h, w = seal.shape[:2]
    center = (w // 2, h // 2)

    # Get rotation matrix
    M = cv2.getRotationMatrix2D(center, angle, 1.0)

    # Compute the new bounding size
    cos = abs(M[0, 0])
    sin = abs(M[0, 1])
    new_w = int((h * sin) + (w * cos))
    new_h = int((h * cos) + (w * sin))

    # Adjust transformation matrix to keep the image centered
    M[0, 2] += (new_w / 2) - center[0]
    M[1, 2] += (new_h / 2) - center[1]

    # Rotate only the seal
    rotated_seal = cv2.warpAffine(seal, M, (new_w, new_h), borderMode=cv2.BORDER_CONSTANT, borderValue=(255, 255, 255))

    return rotated_seal, (new_w, new_h)

def blend_seal_back(original_img, rotated_seal, seal_bbox):
    """Blend the rotated seal back into the original image."""
    x, y, w, h = seal_bbox
    new_w, new_h = rotated_seal.shape[1], rotated_seal.shape[0]

    # Create a transparent mask
    mask = np.ones((new_h, new_w, 3), dtype=np.uint8) * 255
    mask = cv2.cvtColor(mask, cv2.COLOR_BGR2GRAY)

    # Resize the rotated seal to match the original bounding box size
    resized_rotated_seal = cv2.resize(rotated_seal, (w, h))

    # Blend it into the original image
    img_blend = original_img.copy()
    img_blend[y:y+h, x:x+w] = resized_rotated_seal

    return img_blend

def process_single_image(image_path, output_directory, angles=[25, 125, 15]):
    """Process a single image: Remove border, rotate only the extracted seal, and blend back."""
    os.makedirs(output_directory, exist_ok=True)
    
    base_name = os.path.splitext(os.path.basename(image_path))[0]  # Get filename without extension

    # Remove border and extract seal
    original_img, extracted_seal, seal_bbox = remove_seal_border(image_path)
    if extracted_seal is None:
        return  # Exit if image couldn't be processed
    
    for angle in angles:
        # Rotate only the extracted seal
        rotated_seal, _ = rotate_seal(extracted_seal, angle)

        # Blend rotated seal back into the original image
        final_image = blend_seal_back(original_img, rotated_seal, seal_bbox)

        # Save the final image
        output_path = os.path.join(output_directory, f"{base_name}_rotated_{angle}.jpg")
        cv2.imwrite(output_path, final_image)
        print(f"Rotated seal ({angle}°) saved: {output_path}")

# Example usage
image_path = r"/home/<USER>/secondry_drive/Shoaib/old_augmentation/only_annotation_files/remove_borders/recwbrd/first_page_Signature and Name Affidavit Variant 1 Sp 2.jpg"
output_directory = r"/home/<USER>/secondry_drive/Shoaib/old_augmentation/only_annotation_files/remove_borders/newly"
process_single_image(image_path, output_directory, angles=[25, 125, 15])
