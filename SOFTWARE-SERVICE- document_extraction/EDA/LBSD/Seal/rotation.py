"""
### **Circular Seal Detection and Rotation Script**

#### **Objective:**
This script is designed to detect circular seals in scanned document images, rotate them by specified angles, and blend them back into the original image.  
It is particularly useful for **document augmentation, forgery detection, and OCR preprocessing** where rotated variations of seals are required.

---

### **Workflow Breakdown:**

#### **Step 1: Load and Preprocess Image**
- Read the input image and extract the **bottom portion (last 40%)** where seals are typically located.
- Convert the extracted region to **grayscale** to simplify processing.
- Apply **Gaussian blur** to smooth noise and improve seal detection.

#### **Step 2: Detect Circular Seals**
- Use **Hough Circle Transform** to identify circular seals based on gradient changes.
- If a circular seal is detected:
  - Extract the detected seal region from the original image.
  - Apply a **binary mask** to isolate the seal from the background.

#### **Step 3: Rotate the Extracted Seal**
- For each specified rotation angle, apply an **affine transformation**:
  - Compute the **rotation matrix** around the center of the seal.
  - Use **OpenCV’s warpAffine** function to rotate the seal while maintaining its quality.
  - Ensure background regions remain white after rotation.

#### **Step 4: Blend Rotated Seal Back into the Image**
- Resize the rotated seal to match its original bounding box.
- Create a **white mask** for seamless blending.
- Replace the original seal region with the rotated version while keeping the background intact.

#### **Step 5: Process Multiple Images (Batch Processing)**
- The script can process all images in a specified directory.
- It automatically detects seals, rotates them at multiple angles, and saves updated images with filenames indicating the applied rotations.

---

### **Applications:**
✅ **Data Augmentation for Machine Learning** → Generates multiple rotated seal variations.  
✅ **Forgery Detection** → Helps in identifying altered seals by simulating natural distortions.  
✅ **OCR Preprocessing** → Improves recognition of rotated text within seals.  
✅ **Automated Document Processing** → Standardizes seal positions for analysis.  

This script is flexible and can be customized for different document structures or seal detection conditions.
"""

import os
import cv2
import numpy as np

def rotate_seal(image_path, output_dir, angles=[25, 125, 15, 36]):
    """Detects a circular seal (at the bottom) and rotates it with specified angles."""
    # Load image
    img = cv2.imread(image_path)
    if img is None:
        print(f"Error: Could not read {image_path}")
        return
    
    h, w = img.shape[:2]
    bottom_crop = img[int(h * 0.6):h, :]  # Focus on bottom 40% of image

    # Convert to grayscale
    gray = cv2.cvtColor(bottom_crop, cv2.COLOR_BGR2GRAY)

    # Apply GaussianBlur to reduce noise
    blurred = cv2.GaussianBlur(gray, (7, 7), 2)

    # Detect circular seal using HoughCircles
    circles = cv2.HoughCircles(blurred, cv2.HOUGH_GRADIENT, dp=1.2, minDist=50,
                               param1=50, param2=30, minRadius=30, maxRadius=200)

    if circles is not None:
        circles = np.uint16(np.around(circles))
        os.makedirs(output_dir, exist_ok=True)

        for (x, y, r) in circles[0, :1]:  # Take the first detected circle
            y += int(h * 0.6)  # Adjust y-coordinates to full image
            print(f"Seal detected at: x={x}, y={y}, radius={r} in {image_path}")

            # Create mask to extract the seal
            mask = np.zeros((h, w), dtype=np.uint8)
            cv2.circle(mask, (x, y), r, 255, thickness=-1)

            # Extract only the seal region
            seal = cv2.bitwise_and(img, img, mask=mask)

            # Crop circular region
            x1, y1, x2, y2 = x - r, y - r, x + r, y + r
            seal_cropped = seal[y1:y2, x1:x2]

            for angle in angles:
                # Rotate only the seal
                rotated_seal = rotate_image(seal_cropped, angle)

                # Create a white mask for blending
                white_mask = np.ones_like(seal_cropped) * 255
                mask_cropped = mask[y1:y2, x1:x2]
                mask_cropped = cv2.cvtColor(mask_cropped, cv2.COLOR_GRAY2BGR)

                # Create a copy of the original image
                output_img = img.copy()

                # Blend only the rotated seal back
                output_img[y1:y2, x1:x2] = np.where(mask_cropped > 0, rotated_seal, output_img[y1:y2, x1:x2])

                # Save the final image with the angle and image name in the filename
                base_name = os.path.basename(image_path).split('.')[0]
                output_path = os.path.join(output_dir, f"{base_name}_rotated_{angle}.jpg")
                cv2.imwrite(output_path, output_img)
                print(f"Rotated seal ({angle}°) saved: {output_path}")

    else:
        print(f"No circular seal detected in {image_path}.")

def rotate_image(image, angle):
    """Rotate image around its center while keeping transparency."""
    h, w = image.shape[:2]
    center = (w // 2, h // 2)

    # Get rotation matrix
    M = cv2.getRotationMatrix2D(center, angle, 1.0)

    # Rotate the image
    rotated = cv2.warpAffine(image, M, (w, h), borderMode=cv2.BORDER_CONSTANT, borderValue=(255, 255, 255))
    
    return rotated

def process_multiple_images(input_dir, output_dir, angles=[25, 125, 15, 36]):
    """Processes multiple images from a directory."""
    if not os.path.exists(input_dir):
        print(f"Error: Input directory {input_dir} does not exist.")
        return

    image_extensions = {".jpg", ".jpeg", ".png"}  # Supported image formats
    image_files = [f for f in os.listdir(input_dir) if os.path.splitext(f)[1].lower() in image_extensions]

    if not image_files:
        print("No images found in the input directory.")
        return

    print(f"Processing {len(image_files)} images...")
    for image_file in image_files:
        image_path = os.path.join(input_dir, image_file)
        rotate_seal(image_path, output_dir, angles)

# Example Usage
input_dir = r"/home/<USER>/secondry_drive/Shoaib/old_augmentation/only_annotation_files/round seals/test_3/samples"
output_dir = r"/home/<USER>/secondry_drive/Shoaib/old_augmentation/only_annotation_files/round seals/test_3/updated"

process_multiple_images(input_dir, output_dir, angles=[25, 125, 15, 36])

