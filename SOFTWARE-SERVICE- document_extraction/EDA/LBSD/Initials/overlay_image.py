"""
### Script for Overlaying Handwritten Initials on a Background Image  

#### **Objective:**  
This script overlays multiple handwritten-style initials onto a fixed region of a given background image. The purpose is to generate synthetic document images where initials appear in a predefined position, simulating real-world document annotations.  

#### **Workflow:**  

1. **Set Up Paths:**  
   - Define the background image path (base document).  
   - Define the folder containing handwritten initials (overlay images).  
   - Specify the output directory where modified images will be saved.  

2. **Ensure Output Directory Exists:**  
   - If the output folder does not exist, it is created to store the processed images.  

3. **Define a Fixed Bounding Box:**  
   - The script uses a predefined normalized bounding box (relative to image dimensions) to determine where the initials should be placed.  
   - The bounding box is then converted into pixel coordinates based on the background image’s size.  
   - Adjustments are made to the placement to fine-tune positioning.  

4. **Load and Process Each Overlay Image:**  
   - Iterate through all images in the overlay folder.  
   - Load each overlay image, ensuring it's correctly read.  
   - Resize the overlay to match the bounding box dimensions.  

5. **Blend Overlay with Background:**  
   - If the overlay image lacks an alpha channel (transparency), it's converted to RGBA format.  
   - The script extracts the alpha channel (if present) to blend the overlay onto the background smoothly.  
   - Each pixel of the overlay is blended with the corresponding background pixel using alpha blending to maintain transparency effects.  

6. **Save the Processed Image:**  
   - Each newly generated image is saved in the specified output directory with a modified filename to distinguish it.  
   - A success message is displayed for each successfully processed image.  

#### **Output:**  
- The script generates multiple synthetic images with initials blended into a fixed position on the background.  
- The processed images are saved in the output directory with the filename format: `overlayed_<original_filename>.jpg`.  

#### **Applications:**  
- Used in data augmentation for handwritten document processing.  
- Simulates real-world handwritten annotations on scanned documents.  
- Helps train machine learning models to recognize handwritten initials on forms and documents.  
"""

import cv2
import os
import numpy as np

# Paths
background_image_path = "/home/<USER>/secondry_drive/Shoaib/old_augmentation/6-3-25_lbsdsdi_dataset/initials/sample/page_1_9000001 (17).jpg"
overlay_folder = "/home/<USER>/secondry_drive/Shoaib/old_augmentation/only_annotation_files/6-3-25_lbsdsdi_dataset/initials/document_initials/document_initials_Z"
output_image_folder = "/home/<USER>/secondry_drive/Shoaib/old_augmentation/only_annotation_files/6-3-25_lbsdsdi_dataset/initials/op_doc_initials/output_images_Z"

# Ensure output folder exists
os.makedirs(output_image_folder, exist_ok=True)

# Fixed bounding box (normalized)
# xmin_fixed, ymin_fixed = 0.391177, 0.191448
# xmax_fixed, ymax_fixed = 0.583877, 0.416417

xmin_fixed, ymin_fixed = 0.409118, 0.198435
xmax_fixed, ymax_fixed = 0.129988, 0.047029

# Load background image (1003 image)
background = cv2.imread(background_image_path)
if background is None:
    raise ValueError("❌ Error: Could not load the 1003 image.")

h_bg, w_bg, _ = background.shape  # Get background dimensions

# Convert normalized bounding box to pixel values
xmin_px = int(xmin_fixed * w_bg)+60
ymin_px = int(ymin_fixed * h_bg)+300
xmax_px = int(xmax_fixed * w_bg)-180
ymax_px = int(ymax_fixed * h_bg)-80

# Compute fixed width and height
w_fixed = xmax_px 
h_fixed = ymax_px 

# Process each overlay image in the folder
for overlay_filename in os.listdir(overlay_folder):
    if overlay_filename.endswith((".jpg", ".png", ".jpeg")):
        overlay_path = os.path.join(overlay_folder, overlay_filename)

        # Load overlay image
        overlay = cv2.imread(overlay_path, cv2.IMREAD_UNCHANGED)
        if overlay is None:
            print(f"⚠️ Skipping: Could not load {overlay_filename}")
            continue

        # Resize overlay to the fixed bounding box size
        overlay_resized = cv2.resize(overlay, (w_fixed, h_fixed), interpolation=cv2.INTER_AREA)

        # Convert to RGBA if necessary
        if overlay_resized.shape[2] == 3:
            overlay_resized = cv2.cvtColor(overlay_resized, cv2.COLOR_BGR2BGRA)

        # Extract the alpha channel (if available) to blend properly
        alpha_channel = overlay_resized[:, :, 3] / 255.0 if overlay_resized.shape[2] == 4 else np.ones((h_fixed, w_fixed))

        # Copy background to avoid modifying the original
        output_image = background.copy()

        # Blend overlay onto background
        for c in range(3):  # B, G, R channels
            output_image[ymin_px:ymin_px+h_fixed, xmin_px:xmin_px+w_fixed, c] = (
                alpha_channel * overlay_resized[:, :, c] + 
                (1 - alpha_channel) * output_image[ymin_px:ymin_px+h_fixed, xmin_px:xmin_px+w_fixed, c]
            )

        # Save the new image
        output_path = os.path.join(output_image_folder, f"overlayed_{overlay_filename}")
        cv2.imwrite(output_path, output_image)
        print(f"✅ Saved: {output_path}")
