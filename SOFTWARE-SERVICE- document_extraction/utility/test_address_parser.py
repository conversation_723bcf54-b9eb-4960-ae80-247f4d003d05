#!/usr/bin/python
# -*- coding: utf-8 -*-
from docvu_de_core.address_parser.us_address_parser import *

if __name__ == '__main__':

    text = ["44 West 22nd Street, New York NY 12345",
            "Street: 10231, Brimhill Cv, Cordova, TN 38016-1023",
            "of the Land: 1795 HAMPTON OAKS DRIVE, ALPHARETTA, GA 30004",
            "4195 Hield Rd,NW, Palm Bay, FL 32900- 00000",
            "of the Land: 1701 Madison Street NE #108, Minneapolis, MN 55413",
            "55 Greentree Ln, Unit 34, South Weymouth, MA, 02190-2019",
            "26, 3099 Big Timber Loop, Lewis Center, OH 43035",
            "11920 Goshen Avenue, Unit 203, Los Angeles, CA 90049",
            "Stewart Title Guaranty Company P.O. Box 2029, Houston, TX 77252-2029",
            "1840 SW 64 Terr., North Lauderdale, FL 33068",
            "EZ Title Services, LLC 13190 SW 134th Street, Suite 206 Miami, FL 33186",
            "75 KENNEDY LANE ROCKMART, <PERSON>ULD<PERSON> COUNTY, GA 30153",
            "1304 W Medicine Lake Dr Apt 321, Plymouth, MN 55441",
            "Michael 4350 Trenton Ln N Apt 321, Plymouth, MN 55442",
            "1488 Douglas Avenue, #4, Unit A, Gardnerville, NV 89410",
            "LOT 5	 11TH STREET VILLAS AMENDED	 ACCORDING TO BOOK 237 OF MAPS	 PAGE 34 AND "
            "AFFIDAVIT OF CORRECTION RECORDED IN DOCKET 15680	 PAGE 692	 RECORDS OF MARICOPA "
            "COUNTY	 ARIZONA.	MARICOPA COUNTY	 ARIZONA",
            "2721 230th Court NW, St. Francis, MN 55070",
            "1720 NE Patterson Dr  , Lee'S Summit, MO 64086",
            "250 Remington Place, Charlottesville, VA 22903 SCHEDULE AName and Address of Title "
            "Insurance Company:STEWART TITLE GUARANTY COMPANYP.O. Box 2029, Houston, TX 77252-2029",
            "Street Name: 832 West 6th Street City/State/Zip: Riviera Beach, FL 33404 County: Palm Beach",
            "Property Information 129 Baltic St Apt 2A 2A, Brooklyn, NY, 11201 Property Address Number of Units 1",
            "Property Information 129 Baltic St Apt 2A 2A, Brooklyn, NY, 11201 Property Address Number of Units 1",
            "GREENWAY MORTGAGE FUNDING CORP 233 Broadway Suite 2305, New York, New York 10279",
            "1304 W Medicine Lake Dr Apt 321, Plymouth, MN 55441",
            "11413,Gun Fight Lane, Austin, TX 78748",
            "L 15 Villas @ Saxony, 13376",
            "30408 Marshall Street, Southfield, Michigan 48076",
            "as shown on the 6005 Sheraton Glen, Colleyville, Texas 76034",
            "10505 Dorothy Dr., Oklahoma City, Oklahoma 73162 Oklahoma County, Oklahoma",
            "Lot 34 Old Ft. Mitchell Subdivision, Fort Mitchell, KY 41011)",
            "2919 Ivanhoe Drive, Columbus, OH 43209)",
            "2227 South Rosewood Street, Philadelphia, PA, Reference:"
            ]
    test_one = True
    if test_one:
        use_text = text[-1]
        print("=" * 50 + "\n")
        usaddress_parser = USAddressParser()
        add = usaddress_parser.parse_text(use_text)
        print(use_text)
        for k, v in add.items():
            print(f"{k}: {v}")

        print("=" * 50 + "\n")
        exit(0)

    for index, use_text in enumerate(text):
        #use_text = text[2]
        print("=" * 50 + "\n")
        print(index)
        usaddress_parser = USAddressParser()
        add = usaddress_parser.parse_text(use_text)
        print(use_text)
        for k,v in add.items():
            print(f"{k}: {v}")

        print("=" * 50 + "\n")


