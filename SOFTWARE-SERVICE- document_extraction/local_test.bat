@echo off
setlocal

REM Set hardcoded settings for script parameters
set "MODE=single"
set "TYPE=lender"
set "FILE_NAME=STEWARTECM_PROD000061010888"
set "DATE_ID=%date:~-4,4%%date:~-10,2%%date:~-7,2%"  REM Adjust based on your locale settings
set "CSV_PATH=./de_results/all_sls_output_new_%DATE_ID%.csv"
set "TO_EXTRACT_FIELD="
set "CLASSIFICATION_REQUIRED=1"  REM 1 represents true, 0 represents false

REM Print the configuration to confirm settings
echo Running document extraction with the following settings:
echo Mode: %MODE%
echo Type: %TYPE%
echo File Name: %FILE_NAME%
echo Date ID: %DATE_ID%
echo CSV Path: %CSV_PATH%
echo To Extract Field: %TO_EXTRACT_FIELD%
echo Classification Required: %CLASSIFICATION_REQUIRED%

REM Execute the Python script with the specified parameters
python local_test.py ^
  --mode "%MODE%" ^
  --type "%TYPE%" ^
  --file_name "%FILE_NAME%" ^
  --date_id "%DATE_ID%" ^
  --csv_path "%CSV_PATH%" ^
  --to_extract_field "%TO_EXTRACT_FIELD%" ^
  --classification_required "%CLASSIFICATION_REQUIRED%"

endlocal
