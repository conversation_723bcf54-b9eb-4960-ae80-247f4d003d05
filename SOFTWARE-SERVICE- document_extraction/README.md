# Document Extraction Parser

The Parser can extract the following (will update as and when we have new updates)
1. Normal key value (right, down and up)
2. Normal key value (right and down) with multi liner value
3. Normal key value (right and down) with end identifier
4. OMR (right and down)
5. Tablular data


## The Pending / WIP TODO's
1. Normlize the OCR coordinates to a fixed ratio.
2. update the yolo inference with onnxruntime.
3. for unstructured document need to check for the key and value in whole data as it can come in two different line.


## Approach
1. Extract text from image using pytesseract or any OCR in XML format.
2. Convert the XML to JSON format with grouping the text which is nearby.
3. Using the list of values to be extracted parse the JSON to get the values.


## Libraries Used
1. Pytesseract
2. XML
3. CV2
4. Numpy
5. Ultralytics
6. FastAPI


## Important files and their usage
1. ``parser.py`` - the code which uses the rules (in de_config json files) to extact the data from the ocr results.
2. ``pytesseract_json.py`` - the ocr which is extracted into xml will be converted into json format. And it will merge the near blocks in to one blocks along with some postprocessing.
3. ``kofax_json.py`` - the ocr which is extracted into xml will be converted into json format. And it will merge the near lines in to one block along with some postprocessing(String->Text Line->Text Block->CBlock).
4. ``checkbox.py`` - The code which gets the prediction(checked box) from yolo model and find the the value which is checked.
5. ``omr.py`` - The code is uses some fixed characters to identify the marked field (rule based).
6. ``table_parser.py`` - The code takes the OCR output in word level and the column names to identify the table structure.
7. ``pdf_to_json.py`` - The code takes PDF as input and uses the pytesseract to extract OCR and convert into json format.
8. ``app.py` - The code for APIs
9. ``excel_to_json_config.py` - Converts the excel to json and does some post processing.


## To run the Fast API application
1. run app.py
2. the api will run in http://0.0.0.0:8003
3. to see the swagger use the url http://0.0.0.0:8003/docs


## To run the GUI application
1. run gui.py using the command `streamlit run gui.py --server.port 8002`
2. the application will start running in the url http://0.0.0.0:8002/


## Integrate new document
1. Prepare the config .json file, and save it in the de_config directory.
2. Add the document name, id, and config path in the config/document_types.json


## Add values to extract
Update or add the json fields in de_config folder (json files)


## Project configuration
/config/config.yaml

## GIT BRANCH
saket/lender_policy2