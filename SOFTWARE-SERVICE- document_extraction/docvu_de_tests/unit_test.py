import os
import traceback
import pandas as pd
from docvu_de_core.api import *
from docvu_de_tests import *

class UnitTest:
    def __init__(self):
        self.api = API(curr_path=os.path.dirname(__file__), load_yolo_v8_for_checkbox=True)
        self.error_log = []
        # Paths for long-form lender policy
        self.policy_xml_path_long = [os.path.join(prod_samples_dir, '/Users/<USER>/Downloads/500_SLSOCR_OUTPUT/ECM_PROD000020433319.json')]
        self.policy_pdf_path_long = [os.path.join(prod_samples_dir, "/Users/<USER>/Downloads/500_SLS_PDF/ECM_PROD000020433319.pdf")]
        # Paths for short-form lender policy
        self.policy_xml_path_short = [os.path.join(prod_samples_dir, '/Users/<USER>/Downloads/500_SLSOCR_OUTPUT/STEWARTECM_PROD000054013333.json')]
        self.policy_pdf_path_short = [os.path.join(prod_samples_dir, '/Users/<USER>/Downloads/500_SLS_PDF/STEWARTECM_PROD000054013333.pdf')]
        # Paths for owner policy
        self.policy_xml_path_owner = [os.path.join(prod_samples_dir, '/Users/<USER>/Downloads/500_SLSOCR_OUTPUT/34674_ALTA_Owners_Policy_Schedules_with_Jacket_6-17-06.json')]
        self.policy_pdf_path_owner = [os.path.join(prod_samples_dir, '/Users/<USER>/Downloads/500_SLS_PDF/34674_ALTA_Owners_Policy_Schedules_with_Jacket_6-17-06.pdf')]

    def log_error(self, file_name, data_name, expected, got, ):
        self.error_log.append({
            'File Name': file_name,
            'Data Name': data_name,
            'Expected': expected,
            'Got': got,

        })

    def save_error_log(self, file_path='./error_log.xlsx'):
        print("Entering save_error_log ")
        if self.error_log:
            df = pd.DataFrame(self.error_log)
            try:
                dir_path = os.path.dirname(file_path)
                if not os.path.exists(dir_path):
                    os.makedirs(dir_path)
                print(f"Attempting to save Excel file to {file_path}")
                df.to_excel(file_path, index=False)
                print(f"Errors logged in {file_path}")
            except PermissionError as e:
                print(f"Permission denied: {e}. Please check file permissions and path.")
            except FileNotFoundError as e:
                print(f"File path not found: {e}. Please check if the directory exists.")
            except Exception as e:
                print(f"Failed to save Excel file: {e}")

    def test_long_form_lender_policy(self):
        try:
            print("test_long_form_lender_policy STARTED")
            policy_xml_path = self.policy_xml_path_long[0]
            policy_pdf_path = self.policy_pdf_path_long[0]
            form_type, search_start_page = (
                self.api._classify(pdf_data_path=None,
                                   ocr_xml_json_path=policy_xml_path,
                                   pdf_file_path=policy_pdf_path,
                                   classification_required=True,
                                   for_testing=True))
            # Check policy type and de_start_page
            assert form_type == 'long_form', f"form_type is {form_type}, expected 'long_form'"
            assert search_start_page == 7, f"search_start_page is {search_start_page}, expected 7"

            document_id = "9002231"

            de_json = self.api._extract(
                form_type,
                search_start_page,
                pdf_data_path=None,
                ocr_xml_json_path=policy_xml_path,
                pdf_file_path=policy_pdf_path,
                to_extract_field=None,
                return_indexing_info=False,
                fetch_prev_to_extract_field=False,
                separate_table_data=True,
                for_testing=True, 
                document_id=document_id)

            print(f"Original_Image_Length: {de_json.get('Original_Image_Length')}")

            # Check if keys are present in de output json
            file_name = policy_xml_path.split('/')[-1].split('.')[0]
            test_gt = tests_gt[file_name]['FieldData']

            assert de_json['Form_type'] == form_type
            assert de_json['Original_Image_Length'] == 10
            assert 'FieldData' in de_json['Value'].keys(), f"'FieldData' not found in de_json['Value'].keys()"
            field_data = de_json['Value']['FieldData']
            for data in field_data:
                field_values = [d for d in test_gt if d['Name'] == data['Name']]
                if field_values:
                    field_values = field_values[0]
                    print(f"Asserting {data['Name']}")
                    assert field_values['Value'] == data['PostProcessingValue'], f"{data['Name']} Values are Not Matching. Expected: {field_values['Value']}, Got: {data['PostProcessingValue']}"
                    assert field_values['PageNumber'] == data['PageNumber'], f"{data['Name']} Page Numbers are Not Matching. Expected: {field_values['PageNumber']}, Got: {data['PageNumber']}"

        except AssertionError as e:
            # Extracting expected and got values from the error message
            file_name = policy_xml_path.split('/')[-1].split('.')[0]
            expected = e.args[0].split('Expected: ')[1].split(', Got: ')[0]
            got = e.args[0].split('Got: ')[-1]
            self.log_error(file_name, data['Name'], expected, got)
            print(f"Error in test_long_form_lender_policy: {e}")
        except Exception as e:
            print(f"Unexpected error in test_long_form_lender_policy: {traceback.format_exc()}")

    def test_short_form_lender_policy(self):
        try:
            print("test_short_form_lender_policy STARTED")
            policy_xml_path = self.policy_xml_path_short[0]
            policy_pdf_path = self.policy_pdf_path_short[0]
            form_type, search_start_page = (
                self.api._classify(pdf_data_path=None,
                                   ocr_xml_json_path=policy_xml_path,
                                   pdf_file_path=policy_pdf_path,
                                   classification_required=True,
                                   for_testing=True))

            assert form_type == 'short_form'
            assert search_start_page == 1

            document_id = "9002233"

            de_json = self.api._extract(
                form_type,
                search_start_page,
                pdf_data_path=None,
                ocr_xml_json_path=policy_xml_path,
                pdf_file_path=policy_pdf_path,
                to_extract_field=None,
                return_indexing_info=False,
                fetch_prev_to_extract_field=False,
                separate_table_data=True,
                for_testing=True,
                document_id=document_id)

            print(f"Original_Image_Length: {de_json.get('Original_Image_Length')}")

            file_name = policy_xml_path.split('/')[-1].split('.')[0]
            test_gt = tests_gt[file_name]['FieldData']

            assert de_json['Form_type'] == form_type
            assert de_json['Original_Image_Length'] == 7
            assert 'FieldData' in de_json['Value'].keys()
            field_data = de_json['Value']['FieldData']
            table_data = de_json["Value"]['TableData']
            for data in field_data:
                field_values = [d for d in test_gt if d['Name'] == data['Name']]
                if field_values:
                    field_values = field_values[0]
                    print(f"Asserting {data['Name']}")
                    assert field_values['Value'] == data['PostProcessingValue'], f"{data['Name']} Values are Not Matching. Expected: {field_values['Value']}, Got: {data['PostProcessingValue']}"
                    assert field_values['PageNumber'] == data['PageNumber'], f"{data['Name']} Page Numbers are Not Matching"

        except AssertionError as e:
            # Extracting expected and got values from the error message
            file_name = policy_xml_path.split('/')[-1].split('.')[0]
            expected = e.args[0].split('Expected: ')[1].split(', Got: ')[0]
            got = e.args[0].split('Got: ')[-1]
            self.log_error(file_name, data['Name'], expected, got)
            print(f"Error in test_short_form_lender_policy: {e}")
        except Exception as e:
            print(f"Unexpected error in test_short_form_lender_policy: {traceback.format_exc()}")

    def test_owner_policy(self):
        try:
            print("test_owner_policy STARTED")
            policy_xml_path = self.policy_xml_path_owner[0]
            policy_pdf_path = self.policy_pdf_path_owner[0]
            form_type, search_start_page = (
                self.api._classify(pdf_data_path=None,
                                   ocr_xml_json_path=policy_xml_path,
                                   pdf_file_path=policy_pdf_path,
                                   classification_required=True,
                                   for_testing=True))

            print(f"Search Start Page: {search_start_page}")

            assert form_type == 'owner_form'
            assert search_start_page == 5

            document_id = "9002236"
            
            de_json = self.api._extract(
                form_type,
                search_start_page,
                pdf_data_path=None,
                ocr_xml_json_path=policy_xml_path,
                pdf_file_path=policy_pdf_path,
                to_extract_field=None,
                return_indexing_info=False,
                fetch_prev_to_extract_field=False,
                separate_table_data=True,
                for_testing=True,
                document_id=document_id)

            print(f"Original_Image_Length: {de_json.get('Original_Image_Length')}")

            file_name = policy_xml_path.split('/')[-1].split('.')[0]
            test_gt = tests_gt[file_name]['FieldData']

            assert de_json['Form_type'] == form_type
            assert de_json['Original_Image_Length'] == 7
            assert 'FieldData' in de_json['Value'].keys()
            field_data = de_json['Value']['FieldData']
            for data in field_data:
                field_values = [d for d in test_gt if d['Name'] == data['Name']]
                if field_values:
                    field_values = field_values[0]
                    print(f"Asserting {data['Name']}")
                    assert field_values['Value'] == data['PostProcessingValue'], f"{data['Name']} Values are Not Matching. Expected: {field_values['Value']}, Got: {data['PostProcessingValue']}"
                    assert field_values['PageNumber'] == data['PageNumber'], f"{data['Name']} Page Numbers are Not Matching"

        except AssertionError as e:
            # Extracting expected and got values from the error message
            file_name = policy_xml_path.split('/')[-1].split('.')[0]
            expected = e.args[0].split('Expected: ')[1].split(', Got: ')[0]
            got = e.args[0].split('Got: ')[-1]
            self.log_error(file_name, data['Name'], expected, got)
            print(f"Error in test_owner_policy: {e}")
        except Exception as e:
            print(f"Unexpected error in test_owner_policy: {traceback.format_exc()}")

if __name__ == '__main__':
    ut = UnitTest()
    ut.test_long_form_lender_policy()
    ut.test_short_form_lender_policy()
    ut.test_owner_policy()
    ut.save_error_log()
    print('Done')
