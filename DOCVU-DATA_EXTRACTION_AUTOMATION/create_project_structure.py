#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the complete project structure for Document Extraction Automation
"""
import os

def create_directory_structure():
    """Create the complete directory structure"""
    
    directories = [
        # Main project structure
        "document_extraction_automation",
        "document_extraction_automation/backend",
        "document_extraction_automation/backend/app",
        "document_extraction_automation/backend/app/core",
        "document_extraction_automation/backend/app/core/phase1_template",
        "document_extraction_automation/backend/app/core/phase2_ner",
        "document_extraction_automation/backend/app/core/phase3_ml",
        "document_extraction_automation/backend/app/api",
        "document_extraction_automation/backend/app/api/v1",
        "document_extraction_automation/backend/app/api/v1/endpoints",
        "document_extraction_automation/backend/app/models",
        "document_extraction_automation/backend/app/schemas",
        "document_extraction_automation/backend/app/services",
        "document_extraction_automation/backend/app/utils",
        "document_extraction_automation/backend/app/config",
        
        # Legacy integration
        "document_extraction_automation/backend/app/legacy",
        "document_extraction_automation/backend/app/legacy/ocr",
        "document_extraction_automation/backend/app/legacy/extraction",
        "document_extraction_automation/backend/app/legacy/models",
        
        # Frontend
        "document_extraction_automation/frontend",
        "document_extraction_automation/frontend/src",
        "document_extraction_automation/frontend/src/components",
        "document_extraction_automation/frontend/src/components/annotation",
        "document_extraction_automation/frontend/src/components/templates",
        "document_extraction_automation/frontend/src/components/extraction",
        "document_extraction_automation/frontend/src/services",
        "document_extraction_automation/frontend/src/utils",
        "document_extraction_automation/frontend/public",
        
        # Data storage
        "document_extraction_automation/data",
        "document_extraction_automation/data/templates",
        "document_extraction_automation/data/annotations",
        "document_extraction_automation/data/outputs",
        "document_extraction_automation/data/temp",
        
        # Models and ML
        "document_extraction_automation/models",
        "document_extraction_automation/models/pretrained",
        "document_extraction_automation/models/custom",
        
        # Tests
        "document_extraction_automation/tests",
        "document_extraction_automation/tests/unit",
        "document_extraction_automation/tests/integration",
        "document_extraction_automation/tests/e2e",
        
        # Documentation
        "document_extraction_automation/docs",
        "document_extraction_automation/docs/api",
        "document_extraction_automation/docs/architecture",
        
        # Scripts and utilities
        "document_extraction_automation/scripts",
        "document_extraction_automation/scripts/migration",
        "document_extraction_automation/scripts/setup",
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")

if __name__ == "__main__":
    create_directory_structure()
    print("Project structure created successfully!")
