#!/usr/bin/env python3
"""
Simple test to verify key-value extraction is working
"""
import json
import sys
from pathlib import Path

def test_template_loading():
    """Test loading the template"""
    print("🔍 Testing Template Loading...")
    
    template_file = Path("backend/data/templates/note_v1.0.0.json")
    
    if not template_file.exists():
        print(f"❌ Template file not found: {template_file}")
        return None
    
    try:
        with open(template_file, 'r') as f:
            template = json.load(f)
        
        print(f"✅ Template loaded successfully!")
        print(f"   - Name: {template.get('name', 'Unknown')}")
        print(f"   - Subtype: {template.get('subtype', 'Unknown')}")
        print(f"   - Template fields: {len(template.get('annotations', []))}")
        print(f"   - Key-value fields: {len(template.get('key_value_fields', []))}")
        
        # Print template fields
        if template.get('annotations'):
            print("\n📋 Template Fields:")
            for field in template.get('annotations', []):
                print(f"   - {field.get('field_name', 'Unknown')} (ID: {field.get('field_id', 'Unknown')})")
        
        # Print key-value fields
        if template.get('key_value_fields'):
            print("\n🔍 Key-Value Fields:")
            for field in template.get('key_value_fields', []):
                print(f"   - {field.get('field_name', 'Unknown')} (ID: {field.get('field_id', 'Unknown')})")
                print(f"     Start: '{field.get('start_identifier', '')}' → End: '{field.get('end_identifier', '')}'")
        
        return template
        
    except Exception as e:
        print(f"❌ Error loading template: {e}")
        return None

def test_key_value_extraction():
    """Test key-value extraction logic"""
    print("\n🔧 Testing Key-Value Extraction Logic...")
    
    # Sample OCR data with the identifiers
    sample_words = [
        {"text": "1.BORROWER'S", "bbox": {"x1": 100, "y1": 200, "x2": 200, "y2": 220}},
        {"text": "PROMISE", "bbox": {"x1": 205, "y1": 200, "x2": 260, "y2": 220}},
        {"text": "TO", "bbox": {"x1": 265, "y1": 200, "x2": 285, "y2": 220}},
        {"text": "PAY", "bbox": {"x1": 290, "y1": 200, "x2": 320, "y2": 220}},
        {"text": "Company", "bbox": {"x1": 100, "y1": 240, "x2": 160, "y2": 260}},
        {"text": "ABC", "bbox": {"x1": 165, "y1": 240, "x2": 195, "y2": 260}},
        {"text": "Corporation", "bbox": {"x1": 200, "y1": 240, "x2": 280, "y2": 260}},
        {"text": "2.INTEREST", "bbox": {"x1": 100, "y1": 280, "x2": 180, "y2": 300}}
    ]
    
    start_identifier = "1.BORROWER'S PROMISE TO PAY"
    end_identifier = "2.INTEREST"
    
    print(f"Looking for text between:")
    print(f"  Start: '{start_identifier}'")
    print(f"  End: '{end_identifier}'")
    
    # Find start identifier
    start_index = None
    for i, word in enumerate(sample_words):
        word_text = word.get('text', '').strip()
        if start_identifier.lower() in word_text.lower():
            start_index = i
            print(f"  Found start at index {i}: '{word_text}'")
            break
    
    if start_index is None:
        # Try partial matching
        for i, word in enumerate(sample_words):
            word_text = word.get('text', '').strip()
            if "BORROWER'S" in word_text:
                start_index = i
                print(f"  Found partial start at index {i}: '{word_text}'")
                break
    
    # Find end identifier
    end_index = len(sample_words)
    if end_identifier:
        for i in range((start_index or 0) + 1, len(sample_words)):
            word_text = sample_words[i].get('text', '').strip()
            if end_identifier.lower() in word_text.lower():
                end_index = i
                print(f"  Found end at index {i}: '{word_text}'")
                break
    
    if start_index is not None:
        # Extract text between identifiers
        extracted_words = []
        for i in range(start_index + 1, end_index):
            word = sample_words[i]
            word_text = word.get('text', '').strip()
            if word_text:
                extracted_words.append(word_text)
        
        extracted_text = ' '.join(extracted_words).strip()
        print(f"  ✅ Extracted text: '{extracted_text}'")
        return extracted_text
    else:
        print(f"  ❌ Start identifier not found")
        return None

def main():
    """Main test function"""
    print("🧪 Simple Document Extraction Test")
    print("=" * 50)
    
    # Test 1: Template Loading
    template = test_template_loading()
    
    if template:
        # Test 2: Key-Value Extraction Logic
        extracted_text = test_key_value_extraction()
        
        if extracted_text:
            print(f"\n🎉 Key-value extraction working!")
            print(f"Expected to extract company name from between identifiers")
        else:
            print(f"\n❌ Key-value extraction failed")
    else:
        print(f"\n❌ Template loading failed")

if __name__ == "__main__":
    main()
