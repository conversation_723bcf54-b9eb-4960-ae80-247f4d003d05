#!/usr/bin/env python3
"""
Test the API endpoint directly
"""
import requests
import json
import tempfile
import os
from pathlib import Path

def create_test_files():
    """Create test PDF and OCR files"""
    
    # Create a dummy PDF file
    pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF"
    
    # Create OCR data that matches our template (with blocks/lines/words structure)
    ocr_data = {
        "1": {
            "blocks": [
                {
                    "lines": [
                        {
                            "words": [
                                {
                                    "text": "MIN:",
                                    "bbox": {"x1": 196, "y1": 158, "x2": 220, "y2": 176},
                                    "confidence": 95,
                                    "x1": 196, "y1": 158, "x2": 220, "y2": 176
                                },
                                {
                                    "text": "100754400003869250",
                                    "bbox": {"x1": 225, "y1": 158, "x2": 350, "y2": 176},
                                    "confidence": 95,
                                    "x1": 225, "y1": 158, "x2": 350, "y2": 176
                                },
                                {
                                    "text": "FORT",
                                    "bbox": {"x1": 355, "y1": 158, "x2": 385, "y2": 176},
                                    "confidence": 95,
                                    "x1": 355, "y1": 158, "x2": 385, "y2": 176
                                },
                                {
                                    "text": "WASHINGTON,",
                                    "bbox": {"x1": 390, "y1": 158, "x2": 428, "y2": 176},
                                    "confidence": 95,
                                    "x1": 390, "y1": 158, "x2": 428, "y2": 176
                                },
                                {
                                    "text": "PENNSYLVANIA",
                                    "bbox": {"x1": 430, "y1": 158, "x2": 500, "y2": 176},
                                    "confidence": 95,
                                    "x1": 430, "y1": 158, "x2": 500, "y2": 176
                                }
                            ]
                        },
                        {
                            "words": [
                                {
                                    "text": "1.BORROWER'S",
                                    "bbox": {"x1": 100, "y1": 200, "x2": 200, "y2": 220},
                                    "confidence": 95,
                                    "x1": 100, "y1": 200, "x2": 200, "y2": 220
                                },
                                {
                                    "text": "PROMISE",
                                    "bbox": {"x1": 205, "y1": 200, "x2": 260, "y2": 220},
                                    "confidence": 95,
                                    "x1": 205, "y1": 200, "x2": 260, "y2": 220
                                },
                                {
                                    "text": "TO",
                                    "bbox": {"x1": 265, "y1": 200, "x2": 285, "y2": 220},
                                    "confidence": 95,
                                    "x1": 265, "y1": 200, "x2": 285, "y2": 220
                                },
                                {
                                    "text": "PAY",
                                    "bbox": {"x1": 290, "y1": 200, "x2": 320, "y2": 220},
                                    "confidence": 95,
                                    "x1": 290, "y1": 200, "x2": 320, "y2": 220
                                }
                            ]
                        },
                        {
                            "words": [
                                {
                                    "text": "ABC",
                                    "bbox": {"x1": 100, "y1": 240, "x2": 140, "y2": 260},
                                    "confidence": 95,
                                    "x1": 100, "y1": 240, "x2": 140, "y2": 260
                                },
                                {
                                    "text": "Corporation",
                                    "bbox": {"x1": 145, "y1": 240, "x2": 220, "y2": 260},
                                    "confidence": 95,
                                    "x1": 145, "y1": 240, "x2": 220, "y2": 260
                                },
                                {
                                    "text": "Inc.",
                                    "bbox": {"x1": 225, "y1": 240, "x2": 250, "y2": 260},
                                    "confidence": 95,
                                    "x1": 225, "y1": 240, "x2": 250, "y2": 260
                                }
                            ]
                        },
                        {
                            "words": [
                                {
                                    "text": "2.INTEREST",
                                    "bbox": {"x1": 100, "y1": 280, "x2": 180, "y2": 300},
                                    "confidence": 95,
                                    "x1": 100, "y1": 280, "x2": 180, "y2": 300
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    }
    
    # Create temporary files
    with tempfile.NamedTemporaryFile(mode='wb', suffix='.pdf', delete=False) as pdf_file:
        pdf_file.write(pdf_content)
        pdf_path = pdf_file.name
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as ocr_file:
        json.dump(ocr_data, ocr_file, indent=2)
        ocr_path = ocr_file.name
    
    return pdf_path, ocr_path

def test_extraction_api():
    """Test the extraction API endpoint"""
    print("🔧 Testing Extraction API...")
    
    # Create test files
    pdf_path, ocr_path = create_test_files()
    
    try:
        # Prepare the request
        url = "http://localhost:8000/api/v1/extraction/extract"
        
        with open(pdf_path, 'rb') as pdf_file, open(ocr_path, 'rb') as ocr_file:
            files = {
                'document': ('test.pdf', pdf_file, 'application/pdf'),
                'ocr_data': ('test.json', ocr_file, 'application/json')
            }
            
            data = {
                'strategy': 'template',
                'document_type': 'note'
            }
            
            print(f"Sending request to: {url}")
            print(f"Document type: note")
            print(f"Strategy: template")
            
            # Make the request
            response = requests.post(url, files=files, data=data, timeout=30)
            
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ API request successful!")
                
                # Print the result structure
                print(f"\nResult structure:")
                print(f"- Form_type: {result.get('Form_type', 'Not found')}")
                print(f"- Original_Image_Name: {result.get('Original_Image_Name', 'Not found')}")
                print(f"- Field count: {len(result.get('Value', {}).get('FieldData', []))}")
                
                # Print each field
                field_data = result.get('Value', {}).get('FieldData', [])
                if field_data:
                    print(f"\n📊 Extracted Fields:")
                    for field in field_data:
                        print(f"   - {field.get('Name', 'Unknown')} (ID: {field.get('Id', 'Unknown')})")
                        print(f"     Value: '{field.get('Value', '')}'")
                        print(f"     PostProcessingValue: '{field.get('PostProcessingValue', '')}'")
                        print()
                else:
                    print("❌ No fields extracted!")
                
                return result
            else:
                print(f"❌ API request failed: {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return None
    
    finally:
        # Clean up temporary files
        try:
            os.unlink(pdf_path)
            os.unlink(ocr_path)
        except:
            pass

def main():
    """Main test function"""
    print("🧪 API Extraction Test")
    print("=" * 50)
    
    result = test_extraction_api()
    
    if result:
        field_data = result.get('Value', {}).get('FieldData', [])
        
        # Check if we have both template and key-value fields
        template_fields = [f for f in field_data if f.get('Id') == '12345']
        kv_fields = [f for f in field_data if f.get('Id') == '234567']
        
        print(f"\n📊 Analysis:")
        print(f"- Template fields found: {len(template_fields)}")
        print(f"- Key-value fields found: {len(kv_fields)}")
        
        if template_fields and kv_fields:
            print("🎉 SUCCESS: Both template and key-value fields extracted!")
        elif template_fields:
            print("⚠️  PARTIAL: Only template fields extracted")
        elif kv_fields:
            print("⚠️  PARTIAL: Only key-value fields extracted")
        else:
            print("❌ FAILED: No fields extracted")
    else:
        print("❌ API test failed")

if __name__ == "__main__":
    main()
