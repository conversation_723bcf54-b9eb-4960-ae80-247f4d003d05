{"OrderNumber": "9000671 (13)", "Pages": 3, "Status": 1, "OcrData": [{"PageNumber": 1, "PageText": "NOTE NOVEMBER 26, 2021 [Date] FORT WASHINGTON, [City] PEPPER Loan #: 38121081989 MIN: 100754400003816152 PENNSYLVANIA 112 NEW RD, TABERNACLE, NJ 08088 [Property Address] 1. <PERSON><PERSON><PERSON><PERSON><PERSON>'S PROMISE TO PAY [State] In return for a loan that I have received, I promise to pay U.S. $269,300 . 00 (this amount is called \"Principal\"), plus interest, to the order of the Lender. The Lender is NEWREZ LLC. I will make all payments under this Note in the form, of cash, check or money order. I understand that the Lender may transfer this Note. The Lender or anyone who takes this Note by transfer and who is entitled to receive payments under this Note is called the \"Note Holder.\" 2. INTEREST Interest will be charged on unpaid principal until the full amount of Principal has been paid. I will pay interest at a yearly ra-e of 2.875%. The interest rate required by this Section 2 is the rate I will pay both before and after any default described in Section 6(B) of this Note. 3. PAYMENTS (A) Time and Place of Payments I will pay principal and interest by making a payment every month. I will make my monthly payment on the 1ST day of each month beginning on FEBRUARY 1, 2022. I will make these payments every month until I have paid all of the principal and interest and any other charges described below that I may owe under this Note. Each monthly payment will be applied as of its scheduled due date and will be applied to interest before Principal. If, on JANUARY 1, 2052, I still owe amounts under this Note, I will pay those amounts in full on that date, which is called the \"Maturity Date.\" I will make my monthly payments at PO BOX 740039, CINCINNATI, OH 45274-0039 or at a different place if required by the Note Holder. (B) Amount of Monthly Payments My monthly payment will be in the amount of U.S. $1,117.31. 4. BORROWER'S RIGHT TO PREPAY I have the right to make payments of Principal at any time before they are due. A payment of Principal only is known as a \"Prepayment.\" When I make a Prepayment, I will tell the Note Holder in writing that I am doing so. I may not designate a payment as a Prepayment if I have not made all the monthly payments due under the Note. I may make a full Prepayment or partial Prepayments without paying a Prepayment charge. The Note Holder will use my Prepayments to reduce the amount of Principal that I owe under this Note. However, the Note Holder may apply my Prepayment to the accrued and unpaid interest on the Prepayment amount, before applying my Prepayment to reduce the Principal amount of the Note. If I make a partial Prepayment, there will be no changes in the due date or in the amount of my monthly payment unless the Note Holder agrees in writing to those changes. 5. LOAN CHARGES If a law, which applies to this loan and which sets maximum loan charges, is finally interpreted so that the interest or other loan charges collected or to be collected in connection with this loan exceed the permitted limits, then: '.(a) any such loan charge shall be reduced by the amount necessary to reduce the charge to the permitted limit; and (b) any sums already collected from me which exceeded permitted limits will be refunded to me. The Note Holder may choose to make this refund by reducing the Principal I owe under this Note or by making a direct MULTISTATE FIXED RATE NOTE -Single Family-- Fannie Mae/Freddie Mac UNIFORM INSTRUMENT exIJ 5 74 Form 3200 1/01 (page I of 3 pages) ", "PageXML": "<Page PageWidth='2534' PageHeight='4271'>\r\n<PageNumber>1</PageNumber>\r\n<Lines>\r\n<Line BaseLine='08.94' Height='00.36' >\r\n<LineText>~X[12.43]NOTE </LineText>\r\n<Words>\r\n<Word Height='00035' StartingX='01195'  EndingX='01300'  StartingY='00825' Length='4'  Confidence='0,0,0,0'  >NOTE</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='10.91' Height='00.29' >\r\n<LineText>~X[04.40]NOVEMBER 26, 2021 </LineText>\r\n<Words>\r\n<Word Height='00025' StartingX='00423'  EndingX='00592'  StartingY='01023' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >NOVEMBER</Word>\r\n<Word Height='00025' StartingX='00642'  EndingX='00696'  StartingY='01022' Length='3'  Confidence='0,0,0'  >26,</Word>\r\n<Word Height='00025' StartingX='00738'  EndingX='00812'  StartingY='01022' Length='4'  Confidence='0,0,0,0'  >2021</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='11.41' Height='00.40' >\r\n<LineText>~X[06.08][Date] </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00585'  EndingX='00672'  StartingY='01067' Length='6'  Confidence='0,0,0,0,0,0'  >[Date]</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='10.90' Height='00.29' >\r\n<LineText>~X[11.01]FORT WASHINGTON, </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='01059'  EndingX='01131'  StartingY='01022' Length='4'  Confidence='0,0,0,0'  >FORT</Word>\r\n<Word Height='00028' StartingX='01177'  EndingX='01425'  StartingY='01022' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >WASHINGTON,</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='11.40' Height='00.35' >\r\n<LineText>~X[12.48][City] </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='01200'  EndingX='01315'  StartingY='01068' Length='6'  Confidence='50,0,0,0,0,0'  >[City]</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='09.53' Height='00.20' >\r\n<LineText>~X[17.04]PEPPER </LineText>\r\n<Words>\r\n<Word Height='00019' StartingX='01638'  EndingX='01733'  StartingY='00897' Length='6'  Confidence='0,0,0,0,0,0'  >PEPPER</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='09.92' Height='00.24' >\r\n<LineText>~X[17.04]Loan #: ~F38121081989 ~F</LineText>\r\n<Words>\r\n<Word Height='00022' StartingX='01638'  EndingX='01687'  StartingY='00931' Length='4'  Confidence='0,0,0,0'  >Loan</Word>\r\n<Word Height='00022' StartingX='01711'  EndingX='01729'  StartingY='00931' Length='2'  Confidence='30,30'  >#:</Word>\r\n<Word Height='00022' StartingX='01745'  EndingX='01939'  StartingY='00932' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >38121081989</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='10.32' Height='00.24' >\r\n<LineText>~X[17.02]MIN: ~F100754400003816152 ~F</LineText>\r\n<Words>\r\n<Word Height='00022' StartingX='01637'  EndingX='01701'  StartingY='00970' Length='4'  Confidence='0,50,0,20'  >MIN:</Word>\r\n<Word Height='00022' StartingX='01718'  EndingX='02045'  StartingY='00971' Length='18'  Confidence='0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0'  >100754400003816152</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='10.93' Height='00.28' >\r\n<LineText>~X[18.12]PENNSYLVANIA </LineText>\r\n<Words>\r\n<Word Height='00025' StartingX='01742'  EndingX='02004'  StartingY='01026' Length='12'  Confidence='0,0,0,0,0,0,0,0,0,0,0,0'  >PENNSYLVANIA</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='12.48' Height='00.29' >\r\n<LineText>~X[09.10]112 NEW RD, TABERNACLE, NJ 08088 </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00875'  EndingX='00922'  StartingY='01172' Length='3'  Confidence='0,0,0'  >112</Word>\r\n<Word Height='00028' StartingX='00968'  EndingX='01016'  StartingY='01174' Length='3'  Confidence='0,0,0'  >NEW</Word>\r\n<Word Height='00028' StartingX='01065'  EndingX='01120'  StartingY='01174' Length='3'  Confidence='0,0,50'  >RD,</Word>\r\n<Word Height='00028' StartingX='01162'  EndingX='01409'  StartingY='01174' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >TABERNACLE,</Word>\r\n<Word Height='00028' StartingX='01447'  EndingX='01474'  StartingY='01175' Length='2'  Confidence='0,0'  >NJ</Word>\r\n<Word Height='00028' StartingX='01523'  EndingX='01619'  StartingY='01173' Length='5'  Confidence='0,0,0,0,0'  >08088</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='12.98' Height='00.40' >\r\n<LineText>~X[11.56][Property Address] </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='01112'  EndingX='01241'  StartingY='01218' Length='9'  Confidence='40,0,0,0,0,0,0,0,0'  >[Property</Word>\r\n<Word Height='00028' StartingX='01272'  EndingX='01403'  StartingY='01220' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Address]</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='13.83' Height='00.31' >\r\n<LineText>~X[03.39]1. BORROWER'S PROMISE TO PAY </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00326'  EndingX='00345'  StartingY='01302' Length='2'  Confidence='0,0'  >1.</Word>\r\n<Word Height='00028' StartingX='00364'  EndingX='00619'  StartingY='01302' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >BORROWER'S</Word>\r\n<Word Height='00028' StartingX='00650'  EndingX='00811'  StartingY='01302' Length='7'  Confidence='0,0,0,0,0,0,0'  >PROMISE</Word>\r\n<Word Height='00028' StartingX='00849'  EndingX='00875'  StartingY='01302' Length='2'  Confidence='0,0'  >TO</Word>\r\n<Word Height='00028' StartingX='00917'  EndingX='00970'  StartingY='01303' Length='3'  Confidence='0,0,0'  >PAY</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='11.43' Height='00.40' >\r\n<LineText>~X[19.13][State] </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='01839'  EndingX='01931'  StartingY='01070' Length='7'  Confidence='0,0,0,0,0,0,0'  >[State]</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='14.39' Height='00.40' >\r\n<LineText>~X[04.88]In return for a loan that I have received, I promise to pay U.S. ~F$269,300 ~F. 00 (this amount is called </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00469'  EndingX='00481'  StartingY='01355' Length='2'  Confidence='0,0'  >In</Word>\r\n<Word Height='00028' StartingX='00516'  EndingX='00595'  StartingY='01364' Length='6'  Confidence='0,0,0,0,20,20'  >return</Word>\r\n<Word Height='00028' StartingX='00630'  EndingX='00664'  StartingY='01355' Length='3'  Confidence='0,0,0'  >for</Word>\r\n<Word Height='00028' StartingX='00693'  EndingX='00693'  StartingY='01364' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00725'  EndingX='00775'  StartingY='01355' Length='4'  Confidence='0,0,0,0'  >loan</Word>\r\n<Word Height='00028' StartingX='00809'  EndingX='00860'  StartingY='01359' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='00886'  EndingX='00886'  StartingY='01356' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00912'  EndingX='00973'  StartingY='01355' Length='4'  Confidence='0,0,0,0'  >have</Word>\r\n<Word Height='00028' StartingX='01004'  EndingX='01148'  StartingY='01365' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >received,</Word>\r\n<Word Height='00028' StartingX='01170'  EndingX='01170'  StartingY='01357' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='01197'  EndingX='01314'  StartingY='01366' Length='7'  Confidence='0,0,0,0,0,0,0'  >promise</Word>\r\n<Word Height='00028' StartingX='01345'  EndingX='01357'  StartingY='01360' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01390'  EndingX='01428'  StartingY='01366' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00028' StartingX='01462'  EndingX='01528'  StartingY='01357' Length='4'  Confidence='10,10,10,10'  >U.S.</Word>\r\n<Word Height='00028' StartingX='01551'  EndingX='01722'  StartingY='01355' Length='8'  Confidence='0,30,20,0,30,0,0,30'  >$269,300</Word>\r\n<Word Height='00028' StartingX='01753'  EndingX='01753'  StartingY='01382' Length='1'  Confidence='168'  >.</Word>\r\n<Word Height='00028' StartingX='01772'  EndingX='01796'  StartingY='01359' Length='2'  Confidence='20,0'  >00</Word>\r\n<Word Height='00028' StartingX='01833'  EndingX='01892'  StartingY='01357' Length='5'  Confidence='0,0,0,0,0'  >(this</Word>\r\n<Word Height='00028' StartingX='01921'  EndingX='02034'  StartingY='01367' Length='6'  Confidence='0,0,0,0,0,0'  >amount</Word>\r\n<Word Height='00028' StartingX='02060'  EndingX='02072'  StartingY='01357' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='02101'  EndingX='02181'  StartingY='01367' Length='6'  Confidence='0,0,0,0,0,0'  >called</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='14.88' Height='00.38' >\r\n<LineText>~X[03.38]&quot;Principal&quot;), plus interest, to the order of the Len~Y[00.03]d~Yer. Th~Y[00.03]e ~YL~Y[00.03]en~Yd~Y[00.03]e~Yr ~Y[00.03]i~Ys ~F~Y[00.03]N~YE~Y[00.03]W~YR~Y[00.03]E~YZ ~Y[00.03]L~YL~Y[00.04]C~Y. ~F~Y[00.03]I ~Yw~Y[00.03]i~Yl~Y[00.03]l ~Ym~Y[00.04]a~Yk~Y[00.04]e ~Ya~Y[00.03]l~Yl ~Y[00.03]p~Ya~Y[00.04]y~Ym~Y[00.04]e~Yn~Y[00.04]t~Ys ~Y[00.04]u~Yn~Y[00.04]d~Ye~Y[00.04]r ~Y</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00325'  EndingX='00518'  StartingY='01403' Length='23'  Confidence='0,0,0,0,0,0,0,0,0,20,30,20,20'  >&quot;Principal&quot;),</Word>\r\n<Word Height='00028' StartingX='00538'  EndingX='00590'  StartingY='01412' Length='4'  Confidence='0,0,0,0'  >plus</Word>\r\n<Word Height='00028' StartingX='00617'  EndingX='00738'  StartingY='01403' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >interest,</Word>\r\n<Word Height='00028' StartingX='00757'  EndingX='00768'  StartingY='01407' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00799'  EndingX='00831'  StartingY='01407' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00860'  EndingX='00932'  StartingY='01413' Length='5'  Confidence='0,0,0,0,0'  >order</Word>\r\n<Word Height='00028' StartingX='00956'  EndingX='00976'  StartingY='01413' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01000'  EndingX='01032'  StartingY='01407' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01061'  EndingX='01177'  StartingY='01404' Length='7'  Confidence='0,0,0,0,0,0,0'  >Lender.</Word>\r\n<Word Height='00028' StartingX='01195'  EndingX='01241'  StartingY='01405' Length='3'  Confidence='0,0,0'  >The</Word>\r\n<Word Height='00028' StartingX='01270'  EndingX='01370'  StartingY='01405' Length='6'  Confidence='0,0,0,0,0,0'  >Lender</Word>\r\n<Word Height='00028' StartingX='01395'  EndingX='01406'  StartingY='01404' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='01430'  EndingX='01554'  StartingY='01408' Length='6'  Confidence='0,0,0,0,0,0'  >NEWREZ</Word>\r\n<Word Height='00028' StartingX='01601'  EndingX='01676'  StartingY='01409' Length='4'  Confidence='0,0,0,0'  >LLC.</Word>\r\n<Word Height='00028' StartingX='01695'  EndingX='01695'  StartingY='01406' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='01718'  EndingX='01772'  StartingY='01415' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='01792'  EndingX='01863'  StartingY='01415' Length='4'  Confidence='0,0,0,0'  >make</Word>\r\n<Word Height='00028' StartingX='01891'  EndingX='01921'  StartingY='01415' Length='3'  Confidence='0,0,0'  >all</Word>\r\n<Word Height='00028' StartingX='01942'  EndingX='02083'  StartingY='01415' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >payments</Word>\r\n<Word Height='00028' StartingX='02108'  EndingX='02187'  StartingY='01415' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='15.38' Height='00.31' >\r\n<LineText>~X[03.37]this Note in the form, of cash, check or money order. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00324'  EndingX='00367'  StartingY='01454' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='00391'  EndingX='00452'  StartingY='01450' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00480'  EndingX='00491'  StartingY='01450' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='00521'  EndingX='00553'  StartingY='01454' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00581'  EndingX='00656'  StartingY='01450' Length='5'  Confidence='0,0,30,50,60'  >form,</Word>\r\n<Word Height='00028' StartingX='00668'  EndingX='00689'  StartingY='01460' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='00712'  EndingX='00784'  StartingY='01460' Length='5'  Confidence='0,0,0,0,0'  >cash,</Word>\r\n<Word Height='00028' StartingX='00803'  EndingX='00877'  StartingY='01460' Length='5'  Confidence='0,0,0,0,0'  >check</Word>\r\n<Word Height='00028' StartingX='00907'  EndingX='00927'  StartingY='01460' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='00951'  EndingX='01039'  StartingY='01461' Length='5'  Confidence='0,0,0,0,0'  >money</Word>\r\n<Word Height='00028' StartingX='01070'  EndingX='01156'  StartingY='01461' Length='6'  Confidence='0,0,0,0,0,0'  >order.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='15.94' Height='00.31' >\r\n<LineText>~X[04.88]I understand that the Lender may transfer this Note. The Lender or anyone who takes this Note by transfer </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00469'  EndingX='00469'  StartingY='01504' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00492'  EndingX='00650'  StartingY='01513' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >understand</Word>\r\n<Word Height='00028' StartingX='00682'  EndingX='00731'  StartingY='01508' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='00753'  EndingX='00785'  StartingY='01509' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00813'  EndingX='00915'  StartingY='01505' Length='6'  Confidence='0,0,0,0,0,0'  >Lender</Word>\r\n<Word Height='00028' StartingX='00939'  EndingX='00988'  StartingY='01514' Length='3'  Confidence='0,0,0'  >may</Word>\r\n<Word Height='00028' StartingX='01019'  EndingX='01130'  StartingY='01509' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >transfer</Word>\r\n<Word Height='00028' StartingX='01154'  EndingX='01197'  StartingY='01509' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01222'  EndingX='01304'  StartingY='01506' Length='5'  Confidence='0,0,0,0,0'  >Note.</Word>\r\n<Word Height='00028' StartingX='01322'  EndingX='01368'  StartingY='01506' Length='3'  Confidence='0,0,0'  >The</Word>\r\n<Word Height='00028' StartingX='01396'  EndingX='01496'  StartingY='01506' Length='6'  Confidence='0,0,0,0,0,0'  >Lender</Word>\r\n<Word Height='00028' StartingX='01520'  EndingX='01541'  StartingY='01515' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='01565'  EndingX='01664'  StartingY='01515' Length='6'  Confidence='0,0,0,0,0,0'  >anyone</Word>\r\n<Word Height='00028' StartingX='01692'  EndingX='01742'  StartingY='01516' Length='3'  Confidence='0,0,0'  >who</Word>\r\n<Word Height='00028' StartingX='01772'  EndingX='01840'  StartingY='01511' Length='5'  Confidence='0,0,0,0,0'  >takes</Word>\r\n<Word Height='00028' StartingX='01866'  EndingX='01910'  StartingY='01511' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01935'  EndingX='01997'  StartingY='01507' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='02025'  EndingX='02045'  StartingY='01507' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='02077'  EndingX='02187'  StartingY='01511' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >transfer</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='16.43' Height='00.30' >\r\n<LineText>~X[03.37]an~Y[00.01]d w~Yho ~Y[00.00]is en~Yt~Y[00.00]i~Yt~Y[00.00]le~Yd ~Y[00.01]to rece~Yive paymen~Y[00.01]ts un~Yder ~Y[00.02]t~Yh~Y[00.01]is ~YNo~Y[00.02]te ~Yis ca~Y[00.02]l~Yle~Y[00.02]d t~Yhe ~Y[00.02]&quot;~YNote ~Y[00.02]Ho~Yl~Y[00.02]der.~Y&quot; </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00324'  EndingX='00362'  StartingY='01561' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='00392'  EndingX='00441'  StartingY='01561' Length='3'  Confidence='0,0,0'  >who</Word>\r\n<Word Height='00028' StartingX='00472'  EndingX='00483'  StartingY='01552' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='00508'  EndingX='00608'  StartingY='01561' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >entitled</Word>\r\n<Word Height='00028' StartingX='00638'  EndingX='00650'  StartingY='01556' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00680'  EndingX='00778'  StartingY='01561' Length='7'  Confidence='0,0,0,0,0,0,0'  >receive</Word>\r\n<Word Height='00028' StartingX='00805'  EndingX='00945'  StartingY='01562' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >payments</Word>\r\n<Word Height='00028' StartingX='00969'  EndingX='01047'  StartingY='01562' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n<Word Height='00028' StartingX='01071'  EndingX='01114'  StartingY='01557' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01138'  EndingX='01199'  StartingY='01554' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='01228'  EndingX='01238'  StartingY='01553' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='01263'  EndingX='01339'  StartingY='01562' Length='6'  Confidence='0,0,0,0,0,0'  >called</Word>\r\n<Word Height='00028' StartingX='01369'  EndingX='01401'  StartingY='01558' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01428'  EndingX='01504'  StartingY='01554' Length='10'  Confidence='0,0,0,0,0'  >&quot;Note</Word>\r\n<Word Height='00028' StartingX='01531'  EndingX='01654'  StartingY='01554' Length='13'  Confidence='0,0,0,0,0,0,0,0'  >Holder.&quot;</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='16.98' Height='00.30' >\r\n<LineText>~X[03.36]2. ~FINTEREST ~F</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00323'  EndingX='00344'  StartingY='01605' Length='2'  Confidence='0,0'  >2.</Word>\r\n<Word Height='00028' StartingX='00363'  EndingX='00540'  StartingY='01605' Length='8'  Confidence='60,10,10,10,10,10,10,10'  >INTEREST</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='17.54' Height='00.31' >\r\n<LineText>~X[04.88]Interest will be charged on unpaid principal until the full amount of Principal has been paid. I will pay </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00469'  EndingX='00582'  StartingY='01659' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Interest</Word>\r\n<Word Height='00028' StartingX='00605'  EndingX='00659'  StartingY='01668' Length='4'  Confidence='30,30,60,30'  >will</Word>\r\n<Word Height='00028' StartingX='00682'  EndingX='00705'  StartingY='01659' Length='2'  Confidence='20,0'  >be</Word>\r\n<Word Height='00028' StartingX='00735'  EndingX='00846'  StartingY='01669' Length='7'  Confidence='0,0,0,0,0,0,0'  >charged</Word>\r\n<Word Height='00028' StartingX='00879'  EndingX='00899'  StartingY='01669' Length='2'  Confidence='0,0'  >on</Word>\r\n<Word Height='00028' StartingX='00933'  EndingX='01024'  StartingY='01669' Length='6'  Confidence='0,0,0,0,0,0'  >unpaid</Word>\r\n<Word Height='00028' StartingX='01057'  EndingX='01194'  StartingY='01669' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >principal</Word>\r\n<Word Height='00028' StartingX='01218'  EndingX='01283'  StartingY='01670' Length='5'  Confidence='0,0,0,0,0'  >until</Word>\r\n<Word Height='00028' StartingX='01307'  EndingX='01339'  StartingY='01665' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01370'  EndingX='01417'  StartingY='01660' Length='4'  Confidence='30,30,0,0'  >full</Word>\r\n<Word Height='00028' StartingX='01439'  EndingX='01551'  StartingY='01670' Length='6'  Confidence='0,0,0,0,0,0'  >amount</Word>\r\n<Word Height='00028' StartingX='01575'  EndingX='01597'  StartingY='01670' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01621'  EndingX='01761'  StartingY='01661' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >Principal</Word>\r\n<Word Height='00028' StartingX='01784'  EndingX='01824'  StartingY='01661' Length='3'  Confidence='0,0,0'  >has</Word>\r\n<Word Height='00028' StartingX='01851'  EndingX='01910'  StartingY='01661' Length='4'  Confidence='0,0,0,0'  >been</Word>\r\n<Word Height='00028' StartingX='01943'  EndingX='02016'  StartingY='01671' Length='5'  Confidence='0,0,0,0,0'  >paid.</Word>\r\n<Word Height='00028' StartingX='02037'  EndingX='02037'  StartingY='01662' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='02064'  EndingX='02118'  StartingY='01671' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='02141'  EndingX='02180'  StartingY='01671' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='18.03' Height='00.31' >\r\n<LineText>~X[03.38]interest at a yearly ra-e of ~F2.875~F%. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00325'  EndingX='00431'  StartingY='01706' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >interest</Word>\r\n<Word Height='00028' StartingX='00452'  EndingX='00470'  StartingY='01715' Length='2'  Confidence='0,0'  >at</Word>\r\n<Word Height='00028' StartingX='00491'  EndingX='00491'  StartingY='01715' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00518'  EndingX='00598'  StartingY='01715' Length='6'  Confidence='0,0,0,0,0,0'  >yearly</Word>\r\n<Word Height='00028' StartingX='00629'  EndingX='00672'  StartingY='01715' Length='4'  Confidence='0,20,70,0'  >ra-e</Word>\r\n<Word Height='00028' StartingX='00700'  EndingX='00720'  StartingY='01715' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='00744'  EndingX='00899'  StartingY='01708' Length='7'  Confidence='20,20,20,30,20,30,20'  >2.875%.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='18.60' Height='00.36' >\r\n<LineText>~X[04.87]The interest rate required by this Section 2 is the rate I will pay both before and after any default described </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00468'  EndingX='00513'  StartingY='01760' Length='3'  Confidence='0,0,0'  >The</Word>\r\n<Word Height='00028' StartingX='00542'  EndingX='00650'  StartingY='01759' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >interest</Word>\r\n<Word Height='00028' StartingX='00671'  EndingX='00715'  StartingY='01769' Length='4'  Confidence='0,0,0,0'  >rate</Word>\r\n<Word Height='00028' StartingX='00742'  EndingX='00857'  StartingY='01769' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >required</Word>\r\n<Word Height='00028' StartingX='00888'  EndingX='00908'  StartingY='01759' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='00939'  EndingX='00982'  StartingY='01764' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01009'  EndingX='01109'  StartingY='01760' Length='7'  Confidence='0,0,0,0,0,0,0'  >Section</Word>\r\n<Word Height='00028' StartingX='01140'  EndingX='01140'  StartingY='01761' Length='1'  Confidence='0'  >2</Word>\r\n<Word Height='00028' StartingX='01171'  EndingX='01182'  StartingY='01760' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='01208'  EndingX='01239'  StartingY='01765' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01267'  EndingX='01311'  StartingY='01770' Length='4'  Confidence='0,0,0,0'  >rate</Word>\r\n<Word Height='00028' StartingX='01339'  EndingX='01339'  StartingY='01761' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='01363'  EndingX='01415'  StartingY='01770' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='01434'  EndingX='01473'  StartingY='01771' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00028' StartingX='01503'  EndingX='01555'  StartingY='01761' Length='4'  Confidence='0,0,30,30'  >both</Word>\r\n<Word Height='00028' StartingX='01585'  EndingX='01672'  StartingY='01761' Length='6'  Confidence='0,0,0,0,0,0'  >before</Word>\r\n<Word Height='00028' StartingX='01700'  EndingX='01738'  StartingY='01771' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='01769'  EndingX='01829'  StartingY='01771' Length='5'  Confidence='0,0,0,0,0'  >after</Word>\r\n<Word Height='00028' StartingX='01853'  EndingX='01891'  StartingY='01771' Length='3'  Confidence='0,0,0'  >any</Word>\r\n<Word Height='00028' StartingX='01922'  EndingX='02023'  StartingY='01761' Length='7'  Confidence='0,0,0,0,0,0,0'  >default</Word>\r\n<Word Height='00028' StartingX='02045'  EndingX='02180'  StartingY='01761' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >described</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='19.08' Height='00.40' >\r\n<LineText>~X[03.37]in Section 6(B) of this Note. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00324'  EndingX='00334'  StartingY='01806' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='00366'  EndingX='00465'  StartingY='01806' Length='7'  Confidence='0,0,0,0,0,0,0'  >Section</Word>\r\n<Word Height='00028' StartingX='00495'  EndingX='00555'  StartingY='01806' Length='4'  Confidence='0,20,0,0'  >6(B)</Word>\r\n<Word Height='00028' StartingX='00579'  EndingX='00599'  StartingY='01816' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='00622'  EndingX='00664'  StartingY='01810' Length='4'  Confidence='0,0,0,30'  >this</Word>\r\n<Word Height='00028' StartingX='00690'  EndingX='00771'  StartingY='01807' Length='5'  Confidence='0,0,0,0,0'  >Note.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='19.64' Height='00.30' >\r\n<LineText>~X[03.36]3. PAYMENTS </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00323'  EndingX='00344'  StartingY='01860' Length='2'  Confidence='0,0'  >3.</Word>\r\n<Word Height='00028' StartingX='00364'  EndingX='00567'  StartingY='01860' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >PAYMENTS</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='20.20' Height='00.37' >\r\n<LineText>~X[04.88](A) Time and Place of Payments </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00469'  EndingX='00510'  StartingY='01914' Length='3'  Confidence='0,0,0'  >(A)</Word>\r\n<Word Height='00028' StartingX='00534'  EndingX='00605'  StartingY='01914' Length='4'  Confidence='0,0,0,0'  >Time</Word>\r\n<Word Height='00028' StartingX='00633'  EndingX='00675'  StartingY='01922' Length='3'  Confidence='0,50,0'  >and</Word>\r\n<Word Height='00028' StartingX='00708'  EndingX='00782'  StartingY='01914' Length='5'  Confidence='0,0,0,0,0'  >Place</Word>\r\n<Word Height='00028' StartingX='00810'  EndingX='00830'  StartingY='01923' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='00853'  EndingX='01005'  StartingY='01914' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Payments</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='20.75' Height='00.31' >\r\n<LineText>~X[04.87]I will pay principal and interest by making a payment every month. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00468'  EndingX='00468'  StartingY='01967' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00491'  EndingX='00544'  StartingY='01976' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='00563'  EndingX='00601'  StartingY='01976' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00028' StartingX='00631'  EndingX='00765'  StartingY='01976' Length='9'  Confidence='10,70,60,10,10,10,10,10,10'  >principal</Word>\r\n<Word Height='00028' StartingX='00785'  EndingX='00823'  StartingY='01976' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='00853'  EndingX='00960'  StartingY='01967' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >interest</Word>\r\n<Word Height='00028' StartingX='00981'  EndingX='01001'  StartingY='01967' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='01030'  EndingX='01132'  StartingY='01977' Length='6'  Confidence='0,0,0,0,0,0'  >making</Word>\r\n<Word Height='00028' StartingX='01162'  EndingX='01162'  StartingY='01977' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='01189'  EndingX='01316'  StartingY='01977' Length='7'  Confidence='0,0,0,0,0,0,0'  >payment</Word>\r\n<Word Height='00028' StartingX='01338'  EndingX='01406'  StartingY='01978' Length='5'  Confidence='0,0,0,0,0'  >every</Word>\r\n<Word Height='00028' StartingX='01435'  EndingX='01539'  StartingY='01978' Length='6'  Confidence='0,0,0,0,0,0'  >month.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='21.31' Height='00.31' >\r\n<LineText>~X[04.87]I will make my monthly payment on the 1ST day of each month beginning on ~FFEBRUARY 1, 2022. ~FI </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00468'  EndingX='00468'  StartingY='02021' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00492'  EndingX='00546'  StartingY='02029' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='00567'  EndingX='00639'  StartingY='02029' Length='4'  Confidence='0,0,0,0'  >make</Word>\r\n<Word Height='00028' StartingX='00667'  EndingX='00699'  StartingY='02030' Length='2'  Confidence='0,0'  >my</Word>\r\n<Word Height='00028' StartingX='00731'  EndingX='00847'  StartingY='02030' Length='7'  Confidence='0,0,0,0,0,0,0'  >monthly</Word>\r\n<Word Height='00028' StartingX='00879'  EndingX='01008'  StartingY='02030' Length='7'  Confidence='0,0,0,0,0,0,0'  >payment</Word>\r\n<Word Height='00028' StartingX='01031'  EndingX='01051'  StartingY='02030' Length='2'  Confidence='0,0'  >on</Word>\r\n<Word Height='00028' StartingX='01083'  EndingX='01115'  StartingY='02025' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01147'  EndingX='01194'  StartingY='02023' Length='3'  Confidence='30,0,0'  >1ST</Word>\r\n<Word Height='00028' StartingX='01229'  EndingX='01266'  StartingY='02021' Length='3'  Confidence='0,0,0'  >day</Word>\r\n<Word Height='00028' StartingX='01299'  EndingX='01320'  StartingY='02031' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01345'  EndingX='01399'  StartingY='02031' Length='4'  Confidence='0,0,0,0'  >each</Word>\r\n<Word Height='00028' StartingX='01429'  EndingX='01514'  StartingY='02031' Length='5'  Confidence='0,0,0,0,0'  >month</Word>\r\n<Word Height='00028' StartingX='01545'  EndingX='01688'  StartingY='02021' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >beginning</Word>\r\n<Word Height='00028' StartingX='01720'  EndingX='01740'  StartingY='02031' Length='2'  Confidence='0,0'  >on</Word>\r\n<Word Height='00028' StartingX='01773'  EndingX='01943'  StartingY='02026' Length='8'  Confidence='30,0,0,0,0,0,0,0'  >FEBRUARY</Word>\r\n<Word Height='00028' StartingX='01995'  EndingX='02024'  StartingY='02024' Length='2'  Confidence='0,0'  >1,</Word>\r\n<Word Height='00028' StartingX='02068'  EndingX='02167'  StartingY='02024' Length='5'  Confidence='0,0,0,0,20'  >2022.</Word>\r\n<Word Height='00028' StartingX='02187'  EndingX='02187'  StartingY='02023' Length='1'  Confidence='40'  >I</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='21.80' Height='00.31' >\r\n<LineText>~X[03.35]w~Y[-00.01]i~Yl~Y[-00.01]l ma~Yke ~Y[00.00]t~Yhese paymen~Y[00.00]ts every mont~Yh unt~Y[00.00]i~Yl ~Y[00.00]I ~Yhave pa~Y[00.01]i~Yd a~Y[00.01]l~Yl o~Y[00.01]f t~Yhe pr~Y[00.01]inc~Yipa~Y[00.01]l an~Yd ~Y[00.01]interest an~Yd any ot~Y[00.01]her c~Yharges </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00378'  StartingY='02076' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='00402'  EndingX='00473'  StartingY='02076' Length='4'  Confidence='0,0,0,0'  >make</Word>\r\n<Word Height='00028' StartingX='00503'  EndingX='00572'  StartingY='02071' Length='5'  Confidence='0,0,0,0,0'  >these</Word>\r\n<Word Height='00028' StartingX='00603'  EndingX='00747'  StartingY='02077' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >payments</Word>\r\n<Word Height='00028' StartingX='00776'  EndingX='00847'  StartingY='02076' Length='5'  Confidence='0,0,0,0,0'  >every</Word>\r\n<Word Height='00028' StartingX='00881'  EndingX='00966'  StartingY='02077' Length='5'  Confidence='0,0,0,0,0'  >month</Word>\r\n<Word Height='00028' StartingX='00999'  EndingX='01066'  StartingY='02077' Length='5'  Confidence='0,0,0,0,0'  >until</Word>\r\n<Word Height='00028' StartingX='01090'  EndingX='01090'  StartingY='02069' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='01117'  EndingX='01177'  StartingY='02068' Length='4'  Confidence='0,0,0,0'  >have</Word>\r\n<Word Height='00028' StartingX='01207'  EndingX='01258'  StartingY='02077' Length='4'  Confidence='0,0,0,0'  >paid</Word>\r\n<Word Height='00028' StartingX='01293'  EndingX='01324'  StartingY='02078' Length='3'  Confidence='0,0,0'  >all</Word>\r\n<Word Height='00028' StartingX='01347'  EndingX='01368'  StartingY='02077' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01395'  EndingX='01426'  StartingY='02072' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01456'  EndingX='01595'  StartingY='02078' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >principal</Word>\r\n<Word Height='00028' StartingX='01619'  EndingX='01658'  StartingY='02078' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='01693'  EndingX='01803'  StartingY='02069' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >interest</Word>\r\n<Word Height='00028' StartingX='01828'  EndingX='01868'  StartingY='02078' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='01901'  EndingX='01940'  StartingY='02078' Length='3'  Confidence='0,0,0'  >any</Word>\r\n<Word Height='00028' StartingX='01974'  EndingX='02046'  StartingY='02078' Length='5'  Confidence='0,0,0,0,0'  >other</Word>\r\n<Word Height='00028' StartingX='02073'  EndingX='02185'  StartingY='02078' Length='7'  Confidence='0,0,0,0,0,0,0'  >charges</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='22.28' Height='00.31' >\r\n<LineText>~X[03.36]described below that I may owe under this Note. Each monthly payment will be applied as of its scheduled due date </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00323'  EndingX='00458'  StartingY='02113' Length='9'  Confidence='0,0,0,0,30,0,0,0,0'  >described</Word>\r\n<Word Height='00028' StartingX='00488'  EndingX='00557'  StartingY='02114' Length='5'  Confidence='0,0,0,0,0'  >below</Word>\r\n<Word Height='00028' StartingX='00597'  EndingX='00647'  StartingY='02118' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='00668'  EndingX='00668'  StartingY='02114' Length='1'  Confidence='40'  >I</Word>\r\n<Word Height='00028' StartingX='00693'  EndingX='00741'  StartingY='02123' Length='3'  Confidence='0,0,0'  >may</Word>\r\n<Word Height='00028' StartingX='00773'  EndingX='00822'  StartingY='02123' Length='3'  Confidence='0,0,0'  >owe</Word>\r\n<Word Height='00028' StartingX='00849'  EndingX='00929'  StartingY='02123' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n<Word Height='00028' StartingX='00953'  EndingX='00996'  StartingY='02119' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01020'  EndingX='01102'  StartingY='02115' Length='5'  Confidence='0,0,0,0,0'  >Note.</Word>\r\n<Word Height='00028' StartingX='01121'  EndingX='01181'  StartingY='02115' Length='4'  Confidence='0,0,0,0'  >Each</Word>\r\n<Word Height='00028' StartingX='01212'  EndingX='01325'  StartingY='02124' Length='7'  Confidence='0,0,0,0,0,0,0'  >monthly</Word>\r\n<Word Height='00028' StartingX='01356'  EndingX='01483'  StartingY='02124' Length='7'  Confidence='0,0,0,0,0,0,0'  >payment</Word>\r\n<Word Height='00028' StartingX='01504'  EndingX='01557'  StartingY='02125' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='01577'  EndingX='01598'  StartingY='02115' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='01626'  EndingX='01725'  StartingY='02125' Length='7'  Confidence='0,0,0,0,0,0,0'  >applied</Word>\r\n<Word Height='00028' StartingX='01755'  EndingX='01773'  StartingY='02125' Length='2'  Confidence='0,0'  >as</Word>\r\n<Word Height='00028' StartingX='01799'  EndingX='01820'  StartingY='02125' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01844'  EndingX='01866'  StartingY='02116' Length='3'  Confidence='0,0,0'  >its</Word>\r\n<Word Height='00028' StartingX='01892'  EndingX='02033'  StartingY='02125' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >scheduled</Word>\r\n<Word Height='00028' StartingX='02064'  EndingX='02105'  StartingY='02116' Length='3'  Confidence='0,0,0'  >due</Word>\r\n<Word Height='00028' StartingX='02133'  EndingX='02182'  StartingY='02116' Length='4'  Confidence='0,0,0,0'  >date</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='22.78' Height='00.32' >\r\n<LineText>~X[03.36]an~Y[00.01]d w~Yi~Y[00.00]l~Yl ~Y[00.01]be app~Yl~Y[00.00]ie~Yd to ~Y[00.01]in~Yterest ~Y[00.02]be~Yfore ~Y[00.01]Pr~Yinc~Y[00.01]ipa~Yl. ~Y[00.01]I~Yf, on ~FJANUARY ~F~Y[00.02]1, ~Y2~Y[00.03]0~Y5~Y[00.02]2, ~YI st~Y[00.02]i~Yl~Y[00.02]l owe amounts un~Yder t~Y[00.02]h~Yis ~Y[00.03]Note, ~YI </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00323'  EndingX='00362'  StartingY='02170' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='00392'  EndingX='00446'  StartingY='02170' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='00467'  EndingX='00487'  StartingY='02161' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='00516'  EndingX='00616'  StartingY='02170' Length='7'  Confidence='0,0,0,0,0,0,0'  >applied</Word>\r\n<Word Height='00028' StartingX='00647'  EndingX='00659'  StartingY='02166' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00691'  EndingX='00799'  StartingY='02161' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >interest</Word>\r\n<Word Height='00028' StartingX='00821'  EndingX='00907'  StartingY='02161' Length='6'  Confidence='0,0,0,0,0,0'  >before</Word>\r\n<Word Height='00028' StartingX='00936'  EndingX='01085'  StartingY='02162' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >Principal.</Word>\r\n<Word Height='00028' StartingX='01105'  EndingX='01132'  StartingY='02163' Length='3'  Confidence='0,0,0'  >If,</Word>\r\n<Word Height='00028' StartingX='01153'  EndingX='01173'  StartingY='02171' Length='2'  Confidence='0,0'  >on</Word>\r\n<Word Height='00028' StartingX='01204'  EndingX='01349'  StartingY='02166' Length='7'  Confidence='0,0,0,0,0,0,0'  >JANUARY</Word>\r\n<Word Height='00028' StartingX='01401'  EndingX='01428'  StartingY='02164' Length='2'  Confidence='0,0'  >1,</Word>\r\n<Word Height='00028' StartingX='01472'  EndingX='01569'  StartingY='02164' Length='5'  Confidence='0,0,0,0,0'  >2052,</Word>\r\n<Word Height='00028' StartingX='01589'  EndingX='01589'  StartingY='02163' Length='1'  Confidence='40'  >I</Word>\r\n<Word Height='00028' StartingX='01614'  EndingX='01665'  StartingY='02172' Length='5'  Confidence='0,0,0,0,0'  >still</Word>\r\n<Word Height='00028' StartingX='01686'  EndingX='01735'  StartingY='02172' Length='3'  Confidence='0,0,0'  >owe</Word>\r\n<Word Height='00028' StartingX='01764'  EndingX='01887'  StartingY='02172' Length='7'  Confidence='0,0,0,0,0,0,0'  >amounts</Word>\r\n<Word Height='00028' StartingX='01912'  EndingX='01991'  StartingY='02173' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n<Word Height='00028' StartingX='02016'  EndingX='02060'  StartingY='02167' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='02085'  EndingX='02166'  StartingY='02164' Length='5'  Confidence='0,0,0,0,0'  >Note,</Word>\r\n<Word Height='00028' StartingX='02187'  EndingX='02187'  StartingY='02164' Length='1'  Confidence='0'  >I</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='23.26' Height='00.30' >\r\n<LineText>~X[03.35]w~Y[00.00]i~Yl~Y[00.00]l pay ~Yt~Y[00.00]hose amoun~Yts ~Y[00.00]in ~Yfu~Y[00.01]l~Yl on ~Y[00.01]t~Yha~Y[00.01]t ~Yda~Y[00.01]te, w~Yh~Y[00.01]ic~Yh ~Y[00.01]is ca~Yl~Y[00.01]le~Yd ~Y[00.02]t~Yhe ~Y[00.02]&quot;~YMa~Y[00.02]tur~Yity ~Y[00.02]Date.~Y&quot; </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00375'  StartingY='02218' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='00395'  EndingX='00433'  StartingY='02218' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00028' StartingX='00464'  EndingX='00530'  StartingY='02213' Length='5'  Confidence='0,0,0,0,0'  >those</Word>\r\n<Word Height='00028' StartingX='00559'  EndingX='00680'  StartingY='02218' Length='7'  Confidence='0,0,0,0,0,0,0'  >amounts</Word>\r\n<Word Height='00028' StartingX='00706'  EndingX='00716'  StartingY='02209' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='00746'  EndingX='00792'  StartingY='02209' Length='4'  Confidence='30,30,0,0'  >full</Word>\r\n<Word Height='00028' StartingX='00812'  EndingX='00832'  StartingY='02218' Length='2'  Confidence='0,0'  >on</Word>\r\n<Word Height='00028' StartingX='00862'  EndingX='00911'  StartingY='02213' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='00932'  EndingX='01000'  StartingY='02209' Length='5'  Confidence='0,0,0,0,0'  >date,</Word>\r\n<Word Height='00028' StartingX='01019'  EndingX='01097'  StartingY='02219' Length='5'  Confidence='0,0,0,0,0'  >which</Word>\r\n<Word Height='00028' StartingX='01128'  EndingX='01139'  StartingY='02210' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='01164'  EndingX='01239'  StartingY='02219' Length='6'  Confidence='0,0,0,0,0,0'  >called</Word>\r\n<Word Height='00028' StartingX='01270'  EndingX='01301'  StartingY='02214' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01330'  EndingX='01463'  StartingY='02211' Length='14'  Confidence='0,0,0,0,0,0,0,0,0'  >&quot;Maturity</Word>\r\n<Word Height='00028' StartingX='01494'  EndingX='01581'  StartingY='02211' Length='11'  Confidence='0,0,0,0,0,0'  >Date.&quot;</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='23.81' Height='00.30' >\r\n<LineText>~X[04.87]I will make my monthly payments at ~FPO BOX 740039, CINCINNATI, OH 45274~F-0039 or at a </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00468'  EndingX='00468'  StartingY='02261' Length='1'  Confidence='50'  >I</Word>\r\n<Word Height='00028' StartingX='00492'  EndingX='00547'  StartingY='02270' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='00570'  EndingX='00640'  StartingY='02270' Length='4'  Confidence='0,0,0,0'  >make</Word>\r\n<Word Height='00028' StartingX='00671'  EndingX='00702'  StartingY='02270' Length='2'  Confidence='0,0'  >my</Word>\r\n<Word Height='00028' StartingX='00735'  EndingX='00851'  StartingY='02270' Length='7'  Confidence='0,0,0,0,0,0,0'  >monthly</Word>\r\n<Word Height='00028' StartingX='00884'  EndingX='01026'  StartingY='02271' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >payments</Word>\r\n<Word Height='00028' StartingX='01054'  EndingX='01072'  StartingY='02271' Length='2'  Confidence='0,0'  >at</Word>\r\n<Word Height='00028' StartingX='01097'  EndingX='01120'  StartingY='02266' Length='2'  Confidence='0,30'  >PO</Word>\r\n<Word Height='00028' StartingX='01170'  EndingX='01218'  StartingY='02266' Length='3'  Confidence='0,0,0'  >BOX</Word>\r\n<Word Height='00028' StartingX='01272'  EndingX='01424'  StartingY='02264' Length='7'  Confidence='0,0,0,0,0,0,0'  >740039,</Word>\r\n<Word Height='00028' StartingX='01466'  EndingX='01719'  StartingY='02266' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >CINCINNATI,</Word>\r\n<Word Height='00028' StartingX='01762'  EndingX='01786'  StartingY='02267' Length='2'  Confidence='0,0'  >OH</Word>\r\n<Word Height='00028' StartingX='01837'  EndingX='02061'  StartingY='02265' Length='10'  Confidence='0,0,0,0,20,30,0,0,0,20'  >45274-0039</Word>\r\n<Word Height='00028' StartingX='02094'  EndingX='02115'  StartingY='02272' Length='2'  Confidence='0,20'  >or</Word>\r\n<Word Height='00028' StartingX='02140'  EndingX='02159'  StartingY='02272' Length='2'  Confidence='0,20'  >at</Word>\r\n<Word Height='00028' StartingX='02182'  EndingX='02182'  StartingY='02272' Length='1'  Confidence='0'  >a</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='24.30' Height='00.31' >\r\n<LineText>~X[03.36]different place if required by the Note Holder. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00323'  EndingX='00450'  StartingY='02308' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >different</Word>\r\n<Word Height='00028' StartingX='00470'  EndingX='00538'  StartingY='02318' Length='5'  Confidence='0,0,0,0,0'  >place</Word>\r\n<Word Height='00028' StartingX='00567'  EndingX='00577'  StartingY='02308' Length='2'  Confidence='0,0'  >if</Word>\r\n<Word Height='00028' StartingX='00601'  EndingX='00714'  StartingY='02318' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >required</Word>\r\n<Word Height='00028' StartingX='00744'  EndingX='00764'  StartingY='02308' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='00795'  EndingX='00826'  StartingY='02313' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00853'  EndingX='00915'  StartingY='02309' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00942'  EndingX='01055'  StartingY='02309' Length='7'  Confidence='0,0,0,0,0,0,0'  >Holder.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='24.86' Height='00.37' >\r\n<LineText>~X[04.87](B) Amount of Monthly Payments </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00468'  EndingX='00507'  StartingY='02362' Length='3'  Confidence='30,30,0'  >(B)</Word>\r\n<Word Height='00028' StartingX='00531'  EndingX='00658'  StartingY='02362' Length='6'  Confidence='0,0,0,0,0,0'  >Amount</Word>\r\n<Word Height='00028' StartingX='00682'  EndingX='00702'  StartingY='02371' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='00725'  EndingX='00852'  StartingY='02362' Length='7'  Confidence='0,0,0,0,0,0,0'  >Monthly</Word>\r\n<Word Height='00028' StartingX='00883'  EndingX='01034'  StartingY='02362' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Payments</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='25.41' Height='00.34' >\r\n<LineText>~X[04.86]My monthly payment will be in the amount of U.S. $1,117.31. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00467'  EndingX='00502'  StartingY='02415' Length='2'  Confidence='0,0'  >My</Word>\r\n<Word Height='00028' StartingX='00533'  EndingX='00646'  StartingY='02424' Length='7'  Confidence='0,0,0,0,0,0,0'  >monthly</Word>\r\n<Word Height='00028' StartingX='00676'  EndingX='00804'  StartingY='02424' Length='7'  Confidence='0,0,0,0,0,0,0'  >payment</Word>\r\n<Word Height='00028' StartingX='00825'  EndingX='00878'  StartingY='02425' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='00898'  EndingX='00919'  StartingY='02415' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='00947'  EndingX='00956'  StartingY='02415' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='00987'  EndingX='01019'  StartingY='02420' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01047'  EndingX='01155'  StartingY='02425' Length='6'  Confidence='0,0,0,0,0,0'  >amount</Word>\r\n<Word Height='00028' StartingX='01177'  EndingX='01197'  StartingY='02425' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01220'  EndingX='01283'  StartingY='02417' Length='4'  Confidence='10,10,10,10'  >U.S.</Word>\r\n<Word Height='00028' StartingX='01303'  EndingX='01514'  StartingY='02415' Length='10'  Confidence='0,20,30,20,0,30,30,20,0,0'  >$1,117.31.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='25.97' Height='00.31' >\r\n<LineText>~X[03.35]4. BORROWER'S RIGHT TO PREPAY </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00343'  StartingY='02468' Length='2'  Confidence='0,0'  >4.</Word>\r\n<Word Height='00028' StartingX='00362'  EndingX='00618'  StartingY='02469' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >BORROWER'S</Word>\r\n<Word Height='00028' StartingX='00650'  EndingX='00757'  StartingY='02469' Length='5'  Confidence='0,0,0,0,0'  >RIGHT</Word>\r\n<Word Height='00028' StartingX='00794'  EndingX='00821'  StartingY='02469' Length='2'  Confidence='0,0'  >TO</Word>\r\n<Word Height='00028' StartingX='00862'  EndingX='00995'  StartingY='02469' Length='6'  Confidence='0,0,0,0,0,0'  >PREPAY</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='26.52' Height='00.35' >\r\n<LineText>~X[04.87]I have the right to make payments of Principal at any time before they are due. A payment of Principal only </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00468'  EndingX='00468'  StartingY='02522' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00491'  EndingX='00549'  StartingY='02521' Length='4'  Confidence='0,0,0,0'  >have</Word>\r\n<Word Height='00028' StartingX='00577'  EndingX='00609'  StartingY='02526' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00635'  EndingX='00701'  StartingY='02522' Length='5'  Confidence='30,30,0,0,0'  >right</Word>\r\n<Word Height='00028' StartingX='00722'  EndingX='00734'  StartingY='02526' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00764'  EndingX='00834'  StartingY='02531' Length='4'  Confidence='0,0,0,0'  >make</Word>\r\n<Word Height='00028' StartingX='00861'  EndingX='01000'  StartingY='02532' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >payments</Word>\r\n<Word Height='00028' StartingX='01026'  EndingX='01046'  StartingY='02532' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01069'  EndingX='01204'  StartingY='02523' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >Principal</Word>\r\n<Word Height='00028' StartingX='01224'  EndingX='01242'  StartingY='02532' Length='2'  Confidence='0,0'  >at</Word>\r\n<Word Height='00028' StartingX='01263'  EndingX='01301'  StartingY='02532' Length='3'  Confidence='0,0,0'  >any</Word>\r\n<Word Height='00028' StartingX='01331'  EndingX='01385'  StartingY='02527' Length='4'  Confidence='0,0,0,0'  >time</Word>\r\n<Word Height='00028' StartingX='01412'  EndingX='01497'  StartingY='02523' Length='6'  Confidence='0,0,0,0,0,0'  >before</Word>\r\n<Word Height='00028' StartingX='01524'  EndingX='01573'  StartingY='02527' Length='4'  Confidence='0,0,0,0'  >they</Word>\r\n<Word Height='00028' StartingX='01604'  EndingX='01635'  StartingY='02532' Length='3'  Confidence='0,0,0'  >are</Word>\r\n<Word Height='00028' StartingX='01663'  EndingX='01723'  StartingY='02523' Length='4'  Confidence='0,0,0,0'  >due.</Word>\r\n<Word Height='00028' StartingX='01741'  EndingX='01741'  StartingY='02524' Length='1'  Confidence='0'  >A</Word>\r\n<Word Height='00028' StartingX='01779'  EndingX='01907'  StartingY='02533' Length='7'  Confidence='0,0,0,0,0,0,0'  >payment</Word>\r\n<Word Height='00028' StartingX='01929'  EndingX='01949'  StartingY='02532' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01972'  EndingX='02107'  StartingY='02524' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >Principal</Word>\r\n<Word Height='00028' StartingX='02128'  EndingX='02179'  StartingY='02532' Length='4'  Confidence='0,0,0,0'  >only</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='27.00' Height='00.31' >\r\n<LineText>~X[03.36]is known as a &quot;Prepayment.&quot; When I make a Prepayment, I will tell the Note Holder in writing that I am doing so. I </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00323'  EndingX='00335'  StartingY='02567' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='00360'  EndingX='00449'  StartingY='02567' Length='5'  Confidence='0,0,0,0,0'  >known</Word>\r\n<Word Height='00028' StartingX='00481'  EndingX='00500'  StartingY='02577' Length='2'  Confidence='0,0'  >as</Word>\r\n<Word Height='00028' StartingX='00525'  EndingX='00525'  StartingY='02577' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00555'  EndingX='00776'  StartingY='02569' Length='23'  Confidence='0,0,0,0,0,0,0,0,0,0,0,0,0'  >&quot;Prepayment.&quot;</Word>\r\n<Word Height='00028' StartingX='00801'  EndingX='00877'  StartingY='02569' Length='4'  Confidence='30,30,0,0'  >When</Word>\r\n<Word Height='00028' StartingX='00909'  EndingX='00909'  StartingY='02569' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00931'  EndingX='01002'  StartingY='02578' Length='4'  Confidence='0,0,0,0'  >make</Word>\r\n<Word Height='00028' StartingX='01030'  EndingX='01030'  StartingY='02578' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='01058'  EndingX='01252'  StartingY='02570' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >Prepayment,</Word>\r\n<Word Height='00028' StartingX='01273'  EndingX='01273'  StartingY='02570' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='01296'  EndingX='01348'  StartingY='02579' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='01369'  EndingX='01410'  StartingY='02573' Length='4'  Confidence='0,0,0,0'  >tell</Word>\r\n<Word Height='00028' StartingX='01430'  EndingX='01462'  StartingY='02573' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01489'  EndingX='01551'  StartingY='02570' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='01579'  EndingX='01678'  StartingY='02570' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='01702'  EndingX='01712'  StartingY='02570' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='01744'  EndingX='01840'  StartingY='02579' Length='7'  Confidence='0,0,0,0,0,0,0'  >writing</Word>\r\n<Word Height='00028' StartingX='01871'  EndingX='01921'  StartingY='02574' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='01943'  EndingX='01943'  StartingY='02571' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='01967'  EndingX='01984'  StartingY='02579' Length='2'  Confidence='0,0'  >am</Word>\r\n<Word Height='00028' StartingX='02027'  EndingX='02099'  StartingY='02570' Length='5'  Confidence='0,0,0,0,0'  >doing</Word>\r\n<Word Height='00028' StartingX='02130'  EndingX='02167'  StartingY='02579' Length='3'  Confidence='0,0,0'  >so.</Word>\r\n<Word Height='00028' StartingX='02186'  EndingX='02186'  StartingY='02571' Length='1'  Confidence='0'  >I</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='27.50' Height='00.31' >\r\n<LineText>~X[03.35]may no~Y[00.00]t ~Ydes~Y[00.00]igna~Yte a paymen~Y[00.00]t as a ~YPrepaymen~Y[00.01]t ~Yi~Y[00.01]f ~YI ~Y[00.01]have no~Yt ma~Y[00.02]de a~Yl~Y[00.01]l t~Yhe mont~Y[00.02]h~Yly payments ~Y[00.02]due un~Yder t~Y[00.02]he ~YNote. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00371'  StartingY='02624' Length='3'  Confidence='0,0,0'  >may</Word>\r\n<Word Height='00028' StartingX='00402'  EndingX='00442'  StartingY='02624' Length='3'  Confidence='0,0,0'  >not</Word>\r\n<Word Height='00028' StartingX='00463'  EndingX='00598'  StartingY='02615' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >designate</Word>\r\n<Word Height='00028' StartingX='00625'  EndingX='00625'  StartingY='02624' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00653'  EndingX='00780'  StartingY='02625' Length='7'  Confidence='0,30,0,0,0,0,0'  >payment</Word>\r\n<Word Height='00028' StartingX='00801'  EndingX='00820'  StartingY='02625' Length='2'  Confidence='0,0'  >as</Word>\r\n<Word Height='00028' StartingX='00845'  EndingX='00845'  StartingY='02625' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00872'  EndingX='01053'  StartingY='02616' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >Prepayment</Word>\r\n<Word Height='00028' StartingX='01075'  EndingX='01087'  StartingY='02616' Length='2'  Confidence='0,0'  >if</Word>\r\n<Word Height='00028' StartingX='01109'  EndingX='01109'  StartingY='02617' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='01132'  EndingX='01190'  StartingY='02616' Length='4'  Confidence='0,0,0,0'  >have</Word>\r\n<Word Height='00028' StartingX='01217'  EndingX='01258'  StartingY='02626' Length='3'  Confidence='0,0,0'  >not</Word>\r\n<Word Height='00028' StartingX='01279'  EndingX='01348'  StartingY='02626' Length='4'  Confidence='0,0,0,0'  >made</Word>\r\n<Word Height='00028' StartingX='01376'  EndingX='01406'  StartingY='02626' Length='3'  Confidence='0,0,0'  >all</Word>\r\n<Word Height='00028' StartingX='01425'  EndingX='01456'  StartingY='02621' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01484'  EndingX='01597'  StartingY='02626' Length='7'  Confidence='0,0,0,0,0,0,0'  >monthly</Word>\r\n<Word Height='00028' StartingX='01627'  EndingX='01766'  StartingY='02626' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >payments</Word>\r\n<Word Height='00028' StartingX='01791'  EndingX='01832'  StartingY='02617' Length='3'  Confidence='0,0,0'  >due</Word>\r\n<Word Height='00028' StartingX='01858'  EndingX='01937'  StartingY='02626' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n<Word Height='00028' StartingX='01961'  EndingX='01992'  StartingY='02621' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='02018'  EndingX='02100'  StartingY='02618' Length='5'  Confidence='0,0,0,0,0'  >Note.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='28.05' Height='00.30' >\r\n<LineText>~X[04.87]I may make a full Prepayment or partial Prepayments without paying a Prepayment charge. The Note </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00468'  EndingX='00468'  StartingY='02669' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00494'  EndingX='00544'  StartingY='02677' Length='3'  Confidence='0,0,0'  >may</Word>\r\n<Word Height='00028' StartingX='00579'  EndingX='00651'  StartingY='02677' Length='4'  Confidence='0,0,0,0'  >make</Word>\r\n<Word Height='00028' StartingX='00682'  EndingX='00682'  StartingY='02677' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00714'  EndingX='00761'  StartingY='02668' Length='4'  Confidence='30,30,0,0'  >full</Word>\r\n<Word Height='00028' StartingX='00785'  EndingX='00973'  StartingY='02669' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >Prepayment</Word>\r\n<Word Height='00028' StartingX='00998'  EndingX='01018'  StartingY='02678' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='01045'  EndingX='01143'  StartingY='02678' Length='7'  Confidence='0,0,30,30,0,0,0'  >partial</Word>\r\n<Word Height='00028' StartingX='01167'  EndingX='01367'  StartingY='02670' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >Prepayments</Word>\r\n<Word Height='00028' StartingX='01395'  EndingX='01510'  StartingY='02679' Length='7'  Confidence='0,0,0,0,0,0,0'  >without</Word>\r\n<Word Height='00028' StartingX='01535'  EndingX='01628'  StartingY='02679' Length='6'  Confidence='0,0,0,0,0,0'  >paying</Word>\r\n<Word Height='00028' StartingX='01662'  EndingX='01662'  StartingY='02679' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='01693'  EndingX='01881'  StartingY='02671' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >Prepayment</Word>\r\n<Word Height='00028' StartingX='01906'  EndingX='02019'  StartingY='02679' Length='7'  Confidence='0,0,0,0,0,0,0'  >charge.</Word>\r\n<Word Height='00028' StartingX='02041'  EndingX='02088'  StartingY='02671' Length='3'  Confidence='0,0,0'  >The</Word>\r\n<Word Height='00028' StartingX='02118'  EndingX='02182'  StartingY='02671' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='28.54' Height='00.31' >\r\n<LineText>~X[03.36]Holder will use my Prepayments to reduce the amount of Principal that I owe under this Note. However, the Note </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00323'  EndingX='00423'  StartingY='02715' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='00447'  EndingX='00501'  StartingY='02724' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='00521'  EndingX='00559'  StartingY='02725' Length='3'  Confidence='0,0,0'  >use</Word>\r\n<Word Height='00028' StartingX='00588'  EndingX='00619'  StartingY='02725' Length='2'  Confidence='0,0'  >my</Word>\r\n<Word Height='00028' StartingX='00652'  EndingX='00848'  StartingY='02716' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >Prepayments</Word>\r\n<Word Height='00028' StartingX='00874'  EndingX='00886'  StartingY='02719' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00917'  EndingX='01008'  StartingY='02725' Length='6'  Confidence='0,0,0,0,0,0'  >reduce</Word>\r\n<Word Height='00028' StartingX='01038'  EndingX='01069'  StartingY='02719' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01099'  EndingX='01209'  StartingY='02726' Length='6'  Confidence='0,0,0,0,0,0'  >amount</Word>\r\n<Word Height='00028' StartingX='01232'  EndingX='01253'  StartingY='02726' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01278'  EndingX='01415'  StartingY='02717' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >Principal</Word>\r\n<Word Height='00028' StartingX='01435'  EndingX='01486'  StartingY='02720' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='01509'  EndingX='01509'  StartingY='02717' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='01533'  EndingX='01583'  StartingY='02726' Length='3'  Confidence='0,0,0'  >owe</Word>\r\n<Word Height='00028' StartingX='01611'  EndingX='01692'  StartingY='02727' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n<Word Height='00028' StartingX='01716'  EndingX='01760'  StartingY='02721' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01785'  EndingX='01868'  StartingY='02717' Length='5'  Confidence='0,0,0,0,0'  >Note.</Word>\r\n<Word Height='00028' StartingX='01888'  EndingX='02038'  StartingY='02718' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >However,</Word>\r\n<Word Height='00028' StartingX='02059'  EndingX='02091'  StartingY='02721' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='02119'  EndingX='02182'  StartingY='02718' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='29.03' Height='00.30' >\r\n<LineText>~X[03.36]Holder may apply my Prepayment to the accrued and unpaid interest on the Prepayment amount, before applying </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00323'  EndingX='00423'  StartingY='02763' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='00448'  EndingX='00497'  StartingY='02772' Length='3'  Confidence='0,0,0'  >may</Word>\r\n<Word Height='00028' StartingX='00530'  EndingX='00600'  StartingY='02771' Length='5'  Confidence='0,0,0,0,0'  >apply</Word>\r\n<Word Height='00028' StartingX='00633'  EndingX='00663'  StartingY='02772' Length='2'  Confidence='0,30'  >my</Word>\r\n<Word Height='00028' StartingX='00697'  EndingX='00881'  StartingY='02763' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >Prepayment</Word>\r\n<Word Height='00028' StartingX='00904'  EndingX='00916'  StartingY='02768' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00948'  EndingX='00980'  StartingY='02768' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01009'  EndingX='01117'  StartingY='02772' Length='7'  Confidence='0,0,0,0,0,0,0'  >accrued</Word>\r\n<Word Height='00028' StartingX='01149'  EndingX='01187'  StartingY='02773' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='01218'  EndingX='01311'  StartingY='02773' Length='6'  Confidence='0,0,0,0,0,0'  >unpaid</Word>\r\n<Word Height='00028' StartingX='01343'  EndingX='01451'  StartingY='02764' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >interest</Word>\r\n<Word Height='00028' StartingX='01474'  EndingX='01494'  StartingY='02773' Length='2'  Confidence='0,0'  >on</Word>\r\n<Word Height='00028' StartingX='01526'  EndingX='01558'  StartingY='02768' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01587'  EndingX='01771'  StartingY='02765' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >Prepayment</Word>\r\n<Word Height='00028' StartingX='01795'  EndingX='01919'  StartingY='02774' Length='7'  Confidence='0,0,0,0,0,0,0'  >amount,</Word>\r\n<Word Height='00028' StartingX='01939'  EndingX='02026'  StartingY='02765' Length='6'  Confidence='0,0,0,0,0,0'  >before</Word>\r\n<Word Height='00028' StartingX='02056'  EndingX='02180'  StartingY='02774' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >applying</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='29.52' Height='00.30' >\r\n<LineText>~X[03.36]my ~Y[00.00]Prepaymen~Yt ~Y[00.00]to re~Yduce ~Y[00.01]t~Yhe ~Y[00.00]Pr~Yinc~Y[00.01]ipa~Yl amoun~Y[00.01]t o~Yf ~Y[00.02]t~Yhe ~Y[00.01]No~Yte. ~Y[00.01]I~Yf ~Y[00.01]I ma~Yke a pa~Y[00.02]r~Yt~Y[00.02]ia~Yl ~Y[00.02]Prepayment, t~Yhere w~Y[00.02]i~Yl~Y[00.02]l ~Ybe no c~Y[00.02]hanges ~Y</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00323'  EndingX='00353'  StartingY='02819' Length='2'  Confidence='0,0'  >my</Word>\r\n<Word Height='00028' StartingX='00385'  EndingX='00566'  StartingY='02810' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >Prepayment</Word>\r\n<Word Height='00028' StartingX='00587'  EndingX='00598'  StartingY='02814' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00629'  EndingX='00719'  StartingY='02819' Length='6'  Confidence='0,0,0,0,0,0'  >reduce</Word>\r\n<Word Height='00028' StartingX='00747'  EndingX='00779'  StartingY='02814' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00806'  EndingX='00942'  StartingY='02811' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >Principal</Word>\r\n<Word Height='00028' StartingX='00963'  EndingX='01073'  StartingY='02819' Length='6'  Confidence='0,0,0,0,0,0'  >amount</Word>\r\n<Word Height='00028' StartingX='01094'  EndingX='01114'  StartingY='02820' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01138'  EndingX='01169'  StartingY='02815' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01196'  EndingX='01277'  StartingY='02811' Length='5'  Confidence='0,0,0,0,0'  >Note.</Word>\r\n<Word Height='00028' StartingX='01296'  EndingX='01310'  StartingY='02812' Length='2'  Confidence='0,20'  >If</Word>\r\n<Word Height='00028' StartingX='01333'  EndingX='01333'  StartingY='02812' Length='1'  Confidence='40'  >I</Word>\r\n<Word Height='00028' StartingX='01356'  EndingX='01425'  StartingY='02820' Length='4'  Confidence='0,0,0,0'  >make</Word>\r\n<Word Height='00028' StartingX='01453'  EndingX='01453'  StartingY='02820' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='01480'  EndingX='01573'  StartingY='02820' Length='7'  Confidence='0,0,30,30,0,0,0'  >partial</Word>\r\n<Word Height='00028' StartingX='01594'  EndingX='01787'  StartingY='02812' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >Prepayment,</Word>\r\n<Word Height='00028' StartingX='01806'  EndingX='01869'  StartingY='02816' Length='5'  Confidence='0,0,0,0,0'  >there</Word>\r\n<Word Height='00028' StartingX='01897'  EndingX='01950'  StartingY='02821' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='01970'  EndingX='01991'  StartingY='02812' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='02019'  EndingX='02039'  StartingY='02821' Length='2'  Confidence='0,0'  >no</Word>\r\n<Word Height='00028' StartingX='02070'  EndingX='02184'  StartingY='02821' Length='7'  Confidence='0,0,0,0,0,0,0'  >changes</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='30.00' Height='00.31' >\r\n<LineText>~X[03.37]in the due date or in the amount of my monthly payment unless the Note Holder agrees in writing to those changes. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00324'  EndingX='00334'  StartingY='02856' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='00364'  EndingX='00396'  StartingY='02860' Length='3'  Confidence='0,0,30'  >the</Word>\r\n<Word Height='00028' StartingX='00423'  EndingX='00464'  StartingY='02856' Length='3'  Confidence='0,0,0'  >due</Word>\r\n<Word Height='00028' StartingX='00491'  EndingX='00540'  StartingY='02856' Length='4'  Confidence='0,0,0,0'  >date</Word>\r\n<Word Height='00028' StartingX='00568'  EndingX='00588'  StartingY='02865' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='00612'  EndingX='00622'  StartingY='02857' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='00652'  EndingX='00685'  StartingY='02861' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00712'  EndingX='00821'  StartingY='02866' Length='6'  Confidence='0,0,0,0,0,0'  >amount</Word>\r\n<Word Height='00028' StartingX='00842'  EndingX='00863'  StartingY='02866' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='00886'  EndingX='00917'  StartingY='02866' Length='2'  Confidence='0,0'  >my</Word>\r\n<Word Height='00028' StartingX='00947'  EndingX='01060'  StartingY='02866' Length='7'  Confidence='0,0,0,0,0,0,0'  >monthly</Word>\r\n<Word Height='00028' StartingX='01091'  EndingX='01218'  StartingY='02867' Length='7'  Confidence='0,0,0,0,0,0,0'  >payment</Word>\r\n<Word Height='00028' StartingX='01239'  EndingX='01324'  StartingY='02867' Length='6'  Confidence='0,0,0,0,0,0'  >unless</Word>\r\n<Word Height='00028' StartingX='01349'  EndingX='01381'  StartingY='02862' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01407'  EndingX='01468'  StartingY='02858' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='01495'  EndingX='01593'  StartingY='02859' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='01616'  EndingX='01704'  StartingY='02867' Length='6'  Confidence='0,0,0,0,0,0'  >agrees</Word>\r\n<Word Height='00028' StartingX='01730'  EndingX='01740'  StartingY='02858' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='01769'  EndingX='01866'  StartingY='02868' Length='7'  Confidence='0,0,0,0,0,0,0'  >writing</Word>\r\n<Word Height='00028' StartingX='01896'  EndingX='01908'  StartingY='02862' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01937'  EndingX='02004'  StartingY='02863' Length='5'  Confidence='0,0,0,0,0'  >those</Word>\r\n<Word Height='00028' StartingX='02032'  EndingX='02164'  StartingY='02868' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >changes.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='30.57' Height='00.31' >\r\n<LineText>~X[03.36]5. ~FLOAN CHARGES ~F</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00323'  EndingX='00343'  StartingY='02910' Length='2'  Confidence='0,0'  >5.</Word>\r\n<Word Height='00028' StartingX='00363'  EndingX='00450'  StartingY='02910' Length='4'  Confidence='0,0,0,0'  >LOAN</Word>\r\n<Word Height='00028' StartingX='00489'  EndingX='00665'  StartingY='02910' Length='7'  Confidence='0,0,0,0,0,0,0'  >CHARGES</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='31.12' Height='00.31' >\r\n<LineText>~X[04.87]If a law, which applies to this loan and which sets maximum loan charges, is finally interpreted so that the </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00468'  EndingX='00481'  StartingY='02963' Length='2'  Confidence='0,0'  >If</Word>\r\n<Word Height='00028' StartingX='00505'  EndingX='00505'  StartingY='02972' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00534'  EndingX='00593'  StartingY='02963' Length='4'  Confidence='0,0,0,0'  >law,</Word>\r\n<Word Height='00028' StartingX='00613'  EndingX='00692'  StartingY='02972' Length='5'  Confidence='0,0,0,0,0'  >which</Word>\r\n<Word Height='00028' StartingX='00724'  EndingX='00823'  StartingY='02973' Length='7'  Confidence='0,0,0,0,0,0,0'  >applies</Word>\r\n<Word Height='00028' StartingX='00849'  EndingX='00860'  StartingY='02967' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00892'  EndingX='00935'  StartingY='02968' Length='4'  Confidence='0,10,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='00961'  EndingX='01010'  StartingY='02964' Length='4'  Confidence='0,0,0,0'  >loan</Word>\r\n<Word Height='00028' StartingX='01041'  EndingX='01080'  StartingY='02973' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='01110'  EndingX='01189'  StartingY='02974' Length='5'  Confidence='0,0,0,0,0'  >which</Word>\r\n<Word Height='00028' StartingX='01220'  EndingX='01266'  StartingY='02973' Length='4'  Confidence='0,0,0,0'  >sets</Word>\r\n<Word Height='00028' StartingX='01292'  EndingX='01424'  StartingY='02974' Length='7'  Confidence='0,0,0,0,0,0,0'  >maximum</Word>\r\n<Word Height='00028' StartingX='01466'  EndingX='01515'  StartingY='02964' Length='4'  Confidence='0,0,0,0'  >loan</Word>\r\n<Word Height='00028' StartingX='01546'  EndingX='01670'  StartingY='02974' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >charges,</Word>\r\n<Word Height='00028' StartingX='01691'  EndingX='01702'  StartingY='02965' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='01728'  EndingX='01813'  StartingY='02965' Length='7'  Confidence='30,20,0,0,0,0,0'  >finally</Word>\r\n<Word Height='00028' StartingX='01845'  EndingX='02001'  StartingY='02965' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >interpreted</Word>\r\n<Word Height='00028' StartingX='02032'  EndingX='02047'  StartingY='02974' Length='2'  Confidence='0,0'  >so</Word>\r\n<Word Height='00028' StartingX='02079'  EndingX='02128'  StartingY='02969' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='02151'  EndingX='02182'  StartingY='02969' Length='3'  Confidence='0,0,0'  >the</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='31.61' Height='00.31' >\r\n<LineText>~X[03.36]interest or other loan charges collected or to be collected in connection with this loan exceed the permitted limits, </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00323'  EndingX='00432'  StartingY='03010' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >interest</Word>\r\n<Word Height='00028' StartingX='00456'  EndingX='00475'  StartingY='03019' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='00501'  EndingX='00571'  StartingY='03020' Length='5'  Confidence='0,0,0,0,0'  >other</Word>\r\n<Word Height='00028' StartingX='00597'  EndingX='00646'  StartingY='03010' Length='4'  Confidence='0,0,0,0'  >loan</Word>\r\n<Word Height='00028' StartingX='00679'  EndingX='00789'  StartingY='03012' Length='7'  Confidence='30,40,0,0,0,0,0'  >charges</Word>\r\n<Word Height='00028' StartingX='00816'  EndingX='00944'  StartingY='03020' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >collected</Word>\r\n<Word Height='00028' StartingX='00976'  EndingX='00996'  StartingY='03021' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='01021'  EndingX='01033'  StartingY='03016' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01063'  EndingX='01085'  StartingY='03011' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='01115'  EndingX='01242'  StartingY='03021' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >collected</Word>\r\n<Word Height='00028' StartingX='01275'  EndingX='01285'  StartingY='03012' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='01317'  EndingX='01475'  StartingY='03021' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >connection</Word>\r\n<Word Height='00028' StartingX='01507'  EndingX='01559'  StartingY='03022' Length='4'  Confidence='0,0,0,0'  >with</Word>\r\n<Word Height='00028' StartingX='01591'  EndingX='01635'  StartingY='03016' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01663'  EndingX='01712'  StartingY='03012' Length='4'  Confidence='0,0,0,0'  >loan</Word>\r\n<Word Height='00028' StartingX='01744'  EndingX='01837'  StartingY='03022' Length='6'  Confidence='0,0,0,0,0,0'  >exceed</Word>\r\n<Word Height='00028' StartingX='01870'  EndingX='01902'  StartingY='03016' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01930'  EndingX='02068'  StartingY='03022' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >permitted</Word>\r\n<Word Height='00028' StartingX='02100'  EndingX='02195'  StartingY='03012' Length='7'  Confidence='0,0,0,0,0,0,0'  >limits,</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='32.09' Height='00.38' >\r\n<LineText>~X[03.35]then: '.(a) any such loan charge shall be reduced by the amount necessary to reduce the charge to the permitted limit; </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00395'  StartingY='03061' Length='5'  Confidence='0,0,0,0,0'  >then:</Word>\r\n<Word Height='00028' StartingX='00411'  EndingX='00446'  StartingY='03066' Length='5'  Confidence='70,60,10,0,0'  >'.(a)</Word>\r\n<Word Height='00028' StartingX='00470'  EndingX='00507'  StartingY='03067' Length='3'  Confidence='0,0,0'  >any</Word>\r\n<Word Height='00028' StartingX='00539'  EndingX='00593'  StartingY='03067' Length='4'  Confidence='0,0,0,0'  >such</Word>\r\n<Word Height='00028' StartingX='00624'  EndingX='00673'  StartingY='03057' Length='4'  Confidence='0,0,0,0'  >loan</Word>\r\n<Word Height='00028' StartingX='00705'  EndingX='00795'  StartingY='03067' Length='6'  Confidence='0,0,0,0,0,0'  >charge</Word>\r\n<Word Height='00028' StartingX='00824'  EndingX='00890'  StartingY='03067' Length='5'  Confidence='0,0,0,0,0'  >shall</Word>\r\n<Word Height='00028' StartingX='00910'  EndingX='00931'  StartingY='03058' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='00959'  EndingX='01067'  StartingY='03068' Length='7'  Confidence='0,0,0,0,0,0,0'  >reduced</Word>\r\n<Word Height='00028' StartingX='01098'  EndingX='01118'  StartingY='03058' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='01149'  EndingX='01181'  StartingY='03062' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01209'  EndingX='01320'  StartingY='03068' Length='6'  Confidence='0,0,0,0,0,0'  >amount</Word>\r\n<Word Height='00028' StartingX='01341'  EndingX='01464'  StartingY='03068' Length='9'  Confidence='0,0,0,0,0,0,0,20,20'  >necessary</Word>\r\n<Word Height='00028' StartingX='01508'  EndingX='01520'  StartingY='03063' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01550'  EndingX='01640'  StartingY='03069' Length='6'  Confidence='0,0,0,0,0,0'  >reduce</Word>\r\n<Word Height='00028' StartingX='01669'  EndingX='01701'  StartingY='03063' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01729'  EndingX='01819'  StartingY='03068' Length='6'  Confidence='0,0,0,0,0,0'  >charge</Word>\r\n<Word Height='00028' StartingX='01847'  EndingX='01859'  StartingY='03063' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01889'  EndingX='01921'  StartingY='03063' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01949'  EndingX='02085'  StartingY='03069' Length='9'  Confidence='0,0,30,30,0,0,0,0,0'  >permitted</Word>\r\n<Word Height='00028' StartingX='02117'  EndingX='02194'  StartingY='03059' Length='6'  Confidence='0,0,0,0,0,0'  >limit;</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='32.58' Height='00.38' >\r\n<LineText>~X[03.35]an~Y[00.00]d ~Y(~Y[00.01]b~Y) any sums a~Y[00.00]lrea~Ydy co~Y[00.00]l~Ylecte~Y[00.01]d ~Yf~Y[00.01]rom me w~Yh~Y[00.01]ic~Yh excee~Y[00.02]de~Yd perm~Y[00.02]itte~Yd ~Y[00.02]l~Yim~Y[00.02]its w~Yi~Y[00.02]l~Yl ~Y[00.03]be re~Yfun~Y[00.03]de~Yd to me. ~Y[00.02]T~Yhe ~Y[00.02]Note ~Y</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00361'  StartingY='03113' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='00395'  EndingX='00429'  StartingY='03104' Length='3'  Confidence='0,0,0'  >(b)</Word>\r\n<Word Height='00028' StartingX='00456'  EndingX='00494'  StartingY='03113' Length='3'  Confidence='0,0,0'  >any</Word>\r\n<Word Height='00028' StartingX='00528'  EndingX='00597'  StartingY='03113' Length='4'  Confidence='0,0,0,0'  >sums</Word>\r\n<Word Height='00028' StartingX='00625'  EndingX='00727'  StartingY='03114' Length='7'  Confidence='0,0,0,0,0,0,0'  >already</Word>\r\n<Word Height='00028' StartingX='00761'  EndingX='00891'  StartingY='03114' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >collected</Word>\r\n<Word Height='00028' StartingX='00924'  EndingX='00972'  StartingY='03105' Length='4'  Confidence='30,30,0,0'  >from</Word>\r\n<Word Height='00028' StartingX='01016'  EndingX='01048'  StartingY='03115' Length='2'  Confidence='0,0'  >me</Word>\r\n<Word Height='00028' StartingX='01078'  EndingX='01158'  StartingY='03115' Length='5'  Confidence='0,0,0,0,0'  >which</Word>\r\n<Word Height='00028' StartingX='01192'  EndingX='01326'  StartingY='03115' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >exceeded</Word>\r\n<Word Height='00028' StartingX='01358'  EndingX='01496'  StartingY='03115' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >permitted</Word>\r\n<Word Height='00028' StartingX='01529'  EndingX='01608'  StartingY='03106' Length='6'  Confidence='0,0,0,0,0,0'  >limits</Word>\r\n<Word Height='00028' StartingX='01635'  EndingX='01690'  StartingY='03115' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='01712'  EndingX='01734'  StartingY='03106' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='01764'  EndingX='01892'  StartingY='03116' Length='8'  Confidence='0,0,30,30,0,0,0,0'  >refunded</Word>\r\n<Word Height='00028' StartingX='01924'  EndingX='01936'  StartingY='03110' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01969'  EndingX='02021'  StartingY='03116' Length='3'  Confidence='0,0,0'  >me.</Word>\r\n<Word Height='00028' StartingX='02042'  EndingX='02089'  StartingY='03107' Length='3'  Confidence='0,0,0'  >The</Word>\r\n<Word Height='00028' StartingX='02119'  EndingX='02182'  StartingY='03107' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='33.07' Height='00.31' >\r\n<LineText>~X[03.35]Holder may choose to make this refund by reducing the Principal I owe under this Note or by making a direct </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00423'  StartingY='03151' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='00451'  EndingX='00501'  StartingY='03160' Length='3'  Confidence='0,0,0'  >may</Word>\r\n<Word Height='00028' StartingX='00535'  EndingX='00633'  StartingY='03160' Length='6'  Confidence='0,0,0,0,0,0'  >choose</Word>\r\n<Word Height='00028' StartingX='00664'  EndingX='00676'  StartingY='03156' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00709'  EndingX='00782'  StartingY='03161' Length='4'  Confidence='0,0,0,0'  >make</Word>\r\n<Word Height='00028' StartingX='00812'  EndingX='00858'  StartingY='03156' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='00886'  EndingX='00975'  StartingY='03161' Length='6'  Confidence='0,0,30,30,0,0'  >refund</Word>\r\n<Word Height='00028' StartingX='01008'  EndingX='01028'  StartingY='03152' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='01062'  EndingX='01188'  StartingY='03161' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >reducing</Word>\r\n<Word Height='00028' StartingX='01221'  EndingX='01255'  StartingY='03156' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01286'  EndingX='01425'  StartingY='03153' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >Principal</Word>\r\n<Word Height='00028' StartingX='01449'  EndingX='01449'  StartingY='03153' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='01476'  EndingX='01527'  StartingY='03162' Length='3'  Confidence='0,0,0'  >owe</Word>\r\n<Word Height='00028' StartingX='01557'  EndingX='01638'  StartingY='03162' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n<Word Height='00028' StartingX='01665'  EndingX='01711'  StartingY='03157' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01738'  EndingX='01801'  StartingY='03154' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='01833'  EndingX='01853'  StartingY='03162' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='01880'  EndingX='01901'  StartingY='03153' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='01934'  EndingX='02039'  StartingY='03163' Length='6'  Confidence='0,0,0,0,0,0'  >making</Word>\r\n<Word Height='00028' StartingX='02073'  EndingX='02073'  StartingY='03163' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='02104'  EndingX='02188'  StartingY='03153' Length='6'  Confidence='0,0,0,0,0,0'  >direct</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='34.10' Height='00.25' >\r\n<LineText>~X[03.35]MULTISTATE FIXED RATE NOTE ~F-Single Family-- ~FFannie Mae/Freddie Mac UNIFORM INSTRUMENT </LineText>\r\n<Words>\r\n<Word Height='00023' StartingX='00322'  EndingX='00514'  StartingY='03257' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >MULTISTATE</Word>\r\n<Word Height='00023' StartingX='00544'  EndingX='00620'  StartingY='03257' Length='5'  Confidence='0,0,30,30,0'  >FIXED</Word>\r\n<Word Height='00023' StartingX='00652'  EndingX='00720'  StartingY='03257' Length='4'  Confidence='0,0,0,0'  >RATE</Word>\r\n<Word Height='00023' StartingX='00750'  EndingX='00819'  StartingY='03257' Length='4'  Confidence='0,0,0,0'  >NOTE</Word>\r\n<Word Height='00023' StartingX='00850'  EndingX='00938'  StartingY='03270' Length='7'  Confidence='30,30,0,0,0,0,0'  >-Single</Word>\r\n<Word Height='00023' StartingX='00960'  EndingX='01063'  StartingY='03257' Length='8'  Confidence='0,0,0,0,0,20,40,30'  >Family--</Word>\r\n<Word Height='00023' StartingX='01081'  EndingX='01162'  StartingY='03257' Length='6'  Confidence='0,0,0,0,0,0'  >Fannie</Word>\r\n<Word Height='00023' StartingX='01183'  EndingX='01346'  StartingY='03258' Length='11'  Confidence='10,10,10,60,40,10,10,10,10,10,10'  >Mae/Freddie</Word>\r\n<Word Height='00023' StartingX='01367'  EndingX='01414'  StartingY='03258' Length='3'  Confidence='0,0,0'  >Mac</Word>\r\n<Word Height='00023' StartingX='01435'  EndingX='01562'  StartingY='03258' Length='7'  Confidence='30,30,30,0,0,0,0'  >UNIFORM</Word>\r\n<Word Height='00023' StartingX='01599'  EndingX='01796'  StartingY='03258' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >INSTRUMENT</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='34.50' Height='00.31' >\r\n<LineText>~X[03.33]IexJ 5 74~X[17.31]~FForm 3200 1/01~X[20.29]~F(page I ~Fof ~F3 pages)</LineText>\r\n<Words>\r\n<Word Height='00022' StartingX='00320'  EndingX='00358'  StartingY='03292' Length='4'  Confidence='188,60,30,158'  >IexJ</Word>\r\n<Word Height='00022' StartingX='00398'  EndingX='00398'  StartingY='03293' Length='1'  Confidence='20'  >5</Word>\r\n<Word Height='00022' StartingX='00422'  EndingX='00436'  StartingY='03293' Length='2'  Confidence='30,20'  >74</Word>\r\n<Word Height='00022' StartingX='01664'  EndingX='01714'  StartingY='03296' Length='4'  Confidence='0,0,0,0'  >Form</Word>\r\n<Word Height='00022' StartingX='01756'  EndingX='01804'  StartingY='03296' Length='4'  Confidence='0,0,0,0'  >3200</Word>\r\n<Word Height='00022' StartingX='01854'  EndingX='01896'  StartingY='03296' Length='4'  Confidence='20,30,0,0'  >1/01</Word>\r\n<Word Height='00022' StartingX='01951'  EndingX='02009'  StartingY='03296' Length='5'  Confidence='0,0,0,0,30'  >(page</Word>\r\n<Word Height='00022' StartingX='02034'  EndingX='02034'  StartingY='03296' Length='1'  Confidence='60'  >I</Word>\r\n<Word Height='00022' StartingX='02056'  EndingX='02066'  StartingY='03303' Length='2'  Confidence='40,50'  >of</Word>\r\n<Word Height='00022' StartingX='02089'  EndingX='02089'  StartingY='03296' Length='1'  Confidence='0'  >3</Word>\r\n<Word Height='00022' StartingX='02110'  EndingX='02184'  StartingY='03303' Length='6'  Confidence='0,0,0,0,0,0'  >pages)</Word>\r\n</Words>\r\n</Line>\r\n\r\n</Lines>\r\n</Page>\r\n"}, {"PageNumber": 2, "PageText": "38121081989 payment to me. If a refund reduces Principal, the reduction will be treated as a partial Prepayment. -6. BORROWER'S FAILURE TO PAY AS REQUIRED (A) Late Charge for Overdue Payments If the Note Holder has not received the full amount of any monthly payment by the end of 15 calendar days after the date it is due, I will pay a late charge to the Note Holder. The amount of the charge will be 5. 000% of my overdue payment of principal and interest. I will pay this late charge promptly but only once on each late payment. (B) Default If I do not pay the full amount of each monthly payment on the date it is due, I will be in default. (C) Notice of Default If I am in default, the Note Holder may send me a written notice telling me that if I do not pay the overdue amount by a certain date, the Note Holder may require me to pay immediately the full amount of Principal which has not been paid and all the interest that I owe on that amount. That date must be at least 30 days after the date on which the notice is mailed to me or delivered by other means. (D) No Waiver By Note Holder Even if, at a time when I am in default, the Note Holder does not require me to pay immediately in full as described above, the Note Holder will still have the right to do so if I am in default at a later time. (E) Payment of Note Holder's Costs and Expenses If the Note Holder has required me to pay immediately in full as described above, the Note Holder will have the right to be paid back by me for all of its costs and expenses in enforcing this Note to the extent not prohibited by applicable law. Those expenses include, for example, reasonable attorneys' fees. 7. GIVING OF NOTICES Unless applicable law requires a different method, any notice that must be given to me under this Note will be given by delivering it or by mailing it by first class mail to me at the Property Address above or at a different address if I give the Note Holder a notice of my different address. Any notice that must be given to the Note Holder under this Note will be given by delivering it or by mailing it by first class mail to the Note Holder at the address stated in Section 3(A) above or at a different address if I am given a notice of that different address. 8. OBLIGATIONS OF PERSONS UNDER THIS NOTE If more than one person signs this Note, each person is fully and personally obligated to keep all of the promises made in this Note, including the promise to pay the full amount owed. Any person who is a guarantor, surety or endorser of this Note is also obligated to do these things. Any person who takes over these obligations, including the obligations of a guarantor, surety or endorser of this Note, is also obligated to keep all of the promises made in this Note. The Note Holder may enforce its rights under this Note against each person individually or against all of us together. This means that any one of us may be required to pay all of the amounts owed under this Note. 3Jl•:%\\ ►'/ 91:7.y I and any other person who has obligations under this Note waive the rights of Presentment and Notice of Dishonor. \"Presentment\" means the right to require the Note Holder to demand payment of amounts due. \"Notice of Dishonor\" means the right to require the Note Holder to give notice to other persons that amounts due have not been paid. 10. UNIFORM SECURED NOTE This Note is a uniform instrument with limited variations in some jurisdictions. In addition to the protections given to the Note Holder under this Note, a Mortgage, Deed of Trust or Security Deed (the \"Security Instrument\"), dated the same date as this Note, protects the Note Holder from possible losses which might result if I do not keep the promises which I make in this Note. That Security Instrument describes how and under what conditions I may be required to make immediate payment in full of all amounts I owe under this Note. Some of those conditions are described as follows: If all or any part of the Property or any Interest in the Property is sold or transferred (or if Borrower is not a natural person and a beneficial interest in Borrower is sold or transferred) without Lender's prior written consent, Lender may require immediate payment in full of all sums MULTISTATE FIXED RATE NOTE -Single Family-- Fannie Mae/Freddie Mac UNIFORM INSTRUMENT cxlJ 5.74 Form 3200 1/01 (page 2 of 3 pages) ", "PageXML": "<Page PageWidth='2534' PageHeight='4271'>\r\n<PageNumber>2</PageNumber>\r\n<Lines>\r\n<Line BaseLine='08.89' Height='00.25' >\r\n<LineText>~X[20.73]38121081989 </LineText>\r\n<Words>\r\n<Word Height='00023' StartingX='01993'  EndingX='02186'  StartingY='00832' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >38121081989</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='09.62' Height='00.31' >\r\n<LineText>~X[03.40]payment to me. ~Y[00.00]I~Yf a re~Y[00.01]fun~Yd re~Y[00.02]duces ~YPr~Y[00.01]inc~Yipa~Y[00.02]l, t~Yhe re~Y[00.03]duct~Yion w~Y[00.03]i~Yl~Y[00.03]l ~Ybe treate~Y[00.04]d as a partial Prepayment. ~Y</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00327'  EndingX='00455'  StartingY='00905' Length='7'  Confidence='0,0,0,0,0,0,0'  >payment</Word>\r\n<Word Height='00028' StartingX='00475'  EndingX='00487'  StartingY='00900' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00517'  EndingX='00568'  StartingY='00906' Length='3'  Confidence='0,0,0'  >me.</Word>\r\n<Word Height='00028' StartingX='00586'  EndingX='00600'  StartingY='00897' Length='2'  Confidence='0,0'  >If</Word>\r\n<Word Height='00028' StartingX='00623'  EndingX='00623'  StartingY='00906' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00650'  EndingX='00736'  StartingY='00906' Length='6'  Confidence='0,0,30,30,30,0'  >refund</Word>\r\n<Word Height='00028' StartingX='00766'  EndingX='00874'  StartingY='00906' Length='7'  Confidence='0,0,0,0,0,0,0'  >reduces</Word>\r\n<Word Height='00028' StartingX='00899'  EndingX='01044'  StartingY='00898' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >Principal,</Word>\r\n<Word Height='00028' StartingX='01063'  EndingX='01095'  StartingY='00902' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01122'  EndingX='01253'  StartingY='00908' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >reduction</Word>\r\n<Word Height='00028' StartingX='01283'  EndingX='01336'  StartingY='00908' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='01356'  EndingX='01377'  StartingY='00899' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='01404'  EndingX='01492'  StartingY='00903' Length='7'  Confidence='0,30,0,0,0,0,0'  >treated</Word>\r\n<Word Height='00028' StartingX='01522'  EndingX='01540'  StartingY='00909' Length='2'  Confidence='0,0'  >as</Word>\r\n<Word Height='00028' StartingX='01565'  EndingX='01565'  StartingY='00909' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='01592'  EndingX='01685'  StartingY='00909' Length='7'  Confidence='0,0,0,0,0,0,0'  >partial</Word>\r\n<Word Height='00028' StartingX='01706'  EndingX='01899'  StartingY='00901' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >Prepayment.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='10.18' Height='00.38' >\r\n<LineText>~X[03.35]-~Y[00.00]6. ~YB~Y[00.00]O~YR~Y[00.00]R~YO~Y[00.00]W~YE~Y[00.00]R~Y'~Y[00.01]S ~YF~Y[00.01]A~YI~Y[00.01]L~YU~Y[00.01]R~YE ~Y[00.01]T~YO ~Y[00.01]P~YA~Y[00.02]Y ~YA~Y[00.03]S ~YR~Y[00.02]E~YQ~Y[00.03]U~YI~Y[00.02]R~YE~Y[00.03]D ~Y</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00348'  StartingY='00966' Length='3'  Confidence='50,0,0'  >-6.</Word>\r\n<Word Height='00028' StartingX='00368'  EndingX='00623'  StartingY='00950' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >BORROWER'S</Word>\r\n<Word Height='00028' StartingX='00654'  EndingX='00809'  StartingY='00951' Length='7'  Confidence='0,0,0,0,0,0,0'  >FAILURE</Word>\r\n<Word Height='00028' StartingX='00845'  EndingX='00872'  StartingY='00952' Length='2'  Confidence='0,0'  >TO</Word>\r\n<Word Height='00028' StartingX='00913'  EndingX='00967'  StartingY='00952' Length='3'  Confidence='0,0,0'  >PAY</Word>\r\n<Word Height='00028' StartingX='01005'  EndingX='01035'  StartingY='00953' Length='2'  Confidence='0,0'  >AS</Word>\r\n<Word Height='00028' StartingX='01067'  EndingX='01253'  StartingY='00952' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >REQUIRED</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='10.73' Height='00.37' >\r\n<LineText>~X[04.92](A) Late Charge for Overdue Payments </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00473'  EndingX='00513'  StartingY='01004' Length='3'  Confidence='0,0,0'  >(A)</Word>\r\n<Word Height='00028' StartingX='00537'  EndingX='00598'  StartingY='01004' Length='4'  Confidence='0,0,0,0'  >Late</Word>\r\n<Word Height='00028' StartingX='00626'  EndingX='00736'  StartingY='01004' Length='6'  Confidence='0,0,0,0,0,0'  >Charge</Word>\r\n<Word Height='00028' StartingX='00764'  EndingX='00797'  StartingY='01005' Length='3'  Confidence='0,0,0'  >for</Word>\r\n<Word Height='00028' StartingX='00825'  EndingX='00956'  StartingY='01005' Length='7'  Confidence='0,0,0,0,0,0,0'  >Overdue</Word>\r\n<Word Height='00028' StartingX='00984'  EndingX='01136'  StartingY='01006' Length='8'  Confidence='0,0,0,30,0,0,0,0'  >Payments</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='11.29' Height='00.31' >\r\n<LineText>~X[04.91]If the Note Holder has not received the full amount of any monthly payment by the end of ~F15 ~Fcalendar days </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00472'  EndingX='00486'  StartingY='01058' Length='2'  Confidence='0,0'  >If</Word>\r\n<Word Height='00028' StartingX='00509'  EndingX='00540'  StartingY='01061' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00567'  EndingX='00628'  StartingY='01058' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00655'  EndingX='00754'  StartingY='01058' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='00778'  EndingX='00816'  StartingY='01057' Length='3'  Confidence='0,0,0'  >has</Word>\r\n<Word Height='00028' StartingX='00841'  EndingX='00882'  StartingY='01068' Length='3'  Confidence='0,0,0'  >not</Word>\r\n<Word Height='00028' StartingX='00903'  EndingX='01018'  StartingY='01068' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >received</Word>\r\n<Word Height='00028' StartingX='01048'  EndingX='01080'  StartingY='01063' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01108'  EndingX='01153'  StartingY='01059' Length='4'  Confidence='30,30,0,0'  >full</Word>\r\n<Word Height='00028' StartingX='01173'  EndingX='01282'  StartingY='01069' Length='6'  Confidence='0,0,0,0,0,0'  >amount</Word>\r\n<Word Height='00028' StartingX='01304'  EndingX='01324'  StartingY='01069' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01347'  EndingX='01384'  StartingY='01069' Length='3'  Confidence='0,0,0'  >any</Word>\r\n<Word Height='00028' StartingX='01414'  EndingX='01527'  StartingY='01070' Length='7'  Confidence='0,0,0,0,0,0,0'  >monthly</Word>\r\n<Word Height='00028' StartingX='01556'  EndingX='01684'  StartingY='01070' Length='7'  Confidence='0,0,0,0,0,0,0'  >payment</Word>\r\n<Word Height='00028' StartingX='01705'  EndingX='01725'  StartingY='01061' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='01755'  EndingX='01786'  StartingY='01065' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01814'  EndingX='01852'  StartingY='01070' Length='3'  Confidence='0,0,0'  >end</Word>\r\n<Word Height='00028' StartingX='01882'  EndingX='01903'  StartingY='01070' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01928'  EndingX='01951'  StartingY='01064' Length='2'  Confidence='0,0'  >15</Word>\r\n<Word Height='00028' StartingX='01984'  EndingX='02107'  StartingY='01071' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >calendar</Word>\r\n<Word Height='00028' StartingX='02130'  EndingX='02188'  StartingY='01062' Length='4'  Confidence='0,0,0,0'  >days</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='11.79' Height='00.31' >\r\n<LineText>~X[03.40]a~Y[-00.01]fter ~Yt~Y[-00.01]he ~Ydate ~Y[00.00]it ~Yis ~Y[00.00]due, ~YI w~Y[00.00]i~Yl~Y[00.00]l pay a ~Ylate c~Y[00.01]harge to t~Yhe ~Y[00.02]Note ~YHo~Y[00.02]l~Yder. ~Y[00.03]T~Yhe amount o~Y[00.03]f t~Yhe c~Y[00.04]harge will ~Yb~Y[00.05]e ~F~Y5~Y[00.05]. ~F~Y0~Y[00.05]0~Y0~Y[00.05]% ~Yo~Y[00.05]f ~Ym~Y[00.05]y ~Y</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00327'  EndingX='00388'  StartingY='01113' Length='5'  Confidence='0,30,30,0,0'  >after</Word>\r\n<Word Height='00028' StartingX='00411'  EndingX='00443'  StartingY='01108' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00471'  EndingX='00520'  StartingY='01104' Length='4'  Confidence='0,0,0,0'  >date</Word>\r\n<Word Height='00028' StartingX='00549'  EndingX='00559'  StartingY='01105' Length='2'  Confidence='0,0'  >it</Word>\r\n<Word Height='00028' StartingX='00582'  EndingX='00593'  StartingY='01105' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='00618'  EndingX='00678'  StartingY='01105' Length='4'  Confidence='0,0,0,0'  >due,</Word>\r\n<Word Height='00028' StartingX='00698'  EndingX='00698'  StartingY='01106' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00720'  EndingX='00774'  StartingY='01115' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='00793'  EndingX='00832'  StartingY='01115' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00028' StartingX='00863'  EndingX='00863'  StartingY='01115' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00892'  EndingX='00932'  StartingY='01106' Length='4'  Confidence='0,0,0,0'  >late</Word>\r\n<Word Height='00028' StartingX='00960'  EndingX='01049'  StartingY='01116' Length='6'  Confidence='0,0,0,0,0,0'  >charge</Word>\r\n<Word Height='00028' StartingX='01077'  EndingX='01089'  StartingY='01110' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01119'  EndingX='01150'  StartingY='01111' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01177'  EndingX='01238'  StartingY='01108' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='01266'  EndingX='01380'  StartingY='01108' Length='7'  Confidence='0,0,0,0,0,0,0'  >Holder.</Word>\r\n<Word Height='00028' StartingX='01398'  EndingX='01442'  StartingY='01108' Length='3'  Confidence='0,0,0'  >The</Word>\r\n<Word Height='00028' StartingX='01471'  EndingX='01580'  StartingY='01117' Length='6'  Confidence='0,0,0,0,0,0'  >amount</Word>\r\n<Word Height='00028' StartingX='01601'  EndingX='01622'  StartingY='01118' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01645'  EndingX='01677'  StartingY='01112' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01705'  EndingX='01794'  StartingY='01118' Length='6'  Confidence='0,0,0,0,0,0'  >charge</Word>\r\n<Word Height='00028' StartingX='01821'  EndingX='01875'  StartingY='01118' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='01895'  EndingX='01916'  StartingY='01109' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='01945'  EndingX='01975'  StartingY='01111' Length='2'  Confidence='10,20'  >5.</Word>\r\n<Word Height='00028' StartingX='01994'  EndingX='02065'  StartingY='01111' Length='4'  Confidence='30,0,0,0'  >000%</Word>\r\n<Word Height='00028' StartingX='02108'  EndingX='02129'  StartingY='01119' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='02152'  EndingX='02183'  StartingY='01119' Length='2'  Confidence='0,0'  >my</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='12.28' Height='00.32' >\r\n<LineText>~X[03.40]over~Y[00.01]due payment o~Yf pr~Y[00.01]inc~Yipa~Y[00.01]l an~Yd ~Y[00.01]interest. ~YI w~Y[00.02]i~Yl~Y[00.02]l pay t~Yh~Y[00.03]is ~Ylate c~Y[00.03]harge prompt~Yly ~Y[00.04]but ~Yon~Y[00.04]ly ~Yon~Y[00.05]c~Ye ~Y[00.05]on ~Ye~Y[00.05]a~Yc~Y[00.05]h ~Yl~Y[00.05]a~Yt~Y[00.05]e ~Yp~Y[00.05]a~Yy~Y[00.05]m~Ye~Y[00.05]n~Yt~Y[00.05]. ~Y</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00327'  EndingX='00439'  StartingY='01161' Length='7'  Confidence='0,0,0,0,0,0,0'  >overdue</Word>\r\n<Word Height='00028' StartingX='00466'  EndingX='00594'  StartingY='01161' Length='7'  Confidence='0,0,0,0,0,0,0'  >payment</Word>\r\n<Word Height='00028' StartingX='00615'  EndingX='00635'  StartingY='01162' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='00658'  EndingX='00791'  StartingY='01162' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >principal</Word>\r\n<Word Height='00028' StartingX='00811'  EndingX='00849'  StartingY='01163' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='00880'  EndingX='00999'  StartingY='01153' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >interest.</Word>\r\n<Word Height='00028' StartingX='01017'  EndingX='01017'  StartingY='01155' Length='1'  Confidence='50'  >I</Word>\r\n<Word Height='00028' StartingX='01040'  EndingX='01093'  StartingY='01164' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='01113'  EndingX='01150'  StartingY='01164' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00028' StartingX='01181'  EndingX='01224'  StartingY='01159' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01250'  EndingX='01289'  StartingY='01155' Length='4'  Confidence='0,0,0,0'  >late</Word>\r\n<Word Height='00028' StartingX='01317'  EndingX='01406'  StartingY='01164' Length='6'  Confidence='0,0,0,0,0,0'  >charge</Word>\r\n<Word Height='00028' StartingX='01432'  EndingX='01559'  StartingY='01165' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >promptly</Word>\r\n<Word Height='00028' StartingX='01589'  EndingX='01629'  StartingY='01156' Length='3'  Confidence='0,0,0'  >but</Word>\r\n<Word Height='00028' StartingX='01650'  EndingX='01701'  StartingY='01165' Length='4'  Confidence='0,0,0,0'  >only</Word>\r\n<Word Height='00028' StartingX='01732'  EndingX='01789'  StartingY='01165' Length='4'  Confidence='0,0,0,0'  >once</Word>\r\n<Word Height='00028' StartingX='01817'  EndingX='01837'  StartingY='01166' Length='2'  Confidence='0,0'  >on</Word>\r\n<Word Height='00028' StartingX='01868'  EndingX='01921'  StartingY='01166' Length='4'  Confidence='0,0,0,0'  >each</Word>\r\n<Word Height='00028' StartingX='01952'  EndingX='01992'  StartingY='01156' Length='4'  Confidence='0,0,0,0'  >late</Word>\r\n<Word Height='00028' StartingX='02019'  EndingX='02160'  StartingY='01166' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >payment.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='12.84' Height='00.38' >\r\n<LineText>~X[04.91](B) Default </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00472'  EndingX='00511'  StartingY='01206' Length='3'  Confidence='30,30,0'  >(B)</Word>\r\n<Word Height='00028' StartingX='00535'  EndingX='00649'  StartingY='01206' Length='7'  Confidence='0,0,0,0,0,0,0'  >Default</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='13.40' Height='00.31' >\r\n<LineText>~X[04.91]If I do not pay the full amount of each monthly payment on the date it is due, I will be in default. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00472'  EndingX='00485'  StartingY='01260' Length='2'  Confidence='0,0'  >If</Word>\r\n<Word Height='00028' StartingX='00508'  EndingX='00508'  StartingY='01260' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00531'  EndingX='00551'  StartingY='01260' Length='2'  Confidence='0,0'  >do</Word>\r\n<Word Height='00028' StartingX='00581'  EndingX='00622'  StartingY='01269' Length='3'  Confidence='0,0,0'  >not</Word>\r\n<Word Height='00028' StartingX='00643'  EndingX='00680'  StartingY='01270' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00028' StartingX='00712'  EndingX='00743'  StartingY='01264' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00771'  EndingX='00816'  StartingY='01260' Length='4'  Confidence='30,30,0,0'  >full</Word>\r\n<Word Height='00028' StartingX='00836'  EndingX='00946'  StartingY='01270' Length='6'  Confidence='0,0,0,0,0,0'  >amount</Word>\r\n<Word Height='00028' StartingX='00967'  EndingX='00987'  StartingY='01270' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01010'  EndingX='01063'  StartingY='01271' Length='4'  Confidence='0,0,0,0'  >each</Word>\r\n<Word Height='00028' StartingX='01094'  EndingX='01207'  StartingY='01271' Length='7'  Confidence='0,0,0,0,0,0,0'  >monthly</Word>\r\n<Word Height='00028' StartingX='01236'  EndingX='01364'  StartingY='01272' Length='7'  Confidence='0,0,0,0,0,0,0'  >payment</Word>\r\n<Word Height='00028' StartingX='01385'  EndingX='01405'  StartingY='01272' Length='2'  Confidence='0,0'  >on</Word>\r\n<Word Height='00028' StartingX='01434'  EndingX='01465'  StartingY='01267' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01493'  EndingX='01542'  StartingY='01263' Length='4'  Confidence='0,0,0,0'  >date</Word>\r\n<Word Height='00028' StartingX='01571'  EndingX='01581'  StartingY='01263' Length='2'  Confidence='0,0'  >it</Word>\r\n<Word Height='00028' StartingX='01603'  EndingX='01614'  StartingY='01263' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='01639'  EndingX='01698'  StartingY='01263' Length='4'  Confidence='0,0,0,0'  >due,</Word>\r\n<Word Height='00028' StartingX='01717'  EndingX='01717'  StartingY='01264' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='01739'  EndingX='01793'  StartingY='01273' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='01812'  EndingX='01832'  StartingY='01264' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='01861'  EndingX='01872'  StartingY='01264' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='01902'  EndingX='02015'  StartingY='01264' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >default.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='13.96' Height='00.36' >\r\n<LineText>~X[04.91](C) Notice of Default </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00472'  EndingX='00513'  StartingY='01314' Length='3'  Confidence='0,0,0'  >(C)</Word>\r\n<Word Height='00028' StartingX='00536'  EndingX='00628'  StartingY='01314' Length='6'  Confidence='0,0,0,0,0,0'  >Notice</Word>\r\n<Word Height='00028' StartingX='00656'  EndingX='00676'  StartingY='01322' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='00700'  EndingX='00814'  StartingY='01314' Length='7'  Confidence='0,0,0,0,0,0,0'  >Default</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='14.51' Height='00.32' >\r\n<LineText>~X[04.91]If I am in default, the Note Holder may send me a written notice telling me that if I do not pay th~Y[00.06]e ~Yov~Y[00.06]er~Yd~Y[00.06]u~Ye </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00472'  EndingX='00485'  StartingY='01366' Length='2'  Confidence='0,20'  >If</Word>\r\n<Word Height='00028' StartingX='00509'  EndingX='00509'  StartingY='01366' Length='1'  Confidence='40'  >I</Word>\r\n<Word Height='00028' StartingX='00532'  EndingX='00551'  StartingY='01376' Length='2'  Confidence='0,0'  >am</Word>\r\n<Word Height='00028' StartingX='00593'  EndingX='00604'  StartingY='01366' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='00635'  EndingX='00749'  StartingY='01366' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >default,</Word>\r\n<Word Height='00028' StartingX='00768'  EndingX='00800'  StartingY='01370' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00827'  EndingX='00889'  StartingY='01367' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00917'  EndingX='01016'  StartingY='01367' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='01040'  EndingX='01089'  StartingY='01377' Length='3'  Confidence='0,0,0'  >may</Word>\r\n<Word Height='00028' StartingX='01121'  EndingX='01174'  StartingY='01377' Length='4'  Confidence='0,0,0,0'  >send</Word>\r\n<Word Height='00028' StartingX='01205'  EndingX='01236'  StartingY='01378' Length='2'  Confidence='0,0'  >me</Word>\r\n<Word Height='00028' StartingX='01264'  EndingX='01264'  StartingY='01378' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='01293'  EndingX='01387'  StartingY='01379' Length='7'  Confidence='0,0,0,0,0,0,0'  >written</Word>\r\n<Word Height='00028' StartingX='01418'  EndingX='01498'  StartingY='01379' Length='6'  Confidence='0,0,0,0,0,0'  >notice</Word>\r\n<Word Height='00028' StartingX='01526'  EndingX='01609'  StartingY='01373' Length='7'  Confidence='0,0,0,0,0,0,0'  >telling</Word>\r\n<Word Height='00028' StartingX='01640'  EndingX='01672'  StartingY='01379' Length='2'  Confidence='0,0'  >me</Word>\r\n<Word Height='00028' StartingX='01700'  EndingX='01749'  StartingY='01374' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='01772'  EndingX='01783'  StartingY='01370' Length='2'  Confidence='0,0'  >if</Word>\r\n<Word Height='00028' StartingX='01806'  EndingX='01806'  StartingY='01370' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='01830'  EndingX='01851'  StartingY='01370' Length='2'  Confidence='0,0'  >do</Word>\r\n<Word Height='00028' StartingX='01882'  EndingX='01922'  StartingY='01380' Length='3'  Confidence='0,0,0'  >not</Word>\r\n<Word Height='00028' StartingX='01942'  EndingX='01981'  StartingY='01380' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00028' StartingX='02013'  EndingX='02044'  StartingY='01375' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='02073'  EndingX='02186'  StartingY='01380' Length='7'  Confidence='0,0,0,0,0,0,0'  >overdue</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='15.00' Height='00.31' >\r\n<LineText>~X[03.39]amoun~Y[00.01]t ~Yby a ce~Y[00.01]r~Yta~Y[00.01]in ~Yda~Y[00.02]te, ~Yt~Y[00.02]he ~YNo~Y[00.02]te ~YHo~Y[00.03]l~Yder may r~Y[00.04]e~Yq~Y[00.04]u~Yir~Y[00.04]e ~Ym~Y[00.04]e ~Yt~Y[00.04]o ~Yp~Y[00.04]a~Yy ~Y[00.04]i~Ym~Y[00.04]m~Ye~Y[00.05]d~Yi~Y[00.04]a~Yt~Y[00.05]e~Yl~Y[00.04]y ~Yt~Y[00.05]h~Ye ~Y[00.05]f~Yu~Y[00.05]l~Yl ~Y[00.05]a~Ym~Y[00.06]o~Yu~Y[00.05]n~Yt ~Y[00.06]o~Yf ~Y[00.06]P~Yr~Y[00.05]i~Yn~Y[00.06]c~Yi~Y[00.05]p~Ya~Y[00.06]l ~Yw~Y[00.06]h~Yi~Y[00.06]c~Yh </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00326'  EndingX='00438'  StartingY='01423' Length='6'  Confidence='0,0,0,0,0,0'  >amount</Word>\r\n<Word Height='00028' StartingX='00460'  EndingX='00480'  StartingY='01414' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='00513'  EndingX='00513'  StartingY='01423' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00542'  EndingX='00634'  StartingY='01423' Length='7'  Confidence='0,0,20,20,0,0,0'  >certain</Word>\r\n<Word Height='00028' StartingX='00666'  EndingX='00736'  StartingY='01414' Length='5'  Confidence='0,0,0,0,0'  >date,</Word>\r\n<Word Height='00028' StartingX='00757'  EndingX='00789'  StartingY='01419' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00817'  EndingX='00880'  StartingY='01415' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00909'  EndingX='01009'  StartingY='01416' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='01034'  EndingX='01084'  StartingY='01425' Length='3'  Confidence='0,0,0'  >may</Word>\r\n<Word Height='00028' StartingX='01116'  EndingX='01214'  StartingY='01425' Length='7'  Confidence='0,0,0,0,0,0,0'  >require</Word>\r\n<Word Height='00028' StartingX='01243'  EndingX='01275'  StartingY='01426' Length='2'  Confidence='0,0'  >me</Word>\r\n<Word Height='00028' StartingX='01305'  EndingX='01316'  StartingY='01421' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01347'  EndingX='01386'  StartingY='01426' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00028' StartingX='01419'  EndingX='01601'  StartingY='01417' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >immediately</Word>\r\n<Word Height='00028' StartingX='01633'  EndingX='01666'  StartingY='01422' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01695'  EndingX='01741'  StartingY='01417' Length='4'  Confidence='30,30,0,0'  >full</Word>\r\n<Word Height='00028' StartingX='01763'  EndingX='01875'  StartingY='01427' Length='6'  Confidence='0,0,0,0,0,0'  >amount</Word>\r\n<Word Height='00028' StartingX='01897'  EndingX='01919'  StartingY='01427' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01943'  EndingX='02081'  StartingY='01419' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >Principal</Word>\r\n<Word Height='00028' StartingX='02103'  EndingX='02183'  StartingY='01428' Length='5'  Confidence='0,0,0,0,0'  >which</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='15.50' Height='00.31' >\r\n<LineText>~X[03.39]has not been paid and all the interest that I owe on that amount. That date must be at least 30 days after the ~Y[00.06]dat~Ye ~Y[00.06]on ~Y</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00326'  EndingX='00365'  StartingY='01461' Length='3'  Confidence='0,0,0'  >has</Word>\r\n<Word Height='00028' StartingX='00391'  EndingX='00432'  StartingY='01470' Length='3'  Confidence='0,0,0'  >not</Word>\r\n<Word Height='00028' StartingX='00453'  EndingX='00510'  StartingY='01461' Length='4'  Confidence='0,0,0,0'  >been</Word>\r\n<Word Height='00028' StartingX='00540'  EndingX='00591'  StartingY='01471' Length='4'  Confidence='0,0,0,0'  >paid</Word>\r\n<Word Height='00028' StartingX='00623'  EndingX='00661'  StartingY='01471' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='00692'  EndingX='00722'  StartingY='01471' Length='3'  Confidence='0,0,0'  >all</Word>\r\n<Word Height='00028' StartingX='00744'  EndingX='00775'  StartingY='01466' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00804'  EndingX='00912'  StartingY='01462' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >interest</Word>\r\n<Word Height='00028' StartingX='00934'  EndingX='00984'  StartingY='01467' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='01007'  EndingX='01007'  StartingY='01464' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='01031'  EndingX='01081'  StartingY='01473' Length='3'  Confidence='0,0,0'  >owe</Word>\r\n<Word Height='00028' StartingX='01109'  EndingX='01129'  StartingY='01473' Length='2'  Confidence='0,0'  >on</Word>\r\n<Word Height='00028' StartingX='01160'  EndingX='01210'  StartingY='01468' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='01232'  EndingX='01355'  StartingY='01473' Length='7'  Confidence='0,0,0,0,0,0,0'  >amount.</Word>\r\n<Word Height='00028' StartingX='01375'  EndingX='01436'  StartingY='01465' Length='4'  Confidence='0,0,0,0'  >That</Word>\r\n<Word Height='00028' StartingX='01458'  EndingX='01509'  StartingY='01464' Length='4'  Confidence='0,0,0,0'  >date</Word>\r\n<Word Height='00028' StartingX='01537'  EndingX='01604'  StartingY='01474' Length='4'  Confidence='0,0,0,0'  >must</Word>\r\n<Word Height='00028' StartingX='01626'  EndingX='01647'  StartingY='01465' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='01676'  EndingX='01694'  StartingY='01474' Length='2'  Confidence='0,0'  >at</Word>\r\n<Word Height='00028' StartingX='01717'  EndingX='01779'  StartingY='01465' Length='5'  Confidence='0,0,0,0,0'  >least</Word>\r\n<Word Height='00028' StartingX='01802'  EndingX='01821'  StartingY='01466' Length='2'  Confidence='0,0'  >30</Word>\r\n<Word Height='00028' StartingX='01852'  EndingX='01912'  StartingY='01465' Length='4'  Confidence='0,0,0,0'  >days</Word>\r\n<Word Height='00028' StartingX='01938'  EndingX='01998'  StartingY='01475' Length='5'  Confidence='0,0,0,0,0'  >after</Word>\r\n<Word Height='00028' StartingX='02023'  EndingX='02055'  StartingY='01470' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='02084'  EndingX='02134'  StartingY='01466' Length='4'  Confidence='0,0,0,0'  >date</Word>\r\n<Word Height='00028' StartingX='02163'  EndingX='02182'  StartingY='01476' Length='2'  Confidence='0,0'  >on</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='16.00' Height='00.32' >\r\n<LineText>~X[03.39]w~Y[00.00]h~Yic~Y[00.00]h ~Yt~Y[00.00]he no~Yt~Y[00.00]ice ~Yis ma~Y[00.01]i~Yle~Y[00.01]d to me or ~Yde~Y[00.01]l~Yivere~Y[00.02]d ~Yby ot~Y[00.02]her means. ~Y</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00326'  EndingX='00404'  StartingY='01518' Length='5'  Confidence='0,0,0,0,0'  >which</Word>\r\n<Word Height='00028' StartingX='00434'  EndingX='00466'  StartingY='01513' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00493'  EndingX='00574'  StartingY='01518' Length='6'  Confidence='0,0,0,0,0,0'  >notice</Word>\r\n<Word Height='00028' StartingX='00602'  EndingX='00613'  StartingY='01509' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='00638'  EndingX='00728'  StartingY='01519' Length='6'  Confidence='0,0,0,0,0,0'  >mailed</Word>\r\n<Word Height='00028' StartingX='00758'  EndingX='00769'  StartingY='01514' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00799'  EndingX='00831'  StartingY='01519' Length='2'  Confidence='0,0'  >me</Word>\r\n<Word Height='00028' StartingX='00858'  EndingX='00878'  StartingY='01519' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='00902'  EndingX='01031'  StartingY='01510' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >delivered</Word>\r\n<Word Height='00028' StartingX='01060'  EndingX='01081'  StartingY='01510' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='01112'  EndingX='01180'  StartingY='01520' Length='5'  Confidence='0,0,0,0,0'  >other</Word>\r\n<Word Height='00028' StartingX='01203'  EndingX='01308'  StartingY='01521' Length='6'  Confidence='0,0,0,0,0,0'  >means.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='16.55' Height='00.37' >\r\n<LineText>~X[04.90](D) No Waiver By Note Holder </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00471'  EndingX='00512'  StartingY='01563' Length='3'  Confidence='30,30,0'  >(D)</Word>\r\n<Word Height='00028' StartingX='00536'  EndingX='00566'  StartingY='01563' Length='2'  Confidence='0,0'  >No</Word>\r\n<Word Height='00028' StartingX='00595'  EndingX='00706'  StartingY='01563' Length='6'  Confidence='0,0,0,0,0,0'  >Waiver</Word>\r\n<Word Height='00028' StartingX='00733'  EndingX='00759'  StartingY='01564' Length='2'  Confidence='0,0'  >By</Word>\r\n<Word Height='00028' StartingX='00790'  EndingX='00852'  StartingY='01564' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00880'  EndingX='00983'  StartingY='01564' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='17.11' Height='00.32' >\r\n<LineText>~X[04.89]Even if, at a time when I am in default, the Note Holder does not require me to pay immediately in full a~Y[00.06]s ~Y</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00470'  EndingX='00533'  StartingY='01616' Length='4'  Confidence='0,0,0,0'  >Even</Word>\r\n<Word Height='00028' StartingX='00565'  EndingX='00591'  StartingY='01615' Length='3'  Confidence='0,0,0'  >if,</Word>\r\n<Word Height='00028' StartingX='00611'  EndingX='00629'  StartingY='01625' Length='2'  Confidence='0,0'  >at</Word>\r\n<Word Height='00028' StartingX='00652'  EndingX='00652'  StartingY='01625' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00680'  EndingX='00736'  StartingY='01620' Length='4'  Confidence='0,0,0,0'  >time</Word>\r\n<Word Height='00028' StartingX='00764'  EndingX='00831'  StartingY='01626' Length='4'  Confidence='0,0,0,0'  >when</Word>\r\n<Word Height='00028' StartingX='00863'  EndingX='00863'  StartingY='01617' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00888'  EndingX='00905'  StartingY='01626' Length='2'  Confidence='0,0'  >am</Word>\r\n<Word Height='00028' StartingX='00948'  EndingX='00958'  StartingY='01617' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='00991'  EndingX='01105'  StartingY='01617' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >default,</Word>\r\n<Word Height='00028' StartingX='01124'  EndingX='01156'  StartingY='01621' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01183'  EndingX='01246'  StartingY='01618' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='01275'  EndingX='01373'  StartingY='01619' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='01398'  EndingX='01456'  StartingY='01618' Length='4'  Confidence='0,0,0,0'  >does</Word>\r\n<Word Height='00028' StartingX='01482'  EndingX='01523'  StartingY='01628' Length='3'  Confidence='0,0,0'  >not</Word>\r\n<Word Height='00028' StartingX='01545'  EndingX='01643'  StartingY='01628' Length='7'  Confidence='0,0,0,0,0,0,0'  >require</Word>\r\n<Word Height='00028' StartingX='01671'  EndingX='01703'  StartingY='01629' Length='2'  Confidence='0,0'  >me</Word>\r\n<Word Height='00028' StartingX='01732'  EndingX='01744'  StartingY='01623' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01774'  EndingX='01812'  StartingY='01629' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00028' StartingX='01844'  EndingX='02027'  StartingY='01619' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >immediately</Word>\r\n<Word Height='00028' StartingX='02059'  EndingX='02069'  StartingY='01620' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='02102'  EndingX='02147'  StartingY='01620' Length='4'  Confidence='30,30,0,0'  >full</Word>\r\n<Word Height='00028' StartingX='02169'  EndingX='02187'  StartingY='01630' Length='2'  Confidence='0,0'  >as</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='17.60' Height='00.31' >\r\n<LineText>~X[03.39]described above, the Note Holder will still have the right to do so if I am in default at a later time. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00326'  EndingX='00460'  StartingY='01663' Length='9'  Confidence='0,0,0,0,30,30,0,0,0'  >described</Word>\r\n<Word Height='00028' StartingX='00490'  EndingX='00587'  StartingY='01673' Length='6'  Confidence='0,0,0,0,0,0'  >above,</Word>\r\n<Word Height='00028' StartingX='00605'  EndingX='00637'  StartingY='01668' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00663'  EndingX='00725'  StartingY='01665' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00753'  EndingX='00851'  StartingY='01665' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='00874'  EndingX='00927'  StartingY='01674' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='00948'  EndingX='00997'  StartingY='01674' Length='5'  Confidence='0,0,0,0,0'  >still</Word>\r\n<Word Height='00028' StartingX='01017'  EndingX='01076'  StartingY='01665' Length='4'  Confidence='0,0,0,0'  >have</Word>\r\n<Word Height='00028' StartingX='01103'  EndingX='01135'  StartingY='01669' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01161'  EndingX='01226'  StartingY='01666' Length='5'  Confidence='50,50,0,0,0'  >right</Word>\r\n<Word Height='00028' StartingX='01247'  EndingX='01258'  StartingY='01670' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01289'  EndingX='01309'  StartingY='01666' Length='2'  Confidence='0,0'  >do</Word>\r\n<Word Height='00028' StartingX='01340'  EndingX='01355'  StartingY='01675' Length='2'  Confidence='0,0'  >so</Word>\r\n<Word Height='00028' StartingX='01385'  EndingX='01396'  StartingY='01666' Length='2'  Confidence='0,0'  >if</Word>\r\n<Word Height='00028' StartingX='01419'  EndingX='01419'  StartingY='01667' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='01442'  EndingX='01458'  StartingY='01676' Length='2'  Confidence='0,30'  >am</Word>\r\n<Word Height='00028' StartingX='01501'  EndingX='01511'  StartingY='01666' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='01542'  EndingX='01641'  StartingY='01666' Length='7'  Confidence='0,0,0,0,0,0,0'  >default</Word>\r\n<Word Height='00028' StartingX='01663'  EndingX='01681'  StartingY='01676' Length='2'  Confidence='0,0'  >at</Word>\r\n<Word Height='00028' StartingX='01702'  EndingX='01702'  StartingY='01676' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='01731'  EndingX='01787'  StartingY='01667' Length='5'  Confidence='0,0,0,0,0'  >later</Word>\r\n<Word Height='00028' StartingX='01811'  EndingX='01884'  StartingY='01671' Length='5'  Confidence='0,0,0,0,0'  >time.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='18.16' Height='00.37' >\r\n<LineText>~X[04.90](E) Payment of Note Holder's Costs and Expenses </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00471'  EndingX='00510'  StartingY='01717' Length='3'  Confidence='30,30,0'  >(E)</Word>\r\n<Word Height='00028' StartingX='00534'  EndingX='00672'  StartingY='01717' Length='7'  Confidence='0,0,0,0,0,0,0'  >Payment</Word>\r\n<Word Height='00028' StartingX='00696'  EndingX='00716'  StartingY='01726' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='00739'  EndingX='00802'  StartingY='01718' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00829'  EndingX='00961'  StartingY='01718' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Holder's</Word>\r\n<Word Height='00028' StartingX='00987'  EndingX='01065'  StartingY='01719' Length='5'  Confidence='0,0,0,0,0'  >Costs</Word>\r\n<Word Height='00028' StartingX='01091'  EndingX='01133'  StartingY='01727' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='01165'  EndingX='01308'  StartingY='01719' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Expenses</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='18.71' Height='00.31' >\r\n<LineText>~X[04.90]If the Note Holder has required me to pay immediately in full as described above, the Note Holder will </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00471'  EndingX='00485'  StartingY='01771' Length='2'  Confidence='0,0'  >If</Word>\r\n<Word Height='00028' StartingX='00510'  EndingX='00543'  StartingY='01774' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00571'  EndingX='00635'  StartingY='01771' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00664'  EndingX='00766'  StartingY='01771' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='00792'  EndingX='00832'  StartingY='01771' Length='3'  Confidence='0,0,0'  >has</Word>\r\n<Word Height='00028' StartingX='00859'  EndingX='00977'  StartingY='01781' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >required</Word>\r\n<Word Height='00028' StartingX='01009'  EndingX='01041'  StartingY='01781' Length='2'  Confidence='0,0'  >me</Word>\r\n<Word Height='00028' StartingX='01071'  EndingX='01083'  StartingY='01776' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01115'  EndingX='01154'  StartingY='01782' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00028' StartingX='01188'  EndingX='01372'  StartingY='01772' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >immediately</Word>\r\n<Word Height='00028' StartingX='01406'  EndingX='01417'  StartingY='01773' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='01448'  EndingX='01496'  StartingY='01773' Length='4'  Confidence='30,30,0,0'  >full</Word>\r\n<Word Height='00028' StartingX='01519'  EndingX='01537'  StartingY='01783' Length='2'  Confidence='0,0'  >as</Word>\r\n<Word Height='00028' StartingX='01564'  EndingX='01703'  StartingY='01773' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >described</Word>\r\n<Word Height='00028' StartingX='01735'  EndingX='01834'  StartingY='01783' Length='6'  Confidence='0,0,0,0,0,0'  >above,</Word>\r\n<Word Height='00028' StartingX='01856'  EndingX='01888'  StartingY='01778' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01918'  EndingX='01981'  StartingY='01775' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='02011'  EndingX='02112'  StartingY='01775' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='02137'  EndingX='02192'  StartingY='01784' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='19.20' Height='00.31' >\r\n<LineText>~X[03.38]have the right to be paid back by me for all of its costs and expenses in enforcing this Note to the extent n~Y[00.06]ot ~Y</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00325'  EndingX='00386'  StartingY='01817' Length='4'  Confidence='0,0,0,0'  >have</Word>\r\n<Word Height='00028' StartingX='00418'  EndingX='00451'  StartingY='01822' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00482'  EndingX='00551'  StartingY='01826' Length='5'  Confidence='0,0,0,0,0'  >right</Word>\r\n<Word Height='00028' StartingX='00577'  EndingX='00589'  StartingY='01822' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00622'  EndingX='00644'  StartingY='01818' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='00675'  EndingX='00728'  StartingY='01827' Length='4'  Confidence='0,0,0,0'  >paid</Word>\r\n<Word Height='00028' StartingX='00761'  EndingX='00820'  StartingY='01818' Length='4'  Confidence='0,0,0,0'  >back</Word>\r\n<Word Height='00028' StartingX='00854'  EndingX='00875'  StartingY='01818' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='00910'  EndingX='00942'  StartingY='01828' Length='2'  Confidence='0,0'  >me</Word>\r\n<Word Height='00028' StartingX='00974'  EndingX='01009'  StartingY='01819' Length='3'  Confidence='0,0,0'  >for</Word>\r\n<Word Height='00028' StartingX='01036'  EndingX='01068'  StartingY='01828' Length='3'  Confidence='0,0,0'  >all</Word>\r\n<Word Height='00028' StartingX='01093'  EndingX='01114'  StartingY='01828' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01142'  EndingX='01165'  StartingY='01820' Length='3'  Confidence='0,0,0'  >its</Word>\r\n<Word Height='00028' StartingX='01194'  EndingX='01263'  StartingY='01829' Length='5'  Confidence='0,0,0,0,0'  >costs</Word>\r\n<Word Height='00028' StartingX='01292'  EndingX='01332'  StartingY='01829' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='01366'  EndingX='01501'  StartingY='01829' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >expenses</Word>\r\n<Word Height='00028' StartingX='01531'  EndingX='01542'  StartingY='01821' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='01576'  EndingX='01717'  StartingY='01830' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >enforcing</Word>\r\n<Word Height='00028' StartingX='01751'  EndingX='01796'  StartingY='01825' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01824'  EndingX='01889'  StartingY='01822' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='01921'  EndingX='01933'  StartingY='01826' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01967'  EndingX='02000'  StartingY='01826' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='02032'  EndingX='02123'  StartingY='01831' Length='6'  Confidence='0,0,0,0,0,0'  >extent</Word>\r\n<Word Height='00028' StartingX='02149'  EndingX='02190'  StartingY='01831' Length='3'  Confidence='0,0,0'  >not</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='19.69' Height='00.31' >\r\n<LineText>~X[03.38]pro~Y[00.00]h~Yi~Y[00.01]b~Yite~Y[00.01]d ~Yby app~Y[00.01]l~Yica~Y[00.02]b~Yle ~Y[00.01]law. ~YT~Y[00.02]hose expenses ~Yinc~Y[00.03]lu~Yde, ~Y[00.03]for examp~Yle, reasona~Y[00.04]b~Yle attorneys' fees. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00325'  EndingX='00471'  StartingY='01874' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >prohibited</Word>\r\n<Word Height='00028' StartingX='00500'  EndingX='00519'  StartingY='01865' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='00551'  EndingX='00698'  StartingY='01874' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >applicable</Word>\r\n<Word Height='00028' StartingX='00726'  EndingX='00785'  StartingY='01865' Length='4'  Confidence='0,0,0,0'  >law.</Word>\r\n<Word Height='00028' StartingX='00804'  EndingX='00885'  StartingY='01866' Length='5'  Confidence='0,0,0,0,0'  >Those</Word>\r\n<Word Height='00028' StartingX='00912'  EndingX='01042'  StartingY='01875' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >expenses</Word>\r\n<Word Height='00028' StartingX='01068'  EndingX='01185'  StartingY='01867' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >include,</Word>\r\n<Word Height='00028' StartingX='01205'  EndingX='01238'  StartingY='01867' Length='3'  Confidence='0,0,0'  >for</Word>\r\n<Word Height='00028' StartingX='01261'  EndingX='01398'  StartingY='01877' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >example,</Word>\r\n<Word Height='00028' StartingX='01417'  EndingX='01570'  StartingY='01877' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >reasonable</Word>\r\n<Word Height='00028' StartingX='01597'  EndingX='01745'  StartingY='01877' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >attorneys'</Word>\r\n<Word Height='00028' StartingX='01762'  EndingX='01828'  StartingY='01869' Length='5'  Confidence='0,0,0,0,0'  >fees.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='20.25' Height='00.31' >\r\n<LineText>~X[03.38]7. ~FGIVING OF NOTICES ~F</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00325'  EndingX='00346'  StartingY='01919' Length='2'  Confidence='0,0'  >7.</Word>\r\n<Word Height='00028' StartingX='00366'  EndingX='00486'  StartingY='01918' Length='6'  Confidence='0,0,30,0,30,0'  >GIVING</Word>\r\n<Word Height='00028' StartingX='00527'  EndingX='00558'  StartingY='01919' Length='2'  Confidence='0,0'  >OF</Word>\r\n<Word Height='00028' StartingX='00592'  EndingX='00752'  StartingY='01919' Length='7'  Confidence='0,0,0,0,0,0,0'  >NOTICES</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='20.82' Height='00.31' >\r\n<LineText>~X[04.88]Unless applicable law requires a different method, any notice that must be given to me under this Note will </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00469'  EndingX='00564'  StartingY='01972' Length='6'  Confidence='0,0,0,0,0,0'  >Unless</Word>\r\n<Word Height='00028' StartingX='00590'  EndingX='00738'  StartingY='01982' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >applicable</Word>\r\n<Word Height='00028' StartingX='00766'  EndingX='00795'  StartingY='01973' Length='3'  Confidence='0,0,0'  >law</Word>\r\n<Word Height='00028' StartingX='00835'  EndingX='00950'  StartingY='01983' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >requires</Word>\r\n<Word Height='00028' StartingX='00975'  EndingX='00975'  StartingY='01983' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='01003'  EndingX='01131'  StartingY='01974' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >different</Word>\r\n<Word Height='00028' StartingX='01153'  EndingX='01274'  StartingY='01983' Length='7'  Confidence='0,0,0,0,0,0,0'  >method,</Word>\r\n<Word Height='00028' StartingX='01294'  EndingX='01331'  StartingY='01984' Length='3'  Confidence='0,0,0'  >any</Word>\r\n<Word Height='00028' StartingX='01363'  EndingX='01442'  StartingY='01984' Length='6'  Confidence='0,0,0,0,0,0'  >notice</Word>\r\n<Word Height='00028' StartingX='01470'  EndingX='01519'  StartingY='01979' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='01541'  EndingX='01608'  StartingY='01985' Length='4'  Confidence='0,0,0,0'  >must</Word>\r\n<Word Height='00028' StartingX='01628'  EndingX='01649'  StartingY='01976' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='01678'  EndingX='01747'  StartingY='01985' Length='5'  Confidence='0,0,0,0,0'  >given</Word>\r\n<Word Height='00028' StartingX='01777'  EndingX='01788'  StartingY='01980' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01819'  EndingX='01850'  StartingY='01985' Length='2'  Confidence='0,0'  >me</Word>\r\n<Word Height='00028' StartingX='01877'  EndingX='01957'  StartingY='01986' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n<Word Height='00028' StartingX='01981'  EndingX='02024'  StartingY='01980' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='02048'  EndingX='02111'  StartingY='01977' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='02138'  EndingX='02191'  StartingY='01986' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='21.30' Height='00.31' >\r\n<LineText>~X[03.37]be given by delivering it or by mailing it by first class mail to me at the Property Address above or at a different </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00324'  EndingX='00346'  StartingY='02018' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='00376'  EndingX='00446'  StartingY='02028' Length='5'  Confidence='0,0,0,0,0'  >given</Word>\r\n<Word Height='00028' StartingX='00477'  EndingX='00498'  StartingY='02019' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='00531'  EndingX='00678'  StartingY='02019' Length='10'  Confidence='0,0,0,0,0,0,30,30,0,0'  >delivering</Word>\r\n<Word Height='00028' StartingX='00712'  EndingX='00722'  StartingY='02020' Length='2'  Confidence='0,0'  >it</Word>\r\n<Word Height='00028' StartingX='00746'  EndingX='00766'  StartingY='02029' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='00791'  EndingX='00811'  StartingY='02020' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='00844'  EndingX='00950'  StartingY='02029' Length='7'  Confidence='0,0,0,0,0,0,0'  >mailing</Word>\r\n<Word Height='00028' StartingX='00983'  EndingX='00993'  StartingY='02020' Length='2'  Confidence='0,0'  >it</Word>\r\n<Word Height='00028' StartingX='01016'  EndingX='01037'  StartingY='02020' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='01069'  EndingX='01125'  StartingY='02020' Length='5'  Confidence='0,0,0,0,0'  >first</Word>\r\n<Word Height='00028' StartingX='01148'  EndingX='01212'  StartingY='02030' Length='5'  Confidence='0,0,0,0,0'  >class</Word>\r\n<Word Height='00028' StartingX='01239'  EndingX='01302'  StartingY='02030' Length='4'  Confidence='0,0,0,0'  >mail</Word>\r\n<Word Height='00028' StartingX='01325'  EndingX='01336'  StartingY='02026' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01368'  EndingX='01400'  StartingY='02031' Length='2'  Confidence='0,0'  >me</Word>\r\n<Word Height='00028' StartingX='01429'  EndingX='01447'  StartingY='02031' Length='2'  Confidence='0,0'  >at</Word>\r\n<Word Height='00028' StartingX='01470'  EndingX='01502'  StartingY='02026' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01532'  EndingX='01652'  StartingY='02022' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Property</Word>\r\n<Word Height='00028' StartingX='01686'  EndingX='01804'  StartingY='02023' Length='7'  Confidence='0,0,0,0,0,0,0'  >Address</Word>\r\n<Word Height='00028' StartingX='01832'  EndingX='01912'  StartingY='02032' Length='5'  Confidence='0,0,0,0,0'  >above</Word>\r\n<Word Height='00028' StartingX='01942'  EndingX='01962'  StartingY='02032' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='01988'  EndingX='02006'  StartingY='02032' Length='2'  Confidence='0,0'  >at</Word>\r\n<Word Height='00028' StartingX='02030'  EndingX='02030'  StartingY='02032' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='02060'  EndingX='02191'  StartingY='02023' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >different</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='21.78' Height='00.31' >\r\n<LineText>~X[03.38]a~Y[00.01]d~Ydress ~Y[00.01]i~Yf ~Y[00.01]I g~Yive ~Y[00.01]t~Yhe ~Y[00.01]No~Yte ~Y[00.01]Ho~Yl~Y[00.02]der a no~Yt~Y[00.02]ice o~Yf my ~Y[00.03]d~Yi~Y[00.03]f~Yferent a~Y[00.04]d~Ydr~Y[00.04]e~Ys~Y[00.04]s~Y. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00325'  EndingX='00430'  StartingY='02075' Length='7'  Confidence='0,0,0,0,0,0,0'  >address</Word>\r\n<Word Height='00028' StartingX='00456'  EndingX='00467'  StartingY='02066' Length='2'  Confidence='0,0'  >if</Word>\r\n<Word Height='00028' StartingX='00490'  EndingX='00490'  StartingY='02067' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00513'  EndingX='00565'  StartingY='02075' Length='4'  Confidence='0,0,0,0'  >give</Word>\r\n<Word Height='00028' StartingX='00592'  EndingX='00624'  StartingY='02070' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00650'  EndingX='00712'  StartingY='02067' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00740'  EndingX='00837'  StartingY='02067' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='00861'  EndingX='00861'  StartingY='02076' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00889'  EndingX='00970'  StartingY='02076' Length='6'  Confidence='0,0,0,0,0,0'  >notice</Word>\r\n<Word Height='00028' StartingX='00997'  EndingX='01017'  StartingY='02076' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01040'  EndingX='01071'  StartingY='02077' Length='2'  Confidence='0,0'  >my</Word>\r\n<Word Height='00028' StartingX='01102'  EndingX='01228'  StartingY='02068' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >different</Word>\r\n<Word Height='00028' StartingX='01250'  EndingX='01372'  StartingY='02077' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >address.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='22.34' Height='00.31' >\r\n<LineText>~X[04.88]Any notice that must be given to the Note Holder under this Note will be given by delivering it or by </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00469'  EndingX='00519'  StartingY='02119' Length='3'  Confidence='0,20,0'  >Any</Word>\r\n<Word Height='00028' StartingX='00553'  EndingX='00638'  StartingY='02129' Length='6'  Confidence='0,0,0,0,0,0'  >notice</Word>\r\n<Word Height='00028' StartingX='00669'  EndingX='00720'  StartingY='02123' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='00745'  EndingX='00814'  StartingY='02129' Length='4'  Confidence='0,0,0,0'  >must</Word>\r\n<Word Height='00028' StartingX='00838'  EndingX='00860'  StartingY='02120' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='00891'  EndingX='00963'  StartingY='02129' Length='5'  Confidence='0,0,0,0,0'  >given</Word>\r\n<Word Height='00028' StartingX='00996'  EndingX='01009'  StartingY='02124' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01042'  EndingX='01076'  StartingY='02125' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01105'  EndingX='01169'  StartingY='02122' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='01200'  EndingX='01301'  StartingY='02122' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='01328'  EndingX='01408'  StartingY='02131' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n<Word Height='00028' StartingX='01434'  EndingX='01480'  StartingY='02126' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01508'  EndingX='01571'  StartingY='02123' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='01601'  EndingX='01656'  StartingY='02132' Length='4'  Confidence='0,0,0,0'  >will</Word>\r\n<Word Height='00028' StartingX='01680'  EndingX='01702'  StartingY='02122' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='01732'  EndingX='01804'  StartingY='02132' Length='5'  Confidence='0,0,0,0,0'  >given</Word>\r\n<Word Height='00028' StartingX='01837'  EndingX='01859'  StartingY='02123' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='01893'  EndingX='02042'  StartingY='02123' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >delivering</Word>\r\n<Word Height='00028' StartingX='02076'  EndingX='02088'  StartingY='02123' Length='2'  Confidence='0,0'  >it</Word>\r\n<Word Height='00028' StartingX='02113'  EndingX='02133'  StartingY='02132' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='02160'  EndingX='02181'  StartingY='02123' Length='2'  Confidence='0,0'  >by</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='22.83' Height='00.40' >\r\n<LineText>~X[03.37]ma~Y[00.00]i~Yl~Y[00.00]ing ~Yi~Y[00.00]t ~Yby ~Y[00.01]firs~Yt c~Y[00.01]lass ma~Yi~Y[00.01]l to t~Yhe ~Y[00.02]Note ~YHo~Y[00.02]l~Yder at t~Y[00.02]he a~Yd~Y[00.03]dress state~Yd ~Y[00.04]in ~YSect~Y[00.03]ion ~Y3~Y[00.04](~YA~Y[00.04]) a~Yb~Y[00.05]ove ~Yor at a ~Y[00.05]d~Yi~Y[00.04]f~Yf~Y[00.05]er~Yent a~Y[00.05]d~Yd~Y[00.05]r~Ye~Y[00.05]s~Ys ~Y[00.05]i~Yf </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00324'  EndingX='00427'  StartingY='02175' Length='7'  Confidence='0,0,0,0,0,0,0'  >mailing</Word>\r\n<Word Height='00028' StartingX='00458'  EndingX='00468'  StartingY='02166' Length='2'  Confidence='0,0'  >it</Word>\r\n<Word Height='00028' StartingX='00489'  EndingX='00509'  StartingY='02166' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00028' StartingX='00540'  EndingX='00593'  StartingY='02166' Length='5'  Confidence='20,50,50,0,0'  >first</Word>\r\n<Word Height='00028' StartingX='00615'  EndingX='00677'  StartingY='02176' Length='5'  Confidence='0,0,0,0,0'  >class</Word>\r\n<Word Height='00028' StartingX='00703'  EndingX='00764'  StartingY='02176' Length='4'  Confidence='0,0,0,0'  >mail</Word>\r\n<Word Height='00028' StartingX='00784'  EndingX='00796'  StartingY='02171' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00825'  EndingX='00857'  StartingY='02171' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00884'  EndingX='00945'  StartingY='02168' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00972'  EndingX='01070'  StartingY='02168' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='01095'  EndingX='01112'  StartingY='02177' Length='2'  Confidence='0,0'  >at</Word>\r\n<Word Height='00028' StartingX='01133'  EndingX='01164'  StartingY='02172' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01192'  EndingX='01297'  StartingY='02178' Length='7'  Confidence='0,0,0,0,0,0,0'  >address</Word>\r\n<Word Height='00028' StartingX='01323'  EndingX='01395'  StartingY='02178' Length='6'  Confidence='0,0,0,0,0,0'  >stated</Word>\r\n<Word Height='00028' StartingX='01425'  EndingX='01435'  StartingY='02169' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='01467'  EndingX='01565'  StartingY='02169' Length='7'  Confidence='0,0,0,0,0,0,0'  >Section</Word>\r\n<Word Height='00028' StartingX='01596'  EndingX='01658'  StartingY='02170' Length='4'  Confidence='0,0,0,30'  >3(A)</Word>\r\n<Word Height='00028' StartingX='01682'  EndingX='01760'  StartingY='02179' Length='5'  Confidence='0,10,0,0,0'  >above</Word>\r\n<Word Height='00028' StartingX='01787'  EndingX='01807'  StartingY='02179' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='01831'  EndingX='01848'  StartingY='02179' Length='2'  Confidence='0,0'  >at</Word>\r\n<Word Height='00028' StartingX='01870'  EndingX='01870'  StartingY='02179' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='01898'  EndingX='02024'  StartingY='02170' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >different</Word>\r\n<Word Height='00028' StartingX='02046'  EndingX='02152'  StartingY='02179' Length='7'  Confidence='0,0,0,0,0,0,0'  >address</Word>\r\n<Word Height='00028' StartingX='02177'  EndingX='02188'  StartingY='02170' Length='2'  Confidence='0,0'  >if</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='23.32' Height='00.31' >\r\n<LineText>~X[03.37]I am given a notice of that different address. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00324'  EndingX='00324'  StartingY='02214' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00348'  EndingX='00365'  StartingY='02222' Length='2'  Confidence='0,0'  >am</Word>\r\n<Word Height='00028' StartingX='00407'  EndingX='00475'  StartingY='02222' Length='5'  Confidence='0,0,0,0,0'  >given</Word>\r\n<Word Height='00028' StartingX='00506'  EndingX='00506'  StartingY='02223' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00533'  EndingX='00615'  StartingY='02223' Length='6'  Confidence='0,0,0,0,0,0'  >notice</Word>\r\n<Word Height='00028' StartingX='00642'  EndingX='00663'  StartingY='02223' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='00686'  EndingX='00735'  StartingY='02218' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='00756'  EndingX='00883'  StartingY='02214' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >different</Word>\r\n<Word Height='00028' StartingX='00905'  EndingX='01026'  StartingY='02224' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >address.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='23.88' Height='00.31' >\r\n<LineText>~X[03.37]8. OBLIGATIONS OF PERSONS UNDER THIS NOTE </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00324'  EndingX='00345'  StartingY='02267' Length='2'  Confidence='20,30'  >8.</Word>\r\n<Word Height='00028' StartingX='00365'  EndingX='00628'  StartingY='02267' Length='11'  Confidence='40,20,20,20,20,20,20,20,20,20,20'  >OBLIGATIONS</Word>\r\n<Word Height='00028' StartingX='00661'  EndingX='00691'  StartingY='02268' Length='2'  Confidence='0,0'  >OF</Word>\r\n<Word Height='00028' StartingX='00726'  EndingX='00890'  StartingY='02268' Length='7'  Confidence='0,0,0,0,0,0,0'  >PERSONS</Word>\r\n<Word Height='00028' StartingX='00921'  EndingX='01035'  StartingY='02269' Length='5'  Confidence='0,0,0,0,0'  >UNDER</Word>\r\n<Word Height='00028' StartingX='01074'  EndingX='01148'  StartingY='02269' Length='4'  Confidence='0,0,0,0'  >THIS</Word>\r\n<Word Height='00028' StartingX='01179'  EndingX='01266'  StartingY='02270' Length='4'  Confidence='0,0,0,0'  >NOTE</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='24.43' Height='00.31' >\r\n<LineText>~X[04.89]If more than one person signs this Note, each person is fully and personally obligated to keep all of the </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00470'  EndingX='00483'  StartingY='02320' Length='2'  Confidence='0,0'  >If</Word>\r\n<Word Height='00028' StartingX='00508'  EndingX='00575'  StartingY='02329' Length='4'  Confidence='0,0,0,0'  >more</Word>\r\n<Word Height='00028' StartingX='00605'  EndingX='00656'  StartingY='02324' Length='4'  Confidence='0,0,0,0'  >than</Word>\r\n<Word Height='00028' StartingX='00689'  EndingX='00730'  StartingY='02329' Length='3'  Confidence='0,0,0'  >one</Word>\r\n<Word Height='00028' StartingX='00760'  EndingX='00849'  StartingY='02330' Length='6'  Confidence='0,0,0,0,0,0'  >person</Word>\r\n<Word Height='00028' StartingX='00884'  EndingX='00953'  StartingY='02330' Length='5'  Confidence='0,0,0,0,0'  >signs</Word>\r\n<Word Height='00028' StartingX='00980'  EndingX='01025'  StartingY='02325' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01051'  EndingX='01133'  StartingY='02322' Length='5'  Confidence='0,0,0,0,0'  >Note,</Word>\r\n<Word Height='00028' StartingX='01155'  EndingX='01210'  StartingY='02331' Length='4'  Confidence='0,0,0,0'  >each</Word>\r\n<Word Height='00028' StartingX='01242'  EndingX='01332'  StartingY='02331' Length='6'  Confidence='0,0,0,0,0,0'  >person</Word>\r\n<Word Height='00028' StartingX='01365'  EndingX='01377'  StartingY='02322' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='01405'  EndingX='01460'  StartingY='02322' Length='5'  Confidence='30,30,0,0,0'  >fully</Word>\r\n<Word Height='00028' StartingX='01494'  EndingX='01533'  StartingY='02332' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='01565'  EndingX='01716'  StartingY='02332' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >personally</Word>\r\n<Word Height='00028' StartingX='01750'  EndingX='01883'  StartingY='02333' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >obligated</Word>\r\n<Word Height='00028' StartingX='01916'  EndingX='01928'  StartingY='02328' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01961'  EndingX='02017'  StartingY='02323' Length='4'  Confidence='0,0,0,0'  >keep</Word>\r\n<Word Height='00028' StartingX='02050'  EndingX='02082'  StartingY='02333' Length='3'  Confidence='0,0,0'  >all</Word>\r\n<Word Height='00028' StartingX='02104'  EndingX='02126'  StartingY='02333' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='02151'  EndingX='02183'  StartingY='02328' Length='3'  Confidence='0,0,0'  >the</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='24.92' Height='00.31' >\r\n<LineText>~X[03.37]prom~Y[00.00]ises ma~Yde ~Y[00.00]in t~Yh~Y[00.00]is ~YNote, ~Y[00.01]inc~Ylu~Y[00.02]d~Ying t~Y[00.02]he prom~Yise to pay t~Y[00.03]he ~Yfu~Y[00.03]l~Yl amount owe~Y[00.04]d. Any person who is a guarantor, ~Y</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00324'  EndingX='00458'  StartingY='02376' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >promises</Word>\r\n<Word Height='00028' StartingX='00486'  EndingX='00557'  StartingY='02376' Length='4'  Confidence='0,0,0,0'  >made</Word>\r\n<Word Height='00028' StartingX='00588'  EndingX='00598'  StartingY='02367' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='00631'  EndingX='00676'  StartingY='02371' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='00703'  EndingX='00785'  StartingY='02368' Length='5'  Confidence='0,0,0,0,0'  >Note,</Word>\r\n<Word Height='00028' StartingX='00808'  EndingX='00943'  StartingY='02368' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >including</Word>\r\n<Word Height='00028' StartingX='00976'  EndingX='01008'  StartingY='02372' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01037'  EndingX='01153'  StartingY='02378' Length='7'  Confidence='0,0,0,0,0,0,0'  >promise</Word>\r\n<Word Height='00028' StartingX='01183'  EndingX='01195'  StartingY='02373' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01227'  EndingX='01265'  StartingY='02379' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00028' StartingX='01299'  EndingX='01332'  StartingY='02374' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01362'  EndingX='01409'  StartingY='02370' Length='4'  Confidence='30,30,0,0'  >full</Word>\r\n<Word Height='00028' StartingX='01430'  EndingX='01542'  StartingY='02379' Length='6'  Confidence='0,0,0,0,0,0'  >amount</Word>\r\n<Word Height='00028' StartingX='01566'  EndingX='01656'  StartingY='02380' Length='5'  Confidence='0,0,0,0,0'  >owed.</Word>\r\n<Word Height='00028' StartingX='01677'  EndingX='01727'  StartingY='02372' Length='3'  Confidence='0,0,0'  >Any</Word>\r\n<Word Height='00028' StartingX='01760'  EndingX='01850'  StartingY='02380' Length='6'  Confidence='0,0,0,0,0,0'  >person</Word>\r\n<Word Height='00028' StartingX='01882'  EndingX='01933'  StartingY='02380' Length='3'  Confidence='0,0,0'  >who</Word>\r\n<Word Height='00028' StartingX='01967'  EndingX='01978'  StartingY='02371' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='02005'  EndingX='02005'  StartingY='02381' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='02036'  EndingX='02196'  StartingY='02380' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >guarantor,</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='25.41' Height='00.33' >\r\n<LineText>~X[03.37]surety or en~Y[00.01]dorser o~Yf t~Y[00.01]h~Yis ~Y[00.01]Note ~Yis a~Y[00.01]lso o~Yb~Y[00.01]l~Yigate~Y[00.02]d to ~Ydo t~Y[00.02]hese t~Yh~Y[00.03]ings. ~YAny person w~Y[00.03]ho ta~Ykes over t~Y[00.04]he~Yse ~Y[00.05]o~Yb~Y[00.04]ligati~Yon~Y[00.05]s~Y, </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00324'  EndingX='00393'  StartingY='02423' Length='6'  Confidence='0,0,0,0,30,30'  >surety</Word>\r\n<Word Height='00028' StartingX='00437'  EndingX='00458'  StartingY='02423' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='00483'  EndingX='00611'  StartingY='02423' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >endorser</Word>\r\n<Word Height='00028' StartingX='00637'  EndingX='00658'  StartingY='02424' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='00684'  EndingX='00728'  StartingY='02419' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='00754'  EndingX='00817'  StartingY='02416' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00848'  EndingX='00859'  StartingY='02415' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='00887'  EndingX='00933'  StartingY='02425' Length='4'  Confidence='0,0,0,0'  >also</Word>\r\n<Word Height='00028' StartingX='00965'  EndingX='01099'  StartingY='02425' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >obligated</Word>\r\n<Word Height='00028' StartingX='01131'  EndingX='01142'  StartingY='02420' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01174'  EndingX='01195'  StartingY='02416' Length='2'  Confidence='0,0'  >do</Word>\r\n<Word Height='00028' StartingX='01227'  EndingX='01294'  StartingY='02421' Length='5'  Confidence='0,0,0,0,0'  >these</Word>\r\n<Word Height='00028' StartingX='01324'  EndingX='01425'  StartingY='02421' Length='7'  Confidence='0,0,0,0,0,0,0'  >things.</Word>\r\n<Word Height='00028' StartingX='01446'  EndingX='01496'  StartingY='02418' Length='3'  Confidence='0,0,0'  >Any</Word>\r\n<Word Height='00028' StartingX='01528'  EndingX='01617'  StartingY='02427' Length='6'  Confidence='0,0,0,0,0,0'  >person</Word>\r\n<Word Height='00028' StartingX='01649'  EndingX='01700'  StartingY='02427' Length='3'  Confidence='0,0,0'  >who</Word>\r\n<Word Height='00028' StartingX='01732'  EndingX='01801'  StartingY='02422' Length='5'  Confidence='0,0,0,0,0'  >takes</Word>\r\n<Word Height='00028' StartingX='01829'  EndingX='01888'  StartingY='02427' Length='4'  Confidence='0,0,0,0'  >over</Word>\r\n<Word Height='00028' StartingX='01914'  EndingX='01981'  StartingY='02422' Length='5'  Confidence='0,0,0,0,0'  >these</Word>\r\n<Word Height='00028' StartingX='02010'  EndingX='02196'  StartingY='02427' Length='12'  Confidence='0,0,0,0,0,0,0,0,0,0,0,0'  >obligations,</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='25.90' Height='00.30' >\r\n<LineText>~X[03.38]including the obligations of a guarantor, surety or endorser of this Note, is also obligated to keep all of the promises </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00325'  EndingX='00457'  StartingY='02461' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >including</Word>\r\n<Word Height='00028' StartingX='00487'  EndingX='00519'  StartingY='02466' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00547'  EndingX='00712'  StartingY='02471' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >obligations</Word>\r\n<Word Height='00028' StartingX='00738'  EndingX='00758'  StartingY='02472' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='00781'  EndingX='00781'  StartingY='02472' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00810'  EndingX='00966'  StartingY='02472' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >guarantor,</Word>\r\n<Word Height='00028' StartingX='00986'  EndingX='01063'  StartingY='02472' Length='6'  Confidence='0,0,0,0,0,0'  >surety</Word>\r\n<Word Height='00028' StartingX='01094'  EndingX='01114'  StartingY='02473' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='01138'  EndingX='01263'  StartingY='02473' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >endorser</Word>\r\n<Word Height='00028' StartingX='01288'  EndingX='01308'  StartingY='02473' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01331'  EndingX='01375'  StartingY='02468' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01399'  EndingX='01479'  StartingY='02465' Length='5'  Confidence='0,0,0,0,0'  >Note,</Word>\r\n<Word Height='00028' StartingX='01499'  EndingX='01510'  StartingY='02465' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='01536'  EndingX='01580'  StartingY='02474' Length='4'  Confidence='0,0,0,0'  >also</Word>\r\n<Word Height='00028' StartingX='01611'  EndingX='01741'  StartingY='02474' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >obligated</Word>\r\n<Word Height='00028' StartingX='01771'  EndingX='01783'  StartingY='02469' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01813'  EndingX='01869'  StartingY='02466' Length='4'  Confidence='0,0,0,0'  >keep</Word>\r\n<Word Height='00028' StartingX='01900'  EndingX='01930'  StartingY='02475' Length='3'  Confidence='0,0,0'  >all</Word>\r\n<Word Height='00028' StartingX='01951'  EndingX='01971'  StartingY='02475' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01995'  EndingX='02026'  StartingY='02470' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='02054'  EndingX='02185'  StartingY='02475' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >promises</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='26.38' Height='00.31' >\r\n<LineText>~X[03.37]ma~Y[00.01]de ~Yin ~Y[00.01]t~Yh~Y[00.00]is ~YNo~Y[00.01]te. ~YT~Y[00.01]he ~YNote ~Y[00.01]Ho~Yl~Y[00.02]der may en~Yforce ~Y[00.02]its r~Yig~Y[00.03]hts un~Yder t~Y[00.03]h~Yis ~Y[00.03]Note aga~Yin~Y[00.05]st eac~Yh p~Y[00.05]er~Ys~Y[00.05]on ~Yin~Y[00.05]d~Yiv~Y[00.04]i~Yd~Y[00.05]u~Ya~Y[00.05]l~Yl~Y[00.05]y ~Yo~Y[00.05]r ~Y</LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00324'  EndingX='00396'  StartingY='02517' Length='4'  Confidence='0,0,0,0'  >made</Word>\r\n<Word Height='00028' StartingX='00427'  EndingX='00438'  StartingY='02509' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='00472'  EndingX='00517'  StartingY='02513' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='00544'  EndingX='00628'  StartingY='02509' Length='5'  Confidence='0,0,0,0,0'  >Note.</Word>\r\n<Word Height='00028' StartingX='00650'  EndingX='00697'  StartingY='02510' Length='3'  Confidence='0,0,0'  >The</Word>\r\n<Word Height='00028' StartingX='00726'  EndingX='00790'  StartingY='02510' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00821'  EndingX='00923'  StartingY='02510' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='00950'  EndingX='00999'  StartingY='02519' Length='3'  Confidence='0,0,0'  >may</Word>\r\n<Word Height='00028' StartingX='01034'  EndingX='01141'  StartingY='02520' Length='7'  Confidence='0,0,0,0,0,0,0'  >enforce</Word>\r\n<Word Height='00028' StartingX='01173'  EndingX='01196'  StartingY='02511' Length='3'  Confidence='0,0,0'  >its</Word>\r\n<Word Height='00028' StartingX='01224'  EndingX='01304'  StartingY='02520' Length='6'  Confidence='0,0,0,0,0,0'  >rights</Word>\r\n<Word Height='00028' StartingX='01332'  EndingX='01413'  StartingY='02520' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n<Word Height='00028' StartingX='01439'  EndingX='01484'  StartingY='02516' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01511'  EndingX='01575'  StartingY='02512' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='01606'  EndingX='01713'  StartingY='02521' Length='7'  Confidence='0,0,0,0,0,0,0'  >against</Word>\r\n<Word Height='00028' StartingX='01737'  EndingX='01793'  StartingY='02521' Length='4'  Confidence='0,0,0,0'  >each</Word>\r\n<Word Height='00028' StartingX='01826'  EndingX='01917'  StartingY='02522' Length='6'  Confidence='0,0,0,0,0,0'  >person</Word>\r\n<Word Height='00028' StartingX='01952'  EndingX='02132'  StartingY='02513' Length='12'  Confidence='0,0,0,0,0,0,0,0,0,0,0,0'  >individually</Word>\r\n<Word Height='00028' StartingX='02166'  EndingX='02187'  StartingY='02522' Length='2'  Confidence='0,0'  >or</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='26.87' Height='00.30' >\r\n<LineText>~X[03.37]aga~Y[00.00]inst a~Yl~Y[00.00]l o~Yf us toget~Y[00.01]her. ~YT~Y[00.01]h~Yis means t~Y[00.02]hat any one o~Yf us may ~Y[00.03]be requ~Yire~Y[00.04]d to pay a~Yl~Y[00.03]l o~Yf t~Y[00.04]he am~Yount~Y[00.05]s ~Yowe~Y[00.05]d ~Yun~Y[00.05]d~Yer ~Y[00.05]thi~Ys </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00324'  EndingX='00428'  StartingY='02564' Length='7'  Confidence='0,0,0,0,0,0,0'  >against</Word>\r\n<Word Height='00028' StartingX='00451'  EndingX='00480'  StartingY='02565' Length='3'  Confidence='0,0,0'  >all</Word>\r\n<Word Height='00028' StartingX='00502'  EndingX='00523'  StartingY='02565' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='00546'  EndingX='00567'  StartingY='02566' Length='2'  Confidence='0,0'  >us</Word>\r\n<Word Height='00028' StartingX='00594'  EndingX='00729'  StartingY='02560' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >together.</Word>\r\n<Word Height='00028' StartingX='00748'  EndingX='00805'  StartingY='02557' Length='4'  Confidence='0,0,0,0'  >This</Word>\r\n<Word Height='00028' StartingX='00832'  EndingX='00920'  StartingY='02566' Length='5'  Confidence='0,0,0,0,0'  >means</Word>\r\n<Word Height='00028' StartingX='00946'  EndingX='00995'  StartingY='02562' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='01018'  EndingX='01055'  StartingY='02567' Length='3'  Confidence='0,0,0'  >any</Word>\r\n<Word Height='00028' StartingX='01087'  EndingX='01128'  StartingY='02567' Length='3'  Confidence='0,0,0'  >one</Word>\r\n<Word Height='00028' StartingX='01157'  EndingX='01177'  StartingY='02567' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01201'  EndingX='01222'  StartingY='02568' Length='2'  Confidence='0,0'  >us</Word>\r\n<Word Height='00028' StartingX='01248'  EndingX='01297'  StartingY='02567' Length='3'  Confidence='0,0,0'  >may</Word>\r\n<Word Height='00028' StartingX='01328'  EndingX='01349'  StartingY='02558' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='01377'  EndingX='01491'  StartingY='02568' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >required</Word>\r\n<Word Height='00028' StartingX='01523'  EndingX='01534'  StartingY='02563' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01565'  EndingX='01603'  StartingY='02568' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00028' StartingX='01634'  EndingX='01665'  StartingY='02568' Length='3'  Confidence='0,0,0'  >all</Word>\r\n<Word Height='00028' StartingX='01686'  EndingX='01707'  StartingY='02568' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01730'  EndingX='01762'  StartingY='02563' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01791'  EndingX='01913'  StartingY='02569' Length='7'  Confidence='0,0,0,0,0,0,0'  >amounts</Word>\r\n<Word Height='00028' StartingX='01939'  EndingX='02007'  StartingY='02569' Length='4'  Confidence='0,0,0,0'  >owed</Word>\r\n<Word Height='00028' StartingX='02038'  EndingX='02118'  StartingY='02570' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n<Word Height='00028' StartingX='02141'  EndingX='02185'  StartingY='02564' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='27.36' Height='00.29' >\r\n<LineText>~X[03.35]Note. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00404'  StartingY='02603' Length='5'  Confidence='0,0,0,0,0'  >Note.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='27.90' Height='00.30' >\r\n<LineText>~X[03.37]3Jl:%\\ '/ 91:7.y </LineText>\r\n<Words>\r\n<Word Height='00027' StartingX='00324'  EndingX='00418'  StartingY='02656' Length='6'  Confidence='198,188,198,198,198,198,198'  >3Jl:%\\</Word>\r\n<Word Height='00027' StartingX='00443'  EndingX='00463'  StartingY='02658' Length='2'  Confidence='198,198,198'  >'/</Word>\r\n<Word Height='00027' StartingX='00488'  EndingX='00539'  StartingY='02659' Length='6'  Confidence='80,60,70,80,80,80'  >91:7.y</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='28.48' Height='00.32' >\r\n<LineText>~X[04.88]I and any other person who has obligations under this Note waive the rights of Presentment and Notice of </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00469'  EndingX='00469'  StartingY='02710' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00493'  EndingX='00531'  StartingY='02718' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='00562'  EndingX='00600'  StartingY='02718' Length='3'  Confidence='0,0,0'  >any</Word>\r\n<Word Height='00028' StartingX='00632'  EndingX='00702'  StartingY='02718' Length='5'  Confidence='0,0,0,0,0'  >other</Word>\r\n<Word Height='00028' StartingX='00726'  EndingX='00815'  StartingY='02719' Length='6'  Confidence='0,0,0,0,0,0'  >person</Word>\r\n<Word Height='00028' StartingX='00846'  EndingX='00897'  StartingY='02719' Length='3'  Confidence='0,0,0'  >who</Word>\r\n<Word Height='00028' StartingX='00927'  EndingX='00967'  StartingY='02710' Length='3'  Confidence='0,0,0'  >has</Word>\r\n<Word Height='00028' StartingX='00993'  EndingX='01159'  StartingY='02719' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >obligations</Word>\r\n<Word Height='00028' StartingX='01184'  EndingX='01263'  StartingY='02720' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n<Word Height='00028' StartingX='01289'  EndingX='01332'  StartingY='02715' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01357'  EndingX='01419'  StartingY='02712' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='01446'  EndingX='01527'  StartingY='02721' Length='5'  Confidence='0,0,0,0,0'  >waive</Word>\r\n<Word Height='00028' StartingX='01555'  EndingX='01587'  StartingY='02715' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01615'  EndingX='01693'  StartingY='02721' Length='6'  Confidence='0,0,0,0,0,0'  >rights</Word>\r\n<Word Height='00028' StartingX='01720'  EndingX='01740'  StartingY='02721' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01764'  EndingX='01955'  StartingY='02713' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >Presentment</Word>\r\n<Word Height='00028' StartingX='01977'  EndingX='02015'  StartingY='02722' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='02046'  EndingX='02137'  StartingY='02713' Length='6'  Confidence='0,0,0,0,0,0'  >Notice</Word>\r\n<Word Height='00028' StartingX='02166'  EndingX='02187'  StartingY='02722' Length='2'  Confidence='0,0'  >of</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='28.96' Height='00.31' >\r\n<LineText>~X[03.36]Dishonor. &quot;Presentment&quot; means the right to require the Note Holder to demand payment of amounts due. &quot;Notice of </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00323'  EndingX='00475'  StartingY='02756' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >Dishonor.</Word>\r\n<Word Height='00028' StartingX='00495'  EndingX='00712'  StartingY='02757' Length='23'  Confidence='0,0,0,0,0,0,0,0,0,0,0,0,0'  >&quot;Presentment&quot;</Word>\r\n<Word Height='00028' StartingX='00737'  EndingX='00825'  StartingY='02766' Length='5'  Confidence='0,0,0,0,0'  >means</Word>\r\n<Word Height='00028' StartingX='00851'  EndingX='00883'  StartingY='02761' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00911'  EndingX='00975'  StartingY='02757' Length='5'  Confidence='30,30,0,0,0'  >right</Word>\r\n<Word Height='00028' StartingX='00997'  EndingX='01008'  StartingY='02762' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01038'  EndingX='01136'  StartingY='02767' Length='7'  Confidence='0,0,0,0,0,0,0'  >require</Word>\r\n<Word Height='00028' StartingX='01163'  EndingX='01195'  StartingY='02762' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01223'  EndingX='01284'  StartingY='02759' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='01312'  EndingX='01410'  StartingY='02759' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='01433'  EndingX='01444'  StartingY='02763' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01475'  EndingX='01582'  StartingY='02759' Length='6'  Confidence='0,0,0,0,0,0'  >demand</Word>\r\n<Word Height='00028' StartingX='01612'  EndingX='01739'  StartingY='02769' Length='7'  Confidence='0,0,0,0,0,0,0'  >payment</Word>\r\n<Word Height='00028' StartingX='01762'  EndingX='01782'  StartingY='02769' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01806'  EndingX='01927'  StartingY='02769' Length='7'  Confidence='0,0,0,0,0,0,0'  >amounts</Word>\r\n<Word Height='00028' StartingX='01953'  EndingX='02013'  StartingY='02760' Length='4'  Confidence='0,0,0,0'  >due.</Word>\r\n<Word Height='00028' StartingX='02033'  EndingX='02138'  StartingY='02761' Length='12'  Confidence='0,0,0,0,0,0,0'  >&quot;Notice</Word>\r\n<Word Height='00028' StartingX='02166'  EndingX='02188'  StartingY='02770' Length='2'  Confidence='0,0'  >of</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='29.45' Height='00.31' >\r\n<LineText>~X[03.36]Dishonor&quot; means the right to require the Note Holder to give notice to other persons that amounts due have not been </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00323'  EndingX='00475'  StartingY='02804' Length='14'  Confidence='0,0,0,0,0,0,0,0,0'  >Dishonor&quot;</Word>\r\n<Word Height='00028' StartingX='00499'  EndingX='00587'  StartingY='02813' Length='5'  Confidence='0,0,0,0,0'  >means</Word>\r\n<Word Height='00028' StartingX='00613'  EndingX='00645'  StartingY='02808' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00672'  EndingX='00737'  StartingY='02813' Length='5'  Confidence='0,0,0,0,0'  >right</Word>\r\n<Word Height='00028' StartingX='00759'  EndingX='00770'  StartingY='02808' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00800'  EndingX='00897'  StartingY='02813' Length='7'  Confidence='0,0,0,0,0,0,0'  >require</Word>\r\n<Word Height='00028' StartingX='00925'  EndingX='00956'  StartingY='02808' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00983'  EndingX='01045'  StartingY='02805' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='01072'  EndingX='01171'  StartingY='02806' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='01194'  EndingX='01205'  StartingY='02809' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01236'  EndingX='01288'  StartingY='02814' Length='4'  Confidence='0,0,0,0'  >give</Word>\r\n<Word Height='00028' StartingX='01315'  EndingX='01396'  StartingY='02815' Length='6'  Confidence='0,0,0,0,0,0'  >notice</Word>\r\n<Word Height='00028' StartingX='01423'  EndingX='01434'  StartingY='02810' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='01464'  EndingX='01533'  StartingY='02815' Length='5'  Confidence='0,0,0,0,0'  >other</Word>\r\n<Word Height='00028' StartingX='01556'  EndingX='01665'  StartingY='02815' Length='7'  Confidence='0,0,0,0,0,0,0'  >persons</Word>\r\n<Word Height='00028' StartingX='01690'  EndingX='01739'  StartingY='02810' Length='4'  Confidence='0,0,0,0'  >that</Word>\r\n<Word Height='00028' StartingX='01761'  EndingX='01882'  StartingY='02816' Length='7'  Confidence='0,0,0,0,30,0,0'  >amounts</Word>\r\n<Word Height='00028' StartingX='01907'  EndingX='01948'  StartingY='02807' Length='3'  Confidence='0,0,0'  >due</Word>\r\n<Word Height='00028' StartingX='01975'  EndingX='02034'  StartingY='02807' Length='4'  Confidence='0,0,0,0'  >have</Word>\r\n<Word Height='00028' StartingX='02062'  EndingX='02102'  StartingY='02816' Length='3'  Confidence='0,0,0'  >not</Word>\r\n<Word Height='00028' StartingX='02124'  EndingX='02179'  StartingY='02807' Length='4'  Confidence='0,0,0,0'  >been</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='29.94' Height='00.31' >\r\n<LineText>~X[03.35]pa~Y[00.00]i~Yd. </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00394'  StartingY='02859' Length='5'  Confidence='0,0,0,0,0'  >paid.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='30.49' Height='00.30' >\r\n<LineText>~X[03.38]10. UNIFORM SECURED NOTE </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00325'  EndingX='00364'  StartingY='02904' Length='3'  Confidence='0,0,0'  >10.</Word>\r\n<Word Height='00028' StartingX='00383'  EndingX='00541'  StartingY='02904' Length='7'  Confidence='0,0,0,30,0,0,0'  >UNIFORM</Word>\r\n<Word Height='00028' StartingX='00590'  EndingX='00752'  StartingY='02904' Length='7'  Confidence='0,0,0,0,0,0,0'  >SECURED</Word>\r\n<Word Height='00028' StartingX='00791'  EndingX='00879'  StartingY='02905' Length='4'  Confidence='0,0,0,0'  >NOTE</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='31.04' Height='00.40' >\r\n<LineText>~X[04.86]This Note is a uniform instrument with limited variations in some jurisdictions. In addition to the </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00467'  EndingX='00528'  StartingY='02957' Length='4'  Confidence='0,0,0,0'  >This</Word>\r\n<Word Height='00028' StartingX='00557'  EndingX='00623'  StartingY='02957' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00657'  EndingX='00669'  StartingY='02957' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='00701'  EndingX='00701'  StartingY='02966' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00733'  EndingX='00839'  StartingY='02967' Length='7'  Confidence='0,0,0,0,0,0,0'  >uniform</Word>\r\n<Word Height='00028' StartingX='00888'  EndingX='01059'  StartingY='02958' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >instrument</Word>\r\n<Word Height='00028' StartingX='01086'  EndingX='01141'  StartingY='02968' Length='4'  Confidence='0,0,0,0'  >with</Word>\r\n<Word Height='00028' StartingX='01178'  EndingX='01279'  StartingY='02958' Length='7'  Confidence='0,0,0,0,0,0,0'  >limited</Word>\r\n<Word Height='00028' StartingX='01314'  EndingX='01468'  StartingY='02968' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >variations</Word>\r\n<Word Height='00028' StartingX='01499'  EndingX='01511'  StartingY='02959' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='01547'  EndingX='01618'  StartingY='02969' Length='4'  Confidence='0,0,0,0'  >some</Word>\r\n<Word Height='00028' StartingX='01647'  EndingX='01868'  StartingY='02960' Length='14'  Confidence='0,0,0,0,0,0,0,0,0,0,0,0,0,0'  >jurisdictions.</Word>\r\n<Word Height='00028' StartingX='01893'  EndingX='01907'  StartingY='02961' Length='2'  Confidence='0,0'  >In</Word>\r\n<Word Height='00028' StartingX='01943'  EndingX='02063'  StartingY='02970' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >addition</Word>\r\n<Word Height='00028' StartingX='02100'  EndingX='02112'  StartingY='02965' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='02148'  EndingX='02182'  StartingY='02964' Length='3'  Confidence='0,0,0'  >the</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='31.53' Height='00.40' >\r\n<LineText>~X[03.35]protect~Y[00.01]ions g~Yiven to t~Y[00.01]he ~YNote ~Y[00.02]Ho~Yl~Y[00.02]der un~Yder t~Y[00.02]h~Yis ~Y[00.03]Note, a ~YMortgage, ~Y[00.04]Dee~Yd o~Y[00.04]f Trust or Secur~Yity Dee~Y[00.05]d ~Y(the &quot;Security </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00490'  StartingY='03013' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >protections</Word>\r\n<Word Height='00028' StartingX='00518'  EndingX='00589'  StartingY='03013' Length='5'  Confidence='0,0,0,0,0'  >given</Word>\r\n<Word Height='00028' StartingX='00621'  EndingX='00633'  StartingY='03008' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00665'  EndingX='00698'  StartingY='03009' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00726'  EndingX='00789'  StartingY='03005' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='00819'  EndingX='00919'  StartingY='03006' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='00944'  EndingX='01024'  StartingY='03015' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n<Word Height='00028' StartingX='01049'  EndingX='01095'  StartingY='03010' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01120'  EndingX='01202'  StartingY='03007' Length='5'  Confidence='0,0,0,0,0'  >Note,</Word>\r\n<Word Height='00028' StartingX='01223'  EndingX='01223'  StartingY='03015' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='01253'  EndingX='01414'  StartingY='03007' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >Mortgage,</Word>\r\n<Word Height='00028' StartingX='01434'  EndingX='01500'  StartingY='03007' Length='4'  Confidence='0,0,0,0'  >Deed</Word>\r\n<Word Height='00028' StartingX='01532'  EndingX='01552'  StartingY='03016' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01578'  EndingX='01653'  StartingY='03008' Length='5'  Confidence='0,0,0,0,0'  >Trust</Word>\r\n<Word Height='00028' StartingX='01677'  EndingX='01697'  StartingY='03016' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='01723'  EndingX='01827'  StartingY='03008' Length='8'  Confidence='0,0,0,0,0,0,30,30'  >Security</Word>\r\n<Word Height='00028' StartingX='01872'  EndingX='01937'  StartingY='03008' Length='4'  Confidence='0,0,0,0'  >Deed</Word>\r\n<Word Height='00028' StartingX='01969'  EndingX='02016'  StartingY='03007' Length='4'  Confidence='0,0,0,0'  >(the</Word>\r\n<Word Height='00028' StartingX='02047'  EndingX='02168'  StartingY='03009' Length='14'  Confidence='0,0,0,0,0,0,0,30,30'  >&quot;Security</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='32.02' Height='00.37' >\r\n<LineText>~X[03.35]Instrument&quot;), dated the same date as this Note, protects the Note Holder from possible losses which might result if I </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00529'  StartingY='03051' Length='18'  Confidence='0,0,0,0,0,0,0,0,0,20,30,20,20'  >Instrument&quot;),</Word>\r\n<Word Height='00028' StartingX='00548'  EndingX='00616'  StartingY='03051' Length='5'  Confidence='0,0,0,0,0'  >dated</Word>\r\n<Word Height='00028' StartingX='00646'  EndingX='00678'  StartingY='03056' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00707'  EndingX='00772'  StartingY='03061' Length='4'  Confidence='0,0,0,0'  >same</Word>\r\n<Word Height='00028' StartingX='00800'  EndingX='00849'  StartingY='03052' Length='4'  Confidence='0,0,0,0'  >date</Word>\r\n<Word Height='00028' StartingX='00878'  EndingX='00897'  StartingY='03061' Length='2'  Confidence='0,0'  >as</Word>\r\n<Word Height='00028' StartingX='00922'  EndingX='00965'  StartingY='03057' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='00989'  EndingX='01070'  StartingY='03053' Length='5'  Confidence='0,0,0,0,0'  >Note,</Word>\r\n<Word Height='00028' StartingX='01089'  EndingX='01202'  StartingY='03062' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >protects</Word>\r\n<Word Height='00028' StartingX='01228'  EndingX='01259'  StartingY='03058' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01287'  EndingX='01348'  StartingY='03054' Length='4'  Confidence='0,0,0,0'  >Note</Word>\r\n<Word Height='00028' StartingX='01377'  EndingX='01474'  StartingY='03054' Length='6'  Confidence='0,0,0,0,0,0'  >Holder</Word>\r\n<Word Height='00028' StartingX='01499'  EndingX='01545'  StartingY='03054' Length='4'  Confidence='30,30,0,0'  >from</Word>\r\n<Word Height='00028' StartingX='01586'  EndingX='01701'  StartingY='03063' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >possible</Word>\r\n<Word Height='00028' StartingX='01730'  EndingX='01811'  StartingY='03055' Length='6'  Confidence='0,0,0,0,0,0'  >losses</Word>\r\n<Word Height='00028' StartingX='01835'  EndingX='01915'  StartingY='03064' Length='5'  Confidence='0,0,0,0,0'  >which</Word>\r\n<Word Height='00028' StartingX='01946'  EndingX='02028'  StartingY='03064' Length='5'  Confidence='0,0,0,0,0'  >might</Word>\r\n<Word Height='00028' StartingX='02050'  EndingX='02129'  StartingY='03065' Length='6'  Confidence='0,0,0,0,0,0'  >result</Word>\r\n<Word Height='00028' StartingX='02152'  EndingX='02162'  StartingY='03055' Length='2'  Confidence='0,0'  >if</Word>\r\n<Word Height='00028' StartingX='02186'  EndingX='02186'  StartingY='03056' Length='1'  Confidence='0'  >I</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='32.52' Height='00.34' >\r\n<LineText>~X[03.35]do not keep the promises which I make in this Note. That Security Instrument describes how and under what </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00343'  StartingY='03098' Length='2'  Confidence='0,0'  >do</Word>\r\n<Word Height='00028' StartingX='00377'  EndingX='00420'  StartingY='03107' Length='3'  Confidence='0,0,0'  >not</Word>\r\n<Word Height='00028' StartingX='00445'  EndingX='00503'  StartingY='03098' Length='4'  Confidence='0,0,0,0'  >keep</Word>\r\n<Word Height='00028' StartingX='00537'  EndingX='00570'  StartingY='03103' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00601'  EndingX='00739'  StartingY='03108' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >promises</Word>\r\n<Word Height='00028' StartingX='00767'  EndingX='00850'  StartingY='03108' Length='5'  Confidence='0,0,0,0,0'  >which</Word>\r\n<Word Height='00028' StartingX='00884'  EndingX='00884'  StartingY='03100' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00911'  EndingX='00983'  StartingY='03109' Length='4'  Confidence='0,0,0,0'  >make</Word>\r\n<Word Height='00028' StartingX='01016'  EndingX='01026'  StartingY='03100' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='01061'  EndingX='01107'  StartingY='03104' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01134'  EndingX='01219'  StartingY='03101' Length='5'  Confidence='0,0,0,0,0'  >Note.</Word>\r\n<Word Height='00028' StartingX='01241'  EndingX='01306'  StartingY='03101' Length='4'  Confidence='0,0,0,0'  >That</Word>\r\n<Word Height='00028' StartingX='01333'  EndingX='01438'  StartingY='03101' Length='8'  Confidence='0,0,0,0,0,0,30,30'  >Security</Word>\r\n<Word Height='00028' StartingX='01484'  EndingX='01654'  StartingY='03102' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >Instrument</Word>\r\n<Word Height='00028' StartingX='01680'  EndingX='01821'  StartingY='03102' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >describes</Word>\r\n<Word Height='00028' StartingX='01850'  EndingX='01892'  StartingY='03102' Length='3'  Confidence='0,0,0'  >how</Word>\r\n<Word Height='00028' StartingX='01936'  EndingX='01975'  StartingY='03111' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='02008'  EndingX='02091'  StartingY='03111' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n<Word Height='00028' StartingX='02118'  EndingX='02188'  StartingY='03111' Length='4'  Confidence='0,0,0,0'  >what</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='33.01' Height='00.30' >\r\n<LineText>~X[03.35]con~Y[00.00]d~Yi~Y[00.00]t~Yions ~Y[00.00]I may ~Ybe requ~Y[00.00]ire~Yd to ma~Y[00.01]ke ~Yimme~Y[00.02]d~Yiate payment ~Y[00.02]in ~Yfu~Y[00.02]l~Yl o~Y[00.02]f a~Yl~Y[00.02]l amounts ~YI owe un~Y[00.04]der t~Yh~Y[00.03]is ~YNote. ~Y[00.04]Some o~Yf </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00479'  StartingY='03154' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >conditions</Word>\r\n<Word Height='00028' StartingX='00508'  EndingX='00508'  StartingY='03146' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='00533'  EndingX='00583'  StartingY='03155' Length='3'  Confidence='0,0,0'  >may</Word>\r\n<Word Height='00028' StartingX='00616'  EndingX='00637'  StartingY='03146' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00028' StartingX='00667'  EndingX='00786'  StartingY='03155' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >required</Word>\r\n<Word Height='00028' StartingX='00818'  EndingX='00830'  StartingY='03150' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00028' StartingX='00863'  EndingX='00934'  StartingY='03156' Length='4'  Confidence='0,0,0,0'  >make</Word>\r\n<Word Height='00028' StartingX='00965'  EndingX='01121'  StartingY='03147' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >immediate</Word>\r\n<Word Height='00028' StartingX='01151'  EndingX='01281'  StartingY='03156' Length='7'  Confidence='0,0,0,0,0,0,0'  >payment</Word>\r\n<Word Height='00028' StartingX='01306'  EndingX='01317'  StartingY='03148' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='01350'  EndingX='01396'  StartingY='03148' Length='4'  Confidence='30,30,0,0'  >full</Word>\r\n<Word Height='00028' StartingX='01419'  EndingX='01439'  StartingY='03157' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01465'  EndingX='01496'  StartingY='03157' Length='3'  Confidence='0,0,0'  >all</Word>\r\n<Word Height='00028' StartingX='01519'  EndingX='01643'  StartingY='03157' Length='7'  Confidence='0,0,0,0,0,0,0'  >amounts</Word>\r\n<Word Height='00028' StartingX='01671'  EndingX='01671'  StartingY='03149' Length='1'  Confidence='0'  >I</Word>\r\n<Word Height='00028' StartingX='01697'  EndingX='01747'  StartingY='03157' Length='3'  Confidence='0,0,0'  >owe</Word>\r\n<Word Height='00028' StartingX='01776'  EndingX='01858'  StartingY='03158' Length='5'  Confidence='0,0,0,0,0'  >under</Word>\r\n<Word Height='00028' StartingX='01883'  EndingX='01928'  StartingY='03153' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00028' StartingX='01955'  EndingX='02038'  StartingY='03149' Length='5'  Confidence='0,0,0,0,0'  >Note.</Word>\r\n<Word Height='00028' StartingX='02060'  EndingX='02135'  StartingY='03149' Length='4'  Confidence='0,0,0,0'  >Some</Word>\r\n<Word Height='00028' StartingX='02165'  EndingX='02186'  StartingY='03158' Length='2'  Confidence='0,0'  >of</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='33.50' Height='00.30' >\r\n<LineText>~X[03.35]those conditions are described as follows: </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00322'  EndingX='00390'  StartingY='03196' Length='5'  Confidence='0,0,0,0,0'  >those</Word>\r\n<Word Height='00028' StartingX='00417'  EndingX='00569'  StartingY='03201' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >conditions</Word>\r\n<Word Height='00028' StartingX='00594'  EndingX='00625'  StartingY='03201' Length='3'  Confidence='0,0,0'  >are</Word>\r\n<Word Height='00028' StartingX='00653'  EndingX='00787'  StartingY='03192' Length='9'  Confidence='0,0,0,0,30,30,0,0,0'  >described</Word>\r\n<Word Height='00028' StartingX='00818'  EndingX='00836'  StartingY='03202' Length='2'  Confidence='0,0'  >as</Word>\r\n<Word Height='00028' StartingX='00862'  EndingX='00984'  StartingY='03193' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >follows:</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='34.05' Height='00.37' >\r\n<LineText>~X[06.35]If all or any part of the Property or any Interest in the Property is sold or transferred (or if </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00611'  EndingX='00625'  StartingY='03246' Length='2'  Confidence='0,0'  >If</Word>\r\n<Word Height='00028' StartingX='00648'  EndingX='00679'  StartingY='03255' Length='3'  Confidence='0,0,0'  >all</Word>\r\n<Word Height='00028' StartingX='00700'  EndingX='00720'  StartingY='03255' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='00743'  EndingX='00781'  StartingY='03255' Length='3'  Confidence='0,0,0'  >any</Word>\r\n<Word Height='00028' StartingX='00811'  EndingX='00862'  StartingY='03255' Length='4'  Confidence='0,0,0,0'  >part</Word>\r\n<Word Height='00028' StartingX='00885'  EndingX='00905'  StartingY='03255' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='00929'  EndingX='00960'  StartingY='03250' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='00988'  EndingX='01106'  StartingY='03247' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Property</Word>\r\n<Word Height='00028' StartingX='01137'  EndingX='01157'  StartingY='03256' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='01181'  EndingX='01218'  StartingY='03256' Length='3'  Confidence='0,0,0'  >any</Word>\r\n<Word Height='00028' StartingX='01249'  EndingX='01359'  StartingY='03247' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Interest</Word>\r\n<Word Height='00028' StartingX='01381'  EndingX='01391'  StartingY='03247' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='01421'  EndingX='01452'  StartingY='03251' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00028' StartingX='01480'  EndingX='01598'  StartingY='03248' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Property</Word>\r\n<Word Height='00028' StartingX='01629'  EndingX='01640'  StartingY='03248' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='01667'  EndingX='01713'  StartingY='03257' Length='4'  Confidence='0,0,0,0'  >sold</Word>\r\n<Word Height='00028' StartingX='01743'  EndingX='01763'  StartingY='03257' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='01787'  EndingX='01942'  StartingY='03252' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >transferred</Word>\r\n<Word Height='00028' StartingX='01972'  EndingX='02005'  StartingY='03249' Length='3'  Confidence='0,0,0'  >(or</Word>\r\n<Word Height='00028' StartingX='02030'  EndingX='02041'  StartingY='03249' Length='2'  Confidence='0,0'  >if</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='34.53' Height='00.38' >\r\n<LineText>~X[04.86]Borrower is not a natural person and a beneficial interest in Borrower is sold or transferred) </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00467'  EndingX='00613'  StartingY='03291' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Borrower</Word>\r\n<Word Height='00028' StartingX='00642'  EndingX='00653'  StartingY='03291' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='00683'  EndingX='00725'  StartingY='03301' Length='3'  Confidence='0,0,0'  >not</Word>\r\n<Word Height='00028' StartingX='00751'  EndingX='00751'  StartingY='03301' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='00782'  EndingX='00889'  StartingY='03301' Length='7'  Confidence='0,0,0,0,0,0,0'  >natural</Word>\r\n<Word Height='00028' StartingX='00912'  EndingX='01005'  StartingY='03302' Length='6'  Confidence='0,0,0,0,0,0'  >person</Word>\r\n<Word Height='00028' StartingX='01039'  EndingX='01079'  StartingY='03302' Length='3'  Confidence='0,0,0'  >and</Word>\r\n<Word Height='00028' StartingX='01113'  EndingX='01113'  StartingY='03302' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00028' StartingX='01144'  EndingX='01301'  StartingY='03293' Length='10'  Confidence='0,0,0,0,30,30,0,0,0,0'  >beneficial</Word>\r\n<Word Height='00028' StartingX='01326'  EndingX='01438'  StartingY='03293' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >interest</Word>\r\n<Word Height='00028' StartingX='01464'  EndingX='01475'  StartingY='03294' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='01509'  EndingX='01655'  StartingY='03294' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Borrower</Word>\r\n<Word Height='00028' StartingX='01684'  EndingX='01695'  StartingY='03294' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00028' StartingX='01725'  EndingX='01774'  StartingY='03303' Length='4'  Confidence='0,0,0,0'  >sold</Word>\r\n<Word Height='00028' StartingX='01808'  EndingX='01829'  StartingY='03303' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00028' StartingX='01857'  EndingX='02040'  StartingY='03298' Length='12'  Confidence='0,0,0,0,0,0,0,0,0,0,0,0'  >transferred)</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='35.02' Height='00.31' >\r\n<LineText>~X[04.85]w~Y[-00.01]i~Yt~Y[00.00]hou~Yt ~Y[00.00]Len~Yder~Y[00.00]'s pr~Yior wr~Y[00.01]itten consent, ~YLen~Y[00.02]der may requ~Yire ~Y[00.03]imme~Yd~Y[00.02]iate payment ~Yin ~Y[00.03]fu~Yl~Y[00.03]l o~Yf a~Y[00.03]l~Yl sums </LineText>\r\n<Words>\r\n<Word Height='00028' StartingX='00466'  EndingX='00578'  StartingY='03347' Length='7'  Confidence='0,0,0,0,0,0,0'  >without</Word>\r\n<Word Height='00028' StartingX='00600'  EndingX='00723'  StartingY='03338' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Lender's</Word>\r\n<Word Height='00028' StartingX='00749'  EndingX='00814'  StartingY='03348' Length='5'  Confidence='0,0,0,0,0'  >prior</Word>\r\n<Word Height='00028' StartingX='00838'  EndingX='00932'  StartingY='03348' Length='7'  Confidence='0,0,0,30,30,0,0'  >written</Word>\r\n<Word Height='00028' StartingX='00963'  EndingX='01089'  StartingY='03348' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >consent,</Word>\r\n<Word Height='00028' StartingX='01108'  EndingX='01208'  StartingY='03340' Length='6'  Confidence='0,0,0,0,0,0'  >Lender</Word>\r\n<Word Height='00028' StartingX='01232'  EndingX='01282'  StartingY='03349' Length='3'  Confidence='0,0,0'  >may</Word>\r\n<Word Height='00028' StartingX='01313'  EndingX='01409'  StartingY='03349' Length='7'  Confidence='0,0,0,0,0,0,0'  >require</Word>\r\n<Word Height='00028' StartingX='01437'  EndingX='01589'  StartingY='03340' Length='9'  Confidence='0,30,0,0,0,0,0,0,0'  >immediate</Word>\r\n<Word Height='00028' StartingX='01617'  EndingX='01745'  StartingY='03350' Length='7'  Confidence='0,0,0,0,0,0,0'  >payment</Word>\r\n<Word Height='00028' StartingX='01767'  EndingX='01777'  StartingY='03341' Length='2'  Confidence='0,0'  >in</Word>\r\n<Word Height='00028' StartingX='01809'  EndingX='01854'  StartingY='03341' Length='4'  Confidence='30,30,0,0'  >full</Word>\r\n<Word Height='00028' StartingX='01876'  EndingX='01896'  StartingY='03350' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00028' StartingX='01920'  EndingX='01950'  StartingY='03351' Length='3'  Confidence='0,0,0'  >all</Word>\r\n<Word Height='00028' StartingX='01972'  EndingX='02039'  StartingY='03351' Length='4'  Confidence='0,0,0,0'  >sums</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='35.73' Height='00.25' >\r\n<LineText>~X[03.35]MULTISTATE FIXED RATE NOTE ~F-Single Family-- ~FFannie Mae/Freddie Mac UNIFORM INSTRUMENT </LineText>\r\n<Words>\r\n<Word Height='00022' StartingX='00322'  EndingX='00515'  StartingY='03414' Length='10'  Confidence='0,30,30,0,0,0,0,0,0,0'  >MULTISTATE</Word>\r\n<Word Height='00022' StartingX='00544'  EndingX='00621'  StartingY='03414' Length='5'  Confidence='0,0,30,30,0'  >FIXED</Word>\r\n<Word Height='00022' StartingX='00652'  EndingX='00720'  StartingY='03414' Length='4'  Confidence='0,0,0,0'  >RATE</Word>\r\n<Word Height='00022' StartingX='00749'  EndingX='00819'  StartingY='03415' Length='4'  Confidence='0,0,0,0'  >NOTE</Word>\r\n<Word Height='00022' StartingX='00849'  EndingX='00938'  StartingY='03428' Length='7'  Confidence='30,20,0,0,0,0,0'  >-Single</Word>\r\n<Word Height='00022' StartingX='00960'  EndingX='01062'  StartingY='03415' Length='8'  Confidence='0,0,0,0,0,20,30,30'  >Family--</Word>\r\n<Word Height='00022' StartingX='01081'  EndingX='01161'  StartingY='03415' Length='6'  Confidence='0,0,0,0,0,0'  >Fannie</Word>\r\n<Word Height='00022' StartingX='01183'  EndingX='01345'  StartingY='03416' Length='11'  Confidence='158,158,158,208,178,158,158,158,158,158,158'  >Mae/Freddie</Word>\r\n<Word Height='00022' StartingX='01367'  EndingX='01413'  StartingY='03416' Length='3'  Confidence='0,0,0'  >Mac</Word>\r\n<Word Height='00022' StartingX='01434'  EndingX='01560'  StartingY='03416' Length='7'  Confidence='40,60,60,10,10,10,10'  >UNIFORM</Word>\r\n<Word Height='00022' StartingX='01599'  EndingX='01795'  StartingY='03417' Length='10'  Confidence='0,0,0,0,0,30,30,0,0,0'  >INSTRUMENT</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='36.13' Height='00.31' >\r\n<LineText>~X[03.33]lcxJ 5.74~X[17.30]Form 3200 1/01~X[20.29]~F(page 2 ~Fof ~F3 pages)~F</LineText>\r\n<Words>\r\n<Word Height='00022' StartingX='00320'  EndingX='00358'  StartingY='03449' Length='4'  Confidence='188,198,198,158'  >lcxJ</Word>\r\n<Word Height='00022' StartingX='00397'  EndingX='00436'  StartingY='03450' Length='4'  Confidence='20,0,0,0'  >5.74</Word>\r\n<Word Height='00022' StartingX='01663'  EndingX='01714'  StartingY='03453' Length='4'  Confidence='0,0,0,0'  >Form</Word>\r\n<Word Height='00022' StartingX='01755'  EndingX='01804'  StartingY='03453' Length='4'  Confidence='0,0,0,0'  >3200</Word>\r\n<Word Height='00022' StartingX='01853'  EndingX='01895'  StartingY='03453' Length='4'  Confidence='20,20,0,0'  >1/01</Word>\r\n<Word Height='00022' StartingX='01951'  EndingX='02009'  StartingY='03453' Length='5'  Confidence='10,10,10,10,30'  >(page</Word>\r\n<Word Height='00022' StartingX='02031'  EndingX='02031'  StartingY='03454' Length='1'  Confidence='40'  >2</Word>\r\n<Word Height='00022' StartingX='02056'  EndingX='02065'  StartingY='03461' Length='2'  Confidence='0,20'  >of</Word>\r\n<Word Height='00022' StartingX='02089'  EndingX='02089'  StartingY='03454' Length='1'  Confidence='30'  >3</Word>\r\n<Word Height='00022' StartingX='02110'  EndingX='02183'  StartingY='03461' Length='6'  Confidence='10,10,10,10,10,10'  >pages)</Word>\r\n</Words>\r\n</Line>\r\n\r\n</Lines>\r\n</Page>\r\n"}, {"PageNumber": 3, "PageText": "38121081989 secured by this Security Instrument. However, this option shall not be exercised by <PERSON><PERSON> if such exercise is prohibited by Applicable Law. If <PERSON><PERSON> exercises this option, <PERSON><PERSON> shall give <PERSON><PERSON><PERSON> notice of acceleration. The notice shall provide a period of not less than 30 days from the date the notice is given in accordance with Section 15 within which <PERSON><PERSON><PERSON> must pay all sums secured by this Security Instrument. If <PERSON><PERSON><PERSON> fails to pay these sums prior to the expiration of this period, <PERSON><PERSON> may invoke any remedies permitted by this Security Instrument without further notice or demand on <PERSON><PERSON><PERSON>. WITNESS THE HAND(S) AND SEAL(S) OF THE UNDERSIGNED. - BORROWER - SHERRI LYNN Individual Loan Originator: CHRISTINE SCHMIDT, NMLSR ID: 373767 [Sign Original Only] Loan Originator Organization: NEWREZ LLC, NMLSR ID: 3013 MULTISTATE FIXED RATE NOTE --Single Family-- <PERSON><PERSON>/<PERSON> Mac UNIFORM INSTRUMENT cx 5.74 Form 3200 1/01 (page 3 of3 pages) ", "PageXML": "<Page PageWidth='2534' PageHeight='4271'>\r\n<PageNumber>3</PageNumber>\r\n<Lines>\r\n<Line BaseLine='08.94' Height='00.25' >\r\n<LineText>~X[20.76]38121081989 </LineText>\r\n<Words>\r\n<Word Height='00023' StartingX='01996'  EndingX='02190'  StartingY='00837' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >38121081989</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='09.65' Height='00.32' >\r\n<LineText>~X[04.96]secure~Y[00.00]d ~Yby t~Y[00.00]h~Yis ~Y[00.01]Secur~Yity ~Y[00.01]Instrument. ~YHowever, t~Y[00.03]h~Yis opt~Y[00.03]ion s~Yha~Y[00.04]l~Yl n~Y[00.05]ot ~Yb~Y[00.05]e ~Yex~Y[00.05]e~Yr~Y[00.05]c~Yi~Y[00.05]s~Ye~Y[00.05]d ~Yb~Y[00.06]y ~YL~Y[00.06]e~Yn~Y[00.06]d~Ye~Y[00.06]r ~Yi~Y[00.06]f ~Ys~Y[00.06]u~Yc~Y[00.06]h ~Y</LineText>\r\n<Words>\r\n<Word Height='00029' StartingX='00477'  EndingX='00579'  StartingY='00907' Length='7'  Confidence='0,0,0,0,0,0,0'  >secured</Word>\r\n<Word Height='00029' StartingX='00609'  EndingX='00629'  StartingY='00899' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00029' StartingX='00661'  EndingX='00704'  StartingY='00903' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00029' StartingX='00731'  EndingX='00844'  StartingY='00899' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Security</Word>\r\n<Word Height='00029' StartingX='00875'  EndingX='01051'  StartingY='00901' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >Instrument.</Word>\r\n<Word Height='00029' StartingX='01069'  EndingX='01218'  StartingY='00901' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >However,</Word>\r\n<Word Height='00029' StartingX='01237'  EndingX='01281'  StartingY='00906' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00029' StartingX='01306'  EndingX='01388'  StartingY='00911' Length='6'  Confidence='0,0,0,0,0,0'  >option</Word>\r\n<Word Height='00029' StartingX='01420'  EndingX='01485'  StartingY='00912' Length='5'  Confidence='0,0,0,0,0'  >shall</Word>\r\n<Word Height='00029' StartingX='01505'  EndingX='01546'  StartingY='00912' Length='3'  Confidence='0,0,0'  >not</Word>\r\n<Word Height='00029' StartingX='01567'  EndingX='01588'  StartingY='00903' Length='2'  Confidence='0,0'  >be</Word>\r\n<Word Height='00029' StartingX='01616'  EndingX='01747'  StartingY='00913' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >exercised</Word>\r\n<Word Height='00029' StartingX='01777'  EndingX='01797'  StartingY='00903' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00029' StartingX='01828'  EndingX='01929'  StartingY='00905' Length='6'  Confidence='0,0,0,0,0,0'  >Lender</Word>\r\n<Word Height='00029' StartingX='01954'  EndingX='01965'  StartingY='00904' Length='2'  Confidence='0,0'  >if</Word>\r\n<Word Height='00029' StartingX='01989'  EndingX='02042'  StartingY='00914' Length='4'  Confidence='0,0,0,0'  >such</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='10.14' Height='00.31' >\r\n<LineText>~X[04.95]exerc~Y[00.00]ise ~Yis pro~Y[00.01]h~Yi~Y[00.02]b~Yite~Y[00.02]d ~Yby ~Y[00.02]App~Yl~Y[00.02]ica~Yb~Y[00.02]le ~YLaw. </LineText>\r\n<Words>\r\n<Word Height='00029' StartingX='00476'  EndingX='00590'  StartingY='00955' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >exercise</Word>\r\n<Word Height='00029' StartingX='00618'  EndingX='00629'  StartingY='00946' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00029' StartingX='00654'  EndingX='00799'  StartingY='00956' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >prohibited</Word>\r\n<Word Height='00029' StartingX='00829'  EndingX='00850'  StartingY='00947' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00029' StartingX='00879'  EndingX='01037'  StartingY='00949' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >Applicable</Word>\r\n<Word Height='00029' StartingX='01065'  EndingX='01138'  StartingY='00949' Length='4'  Confidence='0,0,0,0'  >Law.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='10.71' Height='00.31' >\r\n<LineText>~X[06.45]If Lender exercises this option, Lender shall give Borrower notice of accelerati~Y[00.06]on. ~YTh~Y[00.06]e ~Y</LineText>\r\n<Words>\r\n<Word Height='00029' StartingX='00620'  EndingX='00635'  StartingY='01001' Length='2'  Confidence='0,0'  >If</Word>\r\n<Word Height='00029' StartingX='00660'  EndingX='00762'  StartingY='01001' Length='6'  Confidence='0,0,0,0,0,0'  >Lender</Word>\r\n<Word Height='00029' StartingX='00788'  EndingX='00924'  StartingY='01010' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >exercises</Word>\r\n<Word Height='00029' StartingX='00951'  EndingX='00996'  StartingY='01006' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00029' StartingX='01023'  EndingX='01129'  StartingY='01012' Length='7'  Confidence='0,0,0,0,0,0,0'  >option,</Word>\r\n<Word Height='00029' StartingX='01149'  EndingX='01252'  StartingY='01004' Length='6'  Confidence='0,0,0,0,0,0'  >Lender</Word>\r\n<Word Height='00029' StartingX='01279'  EndingX='01345'  StartingY='01013' Length='5'  Confidence='0,0,0,0,0'  >shall</Word>\r\n<Word Height='00029' StartingX='01367'  EndingX='01420'  StartingY='01013' Length='4'  Confidence='0,0,0,0'  >give</Word>\r\n<Word Height='00029' StartingX='01450'  EndingX='01593'  StartingY='01005' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Borrower</Word>\r\n<Word Height='00029' StartingX='01618'  EndingX='01702'  StartingY='01014' Length='6'  Confidence='0,0,0,0,0,0'  >notice</Word>\r\n<Word Height='00029' StartingX='01731'  EndingX='01752'  StartingY='01015' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00029' StartingX='01777'  EndingX='01978'  StartingY='01015' Length='13'  Confidence='0,0,0,0,0,0,0,0,0,0,0,0,0'  >acceleration.</Word>\r\n<Word Height='00029' StartingX='01998'  EndingX='02045'  StartingY='01007' Length='3'  Confidence='0,0,0'  >The</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='11.19' Height='00.31' >\r\n<LineText>~X[04.94]no~Y[00.01]t~Yice s~Y[00.01]ha~Yl~Y[00.01]l prov~Yi~Y[00.02]de a per~Yio~Y[00.03]d o~Yf n~Y[00.04]o~Yt ~Y[00.03]l~Ye~Y[00.04]s~Ys ~Y[00.04]t~Yh~Y[00.04]a~Yn ~Y[00.04]3~Y0 ~Y[00.05]d~Ya~Y[00.05]y~Ys ~Y[00.05]f~Yr~Y[00.05]o~Ym ~Y[00.05]t~Yh~Y[00.06]e ~Yd~Y[00.06]a~Yt~Y[00.06]e ~Yt~Y[00.06]h~Ye ~Y[00.06]n~Yo~Y[00.07]t~Yi~Y[00.07]c~Ye ~Y[00.06]i~Ys ~Y[00.07]g~Yi~Y[00.07]v~Ye~Y[00.07]n ~Yi~Y[00.07]n ~Y</LineText>\r\n<Words>\r\n<Word Height='00029' StartingX='00475'  EndingX='00562'  StartingY='01057' Length='6'  Confidence='0,0,0,0,0,0'  >notice</Word>\r\n<Word Height='00029' StartingX='00597'  EndingX='00667'  StartingY='01057' Length='5'  Confidence='0,0,0,0,0'  >shall</Word>\r\n<Word Height='00029' StartingX='00693'  EndingX='00806'  StartingY='01058' Length='7'  Confidence='0,0,0,0,0,0,0'  >provide</Word>\r\n<Word Height='00029' StartingX='00840'  EndingX='00840'  StartingY='01058' Length='1'  Confidence='0'  >a</Word>\r\n<Word Height='00029' StartingX='00874'  EndingX='00963'  StartingY='01058' Length='6'  Confidence='0,0,0,0,0,0'  >period</Word>\r\n<Word Height='00029' StartingX='00999'  EndingX='01020'  StartingY='01059' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00029' StartingX='01047'  EndingX='01091'  StartingY='01059' Length='3'  Confidence='0,0,0'  >not</Word>\r\n<Word Height='00029' StartingX='01119'  EndingX='01167'  StartingY='01050' Length='4'  Confidence='0,0,0,0'  >less</Word>\r\n<Word Height='00029' StartingX='01198'  EndingX='01250'  StartingY='01054' Length='4'  Confidence='0,0,0,0'  >than</Word>\r\n<Word Height='00029' StartingX='01287'  EndingX='01308'  StartingY='01052' Length='2'  Confidence='0,0'  >30</Word>\r\n<Word Height='00029' StartingX='01343'  EndingX='01406'  StartingY='01051' Length='4'  Confidence='0,0,0,0'  >days</Word>\r\n<Word Height='00029' StartingX='01437'  EndingX='01487'  StartingY='01051' Length='4'  Confidence='0,0,0,0'  >from</Word>\r\n<Word Height='00029' StartingX='01534'  EndingX='01568'  StartingY='01056' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00029' StartingX='01601'  EndingX='01655'  StartingY='01052' Length='4'  Confidence='0,0,0,0'  >date</Word>\r\n<Word Height='00029' StartingX='01688'  EndingX='01722'  StartingY='01056' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00029' StartingX='01754'  EndingX='01841'  StartingY='01062' Length='6'  Confidence='0,0,0,0,0,0'  >notice</Word>\r\n<Word Height='00029' StartingX='01876'  EndingX='01888'  StartingY='01053' Length='2'  Confidence='0,0'  >is</Word>\r\n<Word Height='00029' StartingX='01919'  EndingX='01993'  StartingY='01063' Length='5'  Confidence='0,0,0,0,0'  >given</Word>\r\n<Word Height='00029' StartingX='02030'  EndingX='02041'  StartingY='01054' Length='2'  Confidence='0,0'  >in</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='11.70' Height='00.35' >\r\n<LineText>~X[04.95]accor~Y[00.00]dance w~Yit~Y[00.01]h ~YSect~Y[00.01]ion ~Y1~Y[00.02]5 w~Yit~Y[00.02]h~Yin w~Y[00.02]h~Yic~Y[00.02]h ~YBorrower must pay a~Y[00.04]l~Yl ~Y[00.05]s~Yum~Y[00.05]s ~Ys~Y[00.05]e~Yc~Y[00.05]u~Yr~Y[00.05]e~Yd ~Y[00.06]b~Yy ~Y[00.06]t~Yh~Y[00.05]i~Ys ~Y[00.06]S~Ye~Y[00.06]c~Yu~Y[00.06]r~Yi~Y[00.06]t~Yy </LineText>\r\n<Words>\r\n<Word Height='00029' StartingX='00476'  EndingX='00643'  StartingY='01104' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >accordance</Word>\r\n<Word Height='00029' StartingX='00673'  EndingX='00727'  StartingY='01105' Length='4'  Confidence='0,0,0,0'  >with</Word>\r\n<Word Height='00029' StartingX='00761'  EndingX='00863'  StartingY='01097' Length='7'  Confidence='0,0,0,0,0,0,0'  >Section</Word>\r\n<Word Height='00029' StartingX='00900'  EndingX='00917'  StartingY='01097' Length='2'  Confidence='0,0'  >15</Word>\r\n<Word Height='00029' StartingX='00948'  EndingX='01034'  StartingY='01106' Length='6'  Confidence='0,0,0,0,0,0'  >within</Word>\r\n<Word Height='00029' StartingX='01065'  EndingX='01146'  StartingY='01107' Length='5'  Confidence='0,0,0,0,0'  >which</Word>\r\n<Word Height='00029' StartingX='01178'  EndingX='01322'  StartingY='01099' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Borrower</Word>\r\n<Word Height='00029' StartingX='01348'  EndingX='01416'  StartingY='01108' Length='4'  Confidence='0,0,0,0'  >must</Word>\r\n<Word Height='00029' StartingX='01439'  EndingX='01478'  StartingY='01109' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00029' StartingX='01511'  EndingX='01542'  StartingY='01109' Length='3'  Confidence='0,0,0'  >all</Word>\r\n<Word Height='00029' StartingX='01566'  EndingX='01634'  StartingY='01109' Length='4'  Confidence='0,0,0,0'  >sums</Word>\r\n<Word Height='00029' StartingX='01662'  EndingX='01766'  StartingY='01109' Length='7'  Confidence='0,0,0,0,0,0,0'  >secured</Word>\r\n<Word Height='00029' StartingX='01798'  EndingX='01818'  StartingY='01100' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00029' StartingX='01852'  EndingX='01897'  StartingY='01105' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00029' StartingX='01925'  EndingX='02030'  StartingY='01102' Length='8'  Confidence='0,0,0,0,0,0,30,30'  >Security</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='12.19' Height='00.31' >\r\n<LineText>~X[04.94]Instrument. If Borrower fails to pay these sums prior to the expiration ~Y[00.06]of ~Ythi~Y[00.06]s ~Yp~Y[00.06]e~Yr~Y[00.06]i~Yo~Y[00.06]d~Y, ~Y[00.06]L~Ye~Y[00.06]n~Yd~Y[00.07]e~Yr ~Y[00.06]m~Ya~Y[00.07]y ~Y</LineText>\r\n<Words>\r\n<Word Height='00029' StartingX='00475'  EndingX='00654'  StartingY='01144' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >Instrument.</Word>\r\n<Word Height='00029' StartingX='00674'  EndingX='00687'  StartingY='01144' Length='2'  Confidence='0,0'  >If</Word>\r\n<Word Height='00029' StartingX='00711'  EndingX='00854'  StartingY='01144' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Borrower</Word>\r\n<Word Height='00029' StartingX='00879'  EndingX='00933'  StartingY='01145' Length='5'  Confidence='0,0,0,0,0'  >fails</Word>\r\n<Word Height='00029' StartingX='00959'  EndingX='00971'  StartingY='01149' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00029' StartingX='01001'  EndingX='01038'  StartingY='01155' Length='3'  Confidence='0,0,0'  >pay</Word>\r\n<Word Height='00029' StartingX='01070'  EndingX='01136'  StartingY='01149' Length='5'  Confidence='0,0,0,0,0'  >these</Word>\r\n<Word Height='00029' StartingX='01165'  EndingX='01232'  StartingY='01155' Length='4'  Confidence='0,0,0,0'  >sums</Word>\r\n<Word Height='00029' StartingX='01259'  EndingX='01324'  StartingY='01156' Length='5'  Confidence='0,0,0,0,0'  >prior</Word>\r\n<Word Height='00029' StartingX='01348'  EndingX='01360'  StartingY='01150' Length='2'  Confidence='0,0'  >to</Word>\r\n<Word Height='00029' StartingX='01391'  EndingX='01423'  StartingY='01151' Length='3'  Confidence='0,0,0'  >the</Word>\r\n<Word Height='00029' StartingX='01452'  EndingX='01596'  StartingY='01156' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >expiration</Word>\r\n<Word Height='00029' StartingX='01627'  EndingX='01648'  StartingY='01157' Length='2'  Confidence='0,0'  >of</Word>\r\n<Word Height='00029' StartingX='01672'  EndingX='01716'  StartingY='01152' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00029' StartingX='01740'  EndingX='01846'  StartingY='01158' Length='7'  Confidence='0,0,0,0,0,0,0'  >period,</Word>\r\n<Word Height='00029' StartingX='01866'  EndingX='01967'  StartingY='01149' Length='6'  Confidence='0,0,0,0,0,0'  >Lender</Word>\r\n<Word Height='00029' StartingX='01992'  EndingX='02041'  StartingY='01159' Length='3'  Confidence='0,0,0'  >may</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='12.68' Height='00.34' >\r\n<LineText>~X[04.95]invoke any remedies permitted by this Security Instrument with~Y[00.06]ou~Yt f~Y[00.06]ur~Yth~Y[00.06]er ~Yn~Y[00.06]o~Yt~Y[00.06]i~Yc~Y[00.06]e ~Yo~Y[00.06]r ~Yd~Y[00.07]e~Ym~Y[00.07]a~Yn~Y[00.07]d ~Yo~Y[00.07]n ~Y</LineText>\r\n<Words>\r\n<Word Height='00029' StartingX='00476'  EndingX='00569'  StartingY='01191' Length='6'  Confidence='0,0,0,0,0,0'  >invoke</Word>\r\n<Word Height='00029' StartingX='00599'  EndingX='00637'  StartingY='01200' Length='3'  Confidence='0,0,0'  >any</Word>\r\n<Word Height='00029' StartingX='00670'  EndingX='00803'  StartingY='01201' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >remedies</Word>\r\n<Word Height='00029' StartingX='00830'  EndingX='00968'  StartingY='01201' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >permitted</Word>\r\n<Word Height='00029' StartingX='00999'  EndingX='01020'  StartingY='01193' Length='2'  Confidence='0,0'  >by</Word>\r\n<Word Height='00029' StartingX='01052'  EndingX='01096'  StartingY='01197' Length='4'  Confidence='0,0,0,0'  >this</Word>\r\n<Word Height='00029' StartingX='01124'  EndingX='01227'  StartingY='01194' Length='8'  Confidence='0,0,0,0,0,0,20,20'  >Security</Word>\r\n<Word Height='00029' StartingX='01272'  EndingX='01438'  StartingY='01195' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >Instrument</Word>\r\n<Word Height='00029' StartingX='01460'  EndingX='01575'  StartingY='01204' Length='7'  Confidence='0,0,0,0,0,0,0'  >without</Word>\r\n<Word Height='00029' StartingX='01598'  EndingX='01697'  StartingY='01195' Length='7'  Confidence='30,30,0,0,0,0,0'  >further</Word>\r\n<Word Height='00029' StartingX='01722'  EndingX='01804'  StartingY='01205' Length='6'  Confidence='0,0,0,0,0,0'  >notice</Word>\r\n<Word Height='00029' StartingX='01833'  EndingX='01854'  StartingY='01206' Length='2'  Confidence='0,0'  >or</Word>\r\n<Word Height='00029' StartingX='01880'  EndingX='01989'  StartingY='01197' Length='6'  Confidence='0,0,0,0,0,0'  >demand</Word>\r\n<Word Height='00029' StartingX='02021'  EndingX='02042'  StartingY='01206' Length='2'  Confidence='0,0'  >on</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='13.18' Height='00.30' >\r\n<LineText>~X[04.93]Borrower. </LineText>\r\n<Words>\r\n<Word Height='00029' StartingX='00474'  EndingX='00630'  StartingY='01239' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >Borrower.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='14.25' Height='00.40' >\r\n<LineText>~X[03.43]WITNESS THE HAND(S) AND SEAL(S) OF THE UNDERSIGNED. </LineText>\r\n<Words>\r\n<Word Height='00029' StartingX='00330'  EndingX='00482'  StartingY='01341' Length='7'  Confidence='30,30,0,0,0,0,0'  >WITNESS</Word>\r\n<Word Height='00029' StartingX='00514'  EndingX='00567'  StartingY='01342' Length='3'  Confidence='0,0,0'  >THE</Word>\r\n<Word Height='00029' StartingX='00602'  EndingX='00753'  StartingY='01342' Length='7'  Confidence='0,0,0,0,0,0,0'  >HAND(S)</Word>\r\n<Word Height='00029' StartingX='00778'  EndingX='00835'  StartingY='01343' Length='3'  Confidence='0,0,0'  >AND</Word>\r\n<Word Height='00029' StartingX='00876'  EndingX='01010'  StartingY='01343' Length='7'  Confidence='0,0,0,0,0,0,0'  >SEAL(S)</Word>\r\n<Word Height='00029' StartingX='01033'  EndingX='01062'  StartingY='01344' Length='2'  Confidence='0,0'  >OF</Word>\r\n<Word Height='00029' StartingX='01094'  EndingX='01147'  StartingY='01344' Length='3'  Confidence='0,0,0'  >THE</Word>\r\n<Word Height='00029' StartingX='01181'  EndingX='01469'  StartingY='01345' Length='12'  Confidence='30,30,0,0,0,0,0,0,0,0,0,0'  >UNDERSIGNED.</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='18.01' Height='00.28' >\r\n<LineText>~X[03.44]- ~F~Y[00.00]B~YO~Y[00.00]R~YR~Y[00.01]O~YW~Y[00.01]E~YR ~F- ~F~Y[00.02]S~YH~Y[00.01]E~YR~Y[00.01]R~YI ~Y[00.02]L~YY~Y[00.02]N~YN ~F</LineText>\r\n<Words>\r\n<Word Height='00026' StartingX='00331'  EndingX='00331'  StartingY='01717' Length='1'  Confidence='0'  >-</Word>\r\n<Word Height='00026' StartingX='00375'  EndingX='00545'  StartingY='01706' Length='8'  Confidence='0,0,0,0,0,30,30,0'  >BORROWER</Word>\r\n<Word Height='00026' StartingX='00596'  EndingX='00596'  StartingY='01718' Length='1'  Confidence='0'  >-</Word>\r\n<Word Height='00026' StartingX='00655'  EndingX='00775'  StartingY='01707' Length='6'  Confidence='30,50,50,30,30,30'  >SHERRI</Word>\r\n<Word Height='00026' StartingX='00831'  EndingX='00902'  StartingY='01708' Length='4'  Confidence='0,30,30,30'  >LYNN</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='19.48' Height='00.38' >\r\n<LineText>~X[03.41]Individual Loan Originator: ~FCHRISTINE SCHMIDT, ~FNMLSR ID: ~F373767 ~F~Y[-00.33][~YS~Y[-00.33]i~Yg~Y[-00.33]n ~YO~Y[-00.33]r~Yi~Y[-00.33]g~Yi~Y[-00.33]n~Ya~Y[-00.33]l ~YO~Y[-00.33]n~Yl~Y[-00.33]y~Y] </LineText>\r\n<Words>\r\n<Word Height='00026' StartingX='00328'  EndingX='00483'  StartingY='01844' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >Individual</Word>\r\n<Word Height='00026' StartingX='00503'  EndingX='00566'  StartingY='01844' Length='4'  Confidence='0,0,0,0'  >Loan</Word>\r\n<Word Height='00026' StartingX='00596'  EndingX='00766'  StartingY='01845' Length='11'  Confidence='0,0,0,0,0,0,0,0,0,0,0'  >Originator:</Word>\r\n<Word Height='00026' StartingX='00785'  EndingX='00976'  StartingY='01848' Length='9'  Confidence='0,0,0,0,0,0,0,0,0'  >CHRISTINE</Word>\r\n<Word Height='00026' StartingX='01026'  EndingX='01193'  StartingY='01849' Length='8'  Confidence='128,128,128,128,128,128,128,128'  >SCHMIDT,</Word>\r\n<Word Height='00026' StartingX='01211'  EndingX='01324'  StartingY='01847' Length='5'  Confidence='128,158,158,128,128'  >NMLSR</Word>\r\n<Word Height='00026' StartingX='01361'  EndingX='01405'  StartingY='01848' Length='3'  Confidence='0,0,0'  >ID:</Word>\r\n<Word Height='00026' StartingX='01425'  EndingX='01546'  StartingY='01849' Length='6'  Confidence='0,0,0,0,0,0'  >373767</Word>\r\n<Word Height='00026' StartingX='01864'  EndingX='01931'  StartingY='01812' Length='5'  Confidence='0,0,0,0,0'  >[Sign</Word>\r\n<Word Height='00026' StartingX='01963'  EndingX='02090'  StartingY='01813' Length='8'  Confidence='0,0,0,0,0,0,0,0'  >Original</Word>\r\n<Word Height='00026' StartingX='02112'  EndingX='02183'  StartingY='01813' Length='5'  Confidence='0,0,0,0,50'  >Only]</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='19.97' Height='00.31' >\r\n<LineText>~X[03.42]Loan Originator Organization: ~FNEWREZ LLC, ~FNMLSR ID: 3013 </LineText>\r\n<Words>\r\n<Word Height='00026' StartingX='00329'  EndingX='00391'  StartingY='01891' Length='4'  Confidence='0,0,30,30'  >Loan</Word>\r\n<Word Height='00026' StartingX='00421'  EndingX='00575'  StartingY='01891' Length='10'  Confidence='0,50,50,0,0,0,0,0,0,0'  >Originator</Word>\r\n<Word Height='00026' StartingX='00600'  EndingX='00811'  StartingY='01892' Length='13'  Confidence='0,0,0,0,0,0,0,0,0,0,0,0,0'  >Organization:</Word>\r\n<Word Height='00026' StartingX='00828'  EndingX='00952'  StartingY='01896' Length='6'  Confidence='0,0,0,0,0,0'  >NEWREZ</Word>\r\n<Word Height='00026' StartingX='00999'  EndingX='01071'  StartingY='01897' Length='4'  Confidence='0,0,0,0'  >LLC,</Word>\r\n<Word Height='00026' StartingX='01088'  EndingX='01200'  StartingY='01894' Length='5'  Confidence='158,178,208,158,158'  >NMLSR</Word>\r\n<Word Height='00026' StartingX='01237'  EndingX='01283'  StartingY='01894' Length='3'  Confidence='0,0,0'  >ID:</Word>\r\n<Word Height='00026' StartingX='01303'  EndingX='01375'  StartingY='01896' Length='4'  Confidence='0,0,0,0'  >3013</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='35.79' Height='00.25' >\r\n<LineText>~X[03.36]MULTISTATE FIXED RATE NOTE ~F--Single Family-- ~FFannie Mae/Freddie Mac ~Y[00.05]UNIF~YORM IN~Y[00.05]STR~YU~Y[00.05]M~YE~Y[00.05]N~YT </LineText>\r\n<Words>\r\n<Word Height='00023' StartingX='00323'  EndingX='00516'  StartingY='03418' Length='10'  Confidence='0,0,0,0,0,0,0,0,0,0'  >MULTISTATE</Word>\r\n<Word Height='00023' StartingX='00545'  EndingX='00622'  StartingY='03419' Length='5'  Confidence='0,0,0,0,0'  >FIXED</Word>\r\n<Word Height='00023' StartingX='00653'  EndingX='00721'  StartingY='03419' Length='4'  Confidence='0,30,0,0'  >RATE</Word>\r\n<Word Height='00023' StartingX='00751'  EndingX='00821'  StartingY='03420' Length='4'  Confidence='0,0,0,0'  >NOTE</Word>\r\n<Word Height='00023' StartingX='00850'  EndingX='00939'  StartingY='03433' Length='8'  Confidence='20,40,50,0,0,0,0,0'  >--Single</Word>\r\n<Word Height='00023' StartingX='00962'  EndingX='01062'  StartingY='03420' Length='8'  Confidence='0,0,30,0,0,20,40,20'  >Family--</Word>\r\n<Word Height='00023' StartingX='01081'  EndingX='01161'  StartingY='03421' Length='6'  Confidence='0,0,0,0,0,0'  >Fannie</Word>\r\n<Word Height='00023' StartingX='01183'  EndingX='01346'  StartingY='03422' Length='11'  Confidence='158,158,158,198,178,158,158,158,158,158,158'  >Mae/Freddie</Word>\r\n<Word Height='00023' StartingX='01367'  EndingX='01414'  StartingY='03422' Length='3'  Confidence='0,0,0'  >Mac</Word>\r\n<Word Height='00023' StartingX='01436'  EndingX='01562'  StartingY='03423' Length='7'  Confidence='30,30,0,0,0,0,0'  >UNIFORM</Word>\r\n<Word Height='00023' StartingX='01601'  EndingX='01795'  StartingY='03423' Length='10'  Confidence='0,0,0,0,0,30,30,0,0,0'  >INSTRUMENT</Word>\r\n</Words>\r\n</Line>\r\n<Line BaseLine='36.13' Height='00.31' >\r\n<LineText>~X[03.41]cx~X[04.15]~Y[00.03]5~Y.~Y[00.03]7~Y4~X[17.32]~Y[00.07]F~Yo~Y[00.07]r~Ym ~Y[00.08]3~Y2~Y[00.08]0~Y0~X[19.28]~Y[00.08]1~Y/~Y[00.08]0~Y1~X[20.29]~F~Y[00.08](~Yp~Y[00.09]a~Yg~Y[00.09]e ~Y3 ~Y[00.09]o~Yf~Y[00.09]3 ~Yp~Y[00.09]a~Yg~Y[00.09]e~Ys~Y[00.09])~Y~F</LineText>\r\n<Words>\r\n<Word Height='00022' StartingX='00328'  EndingX='00343'  StartingY='03460' Length='2'  Confidence='198,198'  >cx</Word>\r\n<Word Height='00022' StartingX='00399'  EndingX='00437'  StartingY='03455' Length='4'  Confidence='0,0,0,0'  >5.74</Word>\r\n<Word Height='00022' StartingX='01665'  EndingX='01715'  StartingY='03459' Length='4'  Confidence='0,0,0,0'  >Form</Word>\r\n<Word Height='00022' StartingX='01756'  EndingX='01804'  StartingY='03459' Length='4'  Confidence='0,0,0,0'  >3200</Word>\r\n<Word Height='00022' StartingX='01854'  EndingX='01895'  StartingY='03460' Length='4'  Confidence='20,30,0,0'  >1/01</Word>\r\n<Word Height='00022' StartingX='01951'  EndingX='02009'  StartingY='03459' Length='5'  Confidence='10,10,10,10,30'  >(page</Word>\r\n<Word Height='00022' StartingX='02032'  EndingX='02032'  StartingY='03460' Length='1'  Confidence='40'  >3</Word>\r\n<Word Height='00022' StartingX='02056'  EndingX='02089'  StartingY='03467' Length='3'  Confidence='178,158,178'  >of3</Word>\r\n<Word Height='00022' StartingX='02110'  EndingX='02184'  StartingY='03468' Length='6'  Confidence='10,10,10,10,10,10'  >pages)</Word>\r\n</Words>\r\n</Line>\r\n\r\n</Lines>\r\n</Page>\r\n"}], "Exception": null, "ProcessingTimeInSec": 1.2968811999999998}